from typing import Protocol

from domain_services.booking.src.domains import Business
from domain_services.booking.src.domains.analytics.dto import ProductAnalyticsParams
from domain_services.booking.src.domains.appointment.dto.draft import Draft
from domain_services.booking.src.domains.business.dto import BusinessCustomerInfo
from domain_services.booking.src.domains.user.dto import User


class AnalyticService(Protocol):

    def send_draft_created(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        product_analytics_params: ProductAnalyticsParams,
        user: User | None,
        business: Business | None,
        draft: Draft | None,
        business_customer_info: BusinessCustomerInfo | None,
    ) -> None: ...

    def send_appointment_created(
        self, product_analytics_params: ProductAnalyticsParams, user: User
    ) -> None: ...
