# DISCLAIMER

This directory is prepared to easily extract domain logic from Booksy monolithic application.
As some modules are vital and extracting them partially may cause extra effort on maintaining consistent data,
*Cx Booking team* decided to perform it gradualy, first focussing on seprating domain logic.

More detailed analysis is described [here](https://booksy.atlassian.net/wiki/spaces/~896613618/pages/2612690960/Gradual+approach+to+migration+to+micro-services+architecture).

To preserve consistency with application this folder is a Python module, that is why extra `__init__.py` files are added.
