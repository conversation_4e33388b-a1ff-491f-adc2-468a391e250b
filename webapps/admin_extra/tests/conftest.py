import pytest
from django.contrib.auth.models import Group
from django.db import models
from django.http import HttpResponse
from model_bakery import baker

from lib.admin_helpers import BaseModelAdmin
from webapps.admin_extra.custom_permissions_classes import (
    GroupPermissionMixin,
    View,
)
from webapps.admin_extra.sites import BooksyAdminSitePlus
from webapps.business.models import Business
from webapps.pos.models import POS, TaxRate
from webapps.user.groups import GroupName, GroupNameV2
from webapps.warehouse.models import VolumeMeasure
from webapps.warehouse.volume_measures import VolumeMeasureEnum


@pytest.fixture(scope='function')
@pytest.mark.django_db
def business(volume_measures):
    whbus = baker.make(Business)
    whpos = baker.make(
        POS,
        business=whbus,
        active=True,
    )
    baker.make(TaxRate, pos=whpos, rate=10)
    baker.make(TaxRate, pos=whpos, rate=12)
    measure0 = VolumeMeasure.objects.get(label=VolumeMeasureEnum.PIECE)
    whbus.measures.add(measure0)
    measure1 = VolumeMeasure.objects.get(label=VolumeMeasureEnum.LITER)
    whbus.measures.add(measure1)
    measure2 = VolumeMeasure.objects.get(label=VolumeMeasureEnum.PACKAGE)
    whbus.measures.add(measure2)
    measure3 = VolumeMeasure.objects.get(label=VolumeMeasureEnum.GRAM)
    whbus.measures.add(measure3)
    whbus.save()
    yield whbus


@pytest.fixture
def dispatchable_view_factory():
    def _factory(
        full_access_group_list: list[GroupName | GroupNameV2],
        read_only_group_list: list[GroupNameV2],
    ):
        for group in [*full_access_group_list, *read_only_group_list]:
            Group.objects.get_or_create(name=group.value)

        class TestView(GroupPermissionMixin, View):
            full_access_groups = full_access_group_list
            read_only_access_groups = read_only_group_list
            permission_required = ()

            def get(self, request, *args, **kwargs):
                return HttpResponse(status=200)

            def post(self, request, *args, **kwargs):
                return HttpResponse(status=200)

            def put(self, request, *args, **kwargs):
                return HttpResponse(status=200)

            def patch(self, request, *args, **kwargs):
                return HttpResponse(status=200)

            def delete(self, request, *args, **kwargs):
                return HttpResponse(status=200)

        return TestView

    return _factory


@pytest.fixture(name='user_not_in_any_group')
def user_not_in_any_group_fixture(db):
    return baker.make('user.User', email='<EMAIL>')


@pytest.fixture(name='superuser')
def superuser_fixture(db):
    return baker.make('user.User', is_superuser=True)


@pytest.fixture
def user_in_groups_factory(db, user_not_in_any_group):
    def _factory(group_list: list[GroupName | GroupNameV2]):
        groups = []
        for group in group_list:
            group_db, _ = Group.objects.get_or_create(name=group.value)
            groups.append(group_db)
        user_not_in_any_group.groups.add(*groups)

        return user_not_in_any_group

    return _factory


class DummyModel(models.Model):
    class Meta:
        app_label = 'test_app'


@pytest.fixture
def protected_admin_view_factory():
    def _factory(
        full_access_group_list: list[GroupName | GroupNameV2],
        read_only_group_list: list[GroupNameV2],
        model=DummyModel,
    ):
        class MyModelAdmin(GroupPermissionMixin, BaseModelAdmin):
            full_access_groups = full_access_group_list
            read_only_access_groups = read_only_group_list

            def modify_actions(self, request, actions):
                actions['test_action'] = (
                    lambda *args, **kwargs: None,
                    'Test Action',
                    'Test Action',
                )
                actions.pop('delete_selected', None)
                return actions

        return MyModelAdmin(model, BooksyAdminSitePlus())

    return _factory
