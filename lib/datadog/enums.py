from os import environ
from lib.enums import StrEnum

from lib.utils import str_to_bool

COUNTRY_SUFFIX = (
    f"-{environ.get('BOOKSY_COUNTRY_CODE')}"
    if str_to_bool(environ.get("USE_UNIFIED_ENV_PROD", "False"))
    else ""
)


class DatadogCustomServices(StrEnum):
    CORE_DEEPLINKS = f'core-deeplinks{COUNTRY_SUFFIX}'
    FEATURE_FLAGS = f'feature-flags{COUNTRY_SUFFIX}'
    PUBSUB_PUBLISHER = f'core-pubsub-publisher{COUNTRY_SUFFIX}'
    PUBSUB_SUBSCRIBER = f'core-pubsub-subscriber{COUNTRY_SUFFIX}'
    TIMESLOTS_ENGINE = f'timeslots-engine{COUNTRY_SUFFIX}'
    NOTIFICATIONS = f'notifications{COUNTRY_SUFFIX}'
    DJANGO_SIGNALS = f'django-signals{COUNTRY_SUFFIX}'
    DJANGO_COMMANDS = f'django-commands{COUNTRY_SUFFIX}'


class GRPCStatusCodes(StrEnum):
    ALREADY_EXISTS = "StatusCode.ALREADY_EXISTS"
    NOT_FOUND = "StatusCode.NOT_FOUND"


class GRPCCustomMethodNames(StrEnum):
    SEARCH = "search"
    CLAIM = "claim"
