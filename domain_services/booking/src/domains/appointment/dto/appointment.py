from dataclasses import dataclass
from datetime import datetime

from django.conf import settings

from domain_services.booking.src.domains.appointment.enums import AppointmentFeatureId


@dataclass
class AppointmentItemServiceVariant:
    id: int
    name: str
    price: str | None = None
    duration: int | None = None


@dataclass
class AppointmentItemService:
    id: int
    name: str
    variant: AppointmentItemServiceVariant
    price: str | None = None
    omnibus_price: str | None = None


@dataclass
class AppointmentItem:
    id: int
    is_staffer_requested: bool
    service: AppointmentItemService
    staffer_id: int | None
    booked_from: datetime
    booked_till: datetime
    wait_time: int
    price: str | None = None
    discount: str | None = None
    price_before_discount: str | None = None


@dataclass
class Appointment:  # pylint: disable=too-many-instance-attributes
    id: int
    core_id: int
    status: str
    customer_note: str | None
    user_id: int | None
    business_id: int
    booked_from: datetime
    booked_till: datetime
    duration: int
    total: str | None
    items: list[AppointmentItem]

    @property
    def long_wait_time(self) -> bool:
        return any(
            (item.wait_time > settings.LONG_WAIT_TIME if item.wait_time else False)
            for item in self.items
        )


@dataclass(frozen=True)
class AppointmentFeature:
    feature_id: AppointmentFeatureId
    is_involved: bool
