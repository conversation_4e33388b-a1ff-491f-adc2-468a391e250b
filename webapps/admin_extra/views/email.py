import random
import string

from django.shortcuts import render

from lib import jinja_renderer
from lib.email import send_email
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.email import Mock<PERSON>mailSendForm
from webapps.notification.email_mocks.mock_email_params import EmailParameters
from webapps.user.groups import GroupNameV2


class MockEmailSendView(GroupPermissionMixin, FormView):
    full_access_groups = (GroupNameV2.ENGINEERING,)
    form_class = MockEmailSendForm
    template_name = 'admin/custom_views/email_send_form.html'
    success_url = 'email_send_form'

    def has_permission(self):
        return True

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.valid_recipient = ''

    def get(self, request, *args, **kwargs):
        form = self.get_form()
        return render(request, self.template_name, {'form': form})

    def post(self, request, *args, **kwargs):
        form = MockEmailSendForm(request.POST)
        if form.is_valid():
            clean_data = form.cleaned_data
            recipient = clean_data["email_field"]
            language = clean_data["language_field"]
            for template_number in clean_data["email_templates"]:
                scenario_name, template_name = template_number.split('/')
                self.send_mock_email(
                    recipient,
                    language,
                    scenario_name,
                    template_name,
                )
        return super().get(request, *args, **kwargs)

    def get_initial(self):
        """
        Returns the initial data to use for forms on this view.
        """
        initial = super().get_initial()
        initial["email_field"] = self.valid_recipient
        return initial

    @staticmethod
    def send_mock_email(
        recipient,
        language,
        scenario_name,
        template_name,
    ):
        """
        Send one mock email to given email address
        :param recipient: str - email of reipient
        :param language: str - language code of email
        :param template_name: name of template that correspond to
        GLOBAL_EMAIL_LIST
        in webapps/notification/email_mocks/email_list.py

        :return: void
        """

        template_args = getattr(EmailParameters, scenario_name)(template_name)

        mail_id = MockEmailSendView.subject_mail_id_generator()
        subject = ".".join(["Mock email", mail_id])
        sjr = jinja_renderer.ScenariosJinjaRenderer()
        body_email = sjr.render(
            scenario_name=scenario_name,
            template_name=template_name,
            language=language,
            template_args=template_args,
        )
        send_email(
            recipient,
            body_email,
            subject=subject,
            to_name="Mock name",
        )

    @staticmethod
    def subject_mail_id_generator(size=6):
        return ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(size))
