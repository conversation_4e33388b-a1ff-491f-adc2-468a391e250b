from unittest.mock import patch
import pytest

from django.test import TestCase, override_settings

from model_bakery import baker

from lib.feature_flag.feature.public_api import (
    PublicAPIWebhookEnabledFlag,
)
from lib.tests.utils import override_eppo_feature_flag
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.utils import get_model_path


class WebhookSignalConditionsMixin:
    def test_signal_without_defined_webhook(self):
        self.webhook.delete()
        self.assert_checksum_changes(changes=0)
        self.assert_not_schedule_webhooks_task()

    def test_signal_without_active_webhook(self):
        self.webhook.is_active = False
        self.webhook.save(update_fields=['is_active'])
        self.assert_checksum_changes(changes=0)
        self.assert_not_schedule_webhooks_task()

    def test_signal_without_active_application(self):
        self.oauth2_app.is_active = False
        self.oauth2_app.save(update_fields=['is_active'])
        self.assert_checksum_changes(changes=0)
        self.assert_not_schedule_webhooks_task()

    def test_signal_without_connected_business(self):
        self.oauth2_installation.delete()
        self.assert_checksum_changes(changes=0)
        self.assert_not_schedule_webhooks_task()


@pytest.mark.public_api
@pytest.mark.django_db
@override_settings(API_PRIVATE=True)
@override_eppo_feature_flag(
    {
        PublicAPIWebhookEnabledFlag.flag_name: True,
    }
)
class BaseWebhookSignalV04TestCase(TestCase):
    MODEL = NotImplemented
    CHECKSUM_MODEL = NotImplemented
    EVENT = NotImplemented

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = baker.make_recipe('webapps.business.business_recipe')
        cls.user = cls.business.owner
        cls.oauth2_app_owner = baker.make_recipe(
            'webapps.public_partners.oauth2_application_owner_recipe'
        )
        cls.oauth2_app = baker.make_recipe(
            'webapps.public_partners.oauth2_application_recipe',
            user=cls.oauth2_app_owner,
        )
        cls.oauth2_installation = baker.make_recipe(
            'webapps.public_partners.oauth2_installation_recipe',
            application=cls.oauth2_app,
            business=cls.business,
            user=cls.user,
        )
        cls.webhook = baker.make_recipe(
            'webapps.public_partners.webhook_recipe',
            application=cls.oauth2_app,
            event=cls.EVENT,
            payload_version=PublicAPIVersionEnum.V04,
        )
        cls.other_business = baker.make_recipe('webapps.business.business_recipe')
        cls.other_user = cls.other_business.owner
        cls.other_oauth2_app = baker.make_recipe(
            'webapps.public_partners.oauth2_application_recipe',
        )
        cls.other_oauth2_installation = baker.make_recipe(
            'webapps.public_partners.oauth2_installation_recipe',
            application=cls.other_oauth2_app,
            business=cls.other_business,
            user=cls.other_user,
        )
        cls.other_webhook = baker.make_recipe(
            'webapps.public_partners.webhook_recipe',
            application=cls.other_oauth2_app,
            event=cls.EVENT,
            payload_version=PublicAPIVersionEnum.V04,
        )

    def setUp(self) -> None:
        super().setUp()
        target = 'webapps.public_partners.checksums.base.schedule_webhooks_task'
        self.schedule_webhooks_patcher = patch(target=target)
        self.patched_schedule_webhooks_task = self.schedule_webhooks_patcher.start()

    def tearDown(self):
        self.schedule_webhooks_patcher.stop()
        super().tearDown()

    def _current_signal(self, sender=None, instance=None, **kwargs):
        raise NotImplementedError()

    def assert_checksum_changes(self, changes=1, **kwargs):
        current_count = self.CHECKSUM_MODEL.objects.count()
        with self.captureOnCommitCallbacks(execute=True):
            self._current_signal(**kwargs)
        self.assertEqual(self.CHECKSUM_MODEL.objects.count(), current_count + changes)

    def assert_schedule_webhooks_task(self, checksum):
        self.patched_schedule_webhooks_task.delay.assert_called_once()
        self.patched_schedule_webhooks_task.delay.assert_called_once_with(
            checksum_model=get_model_path(self.CHECKSUM_MODEL._meta),
            checksum_id=checksum.pk,
            event=self.EVENT,
            metadata={"entity_id": checksum.entity_id},
        )

    def assert_not_schedule_webhooks_task(self):
        self.patched_schedule_webhooks_task.delay.assert_not_called()
