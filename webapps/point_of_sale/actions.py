from dacite import from_dict

from lib.events import event_receiver
from lib.payment_gateway.entities import BalanceTransactionEntity
from lib.payment_gateway.enums import BalanceTransactionType
from lib.payment_gateway.events import (
    payment_gateway_balance_transaction_created_event,
    payment_gateway_balance_transaction_updated_event,
)
from lib.serializers import safe_get
from webapps.point_of_sale.models import (
    BasketPayment,
    CancellationFeeAuth,
)
from webapps.point_of_sale.services.basket_payment import BasketPaymentService
from webapps.point_of_sale.services.cancellation_fee_auth import CancellationFeeAuthService


@event_receiver(payment_gateway_balance_transaction_created_event)
def payment_gateway_balance_transaction_created_handler(balance_transaction: dict, **kwargs):
    """
    Event of payment and refund won't be handled here, because we expect
    it to be created via initialize_payment/initialize_refund
    """
    balance_transaction = from_dict(
        data_class=BalanceTransactionEntity,
        data=balance_transaction,
    )

    if balance_transaction.transaction_type == BalanceTransactionType.DISPUTE:
        BasketPaymentService.create_dispute_basket_payment(balance_transaction)
        return


def update_basket_payment(
    balance_transaction: BalanceTransactionEntity,
):
    basket_payment = BasketPayment.objects.filter(
        balance_transaction_id=balance_transaction.id
    ).first()

    if not basket_payment:
        return

    match balance_transaction.transaction_type:
        case BalanceTransactionType.PAYMENT:
            new_status = BasketPaymentService.map_balance_transaction_payment_status(
                balance_transaction.related_object.status,
            )

            BasketPaymentService.update_basket_payment_amount_bcr_flow(
                basket_payment=basket_payment,
                new_amount=balance_transaction.amount,
                metadata=balance_transaction.payment.metadata,
            )

        case BalanceTransactionType.REFUND:
            new_status = BasketPaymentService.map_balance_transaction_status(
                balance_transaction.status,
            )
        case _:
            return

    BasketPaymentService.update_basket_payment_details(
        basket_payment=basket_payment,
        new_status=new_status,
        error_code=safe_get(balance_transaction, ['payment', 'error_code']),
        action_required_details=safe_get(
            balance_transaction, ['payment', 'action_required_details']
        ),
    )


def update_cf_auth(
    balance_transaction: BalanceTransactionEntity,
):
    cf_auth = CancellationFeeAuth.objects.filter(
        balance_transaction_id=balance_transaction.id
    ).last()

    if not cf_auth:
        return

    new_status = CancellationFeeAuthService.map_balance_transaction_payment_status(
        balance_transaction.related_object.status
    )
    CancellationFeeAuthService.update_status(
        cf_auth=cf_auth,
        new_status=new_status,
        operator_id=None,
    )


@event_receiver(payment_gateway_balance_transaction_updated_event)
def payment_gateway_balance_transaction_updated_handler(balance_transaction: dict, **kwargs):
    balance_transaction = from_dict(
        data_class=BalanceTransactionEntity,
        data=balance_transaction,
    )

    if balance_transaction.transaction_type == BalanceTransactionType.PAYMENT:
        update_basket_payment(balance_transaction=balance_transaction)
        update_cf_auth(balance_transaction=balance_transaction)
        return

    if balance_transaction.transaction_type == BalanceTransactionType.REFUND:
        update_basket_payment(balance_transaction=balance_transaction)
        return

    if balance_transaction.transaction_type == BalanceTransactionType.DISPUTE:
        # event of dispute update won't be handled, because dispute are unchangeable
        return
