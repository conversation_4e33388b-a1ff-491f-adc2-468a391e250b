from datetime import datetime
from decimal import Decimal
from unittest.mock import MagicMock, call, patch

import braintree
from django.test import TestCase, override_settings
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC

from lib.locks import BoostChargeLock
from lib.tools import tznow
from webapps.billing.enums import SubscriptionStatus, TransactionStatus
from webapps.billing.error_handling.errors import BILLING_100_ERR
from webapps.billing.models import (
    BillingBoostPaymentRequest,
    BillingBoostPaymentRequestHistory,
    BillingBoostTransaction,
    BillingBusinessSettings,
    BillingSubscription,
    BillingTransaction,
    Business,
    PaymentProcessorError,
)
from webapps.billing.payment_processor import StripePaymentProcessor
from webapps.billing.services.boost import OverdueBoostFlowService, OverdueBoostService
from webapps.billing.services.charge import ChargeResult
from webapps.boost.apis.overdue_transactions import (
    BillingBulkPaymentResponse,
    BoostAppointmentDataClass,
    BoostTransaction,
)
from webapps.stripe_app.enums import PaymentIntentStatus
from webapps.stripe_app.errors import ErrorObject
from webapps.stripe_app.models import PaymentIntentObject
from webapps.stripe_app.services.charge import PaymentIntentResult
from webapps.stripe_app.tests.utils import stripe_api_payment_intent


class BoostServiceMixin:
    def setUp(self) -> None:  # pylint: disable=invalid-name
        super().setUp()
        self.business = baker.make(Business)
        self.settings = baker.make(BillingBusinessSettings, business=self.business)
        self.boost_transactions = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='1234',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[
                    BoostAppointmentDataClass(
                        public_id='456',
                        business_id=self.business.id,
                        gross_amount=Decimal('12.567'),
                        client='client 1!',
                        date=tznow(),
                    )
                ],
                description='description',
                created=tznow(),
                currency='pln',
            ),
            BoostTransaction(
                public_id='999',
                charge_lock_key='12345',
                business_id=self.business.id,
                gross_amount=Decimal('11.5'),
                appointments=[
                    BoostAppointmentDataClass(
                        public_id='654',
                        business_id=self.business.id,
                        gross_amount=Decimal('0.1'),
                        client='client 2!',
                        date=tznow(),
                    ),
                    BoostAppointmentDataClass(
                        public_id='111',
                        business_id=self.business.id,
                        gross_amount=Decimal(56),
                        client='client 3!',
                        date=tznow(),
                    ),
                ],
                description='description',
                created=tznow(),
                currency='pln',
            ),
        ]


class TestOverdueBoostService(BoostServiceMixin, TestCase):
    def test_get_object(self):
        payment_request = baker.make(BillingBoostPaymentRequest)

        self.assertEqual(
            OverdueBoostService._get_object(  # pylint: disable=protected-access
                model_klass=BillingBoostPaymentRequest,
                object_or_id=payment_request.id,
            ),
            payment_request,
        )

        self.assertEqual(
            OverdueBoostService._get_object(  # pylint: disable=protected-access
                model_klass=BillingBoostPaymentRequest,
                object_or_id=str(payment_request.id),
            ),
            payment_request,
        )

        self.assertEqual(
            OverdueBoostService._get_object(  # pylint: disable=protected-access
                model_klass=BillingBoostPaymentRequest,
                object_or_id=payment_request,
            ),
            payment_request,
        )

    def test_build_billing_bulk_payment_response__stripe(self):
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
        )
        payment_response = ChargeResult(
            is_success=True,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.SUCCEEDED,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
        )

        result = OverdueBoostService._build_billing_bulk_payment_response(  # pylint: disable=protected-access
            payment_request=payment_request,
            payment_response=payment_response,
        )

        self.assertIsInstance(result, BillingBulkPaymentResponse)
        self.assertTrue(result.is_success)
        self.assertEqual(result.public_id, payment_request.public_id)
        self.assertEqual(result.payment_processor, 'S')
        self.assertEqual(
            result.payment_processor_response,
            PaymentIntentResult(
                is_success=True,
                payment_intent=payment_response.transaction,
            ),
        )

    def test_build_billing_bulk_payment_response__braintree(self):
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
        )
        payment_response = braintree.successful_result.SuccessfulResult(
            {'transaction': MagicMock(id='abc')},
        )

        result = OverdueBoostService._build_billing_bulk_payment_response(  # pylint: disable=protected-access
            payment_request=payment_request,
            payment_response=payment_response,
        )

        self.assertIsInstance(result, BillingBulkPaymentResponse)
        self.assertTrue(result.is_success)
        self.assertEqual(result.public_id, payment_request.public_id)
        self.assertEqual(result.payment_processor, 'B')
        self.assertEqual(result.payment_processor_response, payment_response)

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_get_and_validate_transactions__success(self, mocked_interface):
        mocked_interface.return_value = self.boost_transactions

        response = OverdueBoostService.get_and_validate_transactions(business_id=self.business.id)

        self.assertIsNone(response.error)
        self.assertEqual(response.total_amount, Decimal(123) + Decimal('11.5'))
        self.assertListEqual(response.boost_transactions, self.boost_transactions)
        self.assertEqual(response.currency, 'pln')

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_get_and_validate_transactions__error(self, mocked_interface):
        mocked_interface.return_value = []

        response = OverdueBoostService.get_and_validate_transactions(business_id=self.business.id)

        self.assertEqual(response.error.error_code, BILLING_100_ERR.code)
        self.assertEqual(response.error.message, 'You currently have no overdue payments.')
        self.assertIsNone(response.total_amount)
        self.assertIsNone(response.boost_transactions)
        self.assertIsNone(response.currency)

    @override_settings(SAVE_HISTORY=True)
    def test_initialize_payment_request_state(self):
        payment_request, transactions = OverdueBoostService.initialize_payment_request_state(
            business_id=self.business.id,
            amount=Decimal(100),
            currency='pln',
            boost_transactions=self.boost_transactions,
            operator_id=self.business.owner_id,
        )

        self.assertEqual(payment_request.business, self.business)
        self.assertEqual(payment_request.amount, Decimal(100))
        self.assertEqual(payment_request.currency, 'pln')
        self.assertEqual(payment_request.status, TransactionStatus.INITIAL)

        self.assertTrue(
            BillingBoostPaymentRequestHistory.objects.filter(
                metadata__icontains='initialize_payment_request',
                operator=self.business.owner_id,
            ).exists()
        )

        self.assertEqual(len(transactions), 2)

        api_transaction = self.boost_transactions[0]
        db_transaction = BillingBoostTransaction.objects.get(boost_id=api_transaction.public_id)

        self.assertEqual(db_transaction.payment_request, payment_request)
        self.assertEqual(db_transaction.amount, Decimal(123))
        self.assertEqual(db_transaction.status, TransactionStatus.INITIAL)
        self.assertTrue(db_transaction.metadata)
        self.assertEqual(BoostTransaction.from_dict(db_transaction.metadata), api_transaction)

    @parameterized.expand(
        [
            (TransactionStatus.CHARGED,),
            (TransactionStatus.FAILED,),
        ]
    )
    @override_settings(SAVE_HISTORY=True)
    def test_finalize_payment_request_state(self, transaction_status):
        billing_transaction = baker.make(BillingTransaction, status=transaction_status)
        payment_request = baker.make(BillingBoostPaymentRequest, status=TransactionStatus.INITIAL)
        baker.make(
            BillingBoostTransaction,
            status=TransactionStatus.INITIAL,
            payment_request=payment_request,
            _quantity=2,
        )

        payment_request = OverdueBoostService.finalize_payment_request_state(
            payment_request=payment_request.id,
            billing_transaction=billing_transaction.id,
            operator_id=self.business.owner_id,
        )

        self.assertTrue(
            BillingBoostPaymentRequestHistory.objects.filter(
                metadata__icontains='finalize_payment_request',
                operator=self.business.owner_id,
            ).exists()
        )

        payment_request.refresh_from_db()

        self.assertEqual(payment_request.status, transaction_status)
        self.assertEqual(payment_request.transaction, billing_transaction)

        self.assertEqual(
            BillingBoostTransaction.objects.filter(
                payment_request=payment_request,
                status=transaction_status,
            ).count(),
            2,
        )

    @parameterized.expand(
        [
            (TransactionStatus.CHARGED, TransactionStatus.FAILED),
            (TransactionStatus.FAILED, TransactionStatus.CHARGED),
        ]
    )
    @override_settings(SAVE_HISTORY=True)
    def test_finalize_payment_request_state__dont_save_status(
        self, transaction_status, payment_request_status
    ):
        billing_transaction = baker.make(BillingTransaction, status=transaction_status)
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            status=payment_request_status,
        )

        payment_request = OverdueBoostService.finalize_payment_request_state(
            payment_request=payment_request.id,
            billing_transaction=billing_transaction,
            operator_id=self.business.owner_id,
        )

        payment_request.refresh_from_db()

        self.assertEqual(payment_request.status, payment_request_status)
        self.assertTrue(
            BillingBoostPaymentRequestHistory.objects.filter(
                metadata__icontains='finalize_payment_request',
                operator=self.business.owner_id,
            ).exists()
        )

    @override_settings(SAVE_HISTORY=True)
    def test_finalize_payment_request_state__dont_save_transaction(self):
        billing_transaction = baker.make(BillingTransaction, status=TransactionStatus.CHARGED)
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            status=TransactionStatus.INITIAL,
            transaction=billing_transaction,
        )

        payment_request = OverdueBoostService.finalize_payment_request_state(
            payment_request=payment_request.id,
            billing_transaction=billing_transaction,
            operator_id=self.business.owner_id,
        )

        payment_request.refresh_from_db()

        self.assertEqual(payment_request.transaction, billing_transaction)
        self.assertTrue(
            BillingBoostPaymentRequestHistory.objects.filter(
                metadata__icontains='finalize_payment_request',
                operator=self.business.owner_id,
            ).exists()
        )

    def test_finalize_payment_request_state__no_billing_transaction(self):
        payment_request = baker.make(BillingBoostPaymentRequest, status=TransactionStatus.INITIAL)
        baker.make(
            BillingBoostTransaction,
            status=TransactionStatus.INITIAL,
            payment_request=payment_request,
            _quantity=2,
        )

        payment_request = OverdueBoostService.finalize_payment_request_state(
            payment_request=payment_request.id,
            billing_transaction=None,
            operator_id=self.business.owner_id,
        )

        payment_request.refresh_from_db()

        self.assertEqual(payment_request.status, TransactionStatus.FAILED)
        self.assertIsNone(payment_request.transaction)

        self.assertEqual(
            BillingBoostTransaction.objects.filter(
                payment_request=payment_request,
                status=TransactionStatus.FAILED,
            ).count(),
            2,
        )

    def test_boost_locks(self):
        success, locks = OverdueBoostService.lock_boost_charge(
            boost_transactions=self.boost_transactions
        )
        self.assertTrue(success)
        self.assertEqual(len(locks), 2)

        success, _ = OverdueBoostService.lock_boost_charge(
            boost_transactions=self.boost_transactions
        )
        self.assertFalse(success)

        OverdueBoostService.try_to_unlock_boost_charge(locks=locks)

        success, locks = OverdueBoostService.lock_boost_charge(
            boost_transactions=self.boost_transactions
        )
        self.assertTrue(success)
        self.assertEqual(len(locks), 2)

        OverdueBoostService.try_to_unlock_boost_charge(locks=locks)

    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_create_transaction(self, method_patched):
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
            status=TransactionStatus.INITIAL,
            amount=Decimal(99),
            currency='pln',
        )
        baker.make(
            BillingBoostTransaction,
            status=TransactionStatus.INITIAL,
            payment_request=payment_request,
            _quantity=2,
        )

        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = StripePaymentProcessor

            OverdueBoostService.create_transaction(
                payment_request=payment_request,
                operator_id=self.business.owner_id,
            )

        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=self.business.id,
                amount=Decimal('99'),
                currency='pln',
                description='Boost overdue',
                metadata={
                    'operator_id': self.business.owner_id,
                    'action': 'batch_boost_overdue_charge',
                    'boost_payment_request_id': payment_request.id,
                    'transaction_source': 'boost_overdue',
                },
            ),
        )

    @parameterized.expand(
        [
            (True,),
            (False,),
        ]
    )
    def test_save_transaction__success(self, has_current_subscription):
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
        )

        if has_current_subscription:
            current_subscription = baker.make(
                BillingSubscription,
                business_id=self.business.pk,
                date_start=datetime(2021, 5, 1, tzinfo=UTC),
                status=SubscriptionStatus.ACTIVE,
            )
        else:
            current_subscription = None

        payment_response = ChargeResult(
            is_success=True,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.SUCCEEDED,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
        )

        object_id = OverdueBoostService.save_transaction(
            payment_request=payment_request,
            payment_response=payment_response,
        )

        billing_transaction = BillingTransaction.objects.filter(
            transaction_source='boost_overdue',
            business=self.business,
        ).get()

        self.assertEqual(billing_transaction.id, object_id)
        self.assertEqual(billing_transaction.subscription, current_subscription)

    def test_save_transaction__error(self):
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
        )

        payment_response = ChargeResult(
            is_success=False,
            error=ErrorObject(
                payment_intent=stripe_api_payment_intent(stripe_id='py_123', amount=Decimal(123))
            ),
        )

        object_id = OverdueBoostService.save_transaction(
            payment_request=payment_request,
            payment_response=payment_response,
        )

        self.assertFalse(BillingTransaction.objects.count())
        self.assertEqual(
            PaymentProcessorError.objects.filter(business=self.business, event_type='O').count(),
            1,
        )
        self.assertIsNone(object_id)

    @patch('webapps.billing.interfaces.boost.set_as_paid')
    def test_set_boost_transactions_as_paid__from_boost_transactions(self, mocked_interface):
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
        )
        payment_response = ChargeResult(
            is_success=True,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.SUCCEEDED,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
        )

        OverdueBoostService.set_boost_transactions_as_paid(
            payment_request=payment_request,
            boost_transactions=self.boost_transactions,
            payment_response=payment_response,
        )

        generated_payment_response = OverdueBoostService._build_billing_bulk_payment_response(  # pylint: disable=protected-access
            payment_request=payment_request, payment_response=payment_response
        )

        self.assertEqual(
            mocked_interface.call_args,
            call(
                business_id=self.business.id,
                transactions=self.boost_transactions,
                payment_response=generated_payment_response,
            ),
        )

    @patch('webapps.billing.interfaces.boost.set_as_paid')
    def test_set_boost_transactions_as_paid__from_payment_request(self, mocked_interface):
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
        )
        for boost_transaction in self.boost_transactions:
            baker.make(
                BillingBoostTransaction,
                payment_request=payment_request,
                metadata=boost_transaction.to_dict(encode_json=True),
            )

        payment_response = ChargeResult(
            is_success=True,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.SUCCEEDED,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
        )

        OverdueBoostService.set_boost_transactions_as_paid(
            payment_request=payment_request,
            payment_response=payment_response,
        )

        generated_payment_response = OverdueBoostService._build_billing_bulk_payment_response(  # pylint: disable=protected-access
            payment_request=payment_request, payment_response=payment_response
        )

        self.assertEqual(
            mocked_interface.call_args,
            call(
                business_id=self.business.id,
                transactions=self.boost_transactions,
                payment_response=generated_payment_response,
            ),
        )


@patch('webapps.billing.interfaces.boost.overdue_transactions')
class TestOverdueBoostFlowServiceChargeOverdue(BoostServiceMixin, TestCase):
    def test_error_validate_transactions(self, mocked_interface):
        mocked_interface.return_value = []

        result = OverdueBoostFlowService.charge_overdue(business_id=self.business.id)
        self.assertDictEqual(
            result,
            {'error_code': 100, 'message': 'You currently have no overdue payments.'},
        )

    def test_error_boost_lock(self, mocked_interface):
        mocked_interface.return_value = self.boost_transactions

        lock_ = BoostChargeLock.lock(self.boost_transactions[0].charge_lock_key)

        result = OverdueBoostFlowService.charge_overdue(business_id=self.business.id)
        self.assertDictEqual(
            result,
            {
                'error_code': 132,
                'message': 'Retry charge skipped - conflicting billing process in progress.',
            },
        )

        BoostChargeLock.try_to_unlock(lock_)

    @patch.object(OverdueBoostService, 'create_transaction')
    @patch.object(OverdueBoostService, 'set_boost_transactions_as_paid')
    def test_failed_transaction(
        self,
        mocked_set_boost_transactions_as_paid,
        mocked_create_transaction,
        mocked_interface,
    ):
        mocked_interface.return_value = self.boost_transactions
        mocked_create_transaction.return_value = ChargeResult(
            is_success=False,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.REQUIRES_CONFIRMATION,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
            error=ErrorObject(
                payment_intent=stripe_api_payment_intent(stripe_id='py_123', amount=Decimal(123))
            ),
        )

        result = OverdueBoostFlowService.charge_overdue(business_id=self.business.id)

        payment_request = BillingBoostPaymentRequest.objects.get(
            business_id=self.business.id,
            status=TransactionStatus.FAILED,
            transaction__isnull=False,
        )

        self.assertEqual(
            BillingBoostTransaction.objects.filter(
                payment_request=payment_request, status=TransactionStatus.FAILED
            ).count(),
            2,
        )
        self.assertTrue(BillingTransaction.objects.filter(business_id=self.business.id).count())

        self.assertFalse(mocked_set_boost_transactions_as_paid.call_count)

        self.assertEqual(
            result['message'],
            'We were unable to process your payment but do not worry. We can help. '
            'Please contact our Support Team for assistance.',
        )
        self.assertEqual(result['error_code'], 'contact_with_us')

    @patch.object(OverdueBoostService, 'create_transaction')
    @patch.object(OverdueBoostService, 'set_boost_transactions_as_paid')
    def test_successful_transaction(
        self,
        mocked_set_boost_transactions_as_paid,
        mocked_create_transaction,
        mocked_interface,
    ):
        mocked_interface.return_value = self.boost_transactions
        mocked_create_transaction.return_value = ChargeResult(
            is_success=True,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.SUCCEEDED,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
        )

        result = OverdueBoostFlowService.charge_overdue(business_id=self.business.id)

        payment_request = BillingBoostPaymentRequest.objects.filter(
            business_id=self.business.id
        ).get()

        self.assertEqual(
            BillingBoostTransaction.objects.filter(
                payment_request=payment_request,
                status=TransactionStatus.CHARGED,
            ).count(),
            2,
        )
        self.assertTrue(BillingTransaction.objects.filter(business_id=self.business.id).count())

        self.assertEqual(
            mocked_set_boost_transactions_as_paid.call_args,
            call(
                payment_request=payment_request,
                boost_transactions=self.boost_transactions,
                payment_response=mocked_create_transaction.return_value,
            ),
        )

        self.assertDictEqual(
            result,
            {
                'message': 'Overdue transactions have been successfully paid.',
            },
        )


class TestOverdueBoostFlowServiceFinishInterruptedProcess(BoostServiceMixin, TestCase):
    @patch.object(OverdueBoostService, 'set_boost_transactions_as_paid')
    def test_successful_transaction(
        self,
        mocked_set_boost_transactions_as_paid,
    ):
        payment_response = ChargeResult(
            is_success=True,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.SUCCEEDED,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
        )
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            status=TransactionStatus.INITIAL,
            business_id=self.business.id,
        )
        billing_transaction = baker.make(
            BillingTransaction,
            status=TransactionStatus.CHARGED,
            business_id=self.business.id,
        )

        result = OverdueBoostFlowService.finish_interrupted_process(
            payment_request=payment_request,
            billing_transaction=billing_transaction,
            payment_response=payment_response,
        )

        payment_request.refresh_from_db()

        self.assertEqual(payment_request.status, TransactionStatus.CHARGED)
        self.assertEqual(payment_request.transaction, billing_transaction)

        self.assertEqual(
            mocked_set_boost_transactions_as_paid.call_args,
            call(
                payment_request=payment_request,
                payment_response=payment_response,
            ),
        )

        self.assertDictEqual(
            result,
            {
                'message': 'Boost overdue charged successfully',
            },
        )

    @patch.object(OverdueBoostService, 'set_boost_transactions_as_paid')
    def test_failed_transaction(
        self,
        mocked_set_boost_transactions_as_paid,
    ):
        payment_response = ChargeResult(
            is_success=False,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='pi_123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.REQUIRES_CONFIRMATION,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
            ),
            error=ErrorObject(
                payment_intent=stripe_api_payment_intent(
                    stripe_id='py_123',
                    amount=Decimal(123),
                )
            ),
        )
        payment_request = baker.make(
            BillingBoostPaymentRequest,
            status=TransactionStatus.INITIAL,
            business_id=self.business.id,
        )
        billing_transaction = baker.make(
            BillingTransaction,
            status=TransactionStatus.FAILED,
            business_id=self.business.id,
        )

        result = OverdueBoostFlowService.finish_interrupted_process(
            payment_request=payment_request,
            billing_transaction=billing_transaction,
            payment_response=payment_response,
        )

        payment_request.refresh_from_db()

        self.assertEqual(payment_request.status, TransactionStatus.FAILED)
        self.assertEqual(payment_request.transaction, billing_transaction)

        self.assertFalse(mocked_set_boost_transactions_as_paid.call_count)

        self.assertEqual(
            result['message'],
            'We were unable to process your payment but do not worry. We can help. '
            'Please contact our Support Team for assistance.',
        )
        self.assertEqual(result['error_code'], 'contact_with_us')
