import argparse
import logging
import sys
from pathlib import Path
from typing import Generator, NamedTuple

import requirements


def get_arg_parser() -> argparse.ArgumentParser:
    parser_ = argparse.ArgumentParser(
        description="Script comparing monolit requirements with one from service"
    )
    parser_.add_argument("--ref-path", default="../../requirements.txt")
    parser_.add_argument("--target-path")

    return parser_


def _parse_requirements(path: Path) -> Generator[tuple[str, str], None, None]:
    with open(path, 'r', encoding="utf-8") as file_:
        for req in requirements.parse(file_):
            yield req.name, (
                spec[0][1]
                if ((spec := req.specs) and spec[0][0] == "==")
                else "non-specific-version"
            )


class RequirementsDiff(NamedTuple):
    name: str
    ref_version: str
    target_version: str


def compare_requirmenets(ref_path: Path, target_path: Path) -> list[RequirementsDiff]:
    ref_requirements_map = dict(_parse_requirements(ref_path))
    return [
        RequirementsDiff(
            name=req_name,
            ref_version=ref_version,
            target_version=target_version,
        )
        for req_name, target_version in _parse_requirements(target_path)
        if ((ref_version := ref_requirements_map.get(req_name)) and target_version != ref_version)
    ]


logger = logging.getLogger()

if __name__ == "__main__":
    parser = get_arg_parser()
    args = parser.parse_args()
    ref_path_ = Path(args.ref_path)
    target_path_ = Path(args.target_path)
    diffs = compare_requirmenets(ref_path_, target_path_)
    if diffs:
        logger.error("version conflicts:")
        for req_diff in diffs:
            logger.error(
                f"{req_diff.name}: ref={req_diff.ref_version} " f"target={req_diff.target_version}"
            )
        sys.exit(1)
    else:
        sys.exit(0)
