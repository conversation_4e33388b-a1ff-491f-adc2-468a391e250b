import importlib
from contextlib import contextmanager
from unittest.mock import patch

import pytest
from django.conf import settings
from django.utils.translation import get_language

from country_config import Country
from lib.elasticsearch.consts import ESIndex
from settings import elasticsearch as es_settings
from settings.es_countries.languages import (
    get_lang_filters,
    get_lang_index_settings,
    get_text_multi_fields,
)
from webapps.elasticsearch.elastic import ELASTIC


def clean_elastisearch_index_helper(index_name: ESIndex):
    """Setup elasticsearch index if exists cleans all docs from it"""
    index = ELASTIC.indices[index_name]
    if not index.exists():
        index.save_with_alias(force=True)
        return index
    return delete_documents_from_index(index)


def delete_documents_from_index(index: 'Index'):
    """Delete all documents in given index. Max number of docs 10000. On conflict proceed.

    For more details see:
    https://elasticsearch-py.readthedocs.io/en/7.x/api.html
    https://elasticsearch-dsl.readthedocs.io/en/latest/faceted_search.html?highlight=MatchAll#usage
    https://www.elastic.co/guide/en/elasticsearch/reference/master/docs-delete-by-query.html#docs-delete-by-query
    """
    index.connection.delete_by_query(
        index=index.name,
        body={"query": {"match_all": {}}},
        # 10000 due to the limitations of search
        # index.max_result_window
        # https://www.elastic.co/guide/en/elasticsearch/reference/current/index-modules.html#dynamic-index-settings
        # but for tests should be enough
        max_docs=10000,
        refresh=True,  # make changes visible after operation succeed
        conflicts='proceed',
        wait_for_active_shards='all',
        wait_for_completion=True,
    )
    return index


def change_index_language_settings(index: 'Index', lang_code: str):
    """Change language settings for given index"""
    index.clear_cache()
    lang_index_settings = get_lang_index_settings([lang_code])
    # do not do this at home, it is just for tests
    index.close()  # index should be closed to change analyzers
    index._update_analysis(lang_index_settings)  # pylint: disable=protected-access
    index.save()
    index.open()
    return index


def rebuild_index(index: 'Index'):
    """Deletes and creates new elasticsearch index"""
    index.delete(ignore=404, force=True)
    index.save_with_alias(force=True)
    return index


def rebuild_index_change_language_settings(index: 'Index', lang_code: str):
    """Deletes and creates new elasticsearch index with new language settings"""
    index.delete(ignore=404, force=True)
    lang_index_settings = get_lang_index_settings([lang_code])
    index._update_analysis(lang_index_settings)  # pylint: disable=protected-access
    index.save_with_alias(force=True)
    index.open()
    return index


@contextmanager
def activate_country(country: str):
    """
    We can't truly switch countries, just pretend for the sake of creating
    a localized index.
    """
    es_country_settings = importlib.import_module(f'settings.es_countries.{country}')
    es_languages = es_country_settings.ES_LANGUAGES

    overrides = {
        attr: getattr(es_country_settings, attr)
        for attr in dir(es_country_settings)
        if attr.startswith('ES_')
    }
    overrides.update(
        dict(
            ES_TEXT_MULTI_FIELDS=get_text_multi_fields(es_languages),
            ES_LANG_INDEX_SETTINGS=get_lang_index_settings(es_languages),
            ES_LANG_FILTERS=get_lang_filters(es_languages),
        )
    )

    with (
        patch.multiple(es_settings, **overrides),
        patch.multiple(settings, API_COUNTRY=country, **overrides),
    ):
        yield


def new_es_index_factory(es_index: ESIndex, country: Country, lang_code: str, **kwargs):
    """Creates new fixture. This fixture are responsible for:
        * creating new index from scratch with new language settings.
    Before using it consider using update_es_index_factory.
    """

    @pytest.fixture(**kwargs)
    def _fixture():
        with activate_country(country):
            index = ELASTIC.indices[es_index]
            index = rebuild_index_change_language_settings(index, lang_code)
            yield index
        rebuild_index(index)

    return _fixture


def update_es_index_factory(es_index: ESIndex, country: Country, lang_code: str, **kwargs):
    """Creates new fixture. This fixture are responsible for:
    * deleting all existing document from given index
    * updating index with new language settings and reverting this settings back after test
    """
    old_language = get_language()
    old_country_code = settings.API_COUNTRY

    @pytest.fixture(**kwargs)
    def _fixture():
        with activate_country(country):
            index = ELASTIC.indices[es_index]
            # delete all document from index
            delete_documents_from_index(index)
            # change language settings
            index = change_index_language_settings(index, lang_code)
            yield index
        #  revert changes back
        with activate_country(old_country_code):
            change_index_language_settings(index, old_language)

    return _fixture


def clean_es_index_factory(es_index: ESIndex, **kwargs):
    """Creates fixture that deletes all documents in ElasticSearch index
    Moved from webapps/business/conftest.py
    """

    @pytest.fixture(**kwargs)
    def _fixture():
        index = ELASTIC.indices[es_index]
        index = delete_documents_from_index(index)
        yield index

    return _fixture
