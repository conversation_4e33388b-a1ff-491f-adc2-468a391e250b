from datetime import timedelta, datetime
from decimal import Decimal
from unittest.mock import patch
from urllib.parse import urlencode

import pytz
from django.test import override_settings
from django.urls.base import reverse
from freezegun import freeze_time
from model_bakery import baker
from segment.analytics import Client
from bs4 import BeautifulSoup

from lib.tools import id_to_external_api, tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.admin import BillingBusinessAdmin, BillingBusinessOfferFilter
from webapps.billing.admin.offer import ActiveOffersFilter, ProductOfferAdmin
from webapps.billing.enums import DiscountType, PaymentPeriod
from webapps.billing.forms import ProductOfferForm
from webapps.billing.models import (
    BillingBusinessOffer,
    BillingDiscountCode,
    BillingProductOffer,
    BillingSubscription,
)
from webapps.billing.tests.utils import (
    billing_business,
    create_product_offer_from_saas,
    create_saas,
    create_sms_add_on,
    create_staff_add_on,
)
from webapps.business.models import Business


class TestProductOfferForm(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.saas = create_saas(
            staff_add_on=create_staff_add_on(),
            sms_add_on=create_sms_add_on(),
        )
        self.saas2 = create_saas(
            staff_add_on=create_staff_add_on(),
            sms_add_on=create_sms_add_on(),
        )
        self.saas_invalid = create_saas(
            staff_add_on=create_staff_add_on(
                payment_period=PaymentPeriod.days_21.value[0],
            ),
            sms_add_on=create_sms_add_on(),
        )

    @property
    def no_discount_data(self):
        return dict(
            discount_type=DiscountType.NO_DISCOUNT,
            discount_duration=None,
            discount_frac=None,
            discount_amount=None,
        )

    def get_fixed_discount_data(self, amount, duration=None):
        return dict(
            discount_type=DiscountType.FIXED,
            discount_duration=duration,
            discount_frac=None,
            discount_amount=amount,
        )

    def get_frac_discount_data(self, fraction, duration=None):
        return dict(
            discount_type=DiscountType.PERCENTAGE,
            discount_duration=duration,
            discount_frac=fraction,
            discount_amount=None,
        )

    def assert_items_created(self, product):
        self.assertTrue(product.offer_items.filter(active=True).count(), 3)
        self.assertTrue(product.offer_items.filter(product_id=self.saas.id).exists())
        self.assertTrue(product.offer_items.filter(product_id=self.saas.sms_add_on_id).exists())
        self.assertTrue(product.offer_items.filter(product_id=self.saas.staff_add_on_id).exists())

    def assert_discount(self, item, discount_data):
        if discount_data['discount_frac']:
            discount_data['discount_frac'] = Decimal(discount_data['discount_frac']).quantize(
                Decimal('0.01')
            )
        self.assertEqual(item.discount_type, discount_data['discount_type'])
        self.assertEqual(item.discount_duration, discount_data['discount_duration'])
        self.assertEqual(item.discount_frac, discount_data['discount_frac'])
        self.assertEqual(item.discount_amount, discount_data['discount_amount'])

    def test_no_discount(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        self.assert_items_created(product)
        saas_item = product.offer_items.get(product_id=self.saas.id)
        sms_item = product.offer_items.get(product_id=self.saas.sms_add_on_id)
        staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
        self.assert_discount(saas_item, self.no_discount_data)
        self.assert_discount(staffer_item, self.no_discount_data)
        self.assert_discount(sms_item, self.no_discount_data)

    def test_fixed_discount_saas(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.FIXED,
                discount_saas=3,
                saas=self.saas,
                active=True,
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        self.assert_items_created(product)
        saas_item = product.offer_items.get(product_id=self.saas.id)
        sms_item = product.offer_items.get(product_id=self.saas.sms_add_on_id)
        staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
        self.assert_discount(saas_item, self.get_fixed_discount_data(3))
        self.assert_discount(staffer_item, self.no_discount_data)
        self.assert_discount(sms_item, self.no_discount_data)

    def test_frac_discount_saas(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.PERCENTAGE,
                discount_saas=60,
                saas=self.saas,
                active=True,
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        self.assert_items_created(product)
        saas_item = product.offer_items.get(product_id=self.saas.id)
        sms_item = product.offer_items.get(product_id=self.saas.sms_add_on_id)
        staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
        self.assert_discount(saas_item, self.get_frac_discount_data(0.60))
        self.assert_discount(staffer_item, self.no_discount_data)
        self.assert_discount(sms_item, self.no_discount_data)

    def test_fixed_discount_staffer(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.FIXED,
                discount_staff_add_on=7,
                discount_duration=5,
                saas=self.saas,
                active=True,
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        self.assert_items_created(product)
        saas_item = product.offer_items.get(product_id=self.saas.id)
        sms_item = product.offer_items.get(product_id=self.saas.sms_add_on_id)
        staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
        self.assert_discount(saas_item, self.no_discount_data)
        self.assert_discount(staffer_item, self.get_fixed_discount_data(7, duration=5))
        self.assert_discount(sms_item, self.no_discount_data)

    def test_frac_discount_staffer(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.PERCENTAGE,
                discount_staff_add_on=70,
                saas=self.saas,
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        self.assert_items_created(product)
        saas_item = product.offer_items.get(product_id=self.saas.id)
        sms_item = product.offer_items.get(product_id=self.saas.sms_add_on_id)
        staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
        self.assert_discount(saas_item, self.no_discount_data)
        self.assert_discount(staffer_item, self.get_frac_discount_data(0.70))
        self.assert_discount(sms_item, self.no_discount_data)

    def test_fixed_discount_invalid(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.FIXED,
                discount_saas=self.saas.unit_price + 1,
                saas=self.saas,
            )
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'Fixed discount cannot be greater than product unit price (10.000)',
            str(form.errors),
        )

    def test_frac_discount_invalid(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.PERCENTAGE,
                discount_saas=101,
                saas=self.saas,
            )
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'Percentage discount should be in range: 0 - 100',
            str(form.errors),
        )

    def test_default_offer_already_exists(self):
        baker.make(
            BillingProductOffer,
            default=True,
        )
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
                default=True,
            )
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'There can be only one default offer at a time.',
            str(form.errors),
        )

    def test_active_for_b2b_referral_offer_already_exists(self):
        baker.make(
            BillingProductOffer,
            active_for_b2b_referral=True,
        )
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
                active_for_b2b_referral=True,
            )
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'There can be only one active_for_b2b_referral offer at a time.',
            str(form.errors),
        )

    def test_discount_duration_valid(self):
        for discount_type in (
            DiscountType.FIXED,
            DiscountType.PERCENTAGE,
        ):
            form = ProductOfferForm(
                data=dict(
                    name='test offer',
                    discount_type=discount_type,
                    discount_saas=3,
                    discount_staff_add_on=3,
                    discount_duration=6,
                    saas=self.saas,
                )
            )
            self.assertTrue(form.is_valid())
            product = form.save()
            self.assert_items_created(product)
            saas_item = product.offer_items.get(product_id=self.saas.id)
            staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
            self.assertEqual(saas_item.discount_duration, 6)
            self.assertEqual(staffer_item.discount_duration, 6)

    def test_discount_duration_invalid(self):
        for discount_type in (
            DiscountType.FIXED,
            DiscountType.PERCENTAGE,
        ):
            form = ProductOfferForm(
                data=dict(
                    name='test offer',
                    discount_type=discount_type,
                    discount_duration=15,
                    saas=self.saas,
                )
            )
            self.assertFalse(form.is_valid())
            self.assertIn('discount_duration', form.errors)

    def test_detach_offer_items(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        form = ProductOfferForm(
            instance=product,
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas2,
            ),
        )
        self.assertTrue(form.is_valid())
        product_changed = form.save()
        self.assert_items_created(product_changed)
        self.assertTrue(product_changed.offer_items.count(), 6)

    def test_view_form(self):
        offer = create_product_offer_from_saas(self.saas)
        form = ProductOfferForm(instance=offer)
        self.assertEqual(form.initial['name'], offer.name)
        self.assertEqual(form.initial['default'], True)
        self.assertEqual(form.initial['saas'], self.saas.id)
        self.assertEqual(form.initial['staff_add_on'], self.saas.staff_add_on.id)
        self.assertEqual(form.initial['sms_add_on'], self.saas.sms_add_on.id)
        self.assertEqual(form.initial['discount_type'], DiscountType.NO_DISCOUNT)

    def test_view_form_saas_discount(self):
        offer = create_product_offer_from_saas(self.saas)
        offer_item = offer.offer_items.get(product=self.saas)
        offer_item.discount_type = DiscountType.FIXED
        offer_item.discount_amount = 3
        offer_item.discount_duration = 5
        offer_item.save()
        offer.refresh_from_db()
        form = ProductOfferForm(instance=offer)
        self.assertEqual(form.initial['discount_type'], DiscountType.FIXED)
        self.assertEqual(form.initial['discount_saas'], 3.00)
        self.assertEqual(form.initial['discount_duration'], 5)
        self.assertEqual(form.initial['discount_staff_add_on'], None)

    def test_view_form_staff_add_on_discount(self):
        offer = create_product_offer_from_saas(self.saas)
        offer_item = offer.offer_items.get(product=self.saas.staff_add_on)
        offer_item.discount_type = DiscountType.FIXED
        offer_item.discount_amount = 3
        offer_item.discount_duration = 5
        offer_item.save()
        offer.refresh_from_db()
        form = ProductOfferForm(instance=offer)
        self.assertEqual(form.initial['discount_type'], DiscountType.FIXED)
        self.assertEqual(form.initial['discount_saas'], None)
        self.assertEqual(form.initial['discount_duration'], 5)
        self.assertEqual(form.initial['discount_staff_add_on'], 3.00)

    def test_remove_saas_discount(self):
        offer = create_product_offer_from_saas(self.saas)
        saas_item = offer.offer_items.get(product=self.saas)
        saas_item.discount_type = DiscountType.FIXED
        saas_item.discount_amount = 3
        saas_item.discount_duration = 5
        saas_item.save()
        staff_item = offer.offer_items.get(product=self.saas.staff_add_on)
        staff_item.discount_type = DiscountType.FIXED
        staff_item.discount_amount = 3
        staff_item.discount_duration = 5
        staff_item.save()
        offer.refresh_from_db()
        form = ProductOfferForm(
            instance=offer,
            data=dict(
                name='test offer',
                discount_type=DiscountType.FIXED,
                saas=self.saas,
                discount_saas=None,
                discount_staff_add_on=3,
            ),
        )
        self.assertTrue(form.is_valid())
        form.save()
        saas_item.refresh_from_db()
        staff_item.refresh_from_db()
        self.assertEqual(saas_item.discount_type, DiscountType.NO_DISCOUNT)
        self.assertEqual(saas_item.discount_amount, None)
        self.assertEqual(saas_item.discount_frac, None)
        self.assertEqual(staff_item.discount_type, DiscountType.FIXED)
        self.assertEqual(staff_item.discount_amount, 3)

    def test_remove_staff_discount(self):
        offer = create_product_offer_from_saas(self.saas)
        saas_item = offer.offer_items.get(product=self.saas)
        saas_item.discount_type = DiscountType.FIXED
        saas_item.discount_amount = 3
        saas_item.discount_duration = 5
        saas_item.save()
        staff_item = offer.offer_items.get(product=self.saas.staff_add_on)
        staff_item.discount_type = DiscountType.FIXED
        staff_item.discount_amount = 3
        staff_item.discount_duration = 5
        staff_item.save()
        offer.refresh_from_db()
        form = ProductOfferForm(
            instance=offer,
            data=dict(
                name='test offer',
                discount_type=DiscountType.FIXED,
                saas=self.saas,
                discount_saas=3,
                discount_staff_add_on=None,
            ),
        )
        self.assertTrue(form.is_valid())
        form.save()
        saas_item.refresh_from_db()
        staff_item.refresh_from_db()
        self.assertEqual(saas_item.discount_type, DiscountType.FIXED)
        self.assertEqual(saas_item.discount_amount, 3.00)
        self.assertEqual(staff_item.discount_type, DiscountType.NO_DISCOUNT)
        self.assertEqual(staff_item.discount_amount, None)
        self.assertEqual(staff_item.discount_frac, None)

    def test_update_default_offer(self):
        offer = create_product_offer_from_saas(self.saas)
        offer.default = True
        offer.save()

        form = ProductOfferForm(
            instance=offer,
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
                default=True,
            ),
        )
        self.assertTrue(form.is_valid())

        baker.make(
            BillingProductOffer,
            default=True,
        )
        form = ProductOfferForm(
            instance=offer,
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
                default=True,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'There can be only one default offer at a time.',
            str(form.errors),
        )

    def test_update_active_offer_added_to_active_discount_code(self):
        offer = create_product_offer_from_saas(self.saas)
        baker.make(
            BillingDiscountCode,
            active=True,
            offer=offer,
            valid_to=tznow() + timedelta(days=2),
            valid_from=tznow() - timedelta(days=2),
        )
        form = ProductOfferForm(
            instance=offer,
            data=dict(
                active=False,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'change status if offer is assigned to active Discount Code',
            str(form.errors),
        )

    def test_update_active_offer_added_to_business_offer(self):
        offer = create_product_offer_from_saas(self.saas)
        baker.make(
            BillingBusinessOffer,
            offer=offer,
            active=True,
            valid_to=tznow() + timedelta(days=2),
            valid_from=tznow() - timedelta(days=2),
        )
        form = ProductOfferForm(
            instance=offer,
            data=dict(
                active=False,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'change status if offer is assigned to active Business Offer',
            str(form.errors),
        )

    def test_remove_all_discounts(self):
        offer = create_product_offer_from_saas(self.saas)
        saas_item = offer.offer_items.get(product=self.saas)
        saas_item.discount_type = DiscountType.FIXED
        saas_item.discount_amount = 3
        saas_item.discount_duration = 5
        saas_item.save()
        staff_item = offer.offer_items.get(product=self.saas.staff_add_on)
        staff_item.discount_type = DiscountType.FIXED
        staff_item.discount_amount = 3
        staff_item.discount_duration = 5
        staff_item.save()
        offer.refresh_from_db()
        form = ProductOfferForm(
            instance=offer,
            data=dict(
                name='test offer',
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
                discount_saas=None,
                discount_staff_add_on=None,
            ),
        )
        self.assertTrue(form.is_valid())
        form.save()
        saas_item.refresh_from_db()
        staff_item.refresh_from_db()
        self.assert_discount(saas_item, self.no_discount_data)
        self.assert_discount(staff_item, self.no_discount_data)

    def test_remove_discount_invalid(self):
        offer = create_product_offer_from_saas(self.saas)
        saas_item = offer.offer_items.get(product=self.saas)
        saas_item.discount_type = DiscountType.FIXED
        saas_item.discount_amount = 3
        saas_item.discount_duration = 5
        saas_item.save()
        staff_item = offer.offer_items.get(product=self.saas.staff_add_on)
        staff_item.discount_type = DiscountType.FIXED
        staff_item.discount_amount = 3
        staff_item.discount_duration = 5
        staff_item.save()
        offer.refresh_from_db()
        form = ProductOfferForm(
            instance=offer,
            data=dict(
                name='test offer',
                # can't remove discount because discount value != None
                discount_type=DiscountType.NO_DISCOUNT,
                saas=self.saas,
                discount_saas=3,
                discount_staff_add_on=None,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'Cannot set NO_DISCOUNT because discount value is set',
            str(form.errors),
        )

    def test_change_saas_discount_frac_fixed(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.PERCENTAGE,
                discount_saas=10,
                discount_staff_add_on=40,
                saas=self.saas,  #  10 saas / 20 staffer unit_price
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        form = ProductOfferForm(
            instance=product,
            data=dict(
                name='test offer',
                discount_type=DiscountType.FIXED,
                saas=self.saas,
                discount_saas=3,
                discount_staff_add_on=7,
            ),
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        self.assert_items_created(product)
        saas_item = product.offer_items.get(product_id=self.saas.id)
        sms_item = product.offer_items.get(product_id=self.saas.sms_add_on_id)
        staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
        self.assert_discount(
            saas_item,
            self.get_fixed_discount_data(3),
        )
        self.assert_discount(
            staffer_item,
            self.get_fixed_discount_data(7),
        )
        self.assert_discount(sms_item, self.no_discount_data)

    def test_change_saas_discount_fixed_frac(self):
        form = ProductOfferForm(
            data=dict(
                name='test offer',
                discount_type=DiscountType.FIXED,
                discount_saas=3,
                discount_staff_add_on=7,
                saas=self.saas,  # 10 saas / 20 staffer unit_price
            )
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        form = ProductOfferForm(
            instance=product,
            data=dict(
                name='test offer',
                discount_type=DiscountType.PERCENTAGE,
                saas=self.saas,
                discount_saas=10,
                discount_staff_add_on=40,
            ),
        )
        self.assertTrue(form.is_valid())
        product = form.save()
        self.assert_items_created(product)
        saas_item = product.offer_items.get(product_id=self.saas.id)
        sms_item = product.offer_items.get(product_id=self.saas.sms_add_on_id)
        staffer_item = product.offer_items.get(product_id=self.saas.staff_add_on_id)
        self.assert_discount(
            saas_item,
            self.get_frac_discount_data(0.10),
        )
        self.assert_discount(staffer_item, self.get_frac_discount_data(0.40))
        self.assert_discount(sms_item, self.no_discount_data)


class TestBillingBusinessOfferFilter(DjangoTestCase):
    def setUp(self):
        self.offer1 = baker.make(BillingProductOffer)
        self.offer2 = baker.make(BillingProductOffer)
        self.business1 = baker.make(Business)
        self.business2 = baker.make(Business)
        self.business3 = baker.make(Business)

    def test__none_selected(self):
        admin_filter = BillingBusinessOfferFilter(
            None,
            {'billing_offer': None},
            Business,
            BillingBusinessAdmin,
        )
        self.assertEqual(admin_filter.queryset(None, Business.objects.all()).count(), 3)

    def test__not_assigned(self):
        admin_filter = BillingBusinessOfferFilter(
            None,
            {'billing_offer': self.offer1.id},
            Business,
            BillingBusinessAdmin,
        )
        self.assertEqual(admin_filter.queryset(None, Business.objects.all()).count(), 0)

    def test__one_assigned(self):
        baker.make(BillingSubscription, business=self.business1, offer=self.offer1)
        baker.make(BillingSubscription, business=self.business2, offer=self.offer2)
        baker.make(BillingSubscription, business=self.business3, offer=self.offer2)

        admin_filter = BillingBusinessOfferFilter(
            None,
            {'billing_offer': self.offer1.id},
            Business,
            BillingBusinessAdmin,
        )
        self.assertListEqual(
            list(admin_filter.queryset(None, Business.objects.all())),
            [self.business1],
        )

    def test__multiple_assigned(self):
        baker.make(BillingSubscription, business=self.business1, offer=self.offer1)
        baker.make(BillingSubscription, business=self.business2, offer=self.offer2)
        baker.make(BillingSubscription, business=self.business3, offer=self.offer2)
        baker.make(BillingSubscription, business=self.business3, offer=self.offer1)

        admin_filter = BillingBusinessOfferFilter(
            None,
            {'billing_offer': self.offer2.id},
            Business,
            BillingBusinessAdmin,
        )
        self.assertListEqual(
            list(admin_filter.queryset(None, Business.objects.all())),
            [self.business2, self.business3],
        )

    def test__distinct_result(self):
        baker.make(BillingSubscription, business=self.business1, offer=self.offer1, _quantity=3)

        admin_filter = BillingBusinessOfferFilter(
            None,
            {'billing_offer': self.offer1.id},
            Business,
            BillingBusinessAdmin,
        )
        self.assertListEqual(
            list(admin_filter.queryset(None, Business.objects.all())),
            [self.business1],
        )


def extract_form_values_from_html(content) -> dict:
    soup = BeautifulSoup(content, 'html.parser')

    options = soup.find('select', {'id': 'id_offer'}).find_all('option')
    selected_options = [
        option for option in options if option.has_attr('selected') and option['value']
    ]
    offer_id = selected_options[0]['value'] if selected_options else None

    def extract_input_value(input_name):
        input_element = soup.find('input', {'name': input_name})
        return input_element['value'] if input_element and 'value' in input_element.attrs else None

    return {
        'offer_id': offer_id,
        'business_id': extract_input_value('business'),
        'valid_from_0': extract_input_value('valid_from_0'),
        'valid_from_1': extract_input_value('valid_from_1'),
        'valid_to_0': extract_input_value('valid_to_0'),
        'valid_to_1': extract_input_value('valid_to_1'),
    }


class TestBillingBusinessOfferAdmin(DjangoTestCase):
    url: str

    def setUp(self):
        super().setUp()
        self.login_admin()

        self.url = reverse('admin:billing_billingbusinessoffer_add')

        self.business = billing_business()
        self.saas = create_saas(
            staff_add_on=create_staff_add_on(),
            sms_add_on=create_sms_add_on(),
        )
        self.saas_other2 = create_saas(
            staff_add_on=create_staff_add_on(),
            sms_add_on=create_sms_add_on(),
        )
        self.offer = create_product_offer_from_saas(self.saas)
        self.payload = dict(
            business=self.business.id,
            offer=self.offer.id,
            default=True,
        )

    @override_settings(SAVE_HISTORY=True)
    def test_create__ok(self):
        resp = self.client.post(
            self.url,
            data=self.payload,
            follow=True,
        )
        self.assertContains(resp, 'added successfully')

        business_offer = BillingBusinessOffer.objects.get()
        self.assertTrue(
            business_offer.history.filter(operator__email=self.logged_user.email).exists()
        )

    def test_create__already_has_special_offer(self):
        baker.make(
            BillingBusinessOffer,
            active=True,
            business=self.business,
        )

        resp = self.client.post(
            self.url,
            data=self.payload,
            follow=True,
        )
        self.assertContains(
            resp,
            'Business has an active custom offer already.',
        )

    def test_create__inactive_offer(self):
        BillingProductOffer.objects.update(active=False)
        resp = self.client.post(
            self.url,
            data=self.payload,
            follow=True,
        )
        self.assertNotContains(resp, 'added successfully')

    @patch(
        'webapps.billing.permissions.BillingUserPermission.is_billing_advanced_user',
        False,
    )
    @patch(
        'webapps.billing.permissions.BillingUserPermission.is_billing_admin',
        False,
    )
    def test_create_invisible_product_offers_basic_user(self):
        invisible_for_cs = baker.make(
            BillingProductOffer,
            name='Nieaktywna dla CS',
            default=True,
            active_for_cs=False,
            active=True,
        )
        visible_for_cs = baker.make(
            BillingProductOffer,
            name='Aktywny dla wszystkich',
            default=True,
            active_for_cs=True,
            active=True,
        )
        inactive = baker.make(
            BillingProductOffer,
            name='Test offer inactive',
            default=True,
            active_for_cs=True,
            active=False,
        )

        resp = self.client.get(self.url)
        self.assertNotContains(resp, invisible_for_cs.name)
        self.assertContains(resp, visible_for_cs.name)
        self.assertNotContains(resp, inactive.name)

    @patch(
        'webapps.billing.permissions.BillingUserPermission.is_billing_admin',
        False,
    )
    def test_create_invisible_product_offers_advanced_user(self):
        invisible_for_advanced = baker.make(
            BillingProductOffer,
            name='Nieaktywna dla Advanced',
            active_for_advanced_user=False,
            active_for_cs=True,
            active=True,
        )
        visible_for_advanced = baker.make(
            BillingProductOffer,
            name='Aktywny dla wszystkich',
            active_for_advanced_user=True,
            active_for_cs=False,
            active=True,
        )
        inactive = baker.make(
            BillingProductOffer,
            name='Test offer inactive',
            active_for_advanced_user=True,
            active_for_cs=True,
            active=False,
        )
        resp = self.client.get(self.url)
        self.assertNotContains(resp, invisible_for_advanced.name)
        self.assertContains(resp, visible_for_advanced.name)
        self.assertNotContains(resp, inactive.name)

    def test_change_view_works(self):
        obj = baker.make(
            BillingBusinessOffer,
            active=True,
            business=self.business,
        )

        resp = self.client.get(
            reverse(
                'admin:billing_billingbusinessoffer_change',
                args=(obj.id,),
            ),
            follow=True,
        )
        self.assertContains(
            resp,
            self.business.name,
        )

    def test_change_view_offer_works(self):
        instance = baker.make(BillingBusinessOffer)
        url = reverse('admin:billing_billingbusinessoffer_change', args=(instance.pk,))
        resp = self.client.get(url)
        self.assertEqual(resp.context['adminform'].form.instance, instance)

    def test_search_view_works(self):
        instance = baker.make(BillingBusinessOffer)
        instance2 = baker.make(BillingBusinessOffer)
        self.url = reverse('admin:billing_billingbusinessoffer_changelist')
        resp = self.client.get(self.url + f'?q={instance.business.name}')
        self.assertContains(resp, instance.business.name)
        self.assertNotContains(resp, instance2.business.name)

    @freeze_time(datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
    def test_with_all_parameters(self):
        params = {
            "offer": self.offer.id,
            "business": 5,
            "valid_from": '2024-08-23T14:30:00',
            "valid_to": '2024-08-24T14:30:00',
        }
        url = reverse('admin:billing_billingbusinessoffer_add')
        resp = self.client.get(f"{url}?{urlencode(params)}")
        values = extract_form_values_from_html(resp.content.decode('utf-8'))
        self.assertEqual(str(self.offer.id), values['offer_id'])
        self.assertEqual('5', values['business_id'])
        self.assertEqual('2024-08-23', values['valid_from_0'])
        self.assertEqual('14:30:00', values['valid_from_1'])
        self.assertEqual('2024-08-24', values['valid_to_0'])
        self.assertEqual('14:30:00', values['valid_to_1'])

    @freeze_time(datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
    def test_with_invalid_dates(self):
        params = {"valid_from": '2x24-d:30:00', "valid_to": "invalid-date"}
        url = reverse('admin:billing_billingbusinessoffer_add')
        resp = self.client.get(f"{url}?{urlencode(params)}")
        values = extract_form_values_from_html(resp.content.decode('utf-8'))
        self.assertEqual(None, values['valid_from_0'])
        self.assertEqual(None, values['valid_from_0'])
        self.assertEqual('2021-01-07', values['valid_to_0'])
        self.assertEqual('10:15:00', values['valid_to_1'])

    @freeze_time(datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
    def test_with_no_parameters(self):
        url = reverse('admin:billing_billingbusinessoffer_add')
        resp = self.client.get(url)
        values = extract_form_values_from_html(resp.content.decode('utf-8'))
        self.assertEqual(None, values['offer_id'])
        self.assertEqual(None, values['business_id'])
        self.assertEqual(None, values['valid_from_0'])
        self.assertEqual(None, values['valid_from_1'])
        self.assertEqual('2021-01-07', values['valid_to_0'])
        self.assertEqual('10:15:00', values['valid_to_1'])

    @patch.object(Client, 'track')
    def test_create_product_offer_triggers_analytics_business_special_offer_create_task(
        self, seg_track_mock
    ):
        with self.captureOnCommitCallbacks(execute=True):
            self.client.post(
                self.url,
                data=self.payload,
                follow=True,
            )
        self.assertEqual(1, seg_track_mock.call_count)
        self.assertEqual(
            'Business_Special_Offer_Create',
            seg_track_mock.call_args_list[0][1]['event'],
        )
        self.assertEqual(
            id_to_external_api(self.business.id),
            seg_track_mock.call_args_list[0][1]['properties']['business_id'],
        )
        self.assertEqual(
            self.offer.business_offers.get().id,
            seg_track_mock.call_args_list[0][1]['properties']['billing_business_offer_id'],
        )

    @patch.object(Client, 'track')
    def test_create_product_offer_triggers_analytics_business_special_offer_create_task_data(
        self, seg_track_mock
    ):
        biz = billing_business()
        with self.captureOnCommitCallbacks(execute=True):
            bbo = baker.make(
                BillingBusinessOffer,
                active=True,
                business=biz,
            )
        self.assertEqual(1, seg_track_mock.call_count)
        self.assertEqual(
            id_to_external_api(biz.id),
            seg_track_mock.call_args_list[0][1]['properties']['business_id'],
        )

        self.assertEqual(
            seg_track_mock.call_args_list[0][1]['properties'],
            seg_track_mock.call_args_list[0][1]['properties']
            | {"name": bbo.offer.name, "billing_business_offer_id": bbo.id},
        )
        with self.captureOnCommitCallbacks(execute=True):
            self.client.post(
                self.url,
                data={"business": self.business.id, "offer": self.offer.id, "default": True},
                follow=True,
            )
        self.assertEqual(2, seg_track_mock.call_count)
        self.assertEqual(
            id_to_external_api(self.business.id),
            seg_track_mock.call_args_list[1][1]['properties']['business_id'],
        )

        self.assertEqual(
            seg_track_mock.call_args_list[1][1]['properties'],
            seg_track_mock.call_args_list[1][1]['properties']
            | {
                "name": self.offer.name,
                "billing_business_offer_id": self.offer.business_offers.get().id,
            },
        )

    @patch.object(Client, 'track')
    def test_change_product_offer_not_triggers_analytics_business_special_offer_create_task(
        self, seg_track_mock
    ):
        with self.captureOnCommitCallbacks(execute=True):
            business = billing_business()
            billing_business_offer = baker.make(
                BillingBusinessOffer,
                active=True,
                business=business,
            )
        self.assertEqual(1, seg_track_mock.call_count)
        params = {
            "offer": billing_business_offer.id,
            "business": billing_business_offer.business_id,
            "valid_from": '2024-08-23T14:30:00',
            "valid_to": '2024-08-24T14:30:00',
        }
        url = reverse('admin:billing_billingbusinessoffer_add')
        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(f"{url}?{urlencode(params)}")
        self.assertEqual(1, seg_track_mock.call_count)


class TestActiveOffersFilter(DjangoTestCase):
    def setUp(self):
        self.active_offers = baker.make(BillingProductOffer, active=True, _quantity=2)
        self.inactive_offers = baker.make(BillingProductOffer, active=False, _quantity=2)

    def test_default_visible_active(self):
        self.admin_filter = ActiveOffersFilter(
            None,
            {},
            BillingProductOffer,
            ProductOfferAdmin,
        )
        self.assertQuerysetEqual(
            self.admin_filter.queryset(None, BillingProductOffer.objects.all()),
            self.active_offers,
            ordered=False,
        )

    def test_default_invisible_active(self):
        self.admin_filter = ActiveOffersFilter(
            None,
            {'active': 'no'},
            BillingProductOffer,
            ProductOfferAdmin,
        )
        self.assertQuerysetEqual(
            self.admin_filter.queryset(None, BillingProductOffer.objects.all()),
            self.inactive_offers,
            ordered=False,
        )
