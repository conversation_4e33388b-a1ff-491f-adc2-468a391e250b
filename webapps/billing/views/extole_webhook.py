from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.response import Response
from rest_framework.exceptions import AuthenticationFailed

from django.conf import settings

from drf_api.base_views import BaseBooksyNoSessionApiView
from lib.feature_flag.feature.billing import ExtoleReferralFlag
from webapps.business.models import Business
from webapps.billing.enums import ExtoleRewardeeRole
from webapps.billing.tasks import process_reward_event_task


class ExtoleWebhookAPIView(BaseBooksyNoSessionApiView):
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)
    API_KEY_REQUIRED = False

    def post(self, request):
        auth_header = request.headers.get('Authorization') or request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            raise AuthenticationFailed("Unauthorized.")
        token = auth_header.split(' ', 1)[1].strip()
        expected_token = getattr(settings, 'EXTOLE_WEBHOOK_SECRET_KEY', None)
        if not expected_token or token != expected_token:
            raise AuthenticationFailed("Unauthorized.")

        if not ExtoleReferralFlag():
            return Response({"message": "Feature disabled"}, status=status.HTTP_200_OK)

        payload = request.data if isinstance(request.data, dict) else {}

        journey = payload.get('journey')
        if journey != ExtoleRewardeeRole.FRIEND.name:
            return Response(
                {"message": "Webhook with different rewardee_role."}, status=status.HTTP_200_OK
            )

        data_fields = payload.get('dataFields', {}) or {}
        referee = data_fields.get('referee') or {}

        business = Business.objects.filter(owner__email=referee.get('email')).first()
        if not business:
            return Response({"error": "Business not found."}, status=status.HTTP_404_NOT_FOUND)

        process_reward_event_task.delay(business.id)
        return Response(
            {"message": "Webhook accepted for processing."}, status=status.HTTP_202_ACCEPTED
        )
