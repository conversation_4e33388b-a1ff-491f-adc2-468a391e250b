import dataclasses
from datetime import date

from ....business.dto import PaddingType, PriceList, Service
from ...dto import Occupancy, ResourceBookedOccupancy

(
    SERVICE_ID,
    SERVICE__DECREASED_RESOLUTION__ID,
    SERVICE__WITH_APPLIANCE__ID,
    SERVICE__APPLIANCE_ONLY__ID,
) = range(1, 4 + 1)
(
    STAFFER_1_ID,
    STAFFER_2_ID,
    APPLIANCE_ID,
) = range(1, 3 + 1)

BIN_LENGTH = 60  # bin length increased to 1h for readability

SERVICE = Service(
    service_variant_id=SERVICE_ID,
    service_id=SERVICE_ID,
    duration=BIN_LENGTH * 3,
    time_slot_interval=BIN_LENGTH * 1,
    staffer_ids={STAFFER_1_ID, STAFFER_2_ID},
    appliance_ids=set(),
    padding_duration=BIN_LENGTH * 1,
    padding_type=PaddingType.BEFORE_AND_AFTER,
)

SERVICES = [
    SERVICE,
    dataclasses.replace(
        SERVICE,
        service_id=SERVICE__DECREASED_RESOLUTION__ID,
        service_variant_id=SERVICE__DECREASED_RESOLUTION__ID,
        time_slot_interval=BIN_LENGTH * 2,
    ),
    dataclasses.replace(
        SERVICE,
        service_id=SERVICE__WITH_APPLIANCE__ID,
        service_variant_id=SERVICE__WITH_APPLIANCE__ID,
        appliance_ids={APPLIANCE_ID},
    ),
    dataclasses.replace(
        SERVICE,
        service_id=SERVICE__APPLIANCE_ONLY__ID,
        service_variant_id=SERVICE__APPLIANCE_ONLY__ID,
        staffer_ids=None,
        appliance_ids={APPLIANCE_ID},
    ),
]
PRICE_LIST = PriceList(
    services={s.service_variant_id: s for s in SERVICES},
    addons={},
    combo_children={},
)
EMPTY_OCCUPANCY = Occupancy("0" * 24)
FULL_OCCUPANCY = Occupancy("1" * 24)
EXAMPLE_BOOKED_OCCUPANCY = ResourceBookedOccupancy(
    booking=Occupancy("11111 10001 10000 10000 1111"),
    padding=Occupancy("00000 00001 10000 00000 1000"),
)
RESOURCES_BOOKED_OCCUPANCY_MAP = {
    STAFFER_1_ID: EXAMPLE_BOOKED_OCCUPANCY,
    STAFFER_2_ID: ResourceBookedOccupancy(
        booking=Occupancy("11111 10001 11000 10000 1111"),
        padding=Occupancy("00000 00001 11000 00000 1000"),
    ),
    APPLIANCE_ID: ResourceBookedOccupancy(
        booking=Occupancy("11111 10001 10000 11000 1111"),
        padding=Occupancy("00000 00001 10000 00000 1000"),
    ),
}

DAY__PX_NOT_OPERATING = date(2024, 5, 23)
DAY__NO_WORKING_RESOURCE = date(2024, 5, 24)
DAY_OPERATING = date(2024, 5, 25)
DAILY_RESOURCES_BOOKED_OCCUPANCY_MAP = {
    DAY__PX_NOT_OPERATING: None,
    DAY__NO_WORKING_RESOURCE: {
        STAFFER_1_ID: ResourceBookedOccupancy(
            booking=FULL_OCCUPANCY,
            padding=EMPTY_OCCUPANCY,
        ),
        STAFFER_2_ID: None,
        APPLIANCE_ID: None,
    },
    DAY_OPERATING: RESOURCES_BOOKED_OCCUPANCY_MAP,
}
