import json
from unittest.mock import patch

import pytest
from django.shortcuts import reverse
from django.test import TestCase, override_settings
from django.urls import path
from model_bakery import baker
from rest_framework import status
from rest_framework.test import APITestCase, URLPatternsTestCase

from lib.feature_flag.feature.billing import ExtoleReferralFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.billing.models import BillingBusinessOffer, BillingProductOffer
from webapps.billing.tasks import process_reward_event_task
from webapps.billing.views.extole_webhook import ExtoleWebhookAPIView
from webapps.business.models import Business
from webapps.user.models import User


class ExtoleWebhookMixin:
    @classmethod
    def setUpTestData(cls):  # pylint: disable=invalid-name
        super().setUpTestData()
        cls.params = {
            'content_type': 'application/json',
            'HTTP_X-Extole-Signature': 'valid_signature_hash',
            'HTTP_AUTHORIZATION': 'Bearer test_secret',
        }
        cls.valid_payload = {
            'journey': 'FRIEND',
            'dataFields': {
                'advocate': {
                    'email': '<EMAIL>',
                    'id': 'advocate_id_123',
                    'partner_user_id': 'partner_user_1',
                },
                'referee': {
                    'email': '<EMAIL>',
                    'id': 'referee_id_456',
                    'partner_user_id': 'partner_user_2',
                },
            },
        }


@pytest.mark.django_db
@override_eppo_feature_flag({ExtoleReferralFlag.flag_name: True})
@override_settings(EXTOLE_WEBHOOK_SECRET_KEY='test_secret')
class TestExtoleWebhookAPIView(ExtoleWebhookMixin, APITestCase, URLPatternsTestCase):

    urlpatterns = [
        path('extole/webhook/', ExtoleWebhookAPIView.as_view(), name='extole_webhook'),
    ]

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.url = reverse('extole_webhook')

    def test_non_friend_journey_returns_200(self):
        payload = self.valid_payload.copy()
        payload['journey'] = 'ADVOCATE'

        response = self.client.post(self.url, data=json.dumps(payload), **self.params)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertDictEqual(response.data, {"message": "Webhook with different rewardee_role."})

    @override_eppo_feature_flag({ExtoleReferralFlag.flag_name: True})
    @patch('webapps.billing.views.extole_webhook.process_reward_event_task.delay')
    def test_successful_webhook_processing(self, mock_task):
        user = baker.make(User, email="<EMAIL>")
        business = baker.make(Business, owner=user)

        response = self.client.post(self.url, data=json.dumps(self.valid_payload), **self.params)

        self.assertEqual(status.HTTP_202_ACCEPTED, response.status_code)
        self.assertDictEqual(response.data, {"message": "Webhook accepted for processing."})
        mock_task.assert_called_once_with(business.id)

    def test_only_post_method_allowed(self):
        response = self.client.get(self.url)
        self.assertEqual(status.HTTP_405_METHOD_NOT_ALLOWED, response.status_code)

        response = self.client.put(self.url)
        self.assertEqual(status.HTTP_405_METHOD_NOT_ALLOWED, response.status_code)

        response = self.client.delete(self.url)
        self.assertEqual(status.HTTP_405_METHOD_NOT_ALLOWED, response.status_code)

    @patch('webapps.billing.views.extole_webhook.process_reward_event_task.delay')
    def test_missing_authorization_returns_403(self, mock_task):
        user = baker.make(User, email="<EMAIL>")
        baker.make(Business, owner=user)

        params_without_auth = {k: v for k, v in self.params.items() if k != 'HTTP_AUTHORIZATION'}

        response = self.client.post(
            self.url, data=json.dumps(self.valid_payload), **params_without_auth
        )

        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        mock_task.assert_not_called()

    def test_business_not_found_returns_404(self):
        payload = self.valid_payload.copy()
        payload['dataFields']['referee']['email'] = '<EMAIL>'

        response = self.client.post(self.url, data=json.dumps(payload), **self.params)

        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)

    def test_invalid_json_returns_400(self):
        invalid_json = "{'invalid': json}"

        response = self.client.post(self.url, data=invalid_json, **self.params)

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_empty_json_payload_returns_200(self):
        response = self.client.post(self.url, data=json.dumps({}), **self.params)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertDictEqual(response.data, {"message": "Webhook with different rewardee_role."})

    def test_missing_datafields_returns_404(self):
        payload_without_datafields = {
            'journey': 'FRIEND',
        }

        response = self.client.post(
            self.url, data=json.dumps(payload_without_datafields), **self.params
        )

        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)

    @override_eppo_feature_flag({ExtoleReferralFlag.flag_name: False})
    @patch('webapps.billing.views.extole_webhook.process_reward_event_task.delay')
    def test_feature_flag_disabled_returns_200_and_no_task(self, mock_task):
        response = self.client.post(self.url, data=json.dumps(self.valid_payload), **self.params)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertDictEqual(response.data, {"message": "Feature disabled"})
        mock_task.assert_not_called()


@pytest.mark.django_db
class TestProcessRewardEventTask(TestCase):
    """Tests for the process_reward_event_task Celery task"""

    def setUp(self):
        self.user = baker.make(User, email="<EMAIL>")
        self.business = baker.make(Business, owner=self.user)

        self.default_product_offer = baker.make(
            BillingProductOffer, name="Default B2B Referral Offer", active_for_b2b_referral=True
        )
        self.regular_product_offer = baker.make(
            BillingProductOffer, name="Regular Offer", active_for_b2b_referral=False, default=True
        )

        self.existing_business_offer = baker.make(
            BillingBusinessOffer,
            business=self.business,
            offer=self.regular_product_offer,
            active=True,
        )

    def test_successful_processing(self):
        """Test successful processing of reward event"""
        process_reward_event_task(self.business.id)

        self.business.refresh_from_db()

        self.assertTrue(self.business.has_new_billing)
        self.assertEqual(Business.PaymentSource.BRAINTREE_BILLING, self.business.payment_source)

        new_offers = BillingBusinessOffer.objects.filter(
            business=self.business, offer=self.default_product_offer, active=True
        )
        self.assertEqual(1, new_offers.count())

    def test_business_not_found(self):
        """Test handling when Business doesn't exist"""
        result = process_reward_event_task(99999)

        self.assertIsNone(result)

    def test_no_default_b2b_referral_offer(self):
        """Test handling when no default B2B referral offer exists"""
        self.default_product_offer.delete()

        with self.assertRaises(ValueError) as context:
            process_reward_event_task(self.business.id)

        self.assertEqual("No offer found for B2BReferral", str(context.exception))

    @patch('webapps.billing.models.BillingProductOffer.objects.get_for_business')
    def test_no_existing_active_offer(self, mock_get_for_business):
        """Test processing when business has no existing active offers"""
        mock_get_for_business.return_value = None

        process_reward_event_task(self.business.id)

        new_offers = BillingBusinessOffer.objects.filter(
            business=self.business, offer=self.default_product_offer, active=True
        )
        self.assertEqual(1, new_offers.count())

    def test_existing_active_offer_is_deactivated(self):
        """Existing active business offers must be deactivated when processing the event"""
        self.assertTrue(self.existing_business_offer.active)

        process_reward_event_task(self.business.id)

        self.existing_business_offer.refresh_from_db()
        self.assertFalse(self.existing_business_offer.active)

        active_offers = BillingBusinessOffer.objects.filter(business=self.business, active=True)
        self.assertEqual(1, active_offers.count())
        self.assertEqual(self.default_product_offer, active_offers.first().offer)
