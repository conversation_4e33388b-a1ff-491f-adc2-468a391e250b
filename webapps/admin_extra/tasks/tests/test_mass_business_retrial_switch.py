import csv
import io
from datetime import timedelta
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest
from django.core import mail
from django.test import override_settings
from model_bakery import baker
from parameterized import parameterized
from segment.analytics.client import Client

from lib.tools import tznow
from service.tests import dict_assert
from webapps.admin_extra.tasks.mass_business_retrial_switch import (
    CSVLoader,
    business_validate,
    mass_business_retrial_switch_task,
    parse_duration,
)
from webapps.admin_extra.tasks.tests.test_subscription_buyer_update_task import (
    get_csv_attachment_data,
)
from webapps.admin_extra.tasks.utils import (
    CSVLoaderMaxRecordsInBatchError,
    CSVLoaderParseError,
    DataFrameUniqueIndexError,
)
from webapps.billing.tests.utils import billing_business
from webapps.business.models import Business, ReTrialAttempt
from webapps.segment.enums import AnalyticEventEnums
from webapps.segment.utils import get_track_common_params_for_tests
from webapps.user.models import User


def csv_load_mocked(rows: list[dict]):
    output = io.StringIO()
    fieldnames = ['business_id', 'duration']
    writer = csv.DictWriter(output, fieldnames=fieldnames, delimiter=',')
    writer.writeheader()
    writer.writerows(rows)

    output.seek(0)
    return CSVLoader.read_csv(output)


def csv_load_mocked_old_format(rows: list[dict]):
    """CSV loader for old format (business_id only, no duration column)."""
    output = io.StringIO()
    fieldnames = ['business_id']  # Old format - no duration column
    writer = csv.DictWriter(output, fieldnames=fieldnames, delimiter=',')
    writer.writeheader()
    writer.writerows(rows)

    output.seek(0)
    return CSVLoader.read_csv(output)


@pytest.mark.django_db
class ParseDurationTestCase(TestCase):
    def setUp(self):
        self.loading_date = tznow().replace(hour=12, minute=0, second=0, microsecond=0)

    def test_empty_duration_defaults_to_14(self):
        """
        Given an empty duration value in the CSV
        When the duration is parsed
        Then it should default to 14 days
        And no error should be returned
        """
        result = parse_duration('', self.loading_date)
        self.assertEqual(result.duration, 14)
        self.assertTrue(result.success)
        self.assertIsNone(result.error)

    def test_none_duration_defaults_to_14(self):
        result = parse_duration(None, self.loading_date)
        self.assertEqual(result.duration, 14)
        self.assertTrue(result.success)
        self.assertIsNone(result.error)

    def test_numeric_duration_valid(self):
        result = parse_duration('7', self.loading_date)
        self.assertEqual(result.duration, 7)
        self.assertTrue(result.success)
        self.assertIsNone(result.error)

    def test_float_duration_valid(self):
        result = parse_duration('7.0', self.loading_date)
        self.assertEqual(result.duration, 7)
        self.assertTrue(result.success)
        self.assertIsNone(result.error)

    @override_settings(MAX_TRIAL_EXTENSION_DURATION=14)
    def test_duration_exceeds_max(self):
        result = parse_duration('15', self.loading_date)
        self.assertIsNone(result.duration)
        self.assertFalse(result.success)
        self.assertIn('Duration must be between 1 and 14 days', result.error)

    def test_duration_too_small(self):
        result = parse_duration('0', self.loading_date)
        self.assertIsNone(result.duration)
        self.assertFalse(result.success)
        self.assertIn('Duration must be between 1 and', result.error)

    def test_date_format_yyyy_mm_dd(self):
        """
        Given a future end date in YYYY-MM-DD format
        When the duration is parsed
        Then it should calculate the correct number of days from loading date
        And no error should be returned
        """
        future_date = (self.loading_date + timedelta(days=10)).strftime('%Y-%m-%d')
        result = parse_duration(future_date, self.loading_date)
        self.assertEqual(result.duration, 10)
        self.assertTrue(result.success)
        self.assertIsNone(result.error)

    def test_date_format_dd_mm_yyyy(self):
        future_date = (self.loading_date + timedelta(days=5)).strftime('%d/%m/%Y')
        result = parse_duration(future_date, self.loading_date)
        self.assertEqual(result.duration, 5)
        self.assertTrue(result.success)
        self.assertIsNone(result.error)

    def test_past_date_error(self):
        """
        Given a past end date before the loading date
        When the duration is parsed
        Then it should return None for duration
        And an error message stating the end date must be after loading date
        """
        past_date = (self.loading_date - timedelta(days=1)).strftime('%Y-%m-%d')
        result = parse_duration(past_date, self.loading_date)
        self.assertIsNone(result.duration)
        self.assertFalse(result.success)
        self.assertIn('End date must be after loading date', result.error)

    def test_invalid_date_format(self):
        """
        Given an invalid date format that cannot be parsed
        When the duration is parsed
        Then it should return None for duration
        And an error message about invalid date format
        """
        result = parse_duration('invalid-date', self.loading_date)
        self.assertIsNone(result.duration)
        self.assertFalse(result.success)
        self.assertIn('Invalid date format', result.error)


@pytest.mark.django_db
class CSVLoaderTestCase(TestCase):
    @patch('webapps.admin_extra.tasks.utils.AdminImporterS3.open_file', MagicMock())
    @patch.object(CSVLoader, 'read_csv')
    def test_load_too_many_records_data_data(self, data_frame_mock):
        data_frame_mock.return_value = pd.DataFrame(range(CSVLoader.max_records_in_batch + 1))

        with self.assertRaisesRegex(CSVLoaderMaxRecordsInBatchError, 'There is more than*'):
            CSVLoader.load_from_file('file')

    @parameterized.expand([('NULL',), ('abc',)])
    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_load_data_when_parsing_error(self, entry_data):
        with self.assertRaisesRegex(CSVLoaderParseError, 'CSV loading error.*'):
            CSVLoader.load_from_file(
                [
                    {
                        'business_id': entry_data,
                        'duration': '14',
                    }
                ]
            )

    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_load_data_when_index_error(self):
        with self.assertRaisesRegex(DataFrameUniqueIndexError, '.*Duplicated Business ID.*'):
            data_frames = CSVLoader.load_from_file(
                [
                    {
                        'business_id': '11',
                        'duration': '14',
                    },
                    {
                        'business_id': '11',
                        'duration': '7',
                    },
                ]
            )

            CSVLoader.clean_data_frames(data_frames, business_index=True)

    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_clean_data_frames_ok(self):
        data_frames = CSVLoader.load_from_file(
            [
                {
                    'business_id': '11',
                    'duration': '14',
                },
                {
                    'business_id': '12',
                    'duration': '7',
                },
            ]
        )

        records = CSVLoader.clean_data_frames(data_frames, business_index=True)

        self.assertEqual(len(records), 2)

        self.assertEqual(records[11]['result'], 'SUCCESS')
        self.assertFalse(records[11]['message'])

        self.assertEqual(records[12]['result'], 'SUCCESS')
        self.assertFalse(records[12]['message'])

    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked_old_format)
    def test_clean_data_frames_old_format_no_duration_column(self):
        """
        Given a CSV file with only business_id column (old format)
        When the CSV is processed through the data loader
        Then the data should be loaded successfully without errors
        And the duration column should not be present in the processed records
        """
        data_frames = CSVLoader.load_from_file(
            [
                {
                    'business_id': '11',
                },
                {
                    'business_id': '12',
                },
            ]
        )

        records = CSVLoader.clean_data_frames(data_frames, business_index=True)

        self.assertEqual(len(records), 2)

        # Both records should process successfully
        self.assertEqual(records[11]['result'], 'SUCCESS')
        self.assertFalse(records[11]['message'])
        # Duration column should not be present in old format
        self.assertNotIn('duration', records[11])

        self.assertEqual(records[12]['result'], 'SUCCESS')
        self.assertFalse(records[12]['message'])
        # Duration column should not be present in old format
        self.assertNotIn('duration', records[12])


class BusinessMixin:
    def setUp(self):  # pylint: disable=invalid-name
        super().setUp()
        self.operator = baker.make(User)
        self.business1 = billing_business(status=Business.Status.TRIAL_BLOCKED)
        self.business2 = billing_business(status=Business.Status.PAID)
        self.business3 = billing_business(status=Business.Status.TRIAL_BLOCKED)
        baker.make(ReTrialAttempt, business=self.business3, used=None)
        self.business4 = billing_business(status=Business.Status.TRIAL_BLOCKED)
        self.business5 = billing_business(status=Business.Status.CHURNED)
        baker.make(ReTrialAttempt, business=self.business4, used=tznow())
        baker.make(ReTrialAttempt, _quantity=2)  # not related


@pytest.mark.django_db
class BusinessValidationTestCase(BusinessMixin, TestCase):
    def test_business_is_null(self):
        result = business_validate(
            business=None,
            active_retrial_attempt=False,
        )
        self.assertFalse(result.success)
        self.assertEqual(result.message, 'Business does not exist')

    def test_business_has_invalid_status(self):
        result = business_validate(
            business=self.business2,
            active_retrial_attempt=False,
        )
        self.assertFalse(result.success)
        self.assertEqual(
            result.message,
            'Business status different from: TRIAL_BLOCKED (current status: Paid Active)',
        )

    def test_business_has_active_retrial_attempt(self):
        result = business_validate(
            business=self.business1,
            active_retrial_attempt=True,
        )
        self.assertFalse(result.success)
        self.assertEqual(result.message, 'Business already has an active ReTrial record')

    def test_ok(self):
        result = business_validate(
            business=self.business1,
            active_retrial_attempt=False,
        )

        self.assertTrue(result.success)


@pytest.mark.django_db
class MassBusinessReTrialSwitchTaskTestCase(BusinessMixin, TestCase):
    @patch.object(CSVLoader, 'load_from_file')
    def test_data_frame_unique_index_error(self, load_mocked):
        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business1.id,
                    'duration': '14',
                },
                {
                    'business_id': self.business1.id,
                    'duration': '7',
                },
            ]
        )

        mass_business_retrial_switch_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
        )

        self.assertIn('error loading file', mail.outbox[0].subject)
        self.assertIn('Duplicated Business ID. This row must be unique!', mail.outbox[0].body)

    @patch.object(CSVLoader, 'load_from_file')
    def test_validation_errors(self, load_mocked):
        self.business1.status = Business.Status.PAID
        self.business1.save()

        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business1.id,
                    'duration': '14',
                },
                {
                    'business_id': self.business2.id,
                    'duration': '7',
                },
            ]
        )

        mass_business_retrial_switch_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
        )

        self.assertIn('report', mail.outbox[0].subject)
        self.assertIn('report in attachment', mail.outbox[0].body)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 3)

        for row in report[1:]:
            self.assertEqual(row[-2], 'ERROR')
            self.assertIn('Business status different from: TRIAL_BLOCKED', row[-1])

    @parameterized.expand(
        [
            (True,),
            (False,),
        ],
    )
    @override_settings(SAVE_HISTORY=True)
    @patch.object(CSVLoader, 'load_from_file')
    @patch.object(Client, 'track')
    def test_run_the_entire_process_ok(self, dry_run, analytics_track_mock, load_mocked):
        self.business1.owner.cell_phone = '+**************'
        self.business1.owner.save()

        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business1.id,  # ok
                    'duration': '14',
                },
                {
                    'business_id': self.business2.id,  # status error
                    'duration': '7',
                },
                {
                    'business_id': self.business3.id,  # active retrial error
                    'duration': '10',
                },
                {
                    'business_id': self.business4.id,  # ok
                    'duration': '5',
                },
                {
                    'business_id': self.business5.id,  # status error + no event triggerd
                    'duration': '12',
                },
            ]
        )

        mass_business_retrial_switch_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
            dry_run=dry_run,
        )
        if dry_run:
            self.assertIn('[Dry Run!]', mail.outbox[0].subject)
            self.assertIn('[Dry Run!]', mail.outbox[0].body)
        else:
            self.business1.refresh_from_db()
            self.assertEqual(analytics_track_mock.call_count, 2)
            dict_assert(
                analytics_track_mock.call_args_list[0][1],
                {
                    'event': AnalyticEventEnums.BUSINESS_RE_TRIAL_ELIGIBLE.value,
                    'properties': {
                        **get_track_common_params_for_tests(self.business1),
                    },
                },
            )

        self.assertIn('report', mail.outbox[0].subject)
        self.assertIn('report in attachment', mail.outbox[0].body)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 6)

        expected_success = 'DRY_RUN_SUCCESS' if dry_run else 'SUCCESS'
        self.assertEqual(report[1][-2], expected_success)
        self.assertEqual(report[2][-2], 'ERROR')
        self.assertEqual(report[3][-2], 'ERROR')
        self.assertEqual(report[4][-2], expected_success)
        self.assertEqual(report[5][-2], 'ERROR')

        if not dry_run:
            # Only successful businesses should have ReTrialAttempt records created
            # (2 successful: business1 and business4) Plus 4 existing ones from setup = 6 total
            self.assertEqual(ReTrialAttempt.objects.count(), 6)

            # Check that trials were applied immediately (used is not null)
            retrial_business1 = ReTrialAttempt.objects.get(
                business=self.business1, operator=self.operator
            )
            self.assertIsNotNone(retrial_business1.used)
            self.assertEqual(retrial_business1.duration, 14)

            retrial_business4 = ReTrialAttempt.objects.get(
                business=self.business4, operator=self.operator
            )
            self.assertIsNotNone(retrial_business4.used)
            self.assertEqual(retrial_business4.duration, 5)

            # Check that businesses are now in trial status
            self.business1.refresh_from_db()
            self.business4.refresh_from_db()

            self.assertEqual(self.business1.status, Business.Status.TRIAL)
            self.assertTrue(self.business1.active)
            self.assertIsNotNone(self.business1.trial_till)

            self.assertEqual(self.business4.status, Business.Status.TRIAL)
            self.assertTrue(self.business4.active)
            self.assertIsNotNone(self.business4.trial_till)

    @patch.object(CSVLoader, 'load_from_file')
    def test_old_format_defaults_to_14_days(self, load_mocked):
        """
        Given a CSV file with only business_id column (old format)
        When the mass business retrial switch task is executed
        Then all businesses should receive 14-day trials by default
        And the processing should complete successfully
        """
        load_mocked.return_value = csv_load_mocked_old_format(
            [
                {
                    'business_id': self.business1.id,  # ok, should get 14 days
                },
                {
                    'business_id': self.business4.id,  # ok, should get 14 days
                },
            ]
        )

        mass_business_retrial_switch_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
            dry_run=False,
        )

        self.assertIn('report', mail.outbox[0].subject)

        # Check that both businesses were processed successfully
        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 3)  # header + 2 rows

        self.assertEqual(report[1][-2], 'SUCCESS')
        self.assertEqual(report[2][-2], 'SUCCESS')

        # Verify that both businesses got 14-day trials (default)
        retrial_business1 = ReTrialAttempt.objects.get(
            business=self.business1, operator=self.operator
        )
        self.assertEqual(retrial_business1.duration, 14)  # Default duration

        retrial_business4 = ReTrialAttempt.objects.get(
            business=self.business4, operator=self.operator
        )
        self.assertEqual(retrial_business4.duration, 14)  # Default duration

    @patch.object(CSVLoader, 'load_from_file')
    def test_non_existent_business_id_error(self, load_mocked):
        """
        Given a CSV file containing both existing and non-existent business IDs
        When the mass business retrial switch task is executed
        Then existing businesses should be processed successfully
        And non-existent business IDs should result in "Business does not exist" errors
        And the system should continue processing other valid entries
        """
        non_existent_id = 99999999

        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business1.id,
                    'duration': '10',
                },
                {
                    'business_id': non_existent_id,
                    'duration': '14',
                },
            ]
        )

        mass_business_retrial_switch_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
            dry_run=False,
        )

        self.assertIn('report', mail.outbox[0].subject)

        # Check the report contains both success and error
        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 3)  # header + 2 rows

        # First business should succeed
        self.assertEqual(report[1][-2], 'SUCCESS')

        # Second business should fail with "does not exist" error
        self.assertEqual(report[2][-2], 'ERROR')
        self.assertIn('Business does not exist', report[2][-1])

        # Only one retrial should be created (for existing business)
        new_retrials = ReTrialAttempt.objects.filter(operator=self.operator)
        self.assertEqual(new_retrials.count(), 1)
        self.assertEqual(new_retrials.first().business, self.business1)
