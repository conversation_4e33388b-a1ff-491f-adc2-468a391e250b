#!/usr/bin/env bash
BOOKING_REQUIREMENTS_FILE="requirements-booking.txt"
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

poetry export \
    --without-hashes \
    --without-urls \
    --only main \
    --format requirements.txt \
    | awk '{ print $1 }' FS=';' > $BOOKING_REQUIREMENTS_FILE;

# Script checks if there are any differences between monolith and project requirements
# If there are any, those would be printed
# If not, file would be moved to monolith root directory
poetry run python \
    ${SCRIPT_DIR}/../scripts/compare_requirements.py \
    --target-path=${BOOKING_REQUIREMENTS_FILE}
if [ $? -eq 0 ];then
    mv ${BOOKING_REQUIREMENTS_FILE} ${SCRIPT_DIR}/../../..;
else
    rm -rf ${BOOKING_REQUIREMENTS_FILE};
fi
