[uwsgi]
http = :9090
master = True
processes = 2
#tornado = 50
#greenlet = True
chdir = /home/<USER>/code
disable-logging = true
log-date = 1
log-4xx = false
log-5xx = true
log-slow = 2000
buffer-size = 30000
harakiri = 10
harakiri-verbose = true
reload-mercy = 5
worker-reload-mercy = 8
vacuum = True
single-interpreter = True
need-app = True
listen = 120
close-on-exec = True
#need-plugin = python3
wsgi-file = wsgi.py
python-autoreload = 1
stats = /tmp/stats.socket
py-tracebacker = /tmp/uwsgi-tracebacker-socket
py-autoreload = 3
ignore-sigpipe = True
ignore-write-errors = True
enable-threads = True
lazy-apps = True
thunder-lock = True
backtrace-depth = 1000
strict=true
log-x-forwarded-for = true
