from domain_services.booking.src.domains import BusinessServiceImpl
from domain_services.booking.src.domains.appointment.service import AppointmentService
from lib.feature_flag.feature.booking import InvokeDecoupledDryRunFromSimplifiedBooking
from webapps.booking.timeslots.v1.booksy_slots_generator import BooksySlotsGenerator
from webapps.booking.v2.commons.legacy_api_client import LegacyAPIClient
from webapps.booking.v2.domains import MonolithBusinessRepository
from webapps.booking.v2.domains.appointment.outer.aggregate.appointment import (
    AppointmentAggregateImpl,
)
from webapps.booking.v2.domains.appointment.outer.aggregate.legacy_api_draft import (
    LegacyApiDraftAggregate,
)
from webapps.booking.v2.domains.appointment.outer.aggregate.legacy_code_draft import (
    LegacyCodeDraftAggregate,
)
from webapps.booking.v2.domains.appointment.outer.repository import (
    PostgresAppointmentInfoRepository,
    PostgresDraftRepository,
)
from webapps.booking.v2.domains.calendar.outer.service import CalendarServiceV1


class AppointmentServiceFactory:

    @classmethod
    def create(cls) -> AppointmentService:
        _legacy_api_client = LegacyAPIClient()

        calendar_service = CalendarServiceV1(
            booksy_slots_generator=BooksySlotsGenerator(),
        )
        business_service = BusinessServiceImpl(
            business_repository=MonolithBusinessRepository(),
        )
        draft_aggregate = (
            LegacyCodeDraftAggregate()
            if InvokeDecoupledDryRunFromSimplifiedBooking()
            else LegacyApiDraftAggregate(legacy_api_client=_legacy_api_client)
        )

        return AppointmentService(
            appointment_aggregate=AppointmentAggregateImpl(
                legacy_api_client=_legacy_api_client,
            ),
            business_service=business_service,
            calendar_service=calendar_service,
            draft_aggregate=draft_aggregate,
            draft_repository=PostgresDraftRepository(),
            draft_appointment_info_repository=PostgresAppointmentInfoRepository(),
        )
