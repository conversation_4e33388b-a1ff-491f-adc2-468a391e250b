default_install_hook_types: [pre-commit, commit-msg]
repos:
  - repo: local
    hooks:
      - id: black
        name: black
        description: "Black: The uncompromising Python code formatter"
        entry: python3 commands/run_docker.py python ci_jobs/run_black.py --path
        language: system
        minimum_pre_commit_version: 2.9.2
        require_serial: true
        types_or: [python, pyi]
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.1.0
    hooks:
      - id: conventional-pre-commit
        stages: [commit-msg]
