import os
import json
from datetime import timedelta
from django.conf import settings
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_401_UNAUTHORIZED
from drf_api.base_views import BaseBooksyNoSessionApiView
from sre_performance.serializers import DateRangeSerializer
from webapps.booking.models import BookingChange


class ArchivedBookingChangesView(BaseBooksyNoSessionApiView):
    """View created for performance tests purpose. The use of a heavier database request SELECT FROM
    BETWEEN instead of a lighter SELECT COUNT is on purpose"""

    API_KEY_REQUIRED = True
    query_serializer = DateRangeSerializer

    def get(self, request):
        serializer = self.query_serializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        booking_changes = BookingChange.objects.filter(
            created__range=[
                serializer.validated_data['date_from'],
                serializer.validated_data['date_to'] + timedelta(days=1),
            ]
        )
        booking_changes_count = len(booking_changes)
        return Response({'count': booking_changes_count}, status=HTTP_200_OK)


class BranchInfoView(BaseBooksyNoSessionApiView):
    API_KEY_REQUIRED = True

    def get(self, request):
        """
        swagger:
            summary: Returns branch name, last commit hash and base master commit hash
            notes: Only for performance tests on development environments
        """

        if not self.booking_source.is_performance_test:
            return Response(
                'Endpoint accessible only on development environments for performance tests',
                status=HTTP_401_UNAUTHORIZED,
            )

        return Response(self._get_branch_info(), status=HTTP_200_OK)

    @staticmethod
    def _get_branch_info():
        with open(
            os.path.join(settings.PROJECT_PATH, 'version_file.json'), 'r', encoding='utf-8'
        ) as file:
            return json.load(file)
