import datetime

import pytest
from django.forms import model_to_dict
from freezegun import freeze_time
from mock import patch
from model_bakery import baker

from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import (
    Business,
)
from webapps.business.models.category import BusinessCategory
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.message_blast.models import SMSBlastMarketingConsent
from webapps.message_blast.enums import (
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
    MessageBlastTimeType,
)
from webapps.message_blast.helpers import turn_on_blasts_for_business
from webapps.message_blast.models import (
    CommonMessageBlastTemplate,
    MessageBlast,
    MessageBlastTemplate,
)
from webapps.message_blast.tasks import (
    process_message_blast_templates_batch_task,
    process_message_blast_templates_task,
    process_message_blast_schedules_task,
)
from webapps.message_blast.tests.test_helpers import (
    update_message_blast_groups,
    create_templates,
)
from webapps.structure.models import Region
from webapps.user.models import User


def create_message_template(internal_name, business_id):
    common_template = CommonMessageBlastTemplate.objects.get(internal_name=internal_name)

    common_template_data = model_to_dict(common_template)
    common_template_data.pop('id')
    common_template_data.pop('automated_status')
    common_template_data['group_id'] = common_template_data.pop('group')

    common_template_data['body'] = common_template_data['body']
    common_template_data['body_short'] = common_template_data['body_short']

    return MessageBlastTemplate.objects.create(
        common_template=common_template,
        business_id=business_id,
        automated_status=MessageBlastTemplateStatus.ACTIVE,
        **common_template_data,
    )


@pytest.mark.django_db
@patch('webapps.message_blast.tasks.process_message_blast_templates_batch_task')
@patch('webapps.message_blast.models.MessageBlastTemplate.bcis_excluded_by_limits')
@patch('webapps.message_blast.tasks.get_timezones_with_hour')
def test_process_message_blast_templates_task(
    regions_mock, limit_mock, batch_mock, business, bci_email
):
    limit_mock.return_value = []
    regions_mock.return_value = ['Europe/Warsaw']
    update_message_blast_groups()
    create_templates()
    create_message_template(MessageBlastInternalNames.SPRING, business.id)

    result = process_message_blast_templates_task()
    assert result == {'8 hour': 1, '12 hour': 0, '16 hour': 0, 'templates_count': 1}
    assert batch_mock.has_been_called()

    mb_fathers_day = create_message_template(MessageBlastInternalNames.FATHERS_DAY, business.id)
    mb_fathers_day.date_hour = MessageBlastTimeType.EVENING
    mb_fathers_day.save()

    result = process_message_blast_templates_task()
    assert result == {'8 hour': 1, '12 hour': 0, '16 hour': 1, 'templates_count': 2}


@pytest.mark.django_db
@patch('webapps.message_blast.tasks.process_message_blast_templates_batch_task')
@patch('webapps.message_blast.models.MessageBlastTemplate.bcis_excluded_by_limits')
@patch('webapps.message_blast.tasks.get_timezones_with_hour')
def test_process_message_blast_templates_task_inactive_business(
    regions_mock, limit_mock, batch_mock, business, bci_email
):
    limit_mock.return_value = []
    regions_mock.return_value = ['Europe/Warsaw']
    update_message_blast_groups()
    create_templates()
    create_message_template(MessageBlastInternalNames.SPRING, business.id)

    business.active = False
    business.status = Business.Status.CHURNED
    business.save()
    assert business.active is False

    result = process_message_blast_templates_task()
    assert result == {'8 hour': 0, '12 hour': 0, '16 hour': 0, 'templates_count': 0}
    batch_mock.assert_not_called()


@pytest.mark.django_db
@patch('webapps.message_blast.models.MessageBlastTemplate.bcis_excluded_by_limits')
@patch('webapps.message_blast.tasks.get_timezones_with_hour')
@patch('webapps.message_blast.models.MessageBlastTemplate.get_date')
def test_process_message_blast_templates_batch_task(
    get_date_mock, regions_mock, limit_mock, business
):
    limit_mock.return_value = []
    update_message_blast_groups()
    create_templates()
    bci = baker.make(
        BusinessCustomerInfo,
        business=business,
        user=baker.make(User),
        web_communication_agreement=True,
        visible_in_business=True,
        cell_phone='+***********',
    )
    baker.make(SMSBlastMarketingConsent, bci=bci, cell_phone=bci.cell_phone)

    turn_on_blasts_for_business(business)
    regions_mock.return_value = ['Europe/Warsaw']
    CommonMessageBlastTemplate.objects.update(
        automated_status=MessageBlastTemplateStatus.INACTIVE,
    )
    MessageBlastTemplate.objects.update(
        automated_status=MessageBlastTemplateStatus.INACTIVE,
    )

    # We don't disable template and each message can be sent once a year, so
    # omitted number is increasing
    tests = [
        (MessageBlastInternalNames.HOLIDAY_BOOKING, datetime.datetime(2020, 11, 9), (1, 0)),
        (MessageBlastInternalNames.SPRING, datetime.datetime(2020, 3, 21), (1, 1)),
        (MessageBlastInternalNames.FATHERS_DAY, datetime.datetime(2020, 6, 15), (2, 1)),
        (MessageBlastInternalNames.MOTHERS_DAY, datetime.datetime(2020, 5, 4), (1, 3)),
        (MessageBlastInternalNames.HALLOWEEN, datetime.datetime(2020, 10, 5), (4, 1)),
        (MessageBlastInternalNames.NEW_YEAR, datetime.datetime(2020, 1, 6), (1, 5)),
        (MessageBlastInternalNames.HOLDAY_GIFTCARD, datetime.datetime(2020, 12, 7), (6, 1)),
        (MessageBlastInternalNames.VALENTINES_DAY, datetime.datetime(2020, 2, 3), (1, 7)),
        (MessageBlastInternalNames.BACK_TO_SCHOOL, datetime.datetime(2020, 8, 3), (2, 7)),
        (MessageBlastInternalNames.INDEPENDENCE_DAY, datetime.datetime(2020, 6, 30), (1, 9)),
        (MessageBlastInternalNames.MEMORIAL_DAY, datetime.datetime(2020, 5, 24), (1, 10)),
        (MessageBlastInternalNames.LABOR_DAY, datetime.datetime(2020, 8, 31), (3, 9)),
        (MessageBlastInternalNames.MLK_DAY, datetime.datetime(2020, 1, 13), (1, 12)),
        (MessageBlastInternalNames.VETERANS_DAY, datetime.datetime(2020, 11, 11), (7, 7)),
        (MessageBlastInternalNames.THANKSGIVING_DAY, datetime.datetime(2020, 11, 20), (1, 14)),
    ]

    for test in tests:
        with freeze_time(test[1]):
            create_message_template(test[0], business.id)
            get_date_mock.return_value = test[1]

            templates_ids = (
                MessageBlastTemplate.objects.filter(
                    business_id=business,
                    automated_status=MessageBlastTemplateStatus.ACTIVE,
                )
                .exclude(
                    # Welcome new client should not be processed here
                    internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
                )
                .values_list('id', flat=True)
            )

            res = process_message_blast_templates_batch_task.run(templates_ids)

            # The test expects increasing omitted counts because each BCI can only receive
            # one message per template per year, so after the first message, subsequent
            # attempts are omitted
            assert (res['created'], res['omitted']) == test[
                2
            ], f"{test[0]}: expected {test[2]}, got ({res['created']}, {res['omitted']})"


@pytest.mark.django_db
@patch('webapps.message_blast.models.MessageBlastTemplate.bcis_excluded_by_limits')
@patch('webapps.message_blast.tasks.get_timezones_with_hour')
def test_process_message_blast_schedules_task(regions_mock, limit_mock):
    limit_mock.return_value = []
    region = baker.make(
        Region,
        time_zone_name='Europe/Warsaw',
    )
    business = baker.make(
        Business,
        name='To jest nazwa',
        active=True,
        status=Business.Status.PAID,
        region=region,
        primary_category=baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.BARBERS,
        ),
    )

    bci = baker.make(
        BusinessCustomerInfo,
        business=business,
        user=baker.make(User, email=''),
        visible_in_business=True,
    )
    datetime_1 = datetime.datetime(2020, 11, 9)
    date_1 = datetime_1.date()
    date_2 = datetime.datetime(2020, 11, 12).date()
    message_blast_1 = baker.make(
        MessageBlast,
        business=business,
        internal_name=MessageBlastInternalNames.OWN_MESSAGE,
        sent=False,
        title='title',
        date_hour=MessageBlastTimeType.MORNING,
        scheduled_date=date_1,
        bcis=[bci.id],
        estimated_sms_cost=1.1,
    )
    message_blast_2 = baker.make(
        MessageBlast,
        business=business,
        internal_name=MessageBlastInternalNames.OWN_MESSAGE,
        sent=False,
        title='title 2',
        date_hour=MessageBlastTimeType.MORNING,
        scheduled_date=date_2,
        bcis=[bci.id],
        estimated_sms_cost=1.1,
    )
    regions_mock.side_effect = [['Europe/Warsaw'], ['Indian/Maldives'], ['Pacific/Fiji']]

    with freeze_time(datetime_1):
        result = process_message_blast_schedules_task()
        assert result == {'8 hour': 1, '12 hour': 0, '16 hour': 0, 'templates_count': 1}

    assert MessageBlast.objects.filter(sent=True).first().id == message_blast_1.id
    assert MessageBlast.objects.filter(sent=False).first().id == message_blast_2.id


@pytest.mark.django_db
@patch('webapps.message_blast.tasks.get_timezones_with_hour')
def test_process_message_blast_templates_task_antispam(regions_mock, business, bci_email):
    update_message_blast_groups()
    create_templates()

    turn_on_blasts_for_business(business)
    regions_mock.return_value = ['Europe/Warsaw']

    # Limit is 3, so we need to send 4 blasts to check
    template = MessageBlastTemplate.objects.filter(
        business_id=business.id,
        automated_status=MessageBlastTemplateStatus.ACTIVE,
        internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
    ).first()
    with freeze_time(datetime.datetime(2020, 11, 1)):
        template.create_blast([bci_email.id])

    CommonMessageBlastTemplate.objects.update(automated_status=MessageBlastTemplateStatus.INACTIVE)

    # 3rd template was omitted due to anti spam filter
    tests = [
        (MessageBlastInternalNames.HOLIDAY_BOOKING, datetime.datetime(2020, 11, 9), (1, 0)),
        (MessageBlastInternalNames.VETERANS_DAY, datetime.datetime(2020, 11, 11), (1, 1)),
        (MessageBlastInternalNames.THANKSGIVING_DAY, datetime.datetime(2020, 11, 20), (0, 3)),
    ]

    for test in tests:
        with freeze_time(test[1]):
            create_message_template(test[0], business.id)

            templates_ids = (
                MessageBlastTemplate.objects.filter(
                    business_id=business,
                    automated_status=MessageBlastTemplateStatus.ACTIVE,
                )
                .exclude(
                    # Welcome new client should not be processed here
                    internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
                )
                .values_list('id', flat=True)
            )

            res = process_message_blast_templates_batch_task.run(templates_ids)

            assert (res['created'], res['omitted']) == test[2], test[0]
