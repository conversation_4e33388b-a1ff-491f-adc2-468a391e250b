from datetime import date
from typing import Protocol

from ....commons.types import DayMinutes, ResourceId
from ...business.dto import PriceList
from ..dto import ComboBooking, DailyResourceBookedOccupancyMap, PossibleSlotsMap
from .time_slots import TimeSlotsCalculator


class CalendarAggregate(Protocol):

    @staticmethod
    def extract_resources_ids(
        bookings: list[ComboBooking],
        price_list: PriceList,
    ) -> set[ResourceId]: ...

    def find_time_slots(  # pylint: disable=too-many-arguments
        self,
        bookings: list[ComboBooking],
        price_list: PriceList,
        daily_resources_booked_occupancy_map: DailyResourceBookedOccupancyMap,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
        include_empty_days: bool = False,
    ) -> PossibleSlotsMap: ...

    def find_first_free_time_slot(
        self,
        bookings: list[ComboBooking],
        price_list: PriceList,
        daily_resources_booked_occupancy_map: DailyResourceBookedOccupancyMap,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
    ) -> PossibleSlotsMap: ...


class CalendarAggregateImpl(CalendarAggregate):
    def __init__(
        self,
        time_slots_calculator: TimeSlotsCalculator = TimeSlotsCalculator(),
    ) -> None:
        self.time_slots_calculator = time_slots_calculator

    @staticmethod
    def extract_resources_ids(
        bookings: list[ComboBooking],
        price_list: PriceList,
    ) -> set[ResourceId]:
        resource_ids: set[ResourceId] = set()
        for booking in bookings:
            s_id = booking.service_variant_id
            service = price_list.services[s_id]
            if service.is_combo:
                for combo_child_booking in booking.combo_children:
                    child_service = price_list.services[combo_child_booking.service_variant_id]
                    if combo_child_booking.staffer_id:
                        resource_ids.add(combo_child_booking.service_variant_id)
                    else:
                        resource_ids |= child_service.staffer_ids or set()
                    if child_service.appliance_ids:
                        resource_ids |= child_service.appliance_ids or set()
            elif booking.staffer_id:
                resource_ids.add(booking.staffer_id)
            else:
                resource_ids |= service.staffer_ids or set()
            if service.appliance_ids:
                resource_ids |= service.appliance_ids or set()
        return resource_ids

    def find_time_slots(  # pylint: disable=too-many-arguments
        self,
        bookings: list[ComboBooking],
        price_list: PriceList,
        daily_resources_booked_occupancy_map: DailyResourceBookedOccupancyMap,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
        include_empty_days: bool = False,
    ) -> PossibleSlotsMap:
        return self.time_slots_calculator.find_time_slots(
            bookings=bookings,
            price_list=price_list,
            daily_resources_booked_occupancy_map=daily_resources_booked_occupancy_map,
            minutes_offsets_map=minutes_offsets_map,
            include_empty_days=include_empty_days,
        )

    def find_first_free_time_slot(  # pylint: disable=too-many-arguments
        self,
        bookings: list[ComboBooking],
        price_list: PriceList,
        daily_resources_booked_occupancy_map: DailyResourceBookedOccupancyMap,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
    ) -> PossibleSlotsMap:
        return self.time_slots_calculator.find_time_slots(
            bookings=bookings,
            price_list=price_list,
            daily_resources_booked_occupancy_map=daily_resources_booked_occupancy_map,
            minutes_offsets_map=minutes_offsets_map,
            find_only_first=True,
        )
