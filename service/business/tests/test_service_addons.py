import json
from decimal import Decimal
from django.db import connection
import pytest
from pytest_django import asserts
from dateutil.relativedelta import relativedelta
from mock import MagicMock, patch
from model_bakery import baker
from model_bakery.recipe import related
from rest_framework import status
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_400_BAD_REQUEST

from lib.tools import l_b, tznow
from service.tests import ApiClient, get_request
from webapps.business.baker_recipes import (
    service_addon_recipe,
    service_recipe,
    service_variant_recipe,
    resource_recipe,
)

from webapps.business.enums import (
    ComboPricing,
    ComboType,
    PriceType,
)
from webapps.business.models import Business, ComboMembership, ServiceAddOn
from webapps.photo.models import Photo
from webapps.pos.elasticsearch.addons import ServiceAddOnDocument
from webapps.pos.models import POS, TaxRate


# pylint: disable=redefined-outer-name,unused-argument


@pytest.fixture(scope="function")
def setup_service_with_multiple_addons(db):
    multi_variant_services = service_recipe.extend(
        service_variants=related(service_variant_recipe),
    ).make(_quantity=3)
    busy_business = multi_variant_services[0].business
    baker.make(
        ServiceAddOn,
        name='some addon',
        business=busy_business,
        price=12.50,
        price_type=PriceType.FIXED,
        services=multi_variant_services,
        duration=relativedelta(minutes=30),
        _quantity=3,
    )
    resource_recipe.extend(business=busy_business, services=multi_variant_services).make(
        _quantity=3
    )
    pos = baker.make(
        POS,
        business=busy_business,
    )
    baker.make(
        TaxRate,
        rate=Decimal('14.0'),
        pos=pos,
        default_for_service=True,
        default_for_product=True,
    )
    return multi_variant_services


@pytest.fixture
def service(db):
    return service_recipe.make()


@pytest.fixture
def business(service):
    pos = baker.make(
        POS,
        business=service.business,
    )
    baker.make(
        TaxRate,
        rate=Decimal('14.0'),
        pos=pos,
        default_for_service=True,
        default_for_product=True,
    )
    return service.business


@pytest.fixture
def addon(business, service):
    return baker.make(
        ServiceAddOn,
        name='some addon',
        business=business,
        price=12.50,
        price_type=PriceType.FIXED,
        services=[service],
        duration=relativedelta(minutes=30),
    )


@pytest.fixture
def api_client(db, business):
    client = ApiClient()
    client.setUp()
    client.set_session(business.owner)
    yield client
    client.tearDown()


@pytest.fixture
def busy_api_client(db, busy_business):
    client = ApiClient()
    client.setUp()
    client.set_session(busy_business.owner)
    yield client
    client.tearDown()


@pytest.fixture
def api_url_services_addons_list_handler(business):
    return f'/business_api/me/businesses/{business.id}/services/addons/'


@pytest.fixture
def api_url_services_addons_handler(business, addon):
    return f'/business_api/me/businesses/{business.id}/services/addons/{addon.id}/'


@pytest.fixture
def api_url_service_addons_list_handler(business, service):
    return f'/business_api/me/businesses/{business.id}/services/{service.id}/addons/'


@pytest.fixture
def api_url_customer_service_addons_list_handler(business, service):
    return f'/customer_api/me/businesses/{business.id}/services/{service.id}/addons/'


@pytest.fixture
def api_url_service_addon_photos_handler(business):
    return f'/business_api/me/businesses/{business.id}/services/addons/photo/'


@pytest.fixture
def api_url_service_addon_photo_handler(business, addon):
    return f'/business_api/me/businesses/{business.id}/services/addons/{addon.id}/photo/'


@pytest.fixture
def api_url_service_price():
    return '/customer_api/me/services/services_price/'


@pytest.fixture
def api_url_business_service_price(business):
    return f'/business_api/me/businesses/{business.id}/services/services_price/'


def test_addon_list(business, service, api_client, api_url_services_addons_list_handler):
    unneeded_business = baker.make(
        Business,
    )
    unneeded_addon = baker.make(
        ServiceAddOn,
        name='some addon unneeded',
        business=unneeded_business,
        price=12.50,
        price_type=PriceType.FIXED,
        services=[],
        duration=relativedelta(minutes=30),
    )
    addon_1 = baker.make(
        ServiceAddOn,
        name='Some Add-on',
        business=business,
        price=12.50,
        price_type=PriceType.FIXED,
        services=[service],
        duration=relativedelta(minutes=30),
    )
    photo = baker.make(Photo, image_url='http://test.com/1.png')
    addon_1.photo_id = photo.id
    addon_1.save()
    addon_2 = baker.make(
        ServiceAddOn,
        name='some addon 2',
        business=business,
        price=12.50,
        order=3,
        duration=relativedelta(minutes=30),
    )
    addon_3 = baker.make(
        ServiceAddOn,
        name='some addon 3',
        business=business,
        order=1,
        duration=relativedelta(minutes=200),
        deleted=tznow(),
    )
    addon_4 = baker.make(
        ServiceAddOn,
        name='some 4',
        business=business,
        order=3,
        duration=relativedelta(minutes=200),
    )

    addon_ids = [addon_1.id, addon_2.id, addon_3.id, unneeded_addon.id, addon_4.id]
    ServiceAddOnDocument.reindex(addon_ids, refresh_index=True)

    resp = api_client.fetch(api_url_services_addons_list_handler, method='GET')
    assert resp.code == HTTP_200_OK
    assert len(resp.json['data']) == 3
    assert resp.json['data'][0]['name'] == 'Some Add-on'
    assert resp.json['data'][1]['name'] == 'some addon 2'
    assert resp.json['data'][2]['name'] == 'some 4'

    # search by name
    resp = api_client.fetch(
        api_url_services_addons_list_handler, method='GET', args=dict(query='add')
    )
    assert resp.code == HTTP_200_OK
    assert len(resp.json['data']) == 2
    for i in resp.json['data']:
        del i['meta']
    assert resp.json == {
        'count': 2,
        'per_page': 20,
        'data': [
            {
                'id': addon_1.id,
                'business_id': business.id,
                'name': 'Some Add-on',
                'price': 12.5,
                'price_type': PriceType.FIXED,
                'price_description': None,
                'tax_rate': None,
                'duration': 30.0,
                'photo': {'id': photo.id, 'url': photo.image_url},
                'services': [service.id],
                'order': 1,
                'is_available_for_customer_booking': True,
                'max_allowed_quantity': None,
                'service_price': '$12.50',
            },
            {
                'id': addon_2.id,
                'business_id': business.id,
                'name': 'some addon 2',
                'price': 12.5,
                'price_type': PriceType.FIXED,
                'price_description': None,
                'tax_rate': None,
                'duration': 30.0,
                'photo': None,
                'services': [],
                'order': 3,
                'is_available_for_customer_booking': True,
                'max_allowed_quantity': None,
                'service_price': '$12.50',
            },
        ],
    }

    resp = api_client.fetch(f'{api_url_services_addons_list_handler}{addon_1.id}/', method='GET')
    assert resp.code == HTTP_200_OK
    addon_data = resp.json['addon']
    assert addon_data['name'] == 'Some Add-on'


def test_addon_create(api_client, api_url_services_addons_list_handler, service):
    body = dict(
        name='some addon',
        price=12.50,
        price_type=PriceType.FIXED,
        services=[service.id],
        duration=15,
        tax_rate=14,
    )
    resp = api_client.fetch(api_url_services_addons_list_handler, method='POST', body=body)
    assert resp.code == HTTP_201_CREATED
    addon_data = resp.json['addon']
    assert addon_data['price'] == '12.50'
    assert addon_data['tax_rate'] == '14.00'
    assert addon_data['services'] == [service.id]
    assert addon_data['order'] == 1


def test_addon_create_without_price(api_client, api_url_services_addons_list_handler, service):
    body = dict(
        name='some addon',
        price_type=PriceType.FIXED,
        services=[service.id],
        duration=15,
        tax_rate=14,
    )
    resp = api_client.fetch(api_url_services_addons_list_handler, method='POST', body=body)
    assert resp.code == HTTP_400_BAD_REQUEST

    body['price_type'] = PriceType.FREE
    resp = api_client.fetch(api_url_services_addons_list_handler, method='POST', body=body)
    assert resp.code == HTTP_201_CREATED

    addon_data = resp.json['addon']
    assert addon_data['price'] is None
    assert addon_data['tax_rate'] == '14.00'
    assert addon_data['services'] == [service.id]
    assert addon_data['order'] == 1


def test_addon_reorder(api_client, api_url_services_addons_list_handler, business):
    addon_0 = baker.make(
        ServiceAddOn,
        name='some addon 1',
        business=business,
        order=0,
    )
    addon_1 = baker.make(
        ServiceAddOn,
        name='some addon 2',
        business=business,
        order=1,
    )

    body = {
        'ordering': [
            {
                'id': addon_1.id,
                'order': addon_0.order,
            },
            {
                'id': addon_0.id,
                'order': addon_1.order,
            },
        ],
    }
    resp = api_client.fetch(
        f'{api_url_services_addons_list_handler}?use_objects=true',
        method='PUT',
        body=json.dumps(body),
    )

    assert resp.code == HTTP_200_OK
    resp_json = json.loads(l_b(resp.body))
    assert isinstance(resp_json, dict)
    assert 'ordering' in resp_json
    assert isinstance(resp_json['ordering'], list)
    addon_0.refresh_from_db()
    addon_1.refresh_from_db()
    assert addon_0.order == 1
    assert addon_1.order == 0


def test_addon_get(api_client, api_url_services_addons_handler, service):
    resp = api_client.fetch(api_url_services_addons_handler, method='GET')
    assert resp.code == HTTP_200_OK
    addon_data = resp.json['addon']
    assert addon_data['price'] == '12.50'
    assert addon_data['services'] == [service.id]
    assert addon_data['duration'] == 30


def test_addon_update(api_client, api_url_services_addons_handler, service):
    body = dict(
        name='some addon 1',
        price=13.50,
        price_type=PriceType.FIXED,
        services=[service.id],
        duration=15,
    )

    resp = api_client.fetch(api_url_services_addons_handler, method='PUT', body=body)
    assert resp.code == HTTP_200_OK
    addon_data = resp.json['addon']
    assert addon_data['price'] == '13.50'
    assert addon_data['services'] == [service.id]
    assert addon_data['duration'] == 15
    assert addon_data['tax_rate'] == '14.00'

    body['tax_rate'] = None
    resp = api_client.fetch(api_url_services_addons_handler, method='PUT', body=body)
    addon_data = resp.json['addon']
    assert addon_data['tax_rate'] is None

    body['tax_rate'] = 0
    resp = api_client.fetch(api_url_services_addons_handler, method='PUT', body=body)
    addon_data = resp.json['addon']
    assert addon_data['tax_rate'] == '0.00'


@pytest.mark.django_db
def test_multiple_addon_update(setup_service_with_multiple_addons):
    services = setup_service_with_multiple_addons
    body = dict(
        name='some addon 1',
        price=13.50,
        price_type=PriceType.FIXED,
        duration=15,
        services=[s.id for s in services],
    )

    business = services[0].business
    addon = services[0].addons.first()
    client = ApiClient()
    client.setUp()
    client.set_session(business.owner)
    url = f'/business_api/me/businesses/{business.id}/services/addons/{addon.id}/'

    with asserts.assertNumQueries(29):  # pylint: disable=not-context-manager
        resp = client.fetch(url, method='PUT', body=body)
    queries = connection.queries
    count = 0
    for query in queries:
        if 'sql' in query and 'SELECT "business_servicevariant' in query['sql']:
            count += 1
    assert count == 1
    assert resp.code == HTTP_200_OK


def test_addon_delete(api_client, api_url_services_addons_handler):
    resp = api_client.fetch(api_url_services_addons_handler, method='DELETE')
    assert resp.code == HTTP_200_OK
    assert resp.json == {}


def test_addon_list_for_particular_service_business(
    api_client, api_url_service_addons_list_handler, business, service
):
    addon_1 = baker.make(
        ServiceAddOn,
        name='some addon 1',
        business=business,
        price=12.50,
        price_type=PriceType.FIXED,
        services=[service],
        duration=relativedelta(minutes=30),
        order=1,
    )
    addon_2 = baker.make(
        ServiceAddOn,
        name='some addon 2',
        business=business,
        price=10.50,
        price_type=PriceType.FIXED,
        services=[service],
        duration=relativedelta(minutes=15),
        order=2,
    )

    resp = api_client.fetch(api_url_service_addons_list_handler, method='GET')
    assert resp.code == HTTP_200_OK
    addons_data = resp.json['data']

    assert len(addons_data) == 2

    assert addons_data[0]['name'] == addon_1.name
    assert addons_data[0]['price'] == '12.50'
    assert addons_data[0]['services'] == [service.id]
    assert addons_data[0]['duration'] == 30

    assert addons_data[1]['name'] == addon_2.name
    assert addons_data[1]['price'] == '10.50'
    assert addons_data[1]['services'] == [service.id]
    assert addons_data[1]['duration'] == 15


def test_addon_list_for_particular_service_customer(
    api_client, api_url_customer_service_addons_list_handler, business, service
):
    addon_1 = baker.make(
        ServiceAddOn,
        name='some addon 1',
        business=business,
        price=12.50,
        price_type=PriceType.FIXED,
        services=[service],
        duration=relativedelta(minutes=30),
        order=1,
    )
    baker.make(
        ServiceAddOn,
        name='some addon 2',
        business=business,
        price=12.50,
        price_type=PriceType.FIXED,
        services=[service],
        duration=relativedelta(minutes=30),
        order=1,
        is_available_for_customer_booking=False,
    )

    api_client.fetch('/business_api/account/logout/', method='GET')
    resp = api_client.fetch(api_url_customer_service_addons_list_handler, method='GET')
    assert resp.code == HTTP_200_OK
    addons_data = resp.json['data']

    assert len(addons_data) == 1

    assert addons_data[0]['name'] == addon_1.name
    assert addons_data[0]['price'] == '12.50'
    assert addons_data[0]['services'] == [service.id]
    assert addons_data[0]['duration'] == 30


def test_addon_list_for_particular_service_customer__combo(
    api_client, api_url_customer_service_addons_list_handler, business, service
):
    child_1 = service_variant_recipe.make(
        service__business=business,
    )
    child_2 = service_variant_recipe.make(
        service__business=business,
    )

    service.combo_type = ComboType.SEQUENCE
    service.save()

    service_variant = service_variant_recipe.make(
        service=service,
        combo_pricing=ComboPricing.SERVICES,
    )
    ComboMembership.objects.bulk_create(
        [
            ComboMembership(
                combo=service_variant,
                child=child_1,
            ),
            ComboMembership(
                combo=service_variant,
                child=child_2,
            ),
        ]
    )

    addon_1 = service_addon_recipe.make(
        name='Addon 1',
        business=business,
        services=[child_1.service],
        order=1,
    )
    addon_2 = service_addon_recipe.make(
        name='Addon 2',
        business=business,
        services=[child_2.service],
        duration=relativedelta(minutes=30),
        order=2,
    )
    service_addon_recipe.make(
        name='Addon 3',
        business=business,
        services=[child_1.service, child_2.service],
        order=3,
        is_available_for_customer_booking=False,
    )

    resp = api_client.fetch(
        api_url_customer_service_addons_list_handler,
        method='GET',
        args={
            'combos_with_addons': '1',
        },
    )
    assert resp.code == HTTP_200_OK
    addons_data = resp.json['data']
    assert len(addons_data) == 2

    assert addons_data[0]['name'] == addon_1.name
    assert addons_data[0]['services'] == [child_1.service.id]
    assert set(addons_data[0]['service_variants']) == {child_1.id}

    assert addons_data[1]['name'] == addon_2.name
    assert addons_data[1]['services'] == [child_2.service.id]
    assert set(addons_data[1]['service_variants']) == {child_2.id}

    resp = api_client.fetch(api_url_customer_service_addons_list_handler, method='GET')
    assert resp.code == HTTP_200_OK
    assert len(resp.json['data']) == 0

    child_1_addons_url = (
        f'/customer_api/me/businesses/{business.id}/services/{child_1.service_id}/addons/'
    )
    resp = api_client.fetch(
        child_1_addons_url,
        method='GET',
        args={
            'combos_with_addons': '1',
        },
    )
    assert resp.code == HTTP_200_OK
    assert len(resp.json['data']) == 1
    assert set(resp.json['data'][0]['services']) == {child_1.service_id}

    resp = api_client.fetch(child_1_addons_url, method='GET')
    assert resp.code == HTTP_200_OK
    assert len(resp.json['data']) == 1
    assert set(resp.json['data'][0]['services']) == {child_1.service_id}


def test_customer_should_return_404_on_inactive_service_addon_list(
    api_client,
    api_url_customer_service_addons_list_handler,
    business,
    service,
    addon,
):
    service.active = False
    service.save()

    response = api_client.fetch(api_url_customer_service_addons_list_handler, method='GET')
    assert response.code == status.HTTP_404_NOT_FOUND


@patch('webapps.images.tasks.migrate_photo_to_s3', MagicMock(return_value=None))
def test_addon_add_photo(api_client, api_url_service_addon_photo_handler):
    request = get_request(api_url_service_addon_photo_handler)
    api_client.content_type = request.headers.get('Content-Type')

    resp = api_client.fetch(api_url_service_addon_photo_handler, method='POST', body=request.body)
    assert resp.code == status.HTTP_201_CREATED
    assert resp.json['url'].startswith('https://img.') is True
    assert Photo.objects.last().id == resp.json['id']


@patch('webapps.images.tasks.migrate_photo_to_s3', MagicMock(return_value=None))
def test_delete_addon_photo(addon, api_client, api_url_service_addon_photo_handler):
    photo = baker.make(Photo)
    addon.photo_id = photo.id
    addon.save()

    response = api_client.fetch(api_url_service_addon_photo_handler, method='DELETE')
    addon.refresh_from_db()
    assert response.code == status.HTTP_200_OK
    assert addon.photo is None


def test_calculate_price(api_client, api_url_service_price):
    body = {
        "prices": [
            {"quantity": 3, "price": 10, "type": PriceType.FIXED},
            {"quantity": 2, "price": 12, "type": PriceType.STARTS_AT},
            {"quantity": 1, "price": 5, "type": PriceType.FIXED},
        ],
    }
    result = {
        "price": 59,
        "type": PriceType.STARTS_AT,
        "service_price": "$59.00+",
    }
    resp = api_client.fetch(api_url_service_price, method='POST', body=body)
    assert resp.code == status.HTTP_200_OK
    assert resp.json == result


def test_calculate_business_price(api_client, api_url_business_service_price):
    body = {
        "prices": [
            {"quantity": 3, "price": 10, "type": PriceType.FIXED},
            {"quantity": 2, "price": 12, "type": PriceType.STARTS_AT},
            {"quantity": 1, "price": 5, "type": PriceType.FIXED},
        ],
    }
    result = {
        "price": 59,
        "type": PriceType.STARTS_AT,
        "service_price": "$59.00+",
    }
    resp = api_client.fetch(api_url_business_service_price, method='POST', body=body)
    assert resp.code == status.HTTP_200_OK
    assert resp.json == result

    body = {
        "prices": [
            {"quantity": 3, "price": 10, "type": PriceType.VARIES},
            {"quantity": 2, "price": 12, "type": PriceType.DONT_SHOW},
            {"quantity": 1, "price": 5, "type": PriceType.FIXED},
        ],
    }
    result = {'price': None, 'type': PriceType.VARIES, 'service_price': PriceType.VARIES.label}
    resp = api_client.fetch(api_url_business_service_price, method='POST', body=body)
    assert resp.code == status.HTTP_200_OK
    assert resp.json == result


def test_should_ignore_inactive_service(
    api_client, service, addon, api_url_services_addons_handler
):
    service_variant_recipe.make(service=service)
    assert service.active
    assert service in addon.active_services

    service.active = False
    service.save()

    body = {
        'name': 'some addon 1',
        'price': 13.50,
        'price_type': PriceType.FIXED,
        'services': [service.id],
        'duration': 15,
    }
    resp = api_client.fetch(api_url_services_addons_handler, method='PUT', body=body)
    assert resp.code == status.HTTP_200_OK

    resp = api_client.fetch(api_url_services_addons_handler, method='GET')
    assert resp.code == status.HTTP_200_OK
    assert service.id not in resp.json['addon']['services']


def test_preserve_inactive_services_after_update(
    api_client, service, addon, api_url_services_addons_handler, business
):
    assert service in addon.active_services

    service.active = False
    service.save()
    service2 = service_recipe.make(business=business, active=True)

    body = {
        'name': 'some addon 1',
        'price': 13.50,
        'price_type': PriceType.FIXED,
        'services': [service2.id],
        'duration': 15,
    }
    resp = api_client.fetch(api_url_services_addons_handler, method='PUT', body=body)
    assert resp.code == status.HTTP_200_OK
    assert service.id not in resp.json['addon']['services']
    assert service2.id in resp.json['addon']['services']

    service.active = True
    service.save()

    resp = api_client.fetch(api_url_services_addons_handler, method='GET')
    assert resp.code == status.HTTP_200_OK
    assert service.id in resp.json['addon']['services']
    assert service2.id in resp.json['addon']['services']


def test_should_not_show_inactive_services_on_addon_detail(
    api_client, service, addon, api_url_services_addons_handler
):
    assert service in addon.active_services

    service.active = False
    service.save()

    resp = api_client.fetch(api_url_services_addons_handler, method='GET')
    assert resp.code == status.HTTP_200_OK
    assert service.id not in resp.json['addon']['services']


def test_should_not_show_inactive_services_on_addon_list(
    api_client, service, addon, api_url_services_addons_list_handler
):
    assert service in addon.active_services

    service.active = False
    service.save()

    # list view is served from ES
    ServiceAddOnDocument.reindex([addon.id], refresh_index=True)

    resp = api_client.fetch(api_url_services_addons_list_handler, method='GET')
    assert resp.code == status.HTTP_200_OK
    hit = resp.json['data'][0]
    assert service.id not in hit['services']


def test_should_allow_create_addon_with_inactive_service(
    api_client,
    service,
    api_url_services_addons_list_handler,
):
    service.active = False
    service.save()

    body = {
        'name': 'some addon',
        'price': 12.50,
        'price_type': PriceType.FIXED,
        'services': [service.id],
        'duration': 15,
        'tax_rate': 14,
    }
    resp = api_client.fetch(api_url_services_addons_list_handler, method='POST', body=body)
    assert resp.code == status.HTTP_201_CREATED
    # inactive service, should not be returned
    assert resp.json['addon']['services'] == []


def test_should_not_show_deleted_services_on_addon_list(
    api_client, service, addon, api_url_services_addons_list_handler
):
    assert service in addon.active_services
    # list view is served from ES
    ServiceAddOnDocument.reindex([addon.id], refresh_index=True)
    service.safe_delete()

    resp = api_client.fetch(api_url_services_addons_list_handler, method='GET')

    assert resp.code == status.HTTP_200_OK
    addon = resp.json['data'][0]
    assert addon['services'] == []


def test_should_return_404_on_inactive_service_addon_list(
    api_client,
    api_url_service_addons_list_handler,
    business,
    service,
):
    resp = api_client.fetch(api_url_service_addons_list_handler, method='GET')
    assert resp.code == status.HTTP_200_OK

    service.active = False
    service.save()

    resp = api_client.fetch(api_url_service_addons_list_handler, method='GET')
    assert resp.code == status.HTTP_404_NOT_FOUND
