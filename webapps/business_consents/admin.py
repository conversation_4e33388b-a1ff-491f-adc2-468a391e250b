from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.business_consents.models import BusinessConsent
from webapps.user.groups import GroupNameV2


class BusinessConsentAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'id',
        'consent_code',
        'business_id',
        'visible',
        'decision',
        'filling_date',
    )
    list_filter = [
        'consent_code',
        'decision',
        'filling_date',
        'visible',
    ]
    search_fields = ['business_id']
    readonly_fields = [
        'decision',
        'filling_date',
        'filling_user_id',
        'filling_ip',
        'payload',
        'updated',
        'created',
    ]


admin.site.register(BusinessConsent, BusinessConsentAdmin)
