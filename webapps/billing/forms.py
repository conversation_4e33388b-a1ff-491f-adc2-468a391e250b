import datetime
import random
import typing as t
from collections import OrderedDict
from decimal import ROUND_HALF_UP, Decimal

from django import forms
from django.conf import settings
from django.contrib import admin
from django.contrib.admin.widgets import AdminDateWidget, ForeignKeyRawIdWidget
from django.core.exceptions import NON_FIELD_ERRORS, ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import transaction
from django.db.models import Q
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext as _

from lib.tools import tznow
from webapps import consts
from webapps.billing.cache import get_business_payment_processor
from webapps.billing.enums import (
    BillingLongSubscriptionDuration,
    BillingLongSubStaffQuantity,
    BillingRefundStatus,
    DiscountDuration,
    DiscountReason,
    DiscountType,
    PaymentProcessorType,
    ProductType,
    RefundReason,
    SubscriptionStatus,
)
from webapps.billing.models import (
    BillingBusinessDiscount,
    BillingBusinessOffer,
    BillingCountryConfig,
    BillingCreditCardInfo,
    BillingDiscountCode,
    BillingLongSubMapper,
    BillingOfflineMigration,
    BillingOneOffCharge,
    BillingOneOffChargeProduct,
    BillingPaymentMethod,
    BillingPostpaidSMSProduct,
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingRefundProduct,
    BillingSaaSProduct,
    BillingStafferSaaSProduct,
    BillingSubscription,
    BillingTransaction,
    BillingTransactionRefund,
    ExternalCustomer,
)
from webapps.billing.permissions import BillingUserPermission
from webapps.billing.services.charge import BillingTransactionService
from webapps.billing.tasks import billing_refund_task
from webapps.billing.utils import relativedelta_by_step
from webapps.business.models import Business, CancellationReason
from webapps.business.models.business_change import BusinessChange
from webapps.purchase.models import Subscription
from webapps.stripe_app.apis.charge import PaymentIntentApi
from webapps.stripe_app.enums import Subscriber


def align_discount_field_fixed(field):
    field.max_digits = consts.PRICE_MAX_DIGITS
    field.decimal_places = consts.PRICE_MAX_DECIMALS
    field.validators = [MinValueValidator(Decimal('0.01'))]
    field.label += '(fixed price, e.g.: 20)'
    return field


def align_discount_field_percentage(field):
    field.max_digits = 4
    field.decimal_places = 1
    field.validators = [
        MinValueValidator(Decimal('0.1')),
        MaxValueValidator(100),
    ]
    field.label += '(%, e.g. 10.5 => 10.5%)'
    return field


def align_discount_field(field, discount_type):
    if discount_type == DiscountType.PERCENTAGE:
        return align_discount_field_percentage(field)
    if discount_type == DiscountType.FIXED:
        return align_discount_field_fixed(field)
    return field


def _get_discount_display_value(value, discount_type):
    if value is None:
        return
    value = value if isinstance(value, Decimal) else Decimal(value)
    if discount_type == DiscountType.PERCENTAGE.value:
        return (value * Decimal(100)).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)
    if discount_type == DiscountType.FIXED.value:
        return value


class ProductForm(forms.ModelForm):
    """
    Generic product form.
    User adds each product in separate view, doesn't have to know we keep
    all of them in one db table.
    """

    CURRENCY_CHOICES = ((i, i) for i in settings.BILLING_CURRENCIES)

    currency = forms.ChoiceField(
        initial=settings.CURRENCY_CODE,
        choices=CURRENCY_CHOICES,
    )

    class Meta:
        model = BillingProduct
        fields = (
            'name',
            'unit_price',
            'active',
            'currency',
            'free_staff_qty',
            'max_qty',
            'sms_amount',
            'staff_add_on',
            'sms_add_on',
        )

    def clean_active(self):
        active = self.cleaned_data['active']
        if (
            self.instance.id
            and not active
            and (
                BillingProductOffer.objects.filter(
                    offer_items__product=self.instance.id, active=True
                ).exists()
                or BillingBusinessOffer.objects.active()
                .filter(offer__offer_items__product=self.instance.id)
                .exists()
                or BillingDiscountCode.objects.active()
                .filter(offer__offer_items__product=self.instance.id)
                .exists()
            )
        ):
            raise ValidationError('There is active offer or discount assigned to it.')
        return active

    def save(self, commit=True):
        product = super().save(commit=False)
        product.product_type = product.valid_product_type
        if commit:
            product.save(
                _history={
                    'operator': getattr(self, 'operator', None),
                    'metadata': {'endpoint': 'ProductForm'},
                },
            )
        return product


class SaasProductForm(ProductForm):
    sms_amount = forms.IntegerField(
        min_value=0,
        initial=0,
        label='Free SMS included in the plan (marketing only)',
    )

    staff_add_on = forms.ModelChoiceField(
        queryset=BillingStafferSaaSProduct.objects.filter(
            active=True,
            product_type=ProductType.STAFFER_SAAS,
        ),
        required=True,
        label='Staff add on',
    )
    sms_add_on = forms.ModelChoiceField(
        queryset=BillingPostpaidSMSProduct.objects.filter(
            active=True,
            product_type=ProductType.POSTPAID_SMS,
        ),
        label='SMS add on',
    )

    update_not_allowed_add_ons = ('staff_add_on', 'sms_add_on')

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        super().__init__(*args, **kwargs)
        if instance:
            for field in self.update_not_allowed_add_ons:
                self.fields[field].disabled = True


class StafferProductForm(ProductForm):
    sms_amount = forms.IntegerField(
        min_value=0,
        initial=0,
        label='Free SMS included (per 1 staffer)',
    )
    free_staff_qty = forms.IntegerField(
        required=False,
        min_value=0,
        initial=None,
        label='Free staff members count',
    )
    max_qty = forms.IntegerField(
        required=False,
        min_value=0,
        initial=None,
        label='Maximum quantity',
        help_text=mark_safe(
            'Staffers above this limit will be charge free.<br>'
            'If set to 0 <b>Free staff members count</b> will be marked as <b>unlimited</b>'
        ),
    )


class PaymentMethodDisplayForm(forms.ModelForm):
    """Display payment method data different from credit card"""

    class Meta:
        model = BillingPaymentMethod
        fields = ('payment_method_type',)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name in self.fields:
            self.fields[field_name].disabled = True


class CreditCardDisplayForm(forms.ModelForm):
    class Meta:
        model = BillingCreditCardInfo
        fields = (
            'card_type',
            'masked_number',
            'cardholder_name',
            'expiration_date',
            'country_of_issuance',
            'address_line_1',
            'address_line_2',
            'city',
            'zipcode',
            'country',
        )

    masked_number = forms.CharField(label='Card number')

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if instance:
            initial['masked_number'] = instance.masked_number
            kwargs['initial'] = initial
        super().__init__(*args, **kwargs)
        for field_name in self.fields:
            self.fields[field_name].disabled = True


class BaseBillingBusinessOfferForm(forms.ModelForm):
    class Meta:
        model = BillingBusinessOffer
        fields = (
            'offer',
            'business',
            'active',
            'valid_from',
            'valid_to',
        )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if not instance or (instance and not instance.pk):
            default_valid_to = tznow() + datetime.timedelta(days=7)

            def parse_date(date_str, default_date=None):
                try:
                    return datetime.datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S')
                except (ValueError, TypeError):
                    return default_date

            initial['valid_to'] = parse_date(initial.get('valid_to'), default_valid_to)
            initial['valid_from'] = parse_date(initial.get('valid_from'))

        kwargs['initial'] = initial
        super().__init__(*args, **kwargs)

    def clean(self):
        super().clean()
        valid_from = self.cleaned_data.get('valid_from')
        valid_to = self.cleaned_data.get('valid_to')

        if valid_from and valid_to and (valid_from >= valid_to):
            raise forms.ValidationError("'From' date must be lower than 'To' date.")
        return self.cleaned_data


class BillingOfflineMigrationForm(forms.ModelForm):
    offer = forms.ModelChoiceField(
        queryset=BillingProductOffer.objects.filter(active=True, default=False),
        required=False,
        label='Offer (leave empty for default offer)',
    )

    class Meta:
        model = BillingOfflineMigration
        fields = ('business', 'offline_subscription_end_date')

    def clean_business(self):
        business = self.cleaned_data.get('business')
        if business and business.has_new_billing:
            raise ValidationError(
                'Business is already migrated to new billing.',
            )
        if business and business.payment_source != Business.PaymentSource.OFFLINE:
            raise ValidationError(
                'Business does not have offline subscription.',
            )
        if business.status not in [Business.Status.PAID, Business.Status.OVERDUE]:
            raise ValidationError(
                f'Business status different from PAID or OVERDUE '
                f'(current status: {business.get_status_display()})'
            )
        return business

    def clean(self):
        business = self.cleaned_data.get('business')
        end_date = self.cleaned_data.get('offline_subscription_end_date')

        if end_date and end_date.date() < tznow().date():
            raise ValidationError('The date must be greater than or equal to today')

        if BillingOfflineMigration.objects.filter(business=business, deleted__isnull=True).exists():
            raise ValidationError('Migration for this business already exists.')

        return self.cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        operator = getattr(self, 'operator', None)
        business = self.cleaned_data.get('business')
        active_offer = BillingProductOffer.objects.get_for_business(business)
        offer = self.cleaned_data.get('offer')

        _now = tznow()
        new_expiry_date = self.cleaned_data.get('offline_subscription_end_date')
        subscriptions = business.subscriptions.filter(
            Q(expiry__isnull=True) | Q(expiry__gt=tznow()),
            source=Business.PaymentSource.OFFLINE,
        ).exclude(business__status=Business.Status.OVERDUE)
        for subscription in subscriptions:
            if subscription.start > _now:
                subscription.expiry = subscription.start
            elif new_expiry_date:
                subscription.expiry = new_expiry_date

        Subscription.objects.bulk_update(subscriptions, ['expiry'])

        if offer and offer != active_offer:
            if active_offer:
                BillingBusinessOffer.objects.filter(business=business, active=True).update(
                    active=False,
                    _history={
                        'operator': operator,
                        'metadata': {'form': self.__class__.__name__},
                    },
                )
            business_offer = BillingBusinessOffer(
                business=business,
                offer=offer,
                active=True,
                valid_from=tznow(),
                valid_to=None,
            )
            business_offer.save()
            business_offer.add_to_history(
                metadata={'form': self.__class__.__name__},
                operator_id=operator.id if operator else None,
            )

        if existing_migration := BillingOfflineMigration.objects.filter(
            business_id=business.id,
            deleted__isnull=False,
        ).first():
            instance = existing_migration
            instance.deleted = None

        instance.migration_campaign_valid_to = tznow() + datetime.timedelta(
            days=settings.OFFLINE_TO_ONLINE_MIGRATION_CAMPAIGN_VALID_DAYS,
        )
        return instance


class BillingBusinessOfferForm(BaseBillingBusinessOfferForm):
    def clean(self):
        super().clean()
        business = self.cleaned_data.get('business')

        if business and BillingProductOffer.objects.get_for_business(business.id):
            raise forms.ValidationError('Business has an active custom offer already.')
        return self.cleaned_data


class DiscountFormMixin:
    def _clean_discount(self, field, discount_type, discount, product: t.Optional = None):
        """Set proper discount amount based on discount_type."""

        if discount_type == DiscountType.NO_DISCOUNT and discount:
            self.add_error(field, _('Cannot set NO_DISCOUNT because discount value is set'))
            return
        if discount_type == DiscountType.PERCENTAGE:
            if discount > 100.0:
                self.add_error(field, _('Percentage discount should be in range: 0 - 100'))
                return
            return float((discount / Decimal(100.0)).quantize(Decimal('0.001')))
        if discount_type == DiscountType.FIXED:
            if product and discount > product.unit_price:
                self.add_error(
                    field,
                    _(
                        'Fixed discount cannot be greater '
                        f'than product unit price ({product.unit_price})'
                    ),
                )
            return float(discount.quantize(Decimal('0.01')))


class BillingRefundProductForm(forms.Form):
    reason = forms.ChoiceField(required=True, choices=RefundReason.choices())
    make_full_refund = forms.BooleanField(
        required=False, help_text='If checked, maximum amounts for all products will be refunded'
    )

    def __init__(self, *args, **kwargs):
        self.products_to_refund = kwargs.pop('products_to_refund')
        self.operator = kwargs.pop('operator')
        self.transaction_id = kwargs.pop('transaction_id')
        self.total_refunded_amount = Decimal(0)
        super().__init__(*args, **kwargs)

        for product in self.products_to_refund:
            field_label = str(ProductType.choices_map().get(product['product__product_type']))
            self.fields[product['product__product_type']] = forms.DecimalField(
                decimal_places=2,
                min_value=Decimal('0.01'),
                max_value=product['available_amount_to_refund'],
                label=f"{field_label} amount",
                help_text=f"max {product['available_amount_to_refund']}",
                required=False,
            )
        self.fields = OrderedDict(self.fields)
        self.fields.move_to_end('reason')
        self.fields.move_to_end('make_full_refund')

    def clean(self):
        cleaned_data = super().clean()
        if cleaned_data['make_full_refund']:
            for product in self.products_to_refund:
                product['amount_to_refund'] = product['available_amount_to_refund']
                self.total_refunded_amount += product['amount_to_refund']
        else:
            for product in self.products_to_refund:
                if cleaned_data.get(product['product__product_type']):
                    product['amount_to_refund'] = cleaned_data[product['product__product_type']]
                    self.total_refunded_amount += product['amount_to_refund']
                else:
                    product['amount_to_refund'] = Decimal(0)

        if self.total_refunded_amount == Decimal(0):
            raise ValidationError(
                "You can't make refund for 0 amount",
            )
        return cleaned_data

    def save(self):
        with transaction.atomic():
            billing_transaction_refund = BillingTransactionRefund.objects.create_with_history(
                transaction_id=self.transaction_id,
                amount=self.total_refunded_amount,
                status=BillingRefundStatus.INITIAL,
                operator=self.operator,
                endpoint_description=self.__class__.__name__,
                reason=self.cleaned_data['reason'],
            )
            for product in self.products_to_refund:
                if product['amount_to_refund'] > Decimal(0):
                    BillingRefundProduct.objects.create(
                        refund=billing_transaction_refund,
                        product_id=product['id'],
                        amount=product['amount_to_refund'],
                    )

        metadata = dict(
            operator_id=self.operator.id,
            operator_email=self.operator.email,
            billing_transaction_id=self.transaction_id,
            billing_transaction_refund_id=billing_transaction_refund.id,
            country_code=settings.API_COUNTRY,
            reason=self.cleaned_data['reason'],
        )

        refund_task = billing_refund_task.delay(
            billing_transaction_refund_id=billing_transaction_refund.id,
            metadata=metadata,
        )
        return refund_task


class ProductOfferForm(DiscountFormMixin, forms.ModelForm):
    class Meta:
        model = BillingProductOffer
        fields = (
            'id',
            'name',
            'active',
            'default',
            'active_for_b2b_referral',
            'active_for_cs',
            'active_for_advanced_user',
            # BillingProductOfferItem fields
            'saas',
            'staff_add_on',
            'sms_add_on',
            'discount_type',
            'discount_duration',
            'discount_saas',
            'discount_staff_add_on',
            'subscription_duration',
            # end of BillingProductOfferItem fields
            # Busy products will be enabled in future
            # 'busy',
            # 'staffer_busy',
            # 'discount_busy',
            # 'discount_staffer_busy',
        )
        labels = {
            'active_for_cs': 'Active for CS',
            'active_for_advanced_user': 'Active for advanced User',
        }

    saas = forms.ModelChoiceField(
        queryset=BillingSaaSProduct.objects.filter(
            active=True,
            product_type=ProductType.SAAS,
        ),
    )
    staff_add_on = forms.ModelChoiceField(
        queryset=BillingStafferSaaSProduct.objects.filter(
            active=True,
            product_type=ProductType.STAFFER_SAAS,
        ),
        required=False,
        label='SaaS (Staffer Add on)',
    )
    sms_add_on = forms.ModelChoiceField(
        queryset=BillingPostpaidSMSProduct.objects.filter(
            active=True,
            product_type=ProductType.POSTPAID_SMS,
        ),
        required=False,
        label='SaaS (Postpaid SMS Add on)',
    )
    discount_type = forms.ChoiceField(
        choices=DiscountType.choices(),
    )
    discount_saas = forms.DecimalField(
        required=False,
        decimal_places=2,
        min_value=0,
    )
    discount_staff_add_on = forms.DecimalField(
        required=False,
        decimal_places=2,
        min_value=0,
        help_text='The discount will be applied for each payable Staff Member separately!',
        label='Discount staff add on (per 1 Staff Member)',
    )
    discount_duration = forms.ChoiceField(
        required=False,
        choices=DiscountDuration.choices(),
        label='Discount duration (# of billing cycles)',
        initial=None,
    )

    # Busy products will be enabled in future
    # busy = forms.ModelChoiceField(
    #     queryset=BillingBusyProduct.objects.filter(
    #         active=True, product_type=ProductType.BUSY,
    #     ),
    #     required=False,
    # )
    # staffer_busy = forms.ModelChoiceField(
    #     queryset=BillingStafferBusyProduct.objects.filter(
    #         active=True, product_type=ProductType.STAFFER_BUSY,
    #     ),
    #     required=False,
    # )
    # discount_busy = forms.DecimalField(
    #     required=False, decimal_places=2,
    #     min_value=0,
    # )
    # discount_staffer_busy = forms.DecimalField(
    #     required=False, decimal_places=2,
    #     min_value=0,
    # )

    product_type_to_field = {
        ProductType.SAAS.value: 'saas',
        ProductType.STAFFER_SAAS.value: 'staff_add_on',
        ProductType.POSTPAID_SMS.value: 'sms_add_on',
        # Busy products will be enabled in future
        # ProductType.BUSY.value: 'busy',
        # ProductType.STAFFER_BUSY.value: 'staffer_busy',
    }
    product_type_to_discount_field = {
        ProductType.SAAS.value: 'discount_saas',
        ProductType.STAFFER_SAAS.value: 'discount_staff_add_on',
        # Busy products will be enabled in future
        # ProductType.BUSY.value: 'discount_busy',
        # ProductType.STAFFER_BUSY.value: 'discount_staffer_busy',
    }
    readonly_products = (
        'staff_add_on',
        'sms_add_on',
    )  # create + update
    update_not_allowed_products = ('saas',)  # update

    def clean_discount_duration(self):
        return self.cleaned_data.get('discount_duration') or None

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if (
            instance
            and instance.offer_items.filter(
                product__product_type=ProductType.SAAS,
                active=True,
            ).exists()
        ):
            saas = instance.offer_items.get(
                product__product_type=ProductType.SAAS,
                active=True,
            )
            initial['saas'] = saas.product
            initial['staff_add_on'] = saas.product.staff_add_on
            initial['sms_add_on'] = saas.product.sms_add_on

            offer_items = instance.offer_items.filter(active=True).prefetch_related('product').all()
            # Set initial values for saas, staffer saas etc.
            for item in offer_items:
                discount_type = item.discount_type
                discount_source_field = self._get_discount_source_field(discount_type)
                product_type = item.product.product_type
                # For now all product types can be added only once due to
                # form validation, so we can do simple mapping here
                field_name = self.product_type_to_field.get(product_type)
                if field_name:
                    initial[field_name] = item.product.id
                discount_field = self.product_type_to_discount_field.get(product_type)
                if discount_source_field is None or not discount_field:
                    initial[discount_field] = None
                    continue
                # Set discount value based on
                # discount_amount or discount_frac field
                discount_value = getattr(item, discount_source_field)
                initial[discount_field] = _get_discount_display_value(
                    discount_value,
                    discount_type,
                )

            discounts = [DiscountType.NO_DISCOUNT]
            durations = [None]
            for item in offer_items:
                if item.discount_type != DiscountType.NO_DISCOUNT:
                    discounts.append(item.discount_type)
                    durations.append(item.discount_duration)

            if len(set(discounts)) > 2 or len(set(durations)) > 2:
                raise ValidationError('Offer items have mismatching discounts')
            initial['discount_duration'] = durations[-1]
            initial['discount_type'] = discounts[-1]
            kwargs['initial'] = initial

        super().__init__(*args, **kwargs)
        for field in self.readonly_products:
            self.fields[field].disabled = True

        if instance:
            for field in self.update_not_allowed_products:
                self.fields[field].disabled = True

    @staticmethod
    def _clean_discounts_on_offer_item(offer_item):
        offer_item.discount_type = DiscountType.NO_DISCOUNT
        offer_item.discount_amount = None
        offer_item.discount_frac = None
        offer_item.discount_duration = None

    @staticmethod
    def _get_discount_source_field(discount_type):
        """Add additional source field in BillingProductOfferItems
        where value should be set (discount_frac , discount_amount).
        """
        if discount_type == DiscountType.PERCENTAGE.value:
            return 'discount_frac'
        if discount_type == DiscountType.FIXED.value:
            return 'discount_amount'

    def clean_default(self):
        default = self.cleaned_data['default']

        if (
            default
            and BillingProductOffer.objects.filter(default=True)
            .exclude(pk=self.instance.pk)
            .exists()
        ):
            raise ValidationError(
                'There can be only one default offer at a time.',
            )
        return default

    def clean_active(self):
        active = self.cleaned_data['active']

        if not active:
            if BillingDiscountCode.objects.active().filter(offer_id=self.instance.pk).exists():
                raise ValidationError(
                    "You can't change status if offer is assigned to active Discount Code",
                )
            if BillingBusinessOffer.objects.active().filter(offer_id=self.instance.pk).exists():
                raise ValidationError(
                    "You can't change status if offer is assigned to active Business Offer",
                )
        return active

    def clean_active_for_b2b_referral(self):
        active_for_b2b_referral = self.cleaned_data['active_for_b2b_referral']
        if active_for_b2b_referral and BillingProductOffer.objects.filter(
                active_for_b2b_referral=True
        ).exclude(pk=self.instance.pk):
            raise ValidationError(
                'There can be only one active_for_b2b_referral offer at a time.',
            )
        return active_for_b2b_referral

    def clean(self):
        saas = self.cleaned_data.get('saas')
        if saas:
            self.cleaned_data['staff_add_on'] = saas.staff_add_on
            self.cleaned_data['sms_add_on'] = saas.sms_add_on

        if self.cleaned_data.get('discount_type') is None:
            self.cleaned_data['discount_type'] = DiscountType.NO_DISCOUNT
        discount_type = self.cleaned_data['discount_type']
        for product_type, product_field in self.product_type_to_field.items():
            field = self.product_type_to_discount_field.get(product_type)
            discount = self.cleaned_data.get(field)
            product = self.cleaned_data.get(product_field)
            if discount is None or product is None:
                continue

            discount_value = self._clean_discount(
                field,
                discount_type,
                discount,
                product,
            )
            if not self.has_error(field):
                self.cleaned_data[field] = discount_value

    def save(self, commit=True):
        with transaction.atomic():
            instance = super().save(commit=False)
            instance.save(
                _history={
                    'operator': getattr(self, 'operator', None),
                    'metadata': {'endpoint': 'ProductOfferForm'},
                },
            )
        # Check if any BillingProductOfferItem should be modified
        all_field_names = set(self.fields.keys())
        model_field_names = {'name', 'active'}
        package_item_field_names = all_field_names - model_field_names
        changed_fields = set(self.changed_data)
        changed_offer_field_names = changed_fields.intersection(package_item_field_names)
        discount_type = self.cleaned_data['discount_type']
        discount_duration = self.cleaned_data['discount_duration']
        discount_source_field = self._get_discount_source_field(discount_type)

        if changed_offer_field_names:
            for product_type, product_name in self.product_type_to_field.items():
                product = self.cleaned_data[product_name]
                if not product:
                    continue
                offer_item, _ = BillingProductOfferItem.objects.get_or_create(
                    product=product,
                    offer=instance,
                    defaults=dict(
                        offer=instance,
                        product=product,
                        active=True,
                    ),
                )
                offer_item.active = True
                # need to deactivate old offer item but make accessible
                # for already bought subscriptions
                instance.offer_items.filter(
                    product__product_type=product_type,
                    offer=instance,
                ).exclude(id=offer_item.id).update(active=False)
                self._clean_discounts_on_offer_item(offer_item)
                discount_field_name = self.product_type_to_discount_field.get(product_type)

                if discount_type != DiscountType.NO_DISCOUNT and discount_field_name:
                    self._clean_discounts_on_offer_item(offer_item)
                    if discount_value := self.cleaned_data.get(discount_field_name):
                        offer_item.discount_type = discount_type
                        offer_item.discount_duration = discount_duration
                        setattr(offer_item, discount_source_field, discount_value)

                offer_item.save(
                    _history={
                        'operator': getattr(self, 'operator', None),
                        'metadata': {'endpoint': 'ProductOfferForm'},
                    },
                )
        return instance


class BaseBillingSubscriptionForm(forms.ModelForm):
    class Meta:
        model = BillingSubscription
        fields = (
            'business',
            'offer',
            'subscription_start',
        )

    offer = forms.ModelChoiceField(queryset=BillingProductOffer.objects.all())
    subscription_start = forms.DateField(required=False)

    def clean_subscription_start(self):
        subscription_start = self.cleaned_data.get('subscription_start')

        if subscription_start and subscription_start <= tznow().date():
            raise ValidationError(
                'The start date must be in the future.',
            )
        return subscription_start

    def clean(self):
        super().clean()
        offer = self.cleaned_data.get('offer')
        business = self.cleaned_data.get('business')
        subscription_start = self.cleaned_data.get('subscription_start')

        if offer:
            valid_products = offer.offer_items.select_related('product').only(
                'product_id', 'product__product_type'
            )
            saas_id = next(
                (
                    x.product_id
                    for x in valid_products
                    if x.product.product_type == ProductType.SAAS
                ),
                None,
            )
            if not saas_id:
                raise forms.ValidationError(
                    'No SaaS assigned to offer, please fix offer configuration first'
                )

        if not business:
            return self.cleaned_data

        current_subscription = BillingSubscription.get_current_subscription(business_id=business.pk)
        future_subscriptions = BillingSubscription.get_future_subscriptions(business_id=business.pk)
        if future_subscriptions.exists():
            raise forms.ValidationError('Business has at least one pending subscription')
        if current_subscription and not subscription_start:
            raise forms.ValidationError(
                'The business already has an active subscription (ID: %(subscription_id)s)',
                params={'subscription_id': current_subscription.pk},
            )
        if current_subscription and subscription_start:
            is_long = current_subscription.is_long_subscription
            next_date = (
                current_subscription.date_end.date()
                if is_long
                else current_subscription.next_billing_date.date()
            )

            if subscription_start != next_date:
                if is_long:
                    error_msg = (
                        'The start date must be the same as the end date of the '
                        'current subscription, which is: %(next_billing_date)s'
                    )
                else:
                    error_msg = (
                        'The start date must be the same as the billing cycle end date '
                        'of the current subscription, which is: %(next_billing_date)s'
                    )

                raise forms.ValidationError(
                    error_msg,
                    params={'next_billing_date': next_date.isoformat()},
                )
        if CancellationReason.objects.filter(
            churn_done=False,
            deleted__isnull=True,
            business=business,
        ).exists():
            raise forms.ValidationError('The business has a pending CHURN request.')
        return self.cleaned_data


class BillingSubscriptionForm(BaseBillingSubscriptionForm):
    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request')
        super().__init__(*args, **kwargs)
        self.fields['business'].widget = ForeignKeyRawIdWidget(
            BillingSubscription._meta.get_field('business').remote_field,
            admin.site,
        )
        qs = self.fields['offer'].queryset.filter(active=True)

        if BillingUserPermission(self.request).is_billing_admin:
            qs = self.fields['offer'].queryset.filter(active=True)
        elif BillingUserPermission(self.request).is_billing_advanced_user:
            qs = qs.filter(active_for_advanced_user=True)
        else:
            qs = qs.filter(active_for_cs=True)

        self.fields['offer'].queryset = qs

    subscription_start = forms.DateField(
        required=False,
        help_text=(
            'Use this field for PENDING subscription only.'
            'If you provide a date in the future for the Merchant with '
            'active subscription, an expiry date will be set.'
        ),
        widget=AdminDateWidget(),
    )

    def clean_business(self):
        business = self.cleaned_data.get('business')
        if business and not business.has_new_billing:
            raise ValidationError(
                'Business is not migrated to new billing.',
            )
        return business

    def clean(self):
        super().clean()
        business = self.cleaned_data.get('business')
        if business:
            if not BillingPaymentMethod.objects.filter(
                business_id=business.pk, default=True
            ).exists():
                raise forms.ValidationError(
                    "There's no payment method assigned to this account. "
                    "Please, correct it in Billing Details Search tab"
                )
        return self.cleaned_data


class MigrateStripeTransactionForm(forms.Form):
    stripe_id = forms.CharField()

    def __init__(self, *args, **kwargs):
        self.business_id = kwargs['initial']['business_id']
        self.stripe_transaction = None
        super().__init__(*args, **kwargs)

    def clean_stripe_id(self):
        stripe_id = self.cleaned_data['stripe_id']
        result = PaymentIntentApi.get(stripe_id=stripe_id)
        if not result.is_success:
            raise ValidationError(result.error.message)
        if result.payment_intent.customer.booksy_id != ExternalCustomer.booksy_id(self.business_id):
            raise ValidationError('Current Business is not an owner of that transaction.')
        if result.payment_intent.subscriber != Subscriber.BILLING:
            raise ValidationError('You can migrate only transaction from billing')
        self.stripe_transaction = result.payment_intent

        if BillingTransaction.objects.filter(external_id=stripe_id).exists():
            raise ValidationError(f'Nothing to migrate, transaction {stripe_id} is in billing.')
        return stripe_id

    def save(self):
        BillingTransactionService.create_from_charge(
            business_id=self.business_id,
            transaction=self.stripe_transaction,
        )
        return self.business_id


class BaseBillingBusinessSwitchForm(forms.Form):
    business_id = forms.IntegerField()
    has_new_billing = forms.TypedChoiceField(
        widget=forms.Select(),
        choices=[('Yes', 'Yes'), ('No', 'No')],
        required=True,
        coerce=lambda x: x == 'Yes',
    )

    def __init__(self, *args, **kwargs):
        self.operator = kwargs.pop('operator', None)
        self.business = None
        super().__init__(*args, **kwargs)

    def clean_business_id(self):
        business_id = self.cleaned_data['business_id']
        try:
            self.business = Business.objects.get(id=business_id)
        except Business.DoesNotExist as err:
            raise ValidationError(
                'Business {business_id} does not exists',
                params={'field': 'business_id'},
            ) from err
        return business_id

    def clean(self):
        has_new_billing = self.cleaned_data.get('has_new_billing')

        if self.business:
            if self.business.has_new_billing == has_new_billing:
                raise ValidationError(
                    'Nothing to change, set proper flag value',
                    params={'field': 'has_new_billing'},
                )
            if (
                self.business.has_new_billing
                and BillingSubscription.objects.filter(
                    business_id=self.business.id,
                    date_expiry__isnull=True,
                ).exists()
                and not BillingUserPermission(self.operator).is_billing_superuser
            ):
                raise ValidationError(
                    'Merchant has active Billing subscription. '
                    'Make sure to cancel it before switching to other payment methods.',
                )
        return self.cleaned_data

    @transaction.atomic
    def save(self):
        old_data = BusinessChange.extract_vars(self.business)
        has_new_billing = self.cleaned_data['has_new_billing']
        payment_source = self.cleaned_data.get('payment_source')
        self.business.has_new_billing = has_new_billing
        if self.business.has_new_billing:
            self.business.payment_source = Business.PaymentSource.BRAINTREE_BILLING
        elif payment_source:
            self.business.payment_source = payment_source

        update_fields = (
            ['payment_source', 'has_new_billing']
            if (payment_source or self.business.has_new_billing)
            else ['has_new_billing']
        )

        self.business.save(update_fields=update_fields)
        BusinessChange.add(
            business=self.business,
            obj=self.business,
            old_obj_vars=old_data,
            metadata={'form': self.__class__.__name__},
            operator=self.operator,
        )
        return self.business


class BillingBusinessSwitchForm(BaseBillingBusinessSwitchForm):
    business_id = forms.IntegerField(disabled=True)


class BillingBusinessSwitchFormPaymentSourceTool(BaseBillingBusinessSwitchForm):
    payment_source = forms.TypedChoiceField(
        choices=Business.PaymentSource.choices(),
    )


class BillingBusinessSwitchFormWithPaymentSource(BillingBusinessSwitchForm):
    payment_source = forms.TypedChoiceField(
        widget=forms.Select(),
    )

    def __init__(self, *args, **kwargs):
        self.has_new_billing = kwargs['initial']['has_new_billing']

        super().__init__(*args, **kwargs)
        if not self.has_new_billing:
            self.fields['payment_source'].choices = [
                (
                    Business.PaymentSource.BRAINTREE_BILLING.value,
                    Business.PaymentSource.BRAINTREE_BILLING.label,
                )
            ]
            self.fields['payment_source'].disabled = True
            self.fields['payment_source'].initial = Business.PaymentSource.BRAINTREE_BILLING.value
        else:
            self.fields['payment_source'].choices = [
                payment_source
                for payment_source in Business.PaymentSource.choices()
                if payment_source[0] != Business.PaymentSource.BRAINTREE_BILLING.value
            ]
            self.fields['payment_source'].initial = Business.PaymentSource.UNKNOWN.value


class BillingBusinessDiscountForm(forms.ModelForm):
    class Meta:
        model = BillingBusinessDiscount
        fields = (
            'business',
            'product_type',
            'discount_type',
            'duration',
            'discount_value',
            'reason',
            'comment',
        )  # pylint: disable=duplicate-code

    discount_value = forms.DecimalField(
        decimal_places=2,
        min_value=0,
        label='Discount value',
        help_text=(
            'If you choose "Staffer SaaS" keep in mind that the discount will be applied for '
            'each payable Staff Member separately!'
        ),
    )

    def __init__(self, *args, **kwargs):
        # Editing is not available
        super().__init__(*args, **kwargs)

        self.subscription = None

        discount_type = self.data.get(
            'discount_type', kwargs.get('initial', {}).get('discount_type')
        )
        reason = self.data.get('reason', kwargs.get('initial', {}).get('reason'))

        if discount_type == DiscountType.PERCENTAGE:
            self.fields['discount_value'] = forms.DecimalField(
                max_digits=4,
                decimal_places=1,
                validators=[
                    MinValueValidator(Decimal('0.1')),
                    MaxValueValidator(100),
                ],
                label='Discount value (%, e.g. 10.5 => 10.5%)',
            )
        elif discount_type == DiscountType.FIXED:
            self.fields['discount_value'] = forms.DecimalField(
                max_digits=consts.PRICE_MAX_DIGITS,
                decimal_places=consts.PRICE_MAX_DECIMALS,
                validators=[MinValueValidator(Decimal('0.01'))],
                label='Discount value (fixed price, e.g.: 20)',
            )
        if reason == DiscountReason.OTHER:
            self.fields['comment'].required = True

    def clean_business(self):
        business = self.cleaned_data.get('business')

        if business:
            if not business.has_new_billing:
                raise ValidationError('Only businesses with a new billing are allowed.')
            if business.status != Business.Status.PAID:
                raise ValidationError('Only PAID businesses are allowed.')
        return business

    def clean(self):
        cleaned_data = super().clean()

        business = cleaned_data.get('business')

        if business:
            self.subscription = BillingSubscription.get_current_subscription(
                business_id=business.pk
            )

            if not self.subscription or self.subscription.status != SubscriptionStatus.ACTIVE:
                raise ValidationError({'business': 'Business has no active subscription.'})
            if self.subscription.next_cycle_start <= tznow() or self.subscription.date_expiry:
                raise ValidationError(
                    {
                        'business': (
                            f'Subscription is not properly configured. Next '
                            f'cycle start: {self.subscription.next_cycle_start}, '
                            f'Date expiry: {self.subscription.date_expiry}.'
                        )
                    }
                )
        return cleaned_data

    def save(self, commit=True):
        item = super().save(commit=False)

        if item.discount_type == DiscountType.PERCENTAGE:
            item.amount = None
            item.frac = self.cleaned_data['discount_value'] / Decimal('100.0')
        elif item.discount_type == DiscountType.FIXED:
            item.frac = None
            item.amount = self.cleaned_data['discount_value']

        item.subscription = self.subscription
        item.date_start = self.subscription.next_cycle_start
        item.date_end = relativedelta_by_step(
            start=item.date_start,
            delta=self.subscription.payment_period,
            steps=item.duration,
        )
        if commit:
            item.save(
                _history={
                    'operator': getattr(self, 'operator', None),
                    'metadata': {'endpoint': 'BillingBusinessDiscountForm'},
                }
            )
        return item


class BillingDiscountCodeForm(DiscountFormMixin, forms.ModelForm):
    class Meta:
        model = BillingDiscountCode

        fields = (
            # region Common
            'discount_code',
            'name',
            'for_new_providers',
            'for_existing_providers',
            'valid_from',
            'valid_to',
            'max_usage_limit',
            'active',
            # endregion
            'offer',  # new providers --> BusinessOffer
            # region existing providers --> BillingBusinessDiscount
            'discount_type',
            'discount_duration',
            'discount_value_saas',
            'discount_value_staffer_saas',
            # endregion
        )

    # we cannot edit discount-related fields once created
    update_allowed_fields = (
        'active',
        'name',
        'valid_from',
        'valid_to',
        'max_usage_limit',
    )

    existing_providers_fields = (
        'discount_type',
        'discount_duration',
        'discount_value_saas',
        'discount_value_staffer_saas',
    )

    discount_code = forms.CharField(
        min_length=4,
        max_length=20,
        required=True,
        help_text='Case insensitive discount code. 4-20 characters, have to be unique.'
        'If empty, autogenereted code will be proposed.',
    )

    for_new_providers = forms.BooleanField(
        help_text='Discount code should be valid for new providers',
        required=False,
        initial=False,
    )
    for_existing_providers = forms.BooleanField(
        help_text='Discount code should be valid for existing providers',
        required=False,
        initial=False,
    )

    # region new providers
    offer = forms.ModelChoiceField(
        # active offer is default one for country so we don't need discount code for that one
        queryset=BillingProductOffer.objects.filter(default=False, active=True),
        required=False,
        initial=None,
        help_text=(
            'Discount for new providers is configurable inside the offer only! '
            'Check current list of offers.'
        ),
    )

    # region existing providers
    discount_type = forms.ChoiceField(
        choices=DiscountType.choices(),
        required=False,
        initial=None,
    )

    discount_duration = forms.ChoiceField(
        required=False,
        choices=DiscountDuration.finite_choices(),
        label='Discount duration (# of billing cycles)',
    )

    discount_value_saas = forms.DecimalField(
        decimal_places=2,
        min_value=0,
        label='Discount value saas',
        required=False,
    )

    discount_value_staffer_saas = forms.DecimalField(
        decimal_places=2,
        min_value=0,
        label='Discount value Staffer SaaS',
        required=False,
        help_text=(
            'keep in mind that the discount will be applied for '
            'each payable Staff Member separately!'
        ),
    )

    # endregion

    def __init__(self, *args, **kwargs):
        initial = kwargs.get('initial', {})
        instance = kwargs.get('instance')
        if instance:
            if instance.discount_type:
                initial['discount_value_saas'] = _get_discount_display_value(
                    instance.discount_value_saas, instance.discount_type
                )
                initial['discount_value_staffer_saas'] = _get_discount_display_value(
                    instance.discount_value_staffer_saas, instance.discount_type
                )

        else:
            if not (args and args[0].get('discount_code')):
                initial['discount_code'] = initial.get(
                    'discount_code', self.generate_discount_code()
                ).upper()
        kwargs['initial'] = initial
        super().__init__(*args, **kwargs)

        discount_type = self.initial.get('discount_type')
        align_discount_field(self.fields['discount_value_saas'], discount_type)
        align_discount_field(self.fields['discount_value_staffer_saas'], discount_type)
        if instance:
            for field in set(self.fields).difference(set(self.update_allowed_fields)):
                self.fields[field].disabled = True

    @staticmethod
    def generate_discount_code():
        return ''.join(chr(x) for x in random.sample(range(65, 91), 6)) + f'{random.randint(10,99)}'

    def clean_discount_type(self):
        # we want to coerce blank to null value
        return self.cleaned_data.get('discount_type') or None

    def clean_discount_code(self):
        discount_code = self.cleaned_data.get('discount_code', '')
        return discount_code.upper()

    def clean_valid_to(self):
        valid_to = self.cleaned_data.get('valid_to')

        if valid_to and 'valid_to' in self.changed_data and valid_to <= tznow():
            raise ValidationError(
                'The valid till date have to be in the future.',
            )
        return valid_to

    def check_uniqueness(self, cleaned_data):
        active = cleaned_data.get('active')
        discount_code = cleaned_data.get('discount_code')
        if active:
            existing_code = BillingDiscountCode.objects.filter(
                discount_code=discount_code,
                active=True,
            ).first()
            if existing_code and existing_code != self.instance:
                self.add_error(
                    'discount_code',
                    'Same active discount code already exists! Choose different code.',
                )

    def clean(self):
        cleaned_data = super().clean()

        self.check_uniqueness(cleaned_data)

        discount_type = cleaned_data.get('discount_type')
        new_providers = cleaned_data.get('for_new_providers')
        existing_providers = cleaned_data.get('for_existing_providers')
        if not (new_providers or existing_providers):
            self.add_error(
                '__all__',
                'No target group to apply discounts (please choose at least '
                'one from new and existing providers checkbox',
            )

        offer = cleaned_data.get('offer')

        if new_providers and not offer:
            self.add_error('offer', 'Need to select active offer for new providers discount code')
        elif not new_providers and offer:
            self.add_error(
                'offer', 'Offer can only be selected if discount code is for new providers'
            )

        if existing_providers:
            discount_value_saas = cleaned_data.get('discount_value_saas', 0) or 0
            discount_value_staffer_saas = cleaned_data.get('discount_value_staffer_saas', 0) or 0
            if not discount_type:
                self.add_error(
                    'discount_type',
                    'Need to choose Discount type if discount code'
                    ' is applicable for existing providers',
                )

            if not (discount_value_saas or discount_value_staffer_saas):
                text = (
                    'Need to pass at least one of discount_value_saas / '
                    'discount_value_staffer_saas'
                )
                self.add_error('discount_value_saas', text)
                self.add_error('discount_value_staffer_saas', text)

            discount_value_saas = self._clean_discount(
                'discount_value_saas',
                discount_type,
                discount_value_saas,
            )
            discount_value_staffer_saas = self._clean_discount(
                'discount_value_staffer_saas',
                discount_type,
                discount_value_staffer_saas,
            )

            if not (
                self.has_error('discount_value_saas')
                or self.has_error('discount_value_staffer_saas')
            ):
                if discount_type == DiscountType.FIXED:
                    cleaned_data['amount_saas'] = discount_value_saas
                    cleaned_data['amount_staffer_saas'] = discount_value_staffer_saas

                    cleaned_data['frac_saas'] = None
                    cleaned_data['frac_staffer_saas'] = None
                elif discount_type == DiscountType.PERCENTAGE:
                    cleaned_data['frac_saas'] = discount_value_saas
                    cleaned_data['frac_staffer_saas'] = discount_value_staffer_saas

                    cleaned_data['amount_saas'] = None
                    cleaned_data['amount_staffer_saas'] = None
        else:
            # The value of the discount may change in the future,
            # so we need to save its current value.
            # It's needed to display the offer discount info.
            if offer and (
                saas := offer.offer_items.filter(
                    product__product_type=ProductType.SAAS,
                    active=True,
                ).first()
            ):
                cleaned_data['discount_duration'] = saas.discount_duration
            else:
                cleaned_data['discount_duration'] = None

            cleaned_data['discount_type'] = None
            cleaned_data['amount_saas'] = None
            cleaned_data['amount_staffer_saas'] = None
            cleaned_data['frac_saas'] = None
            cleaned_data['frac_staffer_saas'] = None

        return cleaned_data

    def save(self, commit=True):
        discount_code = super().save(commit=False)

        discount_code.frac_saas = self.cleaned_data.get('frac_saas')
        discount_code.frac_staffer_saas = self.cleaned_data.get('frac_staffer_saas')
        discount_code.amount_saas = self.cleaned_data.get('amount_saas')
        discount_code.amount_staffer_saas = self.cleaned_data.get('amount_staffer_saas')
        if commit:
            discount_code.save(
                _history={
                    'operator': getattr(self, 'operator', None),
                    'metadata': {'endpoint': 'BillingDiscountCodeForm'},
                }
            )
        return discount_code


class BillingSubscriptionCancellationForm(forms.Form):
    cancel_immediately = forms.BooleanField(
        label="Cancel with today's date",
        required=False,
        help_text=mark_safe(
            """Attention! Has to be used only in case if there's in error made by CS in current billing cycle!
            <br><b style="color:red;">If you create another one subscription for this business (pending) don't
             cancel this one! <br>It will be cancelled automatically when pending sub is created.</b>"""
        ),
    )


class BillingOneOffChargeProductForm(forms.ModelForm):

    CURRENCY_CHOICES = ((i, i) for i in settings.BILLING_CURRENCIES)

    currency = forms.ChoiceField(
        initial=settings.CURRENCY_CODE,
        choices=CURRENCY_CHOICES,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            for field in self.fields:
                if field != 'active':
                    self.fields[field].disabled = True

    class Meta:
        model = BillingOneOffChargeProduct

        fields = (
            'name',
            'product_type',
            'currency',
            'suggested_net_price',
            'active',
        )


class BillingOneOffTransactionForm(forms.ModelForm):
    class Meta:
        model = BillingOneOffCharge

        fields = ('business',)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.billing_cycle_id = None

    def clean_business(self):
        business = self.cleaned_data.get('business')

        if not business.has_new_billing:
            raise ValidationError('Only businesses with new billing allowed.')

        if business.status not in Business.Status.paid_statuses():
            raise ValidationError('Business has wrong status, only paid allowed.')

        if (
            get_business_payment_processor(business_id=business.id)
            != PaymentProcessorType.STRIPE.value
        ):
            raise ValidationError('Only businesses with Stripe allowed.')

        if not BillingPaymentMethod.objects.filter(
            business_id=business.pk,
            default=True,
            payment_processor=PaymentProcessorType.STRIPE,
        ).exists():
            raise ValidationError('Business does not have assigned payment method.')

        if not (
            billing_subscription := BillingSubscription.objects.active_subscriptions()
            .filter(
                business_id=business.pk,
            )
            .first()
        ):
            raise ValidationError('There is no active subscription assigned to this account.')

        self.billing_cycle_id = billing_subscription.latest_cycle.id

        return business


class BillingOneOffChargeForm(BillingOneOffTransactionForm):
    class Meta:
        model = BillingOneOffCharge

        fields = (
            'business',
            'product',
            'unit_net_price',
            'quantity',
            'comment',
        )
        help_texts = {
            'unit_net_price': 'If empty, the suggested net price from the product will be charged.',
            'quantity': 'Change if you want charge more than one product in one transaction.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if not kwargs.get('instance'):
            self.fields['business'].widget = ForeignKeyRawIdWidget(
                BillingOneOffCharge._meta.get_field('business').remote_field,
                admin.site,
            )
            self.fields['unit_net_price'].required = False
            self.fields['quantity'].required = True
            self.fields['product'].queryset = BillingOneOffChargeProduct.objects.filter(active=True)

    def save(self, commit=True):
        one_off_charge = super().save(commit=False)
        if not self.cleaned_data.get('unit_net_price'):
            one_off_charge.unit_net_price = self.cleaned_data.get('product').suggested_net_price
        one_off_charge.currency = self.cleaned_data.get('product').currency
        one_off_charge.calculate_tax_related_data(save=False)
        if commit:
            one_off_charge.save()
        return one_off_charge


class BillingLongSubMapperForm(forms.ModelForm):
    class Meta:
        model = BillingLongSubMapper
        fields = (
            'name',
            'offer',
            'subscription_duration',
            'free_staff_qty',
        )
        error_messages = {
            NON_FIELD_ERRORS: {
                'unique_together': (
                    'Long Subscription Offer Mapping with this Subscription '
                    'duration and Free Staff Members Count already exists.'
                )
            }
        }

    offer = forms.ModelChoiceField(
        queryset=BillingProductOffer.objects.filter(
            active=True,
            subscription_duration__in=BillingLongSubscriptionDuration.values(),
        ),
        required=True,
        initial=None,
    )
    free_staff_qty = forms.ChoiceField(
        choices=BillingLongSubStaffQuantity.choices(),
        required=True,
        initial=None,
        label='Free staff members count (incl. owner)',
    )

    def clean_free_staff_qty(self):
        offer = self.cleaned_data.get('offer')
        free_staff_qty = int(self.cleaned_data.get('free_staff_qty'))
        staffer_sass = (
            offer.offer_items.filter(product__product_type=ProductType.STAFFER_SAAS)
            .select_related('product')
            .first()
        )
        if (
            staffer_sass
            and staffer_sass.product.free_staff_qty is not None
            and staffer_sass.product.free_staff_qty != free_staff_qty
        ):
            raise ValidationError(
                (
                    'Invalid Free Staff Members Count %(free_staff_qty)s. '
                    'Product Offer Free Staff Members Count must match.'
                ),
                code='invalid',
                params={'free_staff_qty': free_staff_qty},
            )

        return free_staff_qty

    def clean_subscription_duration(self):
        offer = self.cleaned_data.get('offer')
        subscription_duration = self.cleaned_data.get('subscription_duration')
        if offer.subscription_duration != subscription_duration:
            raise ValidationError(
                (
                    'Invalid subscription_duration %(duration)s. '
                    'Product Offer subscription duration must match.'
                ),
                code='invalid',
                params={'duration': subscription_duration},
            )

        return subscription_duration


class BillingCountryConfigForm(forms.ModelForm):
    class Meta:
        model = BillingCountryConfig
        fields = (
            'country',
            'long_subscription_show_discount',
            'long_subscription_3m_precentage_discount',
            'long_subscription_6m_precentage_discount',
            'long_subscription_12m_precentage_discount',
            'stripe_migration_date',
        )

    def clean(self):
        super().clean()
        discount_show = self.cleaned_data.get('long_subscription_show_discount')
        discount_3m = self.cleaned_data.get('long_subscription_3m_precentage_discount')
        discount_6m = self.cleaned_data.get('long_subscription_6m_precentage_discount')
        discount_12m = self.cleaned_data.get('long_subscription_12m_precentage_discount')

        if (
            discount_show
            and None in [discount_3m, discount_6m, discount_12m]
            and any([discount_3m, discount_6m, discount_12m])
        ):
            raise ValidationError(
                'All perentage discounts settings should be filled either by value or all keept empty'
            )

        return self.cleaned_data
