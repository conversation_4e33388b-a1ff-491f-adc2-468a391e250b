from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class ElasticsearchDeleteByIdsFlag(BooleanFlag):
    flag_name = 'Experiment_ElasticsearchDeleteByIds'
    adapter = FeatureFlagAdapter.LD


class ElasticsearchDeleteWithSlicesFlag(BooleanFlag):
    flag_name = 'Experiment_ElasticsearchDeleteWithSlices'
    adapter = FeatureFlagAdapter.LD


class ElasticsearchDontWaitForDeleteCompletionFlag(BooleanFlag):
    flag_name = 'Experiment_ElasticsearchDontWaitForDeleteCompletion'
    adapter = FeatureFlagAdapter.LD


class ElasticsearchWithoutDeleteByQueryFlag(BooleanFlag):
    flag_name = 'Experiment_ElasticsearchWithoutDeleteByQuery'
    adapter = FeatureFlagAdapter.LD


class MarketplaceCommissionQuerysetOptimizationFlag(BooleanFlag):
    flag_name = 'Experiment_MarketplaceCommissionQuerysetOptimization'
    adapter = FeatureFlagAdapter.LD


class NavisionTargetAPIProductionFlag(BooleanFlag):
    flag_name = 'Experiment_NavisionTargetAPIProduction'
    adapter = FeatureFlagAdapter.EPPO
