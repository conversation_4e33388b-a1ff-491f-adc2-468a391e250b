from decimal import Decimal
from unittest import TestCase
from unittest.mock import call
from datetime import date, datetime

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.test import override_settings, TestCase as DjangoTestCase
from freezegun import freeze_time
from mock import patch
from mock import MagicMock
from model_bakery import baker

from lib.booksy_sms import SMSCosts
from lib.enums import SMSTypeEnum
from lib.feature_flag.feature import SetSMSLimitForActivePeriodFlag
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import (
    UTC,
    convert_date_str_to_aware_date,
    convert_date_str_to_aware_dt,
)
from webapps.billing.enums import ProductType, SubscriptionStatus
from webapps.billing.models import (
    BillingCycle,
    BillingProduct,
    BillingSubscribedProduct,
    BillingSubscription,
)
from webapps.billing.tests import gen_func
from webapps.business.models import Business
from webapps.notification.models import (
    NotificationSMSStatistics,
)
from webapps.purchase.cache import _get_sms_allowance
from webapps.purchase.models import (
    Subscription,
    SubscriptionListing,
    SubscriptionSMSPackage,
)


baker.generators.add('lib.interval.fields.IntervalField', gen_func)

# pylint: disable=protected-access,unbalanced-tuple-unpacking,line-too-long


@pytest.mark.freeze_time('2019-02-26')
@pytest.mark.django_db
class SMSStatsTest(TestCase):

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=123,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=123456,
        BILLING_SMS_HARD_LIMIT=999,
    )
    @patch('webapps.notification.models.get_non_trial_sms_limits')
    def test_limits(self, get_non_trial_sms_limits_mock):
        business = baker.make(
            Business,
            has_new_billing=False,
            sms_limit=333,
            sms_limit_history={'2019-01': 20, '2019-02': 333},
        )
        business_new_billing = baker.make(
            Business,
            has_new_billing=True,
        )
        get_non_trial_sms_limits_mock.return_value = {
            'config': {
                SMSTypeEnum.INVITATION: 6,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: None,
            },
            'prepaid_sms_count': 77,
        }
        period_trial = NotificationSMSStatistics._trial_label()
        self.assertEqual(
            NotificationSMSStatistics._limit_free_from_settings(period_trial),
            123,
        )
        period_paid = 'paid period'
        self.assertEqual(
            NotificationSMSStatistics._limit_free_from_settings(period_paid),
            123456,
        )
        self.assertEqual(NotificationSMSStatistics._limit_free_paid_business(business), 77)
        self.assertEqual(
            NotificationSMSStatistics.get_postpaid_sms_limit(business),
            333,
        )
        self.assertEqual(
            NotificationSMSStatistics.get_postpaid_sms_limit(business_new_billing),
            999,
        )

    def test_blocked_overdue(self):
        # input data data
        business = baker.make(Business, status=Business.Status.BLOCKED_OVERDUE)
        # run function
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = 'expired', 0, 0, 0
        assert result == expected

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=11,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=33,
        SMS_LIMITS_IN_SUBSCRIPTION=False,
        SMS_COST_PER_COUNTRY={'us': (0.01, 'USD')},
    )
    def test_in_paid_period_free_limit_from_settings(self):
        # input data
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            sms_limit=40,
            sms_limit_history={'2019-01': 20, '2019-02': 40},
        )
        sub_start_date = convert_date_str_to_aware_dt('2019-01-07', '%Y-%m-%d')
        baker.make(
            Subscription,
            business=business,
            product=baker.make(SubscriptionListing),
            start=sub_start_date,
        )
        baker.make(  # trial period
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=7,
            sms_count=3,
            date=datetime.min.date(),
        )
        baker.make(
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=8,
            sms_count=3,
            date=convert_date_str_to_aware_date('2019-02-03', '%Y-%m-%d'),
        )
        baker.make(
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=15,
            sms_count=3,
            date=convert_date_str_to_aware_date('2019-02-07', '%Y-%m-%d'),
        )

        # run function
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = (
            'paid',
            15,
            33,
            40,
        )
        assert result == expected

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
    )
    def test_in_paid_period_free_limit_from_subscription(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            sms_limit=40,
            sms_limit_history={'2019-01': 20, '2019-02': 40},
        )
        sub_start_date = convert_date_str_to_aware_dt('2019-01-07', '%Y-%m-%d')
        product = baker.make(SubscriptionListing)
        subscription = baker.make(
            Subscription,
            business=business,
            start=sub_start_date,
            product=product,
        )
        baker.make(
            SubscriptionSMSPackage,
            subscription=subscription,
            date_start=sub_start_date,
            amount_per_billing_cycle=255,
        )

        baker.make(  # trial period
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=11,
            sms_count=3,
            date=datetime.min.date(),
        )
        baker.make(  # previous BC
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=13,
            sms_count=3,
            date=convert_date_str_to_aware_date('2019-01-15', '%Y-%m-%d'),
        )
        baker.make(  # previous BC
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=11,
            sms_count=3,
            date=convert_date_str_to_aware_date('2019-02-03', '%Y-%m-%d'),
        )
        baker.make(  # current BC
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=7,
            sms_count=3,
            date=convert_date_str_to_aware_date('2019-02-07', '%Y-%m-%d'),
        )

        baker.make(  # current BC
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=9,
            sms_count=3,
            date=convert_date_str_to_aware_date('2019-02-18', '%Y-%m-%d'),
        )
        baker.make(  # current BC, today
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=3,
            sms_count=3,
            date=convert_date_str_to_aware_date('2019-02-26', '%Y-%m-%d'),
        )
        # run function
        _get_sms_allowance.cache_clear()
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = (
            'paid',
            19,
            255,
            40,
        )
        assert result == expected

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=11,
        SMS_COST_PER_COUNTRY={'us': (0.01, 'USD')},
    )
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def test_before_paid_period(self):
        business = baker.make(
            Business,
            status=Business.Status.TRIAL,
            sms_limit=40,
            sms_limit_history={'2019-01': 20, '2019-02': 40},
        )
        sub_start_date = convert_date_str_to_aware_dt('2019-03-08', '%Y-%m-%d')
        baker.make('purchase.Subscription', business=business, start=sub_start_date)
        baker.make(
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=7,
            sms_count=3,
            date=datetime.min.date(),
        )
        # run function
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = (
            NotificationSMSStatistics._trial_status(),
            7,
            11,
            0,
        )
        assert result == expected

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=11,
    )
    def test_no_sub(self):
        # input data
        business = baker.make(
            Business,
            status=Business.Status.TRIAL,
            sms_limit=40,
            sms_limit_history={'2019-01': 20, '2019-02': 40},
        )

        baker.make(
            NotificationSMSStatistics,
            business_id=business.id,
            parts_count=7,
            sms_count=3,
            date=datetime.min.date(),
        )
        # run function
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = (NotificationSMSStatistics._trial_status(), 7, 11, 0)
        assert result == expected

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=11,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=33,
        SMS_COST_PER_COUNTRY={'us': (0.01, 'USD')},
    )
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def test_empty_statistics(self):
        # first sub-case, already in paid period
        # input data
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            sms_limit=40,
            sms_limit_history={'2019-01': 20, '2019-02': 40},
        )
        sub_start_date = convert_date_str_to_aware_dt('2018-01-16', '%Y-%m-%d')
        baker.make(
            Subscription,
            business=business,
            product=baker.make(SubscriptionListing),
            start=sub_start_date,
        )
        # run function
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = (
            'paid',
            0,
            33,
            40,
        )
        assert result == expected

        # second sub-case, business before paid period
        # input data
        business = baker.make(
            Business,
            status=Business.Status.TRIAL,
            sms_limit=40,
            sms_limit_history={'2019-01': 20, '2019-02': 40},
        )
        sub_start_date = convert_date_str_to_aware_dt('2019-03-01', '%Y-%m-%d')
        baker.make('purchase.Subscription', business=business, start=sub_start_date)
        # run function
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = (
            NotificationSMSStatistics._trial_status(),
            0,
            11,
            0,
        )
        assert result == expected

        # third sub-case, no subscription
        # input data
        business = baker.make(Business, status=Business.Status.TRIAL, sms_limit=40)
        # run function
        result = NotificationSMSStatistics.sms_stats(business)
        # positive test
        expected = (
            NotificationSMSStatistics._trial_status(),
            0,
            11,
            0,
        )
        assert result == expected

    @override_settings(SMS_DEMO_ACCOUNT_PREPAID_COUNT=0)
    def test_sms_stats_by_country__trial(self):
        business = baker.make(Business, status=Business.Status.TRIAL)
        result = NotificationSMSStatistics.sms_stats(business)
        # status
        self.assertEqual(result[0], 'demo')
        # parts count
        self.assertEqual(result[1], 0)
        # limit free
        self.assertEqual(result[2], 0)
        # limit paid
        self.assertEqual(result[3], 0)

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=0,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=0,
    )
    def test_sms_stats_by_country__paid(self):
        business = baker.make(Business, status=Business.Status.PAID)
        baker.make(
            Subscription,
            business=business,
            product=baker.make(SubscriptionListing),
        )
        result = NotificationSMSStatistics.sms_stats(business)
        # status
        self.assertEqual(result[0], 'paid')
        # parts count
        self.assertEqual(result[1], 0)
        # limit free
        self.assertEqual(result[2], 0)
        # limit paid
        self.assertEqual(result[3], 0)

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=0,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=0,
        SMS_LIMITS_IN_SUBSCRIPTION=True,
    )
    def test_sms_stats_by_country__limit_in_sub(self):
        business_1 = baker.make(Business, status=Business.Status.PAID, has_new_billing=True)
        baker.make(
            Subscription,
            business=business_1,
            product=baker.make(SubscriptionListing),
        )
        result = NotificationSMSStatistics.sms_stats(business_1)
        # parts count
        self.assertEqual(result[1], 0)
        # limit free
        self.assertEqual(result[2], 0)
        # limit paid
        self.assertEqual(result[3], 0)

        business_2 = baker.make(Business, status=Business.Status.PAID, has_new_billing=True)
        baker.make(
            Subscription,
            business=business_2,
            product=baker.make(SubscriptionListing),
        )
        result = NotificationSMSStatistics.sms_stats(business_2)
        # parts count
        self.assertEqual(result[1], 0)
        # limit free
        self.assertEqual(result[2], 0)
        # limit paid
        self.assertEqual(result[3], 0)

    @override_settings(
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=0,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=0,
    )
    def test_limits_free_from_settings(self):
        # demo
        trial_label = 'demo_account'
        result = NotificationSMSStatistics._limit_free_from_settings(trial_label)
        self.assertEqual(result, 0)
        # paid
        paid_label = '2020-02'
        result = NotificationSMSStatistics._limit_free_from_settings(paid_label)
        self.assertEqual(result, 0)

    @override_settings(BILLING_SMS_HARD_LIMIT=0)
    def test_get_postpaid_sms_limit(self):
        business = baker.make(Business, has_new_billing=True)
        result_1 = NotificationSMSStatistics.get_postpaid_sms_limit(business)
        self.assertEqual(result_1, 0)

        business.has_new_billing = False
        business.save()
        result_2 = NotificationSMSStatistics.get_postpaid_sms_limit(business)
        self.assertEqual(result_2, 0)


@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
class SMSOtherFunctionsTest(TestCase):

    @pytest.mark.freeze_time('2019-02-14')
    @pytest.mark.django_db
    def test_get_sms_summary_entry(self):
        # input data
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            sms_limit=40,
            sms_limit_history={'2019-01': 20, '2019-02': 40},
        )
        sub_start_date = convert_date_str_to_aware_dt('2019-01-15', '%Y-%m-%d')
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make('purchase.Subscription', business=business, start=sub_start_date)
        zero_sms_cost_format = SMSCosts.sms_cost_format(0)
        trial = NotificationSMSStatistics.get_summary_entry(
            business, NotificationSMSStatistics._trial_label(), 0
        )
        paid_1 = NotificationSMSStatistics.get_summary_entry(business, '2019-01', 0)
        paid_2 = NotificationSMSStatistics.get_summary_entry(business, '2019-02', 0)
        # positive tests
        self.assertDictEqual(
            paid_2,
            {
                'period': '2019-02',
                'limit_free': settings.SMS_PAID_ACCOUNT_PREPAID_COUNT,
                'limit_paid': 40,
                'count_free': 0,
                'count_paid': 0,
                'payable_cost': 0.0,
                'payable_cost_formatted': zero_sms_cost_format,
                'parts_count': 0,
                'current_limit_free': settings.SMS_PAID_ACCOUNT_PREPAID_COUNT,
                'current_sms_price': SMSCosts.get_part_price_and_currency()[0],
            },
        )
        self.assertDictEqual(
            paid_1,
            {
                'period': '2019-01',
                'limit_free': settings.SMS_PAID_ACCOUNT_PREPAID_COUNT,
                'limit_paid': 20,
                'count_free': 0,
                'count_paid': 0,
                'payable_cost': 0.0,
                'payable_cost_formatted': zero_sms_cost_format,
                'parts_count': 0,
                'current_limit_free': settings.SMS_PAID_ACCOUNT_PREPAID_COUNT,
                'current_sms_price': SMSCosts.get_part_price_and_currency()[0],
            },
        )
        self.assertDictEqual(
            trial,
            {
                'period': NotificationSMSStatistics._trial_label(),
                'limit_free': settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT,
                'limit_paid': 0,
                'count_free': 0,
                'count_paid': 0,
                'payable_cost': 0.0,
                'payable_cost_formatted': zero_sms_cost_format,
                'parts_count': 0,
                'current_limit_free': settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT,
                'current_sms_price': SMSCosts.get_part_price_and_currency()[0],
            },
        )

    def test_compute_free_and_paid_sms_parts(self):
        # first case; under the limit
        # input data
        parts_count = 3
        free_limit = 5
        # run function
        free_parts_count, paid_parts_count = (
            NotificationSMSStatistics._compute_free_and_paid_sms_parts(parts_count, free_limit)
        )
        # positive test
        assert free_parts_count == 3
        assert paid_parts_count == 0

        # second case; equals the limit
        # input data
        parts_count = 5
        free_limit = 5
        # run function
        free_parts_count, paid_parts_count = (
            NotificationSMSStatistics._compute_free_and_paid_sms_parts(parts_count, free_limit)
        )
        # positive test
        assert free_parts_count == 5
        assert paid_parts_count == 0

        # third case; over the limit
        # input data
        parts_count = 24
        free_limit = 5
        # run function
        free_parts_count, paid_parts_count = (
            NotificationSMSStatistics._compute_free_and_paid_sms_parts(parts_count, free_limit)
        )
        # positive test
        assert free_parts_count == 5
        assert paid_parts_count == 19

        # corner case; zero limit
        # input data
        parts_count = 24
        free_limit = 0
        # run function
        free_parts_count, paid_parts_count = (
            NotificationSMSStatistics._compute_free_and_paid_sms_parts(parts_count, free_limit)
        )
        # positive test
        assert free_parts_count == 0
        assert paid_parts_count == 24

        # corner case; zero parts
        # input data
        parts_count = 0
        free_limit = 10
        # run function
        free_parts_count, paid_parts_count = (
            NotificationSMSStatistics._compute_free_and_paid_sms_parts(parts_count, free_limit)
        )
        # positive test
        assert free_parts_count == 0
        assert paid_parts_count == 0

    def test_sms_cost(self):
        self.assertEqual(SMSCosts.get_part_price_and_currency(), (0.005, 'USD'))
        self.assertEqual(SMSCosts.sms_cost_format(1), '$0.01')
        self.assertEqual(SMSCosts.sms_cost_format(6), '$0.03')
        self.assertEqual(SMSCosts.sms_cost_format(7), '$0.04')
        self.assertEqual(SMSCosts.sms_cost_calculate(1), Decimal('0.01'))
        self.assertEqual(SMSCosts.sms_cost_calculate(6), Decimal('0.03'))
        self.assertEqual(SMSCosts.sms_cost_calculate(7), Decimal('0.04'))
        self.assertEqual(SMSCosts.format_sms_cost(1.6), '$1.60')


def add_stats_paid_for_business(business, period_str, parts_count, sms_count=3):

    baker.make(
        NotificationSMSStatistics,
        business_id=business.id,
        parts_count=parts_count,
        sms_count=sms_count,
        date=convert_date_str_to_aware_date(period_str, '%Y-%m-%d'),
    )


def add_stats_trial_for_business(business, parts_count, sms_count=3):
    baker.make(
        NotificationSMSStatistics,
        business_id=business.id,
        parts_count=parts_count,
        sms_count=sms_count,
        date=datetime.min.date(),
    )


class SMSStatsMixin(TestCase):
    def assert_trial_period(self, demo_period, parts_count):
        self.assertEqual(demo_period['period'], NotificationSMSStatistics._trial_label())
        self.assertEqual(demo_period['limit_free'], settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT)
        self.assertEqual(demo_period['limit_paid'], 0)
        self.assertEqual(
            demo_period['count_free'], min(parts_count, settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT)
        )
        self.assertEqual(
            demo_period['count_paid'], max(0, parts_count - settings.SMS_DEMO_ACCOUNT_PREPAID_COUNT)
        )

    def assert_paid_period(
        self,
        period_stats,
        period_str,
        limit_free,
        count_free,
        count_paid,
        current_sms_price,
        current_limit_free=0,
        limit_paid=0,
        payable_cost=0.0,
    ):
        self.assertEqual(period_stats['period'], period_str)
        self.assertEqual(period_stats['limit_free'], limit_free)
        self.assertEqual(period_stats['limit_paid'], limit_paid)
        self.assertEqual(period_stats['count_free'], count_free)
        self.assertEqual(period_stats['count_paid'], count_paid)
        self.assertEqual(period_stats['parts_count'], count_free + count_paid)
        self.assertEqual(period_stats['current_limit_free'], current_limit_free)
        self.assertEqual(period_stats['current_sms_price'], current_sms_price)
        self.assertEqual(period_stats['payable_cost'], payable_cost)


@patch('webapps.purchase.utils.get_subscription_prepaid_sms_allowance')
@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2019, 10, 15, 12, tzinfo=UTC))
class NotificationSMSStatsByBillingCycleTestCase(SMSStatsMixin):

    def setUp(self):
        self.trial_business = baker.make(
            Business, status=Business.Status.TRIAL, has_new_billing=False
        )
        self.paid_business = baker.make(
            Business, status=Business.Status.PAID, has_new_billing=False
        )
        self.plan = baker.make(SubscriptionListing)
        self.sms_cost = SMSCosts.get_part_price_and_currency()[0]

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=123,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=123456,
    )
    def test_sms_summary_by_billing_cycles__trial(self, sub_allowance_mock):
        # Nothing sent yet
        result_1 = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.trial_business, 2)
        self.assertEqual(len(result_1), 0)
        add_stats_trial_for_business(self.trial_business, 130)
        result_2 = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.trial_business, 2)
        self.assertEqual(len(result_2), 0)

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=123,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=123456,
    )
    def test_sms_summary_by_billing_cycles__trial_pending_sub(self, sub_allowance_mock):
        sub_allowance_mock.return_value = 700
        baker.make(
            Subscription,
            product=self.plan,
            business=self.trial_business,
            start=datetime(2019, 10, 11, tzinfo=UTC),
        )
        add_stats_trial_for_business(self.trial_business, 130)
        result = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.trial_business, 2)
        self.assertEqual(len(result), 1)
        self.assert_paid_period(
            result[0], '2019-10', 700, 0, 0, self.sms_cost, current_limit_free=700
        )

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=123,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=123456,
    )
    def test_sms_summary_by_billing_cycles__paid(self, sub_allowance_mock):
        baker.make(
            Subscription,
            product=self.plan,
            business=self.paid_business,
            start=datetime(2019, 9, 2, tzinfo=UTC),
            expiry=datetime(2019, 11, 2, tzinfo=UTC),
        )
        sub_allowance_mock.return_value = 700

        add_stats_trial_for_business(self.paid_business, 130)
        # first billing period
        add_stats_paid_for_business(self.paid_business, '2019-09-02', 100)
        add_stats_paid_for_business(self.paid_business, '2019-09-15', 100)
        add_stats_paid_for_business(self.paid_business, '2019-09-25', 100)
        add_stats_paid_for_business(self.paid_business, '2019-10-01', 330)
        # second billing period
        add_stats_paid_for_business(self.paid_business, '2019-10-02', 450)
        add_stats_paid_for_business(self.paid_business, '2019-10-10', 300)
        result_1 = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.paid_business, 2)
        self.assertEqual(len(result_1), 2)
        current_period, prev_period = result_1
        self.assert_paid_period(
            prev_period, '2019-09', 700, 630, 0, self.sms_cost, current_limit_free=70
        )
        self.assert_paid_period(
            current_period,
            '2019-10',
            700,
            700,
            50,
            self.sms_cost,
            current_limit_free=0,
            payable_cost=50 * self.sms_cost,
        )

        result_2 = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.paid_business, 3)
        self.assertEqual(len(result_2), 2)

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=123,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=123456,
    )
    def test_sms_summary_by_billing_cycles__expired_sub(self, sub_allowance_mock):
        baker.make(
            Subscription,
            product=self.plan,
            business=self.paid_business,
            start=datetime(2019, 8, 2, tzinfo=UTC),
            expiry=datetime(2019, 10, 2, tzinfo=UTC),
        )
        sub_allowance_mock.return_value = 700
        add_stats_trial_for_business(self.paid_business, 130)
        # first billing cycle
        add_stats_paid_for_business(self.paid_business, '2019-08-02', 30)
        add_stats_paid_for_business(self.paid_business, '2019-08-05', 600)
        # second billing cyccle
        add_stats_paid_for_business(self.paid_business, '2019-09-05', 700)
        add_stats_paid_for_business(self.paid_business, '2019-09-10', 50)
        # after expiration date but BC is ongoing
        add_stats_paid_for_business(self.paid_business, '2019-10-02', 17)

        result = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.paid_business, 3)
        self.assertEqual(len(result), 3)
        after_expiry, second_period, first_period = result
        self.assert_paid_period(
            after_expiry, '2019-10', 700, 17, 0, self.sms_cost, current_limit_free=683
        )
        self.assert_paid_period(
            second_period,
            '2019-09',
            700,
            700,
            50,
            self.sms_cost,
            current_limit_free=0,
            payable_cost=50 * self.sms_cost,
        )
        self.assert_paid_period(
            first_period, '2019-08', 700, 630, 0, self.sms_cost, current_limit_free=70
        )

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=123,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=123456,
    )
    def test_sms_summary_by_billing_cycles__overlapping_subs(self, sub_allowance_mock):
        sub_allowance_mock.side_effect = [10, 400, 800]
        baker.make(
            Subscription,
            product=self.plan,
            business=self.paid_business,
            start=datetime(2019, 9, 2, tzinfo=UTC),
            expiry=datetime(2019, 11, 2, tzinfo=UTC),
        )
        plan_2 = baker.make(SubscriptionListing)
        baker.make(
            Subscription,
            product=plan_2,
            business=self.paid_business,
            start=datetime(2019, 9, 8, tzinfo=UTC),
            expiry=datetime(2019, 11, 8, tzinfo=UTC),
        )
        add_stats_trial_for_business(self.paid_business, 17)
        # first subs starts, first billing cycle
        add_stats_paid_for_business(self.paid_business, '2019-09-02', 2)
        add_stats_paid_for_business(self.paid_business, '2019-09-04', 6)
        # second subs starts, first billing cycle
        add_stats_paid_for_business(self.paid_business, '2019-09-08', 10)
        add_stats_paid_for_business(self.paid_business, '2019-09-15', 10)
        add_stats_paid_for_business(self.paid_business, '2019-10-01', 10)
        add_stats_paid_for_business(self.paid_business, '2019-10-07', 10)
        # second subs, second billing cycle
        add_stats_paid_for_business(self.paid_business, '2019-10-08', 10)
        add_stats_paid_for_business(self.paid_business, '2019-10-15', 5)
        result = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.paid_business, 5)
        self.assertEqual(len(result), 3)
        # Should take all billing cycles from subscription with newer expiry
        # and fill gaps with subscription with older expiry
        sub2_m10, sub2_m9, sub1_m9 = result
        self.assert_paid_period(
            sub2_m10, '2019-10', 10, 10, 5, self.sms_cost, current_limit_free=0, payable_cost=0.03
        )
        self.assert_paid_period(
            sub2_m9, '2019-09', 400, 40, 0, self.sms_cost, current_limit_free=360
        )
        self.assert_paid_period(
            sub1_m9, '2019-09', 800, 38, 0, self.sms_cost, current_limit_free=762
        )

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=123,
        SMS_PAID_ACCOUNT_PREPAID_COUNT=123456,
    )
    def test_sms_summary_by_billing_cycles__distinct_subs(self, sub_allowance_mock):
        sub_allowance_mock.return_value = 50
        baker.make(
            Subscription,
            product=self.plan,
            business=self.paid_business,
            start=datetime(2019, 7, 2, tzinfo=UTC),
            expiry=datetime(2019, 9, 2, tzinfo=UTC),
        )
        plan_2 = baker.make(SubscriptionListing)
        baker.make(
            Subscription,
            product=plan_2,
            business=self.paid_business,
            start=datetime(2019, 9, 8, tzinfo=UTC),
            expiry=datetime(2019, 11, 8, tzinfo=UTC),
        )
        add_stats_trial_for_business(self.paid_business, 17)
        # first sub
        add_stats_paid_for_business(self.paid_business, '2019-07-10', 30)
        add_stats_paid_for_business(self.paid_business, '2019-08-10', 40)
        # period with no sub but there is another billing cycle still on
        # OVERDUE period
        add_stats_paid_for_business(self.paid_business, '2019-09-06', 60)
        # second sub
        add_stats_paid_for_business(self.paid_business, '2019-09-10', 60)
        add_stats_paid_for_business(self.paid_business, '2019-10-10', 70)
        result = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.paid_business, 7)
        self.assertEqual(len(result), 5)
        # Should take all billing cycles from subscription with newer expiry
        # and fill gaps with subscription with older expiry
        sub2_m10, sub2_m9, sub1_m9, sub1_m8, sub1_m7 = result
        self.assert_paid_period(sub1_m7, '2019-07', 50, 30, 0, self.sms_cost, current_limit_free=20)
        self.assert_paid_period(sub1_m8, '2019-08', 50, 40, 0, self.sms_cost, current_limit_free=10)
        # TODO should we count some sms twice (if BC are overlapping) ?
        self.assert_paid_period(
            sub1_m9, '2019-09', 50, 50, 70, self.sms_cost, current_limit_free=0, payable_cost=0.35
        )
        self.assert_paid_period(
            sub2_m9, '2019-09', 50, 50, 10, self.sms_cost, current_limit_free=0, payable_cost=0.05
        )
        self.assert_paid_period(
            sub2_m10, '2019-10', 50, 50, 20, self.sms_cost, current_limit_free=0, payable_cost=0.1
        )

    def test_sms_summary_by_billing_cycles__period_format_monthly(self, sub_allowance_mock):
        sub_allowance_mock.return_value = 50
        baker.make(
            Subscription,
            product=self.plan,
            business=self.paid_business,
            start=datetime(2019, 7, 2, tzinfo=UTC),
            expiry=datetime(2019, 9, 2, tzinfo=UTC),
        )

        result = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.paid_business, 4)
        self.assertEqual(len(result), 4)
        current_period, *_ = result
        self.assertEqual(current_period['period'], '2019-10')

    @patch.object(NotificationSMSStatistics, 'sms_summary_by_billing_cycles')
    @patch('webapps.billing.utils.billing_sms_summary_by_billing_cycles')
    def test_sms_summary_fallback_to_proper_function(
        self,
        billing_sms_summary_by_billing_cycles,
        sms_summary_by_billing_cycles_mock,
        sub_allowance_mock,
    ):
        baker.make(
            Subscription,
            product=self.plan,
            business=self.paid_business,
            start=datetime(2019, 9, 2, tzinfo=UTC),
        )

        baker.make(
            Subscription,
            product=self.plan,
            business=self.trial_business,
            start=datetime(2019, 11, 1, tzinfo=UTC),
        )
        NotificationSMSStatistics.sms_summary(self.paid_business, 5)
        NotificationSMSStatistics.sms_summary(self.trial_business, 3)

        # ongoing Sub 2.0 + pending Booksy Billing Sub.
        pending_billing_business = baker.make(Business, has_new_billing=True)
        baker.make(
            Subscription,
            product=self.plan,
            business=pending_billing_business,
            start=datetime(2019, 10, 1, tzinfo=UTC),
        )
        baker.make(
            BillingSubscription,
            status=SubscriptionStatus.PENDING,
            business=pending_billing_business,
            date_start=datetime(2019, 11, 1, tzinfo=UTC),
        )
        NotificationSMSStatistics.sms_summary(pending_billing_business, 4)
        self.assertIn(
            call(self.paid_business, billing_cycles_amount=5),
            sms_summary_by_billing_cycles_mock.mock_calls,
        )
        self.assertIn(
            call(pending_billing_business, billing_cycles_amount=4),
            sms_summary_by_billing_cycles_mock.mock_calls,
        )
        self.assertIn(
            call(self.trial_business, billing_cycles_amount=3),
            sms_summary_by_billing_cycles_mock.mock_calls,
        )
        self.assertEqual(billing_sms_summary_by_billing_cycles.call_count, 0)


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2021, 3, 25, tzinfo=UTC))
class NotificationSMSSummaryBilling3TestCase(SMSStatsMixin):

    def setUp(self):
        self.trial_business = baker.make(
            Business, status=Business.Status.TRIAL, has_new_billing=True
        )
        self.paid_business = baker.make(Business, status=Business.Status.PAID, has_new_billing=True)

    def create_billing_subscription(self):
        postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
        )
        subscription = baker.make(
            BillingSubscription,
            business_id=self.paid_business.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            next_billing_date=datetime(2021, 4, 20, tzinfo=UTC),
            paid_through_date=datetime(2021, 4, 20, tzinfo=UTC),
            currency='USD',
        )
        baker.make(
            BillingCycle,
            subscription_id=subscription.id,
            business_id=self.paid_business.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            date_end=datetime(2021, 2, 20, tzinfo=UTC),
            sms_allowance=43,
        )
        baker.make(
            BillingCycle,
            subscription_id=subscription.id,
            business_id=self.paid_business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
            sms_allowance=99,
        )
        baker.make(
            BillingCycle,
            subscription_id=subscription.id,
            business_id=self.paid_business.id,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 4, 20, tzinfo=UTC),
            sms_allowance=0,
        )
        baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            product_id=postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=subscription.date_start,
            unit_price=Decimal('0.05'),
            currency='USD',
            quantity=0,
        )

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=50,
        BILLING_SMS_HARD_LIMIT=999,
    )
    def test_sms_summary_by_billing_cycles__new_billing_paid(self):
        self.create_billing_subscription()

        # TODO product sms , bcx2
        # TODO mocks
        add_stats_trial_for_business(self.paid_business, 85)
        # first billing_cycle
        add_stats_paid_for_business(self.paid_business, '2021-01-25', 30)
        add_stats_paid_for_business(self.paid_business, '2021-02-10', 40)
        # second billing_cycle, prepaid only
        add_stats_paid_for_business(self.paid_business, '2021-02-23', 53)
        add_stats_paid_for_business(self.paid_business, '2021-03-19', 17)
        # third billing_cycle
        add_stats_paid_for_business(self.paid_business, '2021-03-20', 70)
        add_stats_paid_for_business(self.paid_business, '2021-03-25', 70)
        # future billing cycles
        add_stats_paid_for_business(self.paid_business, '2021-04-23', 70)
        result = NotificationSMSStatistics.sms_summary(self.paid_business, 4)
        self.assertEqual(len(result), 4)
        bc_3, bc_2, bc_1, trial = result
        self.assert_trial_period(trial, 85)
        self.assert_paid_period(
            bc_1,
            '2021-01-20',
            43,
            43,
            27,
            0.05,
            current_limit_free=0,
            limit_paid=999,
            payable_cost=1.35,
        )
        self.assert_paid_period(
            bc_2, '2021-02-20', 99, 70, 0, 0.05, current_limit_free=29, limit_paid=999
        )
        self.assert_paid_period(
            bc_3,
            '2021-03-20',
            0,
            0,
            140,
            0.05,
            current_limit_free=0,
            limit_paid=999,
            payable_cost=7.00,
        )

    @override_settings(
        SMS_LIMITS_IN_SUBSCRIPTION=True,
        SMS_DEMO_ACCOUNT_PREPAID_COUNT=50,
        BILLING_SMS_HARD_LIMIT=999,
    )
    def test_sms_summary_by_billing_cycles__new_billing_trial(self):

        add_stats_trial_for_business(self.trial_business, 85)

        result = NotificationSMSStatistics.sms_summary_by_billing_cycles(self.trial_business, 4)
        self.assertEqual(len(result), 0)

    @patch.object(NotificationSMSStatistics, 'sms_summary_by_billing_cycles')
    @patch('webapps.billing.utils.billing_sms_summary_by_billing_cycles')
    def test_sms_summary_fallback_to_proper_function(
        self,
        billing_sms_summary_by_billing_cycles_mock,
        sms_summary_by_billing_cycles_mock,
    ):
        billing_sms_summary_by_billing_cycles_mock.return_value = []
        started_subscription = baker.make(
            BillingSubscription,
            business_id=self.paid_business.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
        )

        baker.make(
            BillingSubscription,
            business_id=self.trial_business.id,
            date_start=datetime(2021, 4, 1, tzinfo=UTC),
        )
        NotificationSMSStatistics.sms_summary(self.paid_business, 4)
        NotificationSMSStatistics.sms_summary(self.trial_business, 4)

        self.assertEqual(billing_sms_summary_by_billing_cycles_mock.call_count, 1)
        self.assertIn(
            call(started_subscription, billing_cycles_amount=4),
            billing_sms_summary_by_billing_cycles_mock.mock_calls,
        )
        self.assertEqual(sms_summary_by_billing_cycles_mock.call_count, 1)

    @override_settings(
        BILLING_SMS_HARD_LIMIT=999,
    )
    def test_sms_stats__billing_paid(self):
        self.create_billing_subscription()
        add_stats_paid_for_business(self.paid_business, '2021-02-21', 10)  # previous BC
        add_stats_paid_for_business(self.paid_business, '2021-03-15', 10)  # previous BC
        add_stats_paid_for_business(self.paid_business, '2021-03-20', 24)
        add_stats_paid_for_business(self.paid_business, '2021-03-23', 12)
        add_stats_paid_for_business(self.paid_business, '2021-03-25', 30)

        sms_stats = NotificationSMSStatistics.sms_stats(self.paid_business)
        self.assertEqual(
            sms_stats,
            ('paid', 66, 0, 999),
        )


@pytest.mark.django_db
@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
def test_sms_statistics_upsert__paid():
    # first case, already paying business
    business = baker.make('business.Business', id=123)
    sub_start_date = convert_date_str_to_aware_dt('2018-01-01', '%Y-%m-%d')
    with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
        baker.make('purchase.Subscription', business=business, start=sub_start_date)

    # 15 parts total, 2 sms for 2019-01-05
    with freeze_time('2019-01-05'):
        for parts_count in (10, 5):
            NotificationSMSStatistics.upsert_for_business(123, parts_count)
    # 7 parts total, 3 sms
    with freeze_time('2019-02-25'):
        for parts_count in (1, 3, 3):
            NotificationSMSStatistics.upsert_for_business(123, parts_count)
    # another day in january
    with freeze_time('2019-01-10'):
        NotificationSMSStatistics.upsert_for_business(123, 25)

    result = NotificationSMSStatistics.objects.filter(business_id=123).order_by('date')

    assert len(result) == 3

    january_05, january_10, feb_25 = result
    assert january_05.date == convert_date_str_to_aware_date('2019-01-05', '%Y-%m-%d')
    assert january_05.parts_count == 15
    assert january_05.sms_count == 2

    assert january_10.date == convert_date_str_to_aware_date('2019-01-10', '%Y-%m-%d')
    assert january_10.parts_count == 25
    assert january_10.sms_count == 1

    assert feb_25.date == convert_date_str_to_aware_date('2019-02-25', '%Y-%m-%d')
    assert feb_25.parts_count == 7
    assert feb_25.sms_count == 3


@pytest.mark.django_db
def test_sms_statistics_upsert__paid_new_billing():
    # first case, already paying business
    business = baker.make('business.Business', id=123)
    sub_start_date = convert_date_str_to_aware_dt('2018-01-01', '%Y-%m-%d')
    baker.make(
        'billing.BillingSubscription',
        business=business,
        date_start=sub_start_date,
        payment_period=relativedelta(months=1),
    )

    # 15 parts total, 2 sms for 2019-01-05
    with freeze_time('2019-01-05'):
        for parts_count in (10, 5):
            NotificationSMSStatistics.upsert_for_business(123, parts_count)
    # 7 parts total, 3 sms
    with freeze_time('2019-02-25'):
        for parts_count in (1, 3, 3):
            NotificationSMSStatistics.upsert_for_business(123, parts_count)
    # another day in january
    with freeze_time('2019-01-10'):
        NotificationSMSStatistics.upsert_for_business(123, 25)

    result = NotificationSMSStatistics.objects.filter(business_id=123).order_by('date')

    assert len(result) == 3

    january_05, january_10, feb_25 = result
    assert january_05.date == convert_date_str_to_aware_date('2019-01-05', '%Y-%m-%d')
    assert january_05.parts_count == 15
    assert january_05.sms_count == 2

    assert january_10.date == convert_date_str_to_aware_date('2019-01-10', '%Y-%m-%d')
    assert january_10.parts_count == 25
    assert january_10.sms_count == 1

    assert feb_25.date == convert_date_str_to_aware_date('2019-02-25', '%Y-%m-%d')
    assert feb_25.parts_count == 7
    assert feb_25.sms_count == 3


@pytest.mark.django_db
@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
def test_sms_statistics_upsert__future_subs():
    # second case, business with subscription start date in the future
    business = baker.make('business.Business', id=456)
    sub_start_date = convert_date_str_to_aware_dt('2019-03-01', '%Y-%m-%d')
    with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
        baker.make('purchase.Subscription', business=business, start=sub_start_date)
    # 3 parts total, 1 sms
    with freeze_time('2019-01-01'):
        NotificationSMSStatistics.upsert_for_business(456, 3)
    # 9 parts total, 4 sms
    with freeze_time('2019-02-25'):
        for parts_count in (1, 3, 3, 2):
            NotificationSMSStatistics.upsert_for_business(456, parts_count)

    result = NotificationSMSStatistics.objects.filter(business_id=456).order_by('date')
    assert len(result) == 1

    stats = result[0]
    assert stats.date == datetime.min.date()
    assert stats.parts_count == 12
    assert stats.sms_count == 5


@pytest.mark.django_db
def test_sms_statistics_upsert__no_subs():
    # third case, business without a subscription
    baker.make('business.Business', id=777)
    # 1 part total, 1 sms
    with freeze_time('2018-12-23'):
        NotificationSMSStatistics.upsert_for_business(777, 1)

    with freeze_time('2019-02-13'):
        NotificationSMSStatistics.upsert_for_business(777, 4)
        NotificationSMSStatistics.upsert_for_business(777, 3)

    result = NotificationSMSStatistics.objects.filter(business_id=777)

    assert len(result) == 1

    stats = result[0]
    assert stats.date == datetime.min.date()
    assert stats.parts_count == 8
    assert stats.sms_count == 3


@pytest.mark.django_db
@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
def test_sms_statistics_upsert__sub_middle():
    # forth case, business starts subscription in the middle of february
    business = baker.make('business.Business', id=1000)
    sub_start_date = convert_date_str_to_aware_dt('2019-02-15', '%Y-%m-%d')
    with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
        baker.make('purchase.Subscription', business=business, start=sub_start_date)
    # 15 parts total, 2 sms for trial
    with freeze_time('2019-02-01'):
        NotificationSMSStatistics.upsert_for_business(1000, 10)
    with freeze_time('2019-02-10'):
        NotificationSMSStatistics.upsert_for_business(1000, 5)
    # 2 parts total, 2 sms for 2019-02
    with freeze_time('2019-02-15'):
        NotificationSMSStatistics.upsert_for_business(1000, 1)
        NotificationSMSStatistics.upsert_for_business(1000, 3)
    with freeze_time('2019-02-20'):
        NotificationSMSStatistics.upsert_for_business(1000, 1)

    result = NotificationSMSStatistics.objects.filter(business_id=1000)

    assert len(result) == 3

    trial, day_15, day_20 = result
    assert trial.date == datetime.min.date()
    assert trial.parts_count == 15
    assert trial.sms_count == 2

    assert day_15.date == convert_date_str_to_aware_date('2019-02-15', '%Y-%m-%d')
    assert day_15.parts_count == 4
    assert day_15.sms_count == 2

    assert day_20.date == convert_date_str_to_aware_date('2019-02-20', '%Y-%m-%d')
    assert day_20.parts_count == 1
    assert day_20.sms_count == 1


@pytest.mark.django_db
def test_get_parts_count_in_period():
    business = baker.make(Business)
    baker.make(
        NotificationSMSStatistics,
        business_id=business.id,
        parts_count=100,
        sms_count=3,
        date=convert_date_str_to_aware_date('2019-02-07', '%Y-%m-%d'),
    )
    baker.make(
        NotificationSMSStatistics,
        business_id=business.id,
        parts_count=200,
        sms_count=123,
        date=convert_date_str_to_aware_date('2019-02-08', '%Y-%m-%d'),
    )
    baker.make(
        NotificationSMSStatistics,
        business_id=business.id,
        parts_count=1000,
        sms_count=5,
        date=convert_date_str_to_aware_date('2019-02-09', '%Y-%m-%d'),
    )
    result_1 = NotificationSMSStatistics.get_parts_count_in_period(
        business_id=business.id,
        date_start=date(2019, 1, 1),
        date_end=date(2019, 12, 31),
    )
    result_2 = NotificationSMSStatistics.get_parts_count_in_period(
        business_id=business.id,
        date_start=date(2019, 2, 7),
        date_end=date(2019, 2, 9),
    )
    result_3 = NotificationSMSStatistics.get_parts_count_in_period(
        business_id=business.id,
        date_start=date(2019, 2, 7),
        date_end=date(2019, 2, 7),
    )
    result_4 = NotificationSMSStatistics.get_parts_count_in_period(
        business_id=business.id,
        date_start=date(2019, 2, 7),
        date_end=date(2019, 2, 8),
    )
    assert result_1 == 1300
    assert result_2 == 1300
    assert result_3 == 100
    assert result_4 == 300


@pytest.mark.freeze_time(datetime(2023, 3, 15, 12, tzinfo=UTC))
@override_eppo_feature_flag({SetSMSLimitForActivePeriodFlag.flag_name: True})
class TestSMSPaidLimit(DjangoTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make(Business, has_new_billing=False)
        cls.product = baker.make(SubscriptionListing)
        cls.subscription = baker.make(
            Subscription,
            business_id=cls.business.id,
            product=cls.product,
            start=datetime(2023, 2, 20, tzinfo=UTC),
            expiry=datetime(2024, 3, 20, tzinfo=UTC),
        )

    def test_should_recognize_sms_limit_on_subscription_started_mid_last_month(self):
        self.assertDictEqual(self.business.sms_limit_history, {})

        self.business.set_sms_limit(100)
        self.business.save()

        self.assertEqual(self.business.sms_limit, 100)
        self.assertDictEqual(
            self.business.sms_limit_history,
            {
                '2023-02': 100,
                '2023-03': 100,
            },
        )
        self.assertEqual(
            NotificationSMSStatistics.get_postpaid_sms_limit(self.business),
            self.business.sms_limit,
        )
        sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=1)[0]
        self.assertEqual(sms_summary['period'], '2023-02')
        self.assertEqual(sms_summary['limit_paid'], self.business.sms_limit)

        with freeze_time(datetime(2023, 3, 20, 12, tzinfo=UTC)):  # 2023-03 period is active
            self.assertEqual(
                NotificationSMSStatistics.get_postpaid_sms_limit(self.business),
                self.business.sms_limit,
            )
            sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=1)[0]
            self.assertEqual(sms_summary['period'], '2023-03')
            self.assertEqual(sms_summary['limit_paid'], self.business.sms_limit)

    def test_should_correctly_return_limit_if_changed_during_active_subscription(self):
        with freeze_time(datetime(2023, 2, 10, 12, tzinfo=UTC)):  # set on trial
            self.business.set_sms_limit(20)
            self.business.save()

        with freeze_time(datetime(2023, 3, 10, 12, tzinfo=UTC)):
            sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=1)[0]
            self.assertEqual(sms_summary['period'], '2023-02')
            self.assertEqual(sms_summary['limit_paid'], 20)
            self.assertEqual(
                self.business.sms_limit_history,
                {
                    '2023-02': 20,
                },
            )

            self.business.set_sms_limit(30)
            self.business.save()

            self.assertEqual(
                self.business.sms_limit_history,
                {
                    '2023-02': 30,
                    '2023-03': 30,
                },
            )
            sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=1)[0]
            self.assertEqual(sms_summary['period'], '2023-02')
            self.assertEqual(sms_summary['limit_paid'], 30)

        with freeze_time(datetime(2023, 4, 1, 12, tzinfo=UTC)):
            self.business.set_sms_limit(40)
            self.business.save()

            self.assertEqual(
                self.business.sms_limit_history,
                {
                    '2023-02': 30,
                    '2023-03': 40,
                    '2023-04': 40,
                },
            )
            sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=1)[0]
            self.assertEqual(sms_summary['period'], '2023-03')
            self.assertEqual(sms_summary['limit_paid'], 40)

    def test_should_correctly_return_older_sms_limits(self):
        self.business.set_sms_limit(20)
        self.business.save()

        with freeze_time(datetime(2023, 3, 20, 12, tzinfo=UTC)):
            self.business.set_sms_limit(30)
            self.business.save()

        with freeze_time(datetime(2023, 4, 20, 12, tzinfo=UTC)):
            self.business.set_sms_limit(40)
            self.business.save()

            self.assertEqual(
                self.business.sms_limit_history,
                {
                    '2023-02': 20,
                    '2023-03': 30,
                    '2023-04': 40,
                },
            )
            sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=3)
            self.assertListEqual(
                sms_summary,
                [
                    {
                        'count_free': 0,
                        'count_paid': 0,
                        'current_limit_free': 10,
                        'current_sms_price': 0.005,
                        'limit_free': 10,
                        'limit_paid': 40,
                        'parts_count': 0,
                        'payable_cost': 0.0,
                        'payable_cost_formatted': '$0.00',
                        'period': '2023-04',
                    },
                    {
                        'count_free': 0,
                        'count_paid': 0,
                        'current_limit_free': 10,
                        'current_sms_price': 0.005,
                        'limit_free': 10,
                        'limit_paid': 30,
                        'parts_count': 0,
                        'payable_cost': 0.0,
                        'payable_cost_formatted': '$0.00',
                        'period': '2023-03',
                    },
                    {
                        'count_free': 0,
                        'count_paid': 0,
                        'current_limit_free': 10,
                        'current_sms_price': 0.005,
                        'limit_free': 10,
                        'limit_paid': 20,
                        'parts_count': 0,
                        'payable_cost': 0.0,
                        'payable_cost_formatted': '$0.00',
                        'period': '2023-02',
                    },
                ],
            )

    def test_should_zero_sms_limit(self):
        self.business.set_sms_limit(100)
        self.business.save()

        self.assertEqual(
            self.business.sms_limit_history,
            {
                '2023-02': 100,
                '2023-03': 100,
            },
        )

        with freeze_time(datetime(2023, 4, 19, 12, tzinfo=UTC)):
            self.business.set_sms_limit(0)
            self.business.save()

            self.assertEqual(
                self.business.sms_limit_history,
                {
                    '2023-02': 100,
                    '2023-03': 0,
                    '2023-04': 0,
                },
            )
            sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=1)[0]
            self.assertEqual(sms_summary['period'], '2023-03')
            self.assertEqual(sms_summary['limit_paid'], 0)

        with freeze_time(datetime(2023, 4, 20, 12, tzinfo=UTC)):
            sms_summary = NotificationSMSStatistics.sms_summary(self.business, periods_amount=1)[0]
            self.assertEqual(sms_summary['period'], '2023-04')
            self.assertEqual(sms_summary['limit_paid'], 0)
