import logging
from itertools import chain

from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.db.models.query import Prefetch, QuerySet
from django.utils.encoding import force_str
from django.utils.functional import cached_property
from django.utils.translation import gettext as _

from rest_framework import serializers
from rest_framework.fields import get_attribute
from rest_framework.relations import PKOnlyObject

from lib.abc import abstractclassmethod
from lib.tools import (
    format_currency,
    relativedelta_to_json_converter,
    sget,
)

from webapps.booking.models import Appointment, SubBooking
from webapps.booking.serializers.utils import enforce_timezone_util
from webapps.business.enums import PriceType
from webapps.french_certification.services import FiscalReceiptService
from webapps.pos import enums, validators
from webapps.pos.calculations import round_currency
from webapps.pos.enums import compatibilities, receipt_status
from webapps.pos.models import Transaction

log = logging.getLogger('booksy.pos')


class PriceFieldUnformatted(serializers.DecimalField):

    def __init__(self, **kwargs):
        kwargs['max_digits'] = kwargs.get('max_digits', 10)
        kwargs['decimal_places'] = kwargs.get('decimal_places', 2)
        super().__init__(**kwargs)


class PriceField(PriceFieldUnformatted):

    def __init__(self, **kwargs):
        self.currency_symbol = None
        super().__init__(**kwargs)

    def to_representation(self, value):
        if self.context.get('valid_currency'):
            return format_currency(value) if value is not None else ''

        currency_symbol = self.context['currency_symbol']
        log.warning('Transaction currency doesnt match settings: %s', currency_symbol)

        # intentionally display 'technical' value
        return f'{value} {currency_symbol}'


class TypedPriceField(PriceField):

    templates = {
        PriceType.FREE: _('Free'),
        PriceType.STARTS_AT: '{}+',
        PriceType.VARIES: _('Varies'),
    }

    def __init__(self, **kwargs):
        self.price_source = kwargs.pop('source', None)
        kwargs['source'] = '*'
        super().__init__(**kwargs)

    def to_representation(self, value):
        price_path = self.price_source.split('.') if self.price_source else None
        price_source = price_path or [self.field_name]
        price = get_attribute(value, price_source)
        formatted_price = super().to_representation(price)

        price_type = sget(value, ['price_type'])
        if not price_type:
            return formatted_price

        template = self.templates.get(price_type)

        if template is None:
            return formatted_price

        return template.format(force_str(formatted_price))


class SomewhatTypedPriceField(TypedPriceField):
    templates = {
        PriceType.STARTS_AT: '{}+',
    }


class TaxRateReadOnlyField(serializers.ReadOnlyField):

    def __init__(self, **kwargs):
        kwargs['source'] = '*'
        super().__init__(**kwargs)

    def to_representation(self, value):
        rate = get_attribute(value, ['tax_rate'])
        return f'{rate:.2f}%'.replace('.00', '') if rate is not None else _('Free (in tax rates)')


class SummaryRateField(PriceField):
    """
    Return dict as representation for use in TransactionSummarySerializer
    """

    def __init__(self, name, editable=False, show_zero_amount=True, **kwargs):
        self.name_main = name
        self.editable = editable
        self.show_zero_amount = show_zero_amount
        super().__init__(**kwargs)

    def to_representation(self, value):
        editable = self.editable and self.context.get('dry_run')
        rate = get_attribute(value, [f'{self.name_main}_rate'])
        amount = get_attribute(value, [f'{self.name_main}_amount'])
        if not self.show_zero_amount:
            return []
        text = super().to_representation(amount)
        ret = {
            'type': self.name_main,
            'label': f'{force_str(self.label)}',
            'text': text if amount == 0.0 else f'-{text}',
            'amount': amount,
            'value_float': round_currency(amount),
        }
        if editable:
            ret['value'] = rate
        return ret


class POSBusinessRelatedField(serializers.PrimaryKeyRelatedField):
    """
    queryset is restricted to pos.business in context
    """

    def __init__(self, **kwargs):
        self._business_lookup = kwargs.pop('business_lookup', None)
        super().__init__(**kwargs)

    def get_queryset(self):
        queryset = self.queryset
        if isinstance(queryset, QuerySet):
            # Ensure queryset is re-evaluated whenever used.
            # And apply business filter
            queryset = queryset.all()
            if self._business_lookup:
                filter_ = {self._business_lookup: self.context['pos'].business}
                queryset = queryset.filter(**filter_)
        return queryset


class POSRelatedField(serializers.PrimaryKeyRelatedField):
    '''
    Related field with queryset restricted to POS related objects
    '''

    def __init__(self, queryset_attr=None, **kwargs):
        self.queryset_attr = queryset_attr
        if queryset_attr:
            kwargs['queryset'] = queryset_attr
        else:
            self.queryset_filter = kwargs.pop('queryset_filter')
        super().__init__(**kwargs)

    def get_queryset(self):
        if self.queryset_attr:
            qset = get_attribute(self.context['pos'], self.queryset).all()
        else:
            qset = SubBooking.objects.filter(**{self.queryset_filter: self.context['pos']})
        return qset


class VoucherPOSRelatedField(POSRelatedField):

    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .prefetch_related(
                Prefetch(
                    'services',
                    to_attr='prefetched_services',
                ),
                Prefetch(
                    'voucher_template__services',
                    to_attr='prefetched_services',
                ),
            )
        )


class VoucherTemplatePOSRelatedField(POSRelatedField):

    def to_internal_value(self, data):
        pos = self.context.get('pos')
        if hasattr(pos, 'prefetched_voucher_templates'):
            voucher_template = next(
                (vt for vt in pos.prefetched_voucher_templates if vt.id == data), None
            )
            if voucher_template:
                return voucher_template
        return super().to_internal_value(data)


class UserRelatedField(serializers.PrimaryKeyRelatedField):
    """Related field with queryset restricted to User related objects"""

    def get_queryset(self):
        return self.queryset.filter(user=self.context['user'])


class PaymentTypeCodeRelatedField(POSRelatedField):
    """RelatedField identifying instance based on code field instead of pk"""

    default_error_messages = {
        'required': _('This field is required.'),
        'does_not_exist': _('Invalid code "{code}" - object does not exist.'),
        'incorrect_type': _('Incorrect type. Expected code value, received {data_type}.'),
    }

    def get_queryset(self):
        return self.context['pos'].payment_types(manager='all_objects').all()

    def to_internal_value(self, data):
        try:
            return self.get_queryset().get(code=data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', code=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)

    def to_representation(self, value):
        if isinstance(value, PKOnlyObject):
            mapping = {x.pk: x for x in self.get_queryset()}
            return mapping[value.pk].code
        return value.code


class PosDateTimeField(serializers.DateTimeField):
    format = settings.DATETIME_FORMAT
    _timezone = None

    @cached_property
    def cached_timezone(self):
        business = self.context.get('business')

        if business:
            return business.get_timezone()

        pos = self.context.get('pos')
        return pos.business.get_timezone()

    def enforce_timezone(self, value):
        return enforce_timezone_util(value, self.cached_timezone)

    def to_representation(self, value):
        value = value.astimezone(self.cached_timezone)
        return super().to_representation(value)


class CommaSeparatedListField(serializers.ListField):

    def get_value(self, dictionary):
        value = super().get_value(dictionary)
        if value == serializers.empty:
            return []
        return value.split(',')


class BookingPaymentInfoField(serializers.Field):
    """Abstract payment_info class.

    Should be subclassed for business_api and customer_api responses.

    """

    def __init__(self, **kwargs):
        """Force read-only and whole instance as source."""
        kwargs['source'] = kwargs.pop('source', '*')
        kwargs['read_only'] = True
        super().__init__(**kwargs)

    def to_representation(self, value):
        """Return info needed by POS. See BookingPaymentInfo swagger model."""
        if self.context.get('recursive_field'):
            # no need to calculate for multibooking's extra_bookings
            return None

        if isinstance(value, dict):
            # no need to calculate for RepeatingBooking dry_run
            return None

        if self.context.get('pos'):
            pos = self.context.get('pos')
        elif self.context.get('business'):
            pos = self.context.get('business').pos
        elif self.context.get('pos_prefetched'):
            pos = value.appointment.business.pos
        else:
            pos = None

        if not pos:
            return None

        payment, deposit = value.get_payment_and_deposit()  # prefetched

        payable = self.is_payable(value.appointment, payment)
        handle_deposit = self.can_handle_deposit(value, deposit)
        auto_release_deposit_on_cancel = deposit and value.appointment.in_deposit_cancel_time()

        from webapps.pos.serializers import (
            AppointmentBooksyPaySerializer,
            TransactionInfoSerializer,
            TransactionInfoForPendingPaymentSerializer,
        )

        transaction_info = (
            payment
            and TransactionInfoForPendingPaymentSerializer(
                payment,
                context={
                    'pos': pos,
                    'customer_api': self.context.get('customer_api'),
                    compatibilities.COMPATIBILITIES: self.context.get(
                        compatibilities.COMPATIBILITIES, {}
                    ),
                },
            ).data
        )
        deposit_info = (
            deposit
            and TransactionInfoSerializer(
                deposit,
                context={
                    'pos': pos,
                    'customer_api': self.context.get('customer_api'),
                    compatibilities.COMPATIBILITIES: self.context.get(
                        compatibilities.COMPATIBILITIES, {}
                    ),
                },
            ).data
        )

        return {
            'deposit_cancel_time': relativedelta_to_json_converter(
                pos.deposit_cancel_time,
            ),
            'payable': payable,
            'transaction_id': payment and payment.id,
            'transaction_info': transaction_info,
            'handle_deposit': handle_deposit,
            'auto_release_deposit_on_cancel': auto_release_deposit_on_cancel,
            'deposit_id': deposit and deposit.id,
            'deposit_info': deposit_info,
            'booksy_pay': AppointmentBooksyPaySerializer(instance=value.appointment).data,
        }

    @abstractclassmethod
    def is_payable(cls, obj: Appointment, payment: Transaction):
        raise NotImplementedError

    @abstractclassmethod
    def can_handle_deposit(cls, obj: Appointment, deposit: Transaction):
        raise NotImplementedError


class BusinessBookingPaymentInfoField(BookingPaymentInfoField):
    @classmethod
    def is_payable(cls, obj: Appointment, payment: Transaction):
        """Check if Business can charge for this booking."""
        payable = (
            (obj.type in Appointment.TYPES_BOOKABLE)
            and (
                obj.status
                in [
                    Appointment.STATUS.FINISHED,
                    Appointment.STATUS.ACCEPTED,  # free willy!
                ]
            )
            and (
                payment is None
                or payment.latest_receipt.status_code in receipt_status.PAYABLE_STATUSES
            )
        )
        return bool(payable)

    @staticmethod
    def _get_cached_appointment(obj):
        from webapps.booking.appointment_wrapper import AppointmentWrapper

        if isinstance(obj, AppointmentWrapper):
            return obj.appointment
        if isinstance(obj, Appointment):
            return obj
        if isinstance(obj, SubBooking):
            return obj._state.fields_cache.get('appointment')  # pylint: disable=protected-access

    @classmethod
    def can_handle_deposit(cls, obj: Appointment, deposit: Transaction):
        """Check if Business can charge or cancel an authorized deposit."""
        appointment = cls._get_cached_appointment(obj)
        if deposit and appointment:
            # add appointment to cache to avoid making extra query
            deposit._state.fields_cache.setdefault(
                'appointment', appointment
            )  # pylint: disable=protected-access
        handle_deposit = deposit and deposit.action_allowed(
            action=enums.BUSINESS_ACTION__CHARGE_DEPOSIT,
        )
        return bool(handle_deposit)


class FCBusinessBookingPaymentInfoField(BusinessBookingPaymentInfoField):
    def to_representation(self, value):
        result = super().to_representation(value)

        if not isinstance(result, dict):
            return result

        if not isinstance(result['transaction_info'], dict):
            return result

        fiscal_receipt = FiscalReceiptService.fiscal_receipt_sale_from_transaction_id(
            transaction_id=result['transaction_info']['id']
        )

        from webapps.french_certification.serializers import FiscalReceiptBasicDetailSerializer

        if fiscal_receipt:
            fiscal_receipt_data = FiscalReceiptBasicDetailSerializer(fiscal_receipt).data
        else:
            fiscal_receipt_data = None
        result['transaction_info']['fiscal_receipt'] = fiscal_receipt_data
        return result


class CustomerBookingPaymentInfoField(BookingPaymentInfoField):
    @classmethod
    def is_payable(cls, obj: Appointment, payment: Transaction):
        """Check if Customer has a Call for Payment for this booking."""
        payable = (
            payment
            and obj.status != Appointment.STATUS.CANCELED
            and payment.latest_receipt.status_code
            in chain(
                receipt_status.CALL_FOR_PREPAYMENT,
                receipt_status.CALL_STATUSES,  # CALL_FOR_PAYMENT
                receipt_status.FAILED_STATUSES,
            )
        )
        return bool(payable)

    @classmethod
    def can_handle_deposit(cls, obj: Appointment, deposit: Transaction):
        """Customer cannot handle deposits."""
        return False


class CardNumberField(serializers.CharField):
    default_error_messages = {
        'fails_luhn': _('Credit card number is invalid'),
        'not_digit': _('Card number should contain only digits'),
    }

    def __init__(self, **kwargs):
        kwargs['min_length'] = 12
        kwargs['max_length'] = 19
        kwargs['trim_whitespace'] = True

        super().__init__(**kwargs)

        self.validators.append(validators.CardNumber(messages=self.error_messages))

    def to_internal_value(self, data):
        value = super().to_internal_value(data)
        value = value.replace(' ', '')
        return value


class PrimaryKeyRelatedAsInt(serializers.PrimaryKeyRelatedField):

    def to_representation(self, value):
        if isinstance(value, int):
            return value
        return super().to_representation(value)
