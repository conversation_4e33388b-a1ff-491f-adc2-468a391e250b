from webapps.business.models.category import ServiceSuggestion
from webapps.script_runner.runners import DBScriptRunner


class Script(DBScriptRunner):
    def run(self):
        """Set tax_rate to None for all service suggestions in the US."""
        updated_count = ServiceSuggestion.objects.filter(tax_rate__isnull=False).update(
            tax_rate=None
        )

        print(f"Updated {updated_count} service suggestions to have tax_rate=None")
