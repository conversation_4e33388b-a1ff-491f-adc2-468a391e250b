from decimal import Decimal
from model_bakery import baker

import pytest

from webapps.business.models.category import ServiceSuggestion, BusinessCategory
from webapps.script_runner.scripts.script_update_service_suggestions_tax_rate__us import Script


@pytest.mark.django_db
def test_service_suggestions_tax_rate_set_to_none():
    """Test that all service suggestions have their tax_rate set to None using real data."""

    nutrition_consultant = baker.make(
        ServiceSuggestion,
        treatment=baker.make(BusinessCategory, internal_name='nutrition_treatment'),
        name='Nutrition Consultant',
        price=Decimal('60.0'),
        tax_rate=None,
        popularity=0.026097832062232,
    )
    dreadlocks = baker.make(
        ServiceSuggestion,
        treatment=baker.make(BusinessCategory, internal_name='hair_treatment'),
        name='Dreadlocks',
        price=Decimal('65.0'),
        tax_rate=Decimal('5.0'),
        popularity=0.09013116966214,
    )
    manicure = baker.make(
        ServiceSuggestion,
        treatment=baker.make(BusinessCategory, internal_name='nail_treatment'),
        name='Manicure',
        price=Decimal('20.0'),
        tax_rate=Decimal('6.35'),
        popularity=0.145357704589257,
    )

    script = Script()
    script.run()

    nutrition_consultant.refresh_from_db()
    dreadlocks.refresh_from_db()
    manicure.refresh_from_db()

    assert nutrition_consultant.tax_rate is None
    assert dreadlocks.tax_rate is None
    assert manicure.tax_rate is None

    assert nutrition_consultant.name == 'Nutrition Consultant'
    assert nutrition_consultant.price == Decimal('60.0')
    assert nutrition_consultant.popularity == 0.026097832062232

    assert dreadlocks.name == 'Dreadlocks'
    assert dreadlocks.price == Decimal('65.0')
    assert dreadlocks.popularity == 0.09013116966214

    assert manicure.name == 'Manicure'
    assert manicure.price == Decimal('20.0')
    assert manicure.popularity == 0.145357704589257
