from collections.abc import Iterable
from pathlib import Path

from pytest import Item

from booksy_pytest_doctest._reader import Ignore


class Blacklist:
    """Adapts the ignore file to <PERSON>yte<PERSON>'s hooks.

    >>> bl = Blacklist()
    >>> bl.reset(Path.cwd(), [Ignore(collect="file.py")])
    >>> bl.match_path(Path(__file__))
    False
    """

    def __init__(self):
        self.reset(Path.cwd(), [])

    def reset(self, rootdir: str | Path, ignores: Iterable[Ignore]) -> None:
        self._collection_root = Path(rootdir)
        self._collect_ignore_prefixes = list[str]()
        self._test_ignore_prefixes = list[str]()

        for ignore in ignores:
            if ignore.collect:
                self._collect_ignore_prefixes.append(ignore.collect)
            if ignore.testid:
                self._test_ignore_prefixes.append(ignore.testid)

    def match_path(self, path: Path) -> bool:
        return str(path.relative_to(self._collection_root)) in self._collect_ignore_prefixes

    def match_item(self, item: Item) -> bool:
        return item.nodeid in self._test_ignore_prefixes
