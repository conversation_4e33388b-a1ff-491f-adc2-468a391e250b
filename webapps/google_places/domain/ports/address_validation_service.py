from abc import ABC, abstractmethod

from webapps.google_places.domain.dtos.address_validation import GoogleAddressValidationResponse
from webapps.google_places.domain.dtos.shared import AddressComponents
from webapps.google_places.application.dtos.address_validation import PinLocation
from webapps.google_places.domain.enums import Country


class AbstractAddressValidationDomainService(ABC):
    """Abstract interface for address validation domain service."""

    @staticmethod
    @abstractmethod
    def is_address_valid(
        validate_address_response: GoogleAddressValidationResponse,
        pin_location: PinLocation,
    ) -> bool:
        """Determine if an address is valid based on business rules."""

    @staticmethod
    @abstractmethod
    def format_address_lines(
        address_lines: list[str],
        address_components: AddressComponents,
        country: Country,
    ) -> list[str]:
        """Format address lines based on country-specific conventions."""

    @staticmethod
    @abstractmethod
    def should_skip_pin_location_validation(
        address_lines: list[str], address_components: AddressComponents
    ) -> bool:
        """Determine if pin location validation should be skipped based on business rules."""
