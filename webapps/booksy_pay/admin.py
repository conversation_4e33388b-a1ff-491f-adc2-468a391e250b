from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.pos.models import BPActivationSplashAdminProxy
from webapps.user.groups import GroupNameV2


class BPActivationSplashAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    search_fields = ['=business__id', '=operator__id']
    readonly_fields = ['business', 'operator', 'seen_at']
    list_display = ['id', 'business', 'operator', 'seen_at']


admin.site.register(BPActivationSplashAdminProxy, BPActivationSplashAdmin)
