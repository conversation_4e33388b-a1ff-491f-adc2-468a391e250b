import uuid
from dataclasses import dataclass
from datetime import date, datetime, time, timedelta
from typing import Any

from domain_services.booking.src.domains.business.service import BusinessService
from lib.feature_flag.feature.booking import UseLegacyAvailabilityAlgorithm
from webapps.booking.models import BookingSources
from webapps.user.models import User

from ..calendar.dto import ComboBooking, StaffersAvailability, TimeSlotsQuery
from ..calendar.service import CalendarService
from .aggregate.appointment import AppointmentAggregate
from .aggregate.draft import DraftAggregate
from .consts import MAX_DRAFTS_TO_REMOVE_PER_ITERATION, REMOVE_DRAFTS_OLDER_THAN
from .dto.appointment import Appointment
from .dto.draft import Draft, DraftItem
from .errors.draft import (
    DraftBelongsToAnotherUserError,
    DraftNotFoundError,
    DraftVersionMismatchError,
)
from .repository import AppointmentInfoRepository, DraftRepository


@dataclass
class AppointmentServiceConfig:
    staffers_availability_number_of_days_to_check: int = 7


class AppointmentService:
    def __init__(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        appointment_aggregate: AppointmentAggregate,
        business_service: BusinessService,
        calendar_service: CalendarService,
        draft_aggregate: DraftAggregate,
        draft_repository: DraftRepository,
        draft_appointment_info_repository: AppointmentInfoRepository,
        config: AppointmentServiceConfig = AppointmentServiceConfig(),
    ) -> None:
        self._config = config

        self._appointment_aggregate = appointment_aggregate
        self._draft_aggregate = draft_aggregate
        self._draft_repository = draft_repository
        self._draft_appointment_info_repository = draft_appointment_info_repository

        self._business_service = business_service
        self._calendar_service = calendar_service

    def get_draft(
        self,
        draft_id: uuid.UUID,
        user_id: int | None = None,
    ) -> Draft:
        draft = self._get_draft(draft_id, user_id)
        return draft

    def create_draft(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        business_id: int,
        booked_from: datetime,
        service_variant_id: int,
        staffer_id: int | None = None,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **kwargs: Any,
    ) -> Draft:
        draft = self._draft_aggregate.create(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            business_id=business_id,
            service_variant_id=service_variant_id,
            staffer_id=staffer_id,
            booked_from=booked_from,
            **kwargs,
        )

        self._draft_repository.insert(draft)

        return draft

    def check_staffers_availability(  # pylint: disable=too-many-arguments
        self,
        draft_id: uuid.UUID,
        draft_item_id: uuid.UUID,
        user: User | None = None,
        **kwargs: Any,
    ) -> StaffersAvailability:
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user.id,
        )
        return self._check_staffers_availability(draft, draft_item_id, **kwargs)

    def _check_staffers_availability(
        self,
        draft: Draft,
        draft_item_id: uuid.UUID,
        **kwargs: Any,
    ) -> StaffersAvailability:
        item = self._draft_aggregate.get_item(draft, draft_item_id)
        if not UseLegacyAvailabilityAlgorithm():
            return self._get_calendar_service_availability(draft, item)
        return self._get_legacy_item_availability(draft, item, **kwargs)

    def _is_first_item(self, draft: Draft, draft_item_id: uuid.UUID):
        return draft.items[0].id == draft_item_id

    def _get_calendar_service_availability(self, draft: Draft, item: DraftItem):
        price_list = self._business_service.get_price_list(draft.business_id)
        staffer_ids = price_list.services[item.service.variant.id].staffer_ids
        start_date = item.booked_from.date()
        end_date = start_date + timedelta(
            days=self._config.staffers_availability_number_of_days_to_check
        )
        staffers_availability = self._calendar_service.check_staffers_availability(
            time_slots_query=TimeSlotsQuery(
                business_id=draft.business_id,
                start_date=start_date,
                end_date=end_date,
                bookings=[
                    ComboBooking(
                        service_variant_id=item_.service.variant.id,
                        staffer_id=None if item_.id == item.id else item_.staffer_id,
                        addons=[],
                        combo_children=[],
                    )
                    for item_ in draft.items
                    if item_.booked_from >= item.booked_from
                ],
            ),
            staffer_ids=staffer_ids,
            current_time_slot=item.time_slot,
        )
        return staffers_availability

    def _get_legacy_item_availability(self, draft, item, **kwargs) -> StaffersAvailability:
        return self._draft_aggregate.get_draft_item_availability(
            draft=draft,
            draft_item=item,
            **kwargs,
        )

    def change_staffer(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        draft_item_id: uuid.UUID,
        staffer_id: int,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **kwargs: Any,
    ) -> Draft:
        user_id = user.id if user else None
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user_id,
            version=version,
        )
        staffers_availability = self._check_staffers_availability(
            draft=draft,
            draft_item_id=draft_item_id,
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            **kwargs,
        )
        draft = self._draft_aggregate.change_staffer(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft=draft,
            draft_item_id=draft_item_id,
            staffer_id=staffer_id,
            staffers_availability=staffers_availability,
            **kwargs,
        )

        self._draft_repository.update(draft)

        return draft

    def change_time_slot(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        slot_date: date,
        slot_time: time,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **kwargs,
    ) -> Draft:
        user_id = user.id if user else None
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user_id,
            version=version,
        )

        draft = self._draft_aggregate.change_time_slot(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft=draft,
            slot_date=slot_date,
            slot_time=slot_time,
            **kwargs,
        )

        self._draft_repository.update(draft)

        return draft

    def add_service(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        service_variant_id: int | None,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **kwargs,
    ) -> Draft:
        user_id = user.id if user else None
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user_id,
            version=version,
        )

        draft = self._draft_aggregate.add_service(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft=draft,
            service_variant_id=service_variant_id,
            **kwargs,
        )

        self._draft_repository.update(draft)

        return draft

    def reorder(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        draft_item_ids: list[uuid.UUID],
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **kwargs,
    ) -> Draft:
        user_id = user.id if user else None
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user_id,
            version=version,
        )

        draft = self._draft_aggregate.reorder(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft=draft,
            draft_item_ids=draft_item_ids,
            **kwargs,
        )

        self._draft_repository.update(draft)

        return draft

    def add_customer_note(
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        customer_note: str,
        user_id: int,
    ) -> Draft:
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user_id,
            version=version,
        )

        draft = self._draft_aggregate.add_customer_note(
            draft=draft,
            customer_note=customer_note,
        )

        self._draft_repository.update(draft)

        return draft

    def claim_draft(
        self,
        draft_id: uuid.UUID,
        user_id: int,
        version: uuid.UUID,
    ) -> Draft:
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user_id,
            version=version,
        )
        draft = self._draft_aggregate.claim_draft(draft, user_id=user_id)
        self._draft_repository.update(draft)
        return draft

    def schedule(
        self,
        draft_id: uuid.UUID,
        user_id: int | None = None,
        **kwargs,
    ) -> Appointment:
        draft = self.get_draft(draft_id, user_id)

        appointment = self._appointment_aggregate.schedule(
            draft=draft,
            **kwargs,
        )

        self._draft_appointment_info_repository.insert(appointment.id)

        return appointment

    # This function is not used via API calls but called by automatic tasks
    # We don't need to check draft owner here, as the call will be triggered
    # by system and not by user
    def remove(self, draft_id: uuid.UUID) -> None:
        self._draft_repository.remove(draft_id)

    def get_oldest_draft_ids(
        self,
        older_than: int = REMOVE_DRAFTS_OLDER_THAN,
        limit: int = MAX_DRAFTS_TO_REMOVE_PER_ITERATION,
    ) -> list[uuid.UUID]:
        return self._draft_repository.get_oldest_draft_ids(older_than=older_than, limit=limit)

    def _get_draft(
        self,
        draft_id: uuid.UUID,
        user_id: int | None = None,
        version: uuid.UUID | None = None,
    ) -> Draft:
        draft = self._draft_repository.get(draft_id)
        if not draft:
            raise DraftNotFoundError(draft_id)
        if draft.user_id and draft.user_id != user_id:
            raise DraftBelongsToAnotherUserError(draft_id)
        if version and draft.version != version:
            raise DraftVersionMismatchError(draft_id, draft.version, version)

        return draft

    def remove_draft_item(
        self,
        draft_id: uuid.UUID,
        draft_item_id: uuid.UUID,
        version: uuid.UUID,
        user: User | None = None,
        **kwargs,
    ):
        user_id = user.id if user else None
        draft = self._get_draft(
            draft_id=draft_id,
            user_id=user_id,
            version=version,
        )

        draft = self._draft_aggregate.remove_draft_item(
            draft=draft,
            draft_item_id=draft_item_id,
            **kwargs,
        )

        self._draft_repository.update(draft)

        return draft
