from functools import wraps


def grpc_service(request_serializer=None, response_serializer=None):
    """Decorator for GRPC Service methods

    message --> data --> message
    """

    def decorator(func):
        @wraps(func)
        def inner(self, request, *args, **kwargs):
            if request_serializer is not None:
                serializer = request_serializer(message=request)
                serializer.is_valid(raise_exception=True)
                request = serializer.validated_data

            response = func(self, request, *args, **kwargs)

            if response_serializer is not None:
                response = response_serializer(instance=response).message

            return response

        return inner

    return decorator
