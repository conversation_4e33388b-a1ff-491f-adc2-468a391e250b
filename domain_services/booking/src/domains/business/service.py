from typing import Protocol

from domain_services.booking.src.commons.errors import BusinessNotFoundError

from .dto import Business, PriceList, BusinessCustomerInfo
from .repository import BusinessRepository


class BusinessService(Protocol):

    def get_business(self, business_id: int) -> Business: ...

    def get_price_list(self, business_id: int) -> PriceList: ...

    def get_business_customer_info_for_user(
        self, business_id: int, user_id: int | None
    ) -> BusinessCustomerInfo | None: ...


class BusinessServiceImpl(BusinessService):
    _business_repository: BusinessRepository

    def __init__(
        self,
        business_repository: BusinessRepository,
    ) -> None:
        self._business_repository = business_repository

    def get_business(self, business_id: int) -> Business:
        business = self._business_repository.get_business(business_id=business_id)

        if not business:
            raise BusinessNotFoundError(business_id)

        return business

    def get_price_list(self, business_id: int) -> PriceList:
        return self._business_repository.get_price_list(business_id)

    def get_business_customer_info_for_user(
        self, business_id: int, user_id: int | None
    ) -> BusinessCustomerInfo | None:
        return self._business_repository.get_business_customer_info_for_user(
            business_id=business_id, user_id=user_id
        )
