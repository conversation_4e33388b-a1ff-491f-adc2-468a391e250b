from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class BillingAllowBackdatePaidTill(BooleanFlag):
    flag_name = 'Future_BillingAllowBackdatePaidTillFlag'
    adapter = FeatureFlagAdapter.LD


class BillingAllowMakeOneOffTransactionsFlag(BooleanFlag):
    flag_name = 'Future_BillingAllowMakeOneOffTransactionsFlag'
    adapter = FeatureFlagAdapter.LD


class BillingAllowModifyCardNetworkFlag(BooleanFlag):
    flag_name = 'Feature_BillingAllowModifyCardNetworkFlag'
    adapter = FeatureFlagAdapter.EPPO


class BillingAuthorizationWithTransactionFlag(BooleanFlag):
    flag_name = 'Feature_BillingAuthorizationWithTransaction'
    adapter = FeatureFlagAdapter.LD


class BillingAutoRetryChargeDateExtension(BooleanFlag):
    flag_name = 'Feature_AutoRetryChargeDateExtension'
    adapter = FeatureFlagAdapter.EPPO


class BillingDisableBTPaymentProcessor(BooleanFlag):
    flag_name = 'Future_BillingDisableBTPaymentProcessor_Flag'
    adapter = FeatureFlagAdapter.EPPO


class BillingOfflineMigrationEventFlag(BooleanFlag):
    flag_name = 'Feature_BillingOfflineMigrationEvent'
    adapter = FeatureFlagAdapter.LD


class BillingShowSpecialOfferValidationDateFlag(BooleanFlag):
    flag_name = 'Feature_BillingShowSpecialOfferValidationDateFlag'
    adapter = FeatureFlagAdapter.EPPO


class BillingSubscriptionOneDayPeriodFlag(BooleanFlag):
    flag_name = 'Feature_BillingSubscriptionOneDay'
    adapter = FeatureFlagAdapter.LD


class BillingTransactionRefundEventPubSubMessageFlag(BooleanFlag):
    flag_name = 'Feature_BillingTransactionRefundPubSubMessage'
    adapter = FeatureFlagAdapter.EPPO


class BillingUpdateTransactionInfoInvoiceItemFlag(BooleanFlag):
    flag_name = 'Feature_BillingUpdateTransactionInfoInvoiceItem'
    adapter = FeatureFlagAdapter.EPPO


class ExtoleReferralFlag(BooleanFlag):
    flag_name = 'Feature_ExtoleReferralDiscount'
    adapter = FeatureFlagAdapter.EPPO


class ForceConfirmBillingInvoiceDataFlag(BooleanFlag):
    flag_name = 'Feature_ForceConfirmBillingInvoiceDataFlag'
    adapter = FeatureFlagAdapter.EPPO


class NethoneSendDataOnSubscriptionPaymentFlag(BooleanFlag):
    flag_name = 'Feature_NethoneSendDataOnSubscriptionPaymentFlag'
    adapter = FeatureFlagAdapter.EPPO


class NewInvoicingProcessIEFlag(BooleanFlag):
    flag_name = 'Feature_NewInvoicingProcessIE'
    adapter = FeatureFlagAdapter.EPPO
