import pytest

from .....commons.types import ServiceVariantId
from ....business.dto import PriceList, Service
from ..time_slots import TimeSlotsCalculator
from ..types import BinSlot

(
    SIMPLE__S_ID,
    SIMPLE_WITH_GAP__S_ID,
) = range(1, 2 + 1)


BIN_LENGTH = 5
SERVICES = [
    Service(
        service_variant_id=SIMPLE__S_ID,
        service_id=SIMPLE__S_ID,
        duration=3 * BIN_LENGTH,
    ),
    Service(
        service_variant_id=SIMPLE_WITH_GAP__S_ID,
        service_id=SIMPLE_WITH_GAP__S_ID,
        duration=12 * BIN_LENGTH,
        gap_start_after=4 * BIN_LENGTH,
        gap_duration=6 * BIN_LENGTH,
    ),
]

PRICE_LIST = PriceList(
    services={s.service_variant_id: s for s in SERVICES},
    addons={},
    combo_children={},
)


@pytest.mark.parametrize(
    ["service_id", "expected"],
    [
        pytest.param(
            SIMPLE__S_ID,
            [(0, 3)],
            id="without padding, before=False, after=False",
        ),
        pytest.param(
            SIMPLE_WITH_GAP__S_ID,
            [(0, 4), (10, 12)],
            id="without padding, before=False, after=False",
        ),
    ],
)
def test__build_slots(
    service_id: ServiceVariantId,
    expected: list[BinSlot],
) -> None:
    actual = TimeSlotsCalculator(  # pylint: disable=protected-access
        bin_length=BIN_LENGTH,
    )._build_slots(
        service=PRICE_LIST.services[service_id],
    )
    assert actual == expected
