{% extends "admin/custom_views/generic_form_template.html" %}

{% block alert_info %}
<ol>
    <li>Filling form
        <ul>
            <li>Download template file: <br>
                <form action="" class="form-horizontal">
                    <button type="submit" name="download_template" class="btn btn-success">
                        <i class="icon-download icon-white"></i> Download template CSV
                    </button>
                </form>
                <p>Please note that only files in <strong>csv</strong> format will be accepted. Max 500 records in one batch</p>
            </li>
            <li>Fill file fields <strong>according</strong> to the template. First Row must have the same name of columns as templates:
                <ul>
                    <li><code>business_id</code> (number) - Business ID</li>
                    <li><code>duration</code> (optional) - Trial duration or end date. Supports multiple formats:
                        <ul>
                            <li><strong>Number</strong> - Days from upload date (e.g., <code>14</code> for 14 days)</li>
                            <li><strong>Date</strong> - Specific end date in format YYYY-MM-DD or DD/MM/YYYY (e.g., <code>2024-12-31</code>)</li>
                            <li><strong>Empty</strong> - Defaults to 14 days from upload date</li>
                        </ul>
                    </li>
                </ul>
                <p><strong>Note:</strong> Trial period starts from the <strong>upload date</strong>, not when the business logs in.</p>
            </li>
        </ul>
    </li>
</ol>
{% endblock alert_info %}
