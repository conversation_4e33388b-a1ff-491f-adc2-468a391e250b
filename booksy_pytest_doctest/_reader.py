import abc
from collections.abc import Iterable
from dataclasses import dataclass
from pathlib import Path


@dataclass(kw_only=True, frozen=True)
class Ignore:
    collect: str = ""
    testid: str = ""

    def __post_init__(self) -> None:
        assert self.collect or self.testid


class Reader(abc.ABC):
    @abc.abstractmethod
    def read(self) -> Iterable[Ignore]: ...


class DoctestignoreReader(Reader):
    def __init__(self, filepath: str | Path):
        self._filepath = Path(filepath)

    def read(self) -> Iterable[Ignore]:
        with self._filepath.open() as ignorefile:
            for line in ignorefile:
                line = line.strip()
                if not line:
                    continue
                if self._is_test_identifier(line):
                    yield Ignore(testid=line)
                else:
                    yield Ignore(collect=line)

    @staticmethod
    def _is_test_identifier(line: str) -> bool:
        # filename::objectname
        # e.g. webapps/notification/base.py::webapps.notification.base.BaseNotification
        return "::" in line
