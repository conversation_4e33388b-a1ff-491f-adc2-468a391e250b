default_language_version:
    python: python3.11
files: ^domain_services/booking/
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
        files: ^domain_services/booking/
  - repo: https://github.com/psf/black-pre-commit-mirror
    rev: 24.4.2
    hooks:
      - id: black
        name: black (booking)
        require_serial: true
        types_or: [python, pyi]
  - repo: local
    hooks:
      - id: pylint
        name: pylint (booking)
        entry: pylint
        language: system
        types: [python, pyi]
        require_serial: true
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: isort (booking)
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.2.0
    hooks:
      - id: conventional-pre-commit
        stages: [commit-msg]
