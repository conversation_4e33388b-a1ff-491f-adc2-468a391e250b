cliapps/scenarios_for_translators.py
lib/abc.py::lib.abc.abstractclassattribute
lib/abc.py::lib.abc.abstractclassmethod
lib/abc.py::lib.abc.isinstantiable
lib/capping.py::lib.capping.apply_capping
lib/elasticsearch/tests/business_es_test_tools.py
lib/events.py::lib.events.EventSignal
lib/fields/date_time_interval.py::lib.fields.date_time_interval.DatetimeIntervalField
lib/fields/geo_location.py::lib.fields.geo_location.GeoLocationField
lib/fields/geo_location.py::lib.fields.geo_location.GeoLocationViewportField
lib/google_oauth/service.py::lib.google_oauth.service.ServiceAccount.authorized_request
lib/jinja_renderer.py
lib/jinja_renderer.py::lib.jinja_renderer.local_short_time
webapps/admin_extra/versum_data_parser.py
webapps/admin_extra/views/adyen.py::webapps.admin_extra.views.adyen.AdyenPayoutReportView._AdyenPayoutReportView__calculate_settle_amount
webapps/adyen/bases.py::webapps.adyen.bases.IndexedEnum
webapps/adyen/bases.py::webapps.adyen.bases._IndexedEnumMeta
webapps/adyen/flow.py::webapps.adyen.flow.auth
webapps/adyen/flow.py::webapps.adyen.flow.cancel_or_refund
webapps/adyen/flow.py::webapps.adyen.flow.capture
webapps/adyen/flow.py::webapps.adyen.flow.disable
webapps/adyen/flow.py::webapps.adyen.flow.first_auth_ee
webapps/adyen/flow.py::webapps.adyen.flow.refund
webapps/adyen/helpers.py::webapps.adyen.helpers.cents_to_float_amount
webapps/adyen/helpers.py::webapps.adyen.helpers.float_amount_to_cents
webapps/booking/tests/utils.py
webapps/booking/tests/utils.py::webapps.booking.tests.utils.create_appointment
webapps/business/_admin/views.py::webapps.business._admin.views.BusinessAdmin.has_elastic_errors
webapps/business/elasticsearch/tools.py::webapps.business.elasticsearch.tools.compute_booking_rates
webapps/celery/events.py
webapps/donation/models.py
webapps/experiment_v3/exp/base.py::webapps.experiment_v3.exp.base.BaseExperiment
webapps/family_and_friends/helpers/appointment.py::webapps.family_and_friends.helpers.appointment.family_and_friends_role_case_expr
webapps/feeds/google/proto_py/health_pb2.py
webapps/feeds/google/proto_py/health_pb2_grpc.py
webapps/navision/documents.py::webapps.navision.documents.calculate_sum_with_discount
webapps/notification/base.py::webapps.notification.base.BaseNotification
webapps/pos/calculations.py::webapps.pos.calculations.calculate_amount_before_rate
webapps/pos/calculations.py::webapps.pos.calculations.calculate_discount_amount_for_rate
webapps/structure/baker_recipes.py::webapps.structure.baker_recipes.bake_region_graphs
webapps/urlinator/models.py
domain_services/booking/scripts/compare_requirements.py
lib/x_version_compatibility/tests/test_compatibilities/__init___.py
settings/elasticsearch.py
webapps/booking/baker_recipes.py
webapps/booking/v2/application/http_api/response/calendar.py
webapps/booking/v2/application/http_api/tests/utils/decorator.py
webapps/booking/v2/application/http_api/view/utils.py
webapps/booking/v2/commons/errors.py
webapps/booking/v2/commons/utils.py
webapps/booking/v2/domains/user/outer/repository.py
webapps/booking/v2/domains/user/outer/service.py
webapps/booking/v2/targeting/domain/dto/provider_feature_checklist.py
webapps/booking/v2/targeting/domain/factory/provider_feature_checklist.py
webapps/booking/v2/targeting/domain/repository/provider_feature_checklist.py
webapps/booking/v2/targeting/domain/service/provider.py
webapps/booking/views/response/business_service_variants_time_slots.py
webapps/business/v2/infrastructure/facades/import_notifications.py
webapps/google_places/presentation/urls.py
webapps/message_blast/content/base.py
webapps/payment_providers/serializers.py