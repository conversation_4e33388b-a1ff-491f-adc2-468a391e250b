from decimal import Decimal
from unittest.mock import patch

from django.test import TestCase
from model_bakery import baker

from lib.tools import tznow
from webapps.billing.enums import TransactionStatus
from webapps.billing.models import BillingBoostPaymentRequest, BillingBoostTransaction
from webapps.billing.serializers.boost.overdue import BoostOverdueRetryChargeSerializer
from webapps.boost.apis.overdue_transactions import BoostTransaction
from webapps.business.baker_recipes import business_recipe


class TestBoostOverdueRetryChargeSerializer(TestCase):
    serializer = BoostOverdueRetryChargeSerializer

    def setUp(self):
        super().setUp()
        self.business = business_recipe.make()

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_validation_ok(self, mocked_interface):
        boost_transactions = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
                currency='pln',
            ),
            BoostTransaction(
                public_id='999',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(11),
                appointments=[],
                description='description',
                created=tznow(),
                currency='pln',
            ),
        ]
        mocked_interface.return_value = boost_transactions

        serializer = self.serializer(data={}, business_id=self.business.id)
        self.assertTrue(serializer.is_valid())

        self.assertEqual(serializer.total_amount, Decimal(123 + 11))
        self.assertEqual(serializer.boost_transactions, boost_transactions)
        self.assertEqual(serializer.currency, 'pln')

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_validation_ok_with_selected_ids(self, mocked_interface):
        boost_transactions = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
            BoostTransaction(
                public_id='567',
                charge_lock_key='567',
                business_id=self.business.id,
                gross_amount=Decimal(11),
                appointments=[],
                description='description',
                created=tznow(),
            ),
        ]
        mocked_interface.return_value = boost_transactions

        serializer = self.serializer(
            data={'selected_boost_ids': ['567']},
            business_id=self.business.id,
        )
        self.assertTrue(serializer.is_valid())

        self.assertEqual(serializer.total_amount, Decimal(11))
        self.assertEqual(serializer.boost_transactions, [boost_transactions[1]])

    def test_validation_duplicated_id_with_selected_ids(self):
        serializer = self.serializer(
            data={'selected_boost_ids': ['123', '123']},
            business_id=self.business.id,
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            'Duplicated transactions.',
            serializer.errors['selected_boost_ids'][0],
        )

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_validation_missing_id_with_selected_ids(self, mocked_interface):
        mocked_interface.return_value = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
        ]

        serializer = self.serializer(
            data={'selected_boost_ids': ['1234']},
            business_id=self.business.id,
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            'At least one transaction is missing.',
            serializer.errors['non_field_errors'][0],
        )

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_empty_balance(self, mocked_interface):
        mocked_interface.return_value = []
        serializer = self.serializer(data={}, business_id=self.business.id)

        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            'You currently have no overdue payments.',
            serializer.errors['non_field_errors'][0],
        )

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_currently_processing(self, mocked_interface):
        mocked_interface.return_value = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
            BoostTransaction(
                public_id='999',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
        ]
        baker.make(
            BillingBoostPaymentRequest,
            business=self.business,
            status=TransactionStatus.INITIAL,
        )

        serializer = self.serializer(data={}, business_id=self.business.id)

        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            'The payment process has already started. Wait for it to complete or contact '
            'customer service if the error persists.',
            serializer.errors['non_field_errors'][0],
        )

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_one_of_transaction_already_charged(self, mocked_interface):
        mocked_interface.return_value = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
            BoostTransaction(
                public_id='999',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
        ]
        baker.make(
            BillingBoostTransaction,
            boost_id='999',
            status=TransactionStatus.CHARGED,
        )

        serializer = self.serializer(data={}, business_id=self.business.id)

        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            'Some transactions have already been paid. If the error still occurs please '
            'contact customer service.',
            serializer.errors['non_field_errors'][0],
        )
