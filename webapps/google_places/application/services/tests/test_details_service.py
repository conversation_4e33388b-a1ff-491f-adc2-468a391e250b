from unittest.mock import Mock
import pytest

from webapps.google_places.application.dtos.google_place_details import GooglePlaceDetails
from webapps.google_places.application.services.details import DetailsServiceV2
from webapps.google_places.domain.dtos.shared import AddressComponents, Location
from webapps.google_places.domain.enums import Country
from webapps.google_places.domain.ports.address_validation_service import (
    AbstractAddressValidationDomainService,
)


class TestDetailsServiceV2:

    @pytest.fixture
    def mock_gateway(self):
        return Mock()

    @pytest.fixture
    def mock_settings_provider(self):
        mock_provider = Mock()
        mock_provider.get_settings.return_value.api_country = Country.US
        return mock_provider

    @pytest.fixture
    def mock_address_validation_service(self):
        return Mock(spec=AbstractAddressValidationDomainService)

    @pytest.fixture
    def details_service_v2(
        self, mock_gateway, mock_settings_provider, mock_address_validation_service
    ):
        return DetailsServiceV2(
            gateway=mock_gateway,
            settings_provider=mock_settings_provider,
            address_validation_service=mock_address_validation_service,
        )

    def test_get_details_preserves_existing_address_lines(self, details_service_v2, mock_gateway):
        """Test that existing address lines are preserved."""
        google_place = GooglePlaceDetails(
            place_id="test_place_id",
            name="Test Place",
            formatted_address="123 Main Street, Test City, NY 12345, USA",
            location=Location(latitude=40.7128, longitude=-74.0060),
            address_components=AddressComponents(
                "123", "Main Street", "Test City", "US", "12345", "Apt 4B"
            ),
            address_lines=["123 Main Street"],
            administrative_area="NY",
        )
        mock_gateway.get_details.return_value = google_place

        result = details_service_v2.get_details("test_place_id")

        assert result.address_lines == ["123 Main Street"]

    def test_get_details_formats_missing_address_lines(
        self, details_service_v2, mock_gateway, mock_address_validation_service
    ):
        google_place = GooglePlaceDetails(
            place_id="test_place_id",
            name="Test Place",
            formatted_address="123 Main Street, Test City, NY 12345, USA",
            location=Location(latitude=40.7128, longitude=-74.0060),
            address_components=AddressComponents(
                "123", "Main Street", "Test City", "US", "12345", "Apt 4B"
            ),
            address_lines=None,
            administrative_area="NY",
        )
        mock_gateway.get_details.return_value = google_place
        mock_address_validation_service.format_address_lines.return_value = [
            "123 Main Street",
            "Apt 4B",
        ]

        result = details_service_v2.get_details("test_place_id")

        assert result.address_lines == ["123 Main Street", "Apt 4B"]
        mock_address_validation_service.format_address_lines.assert_called_once()
