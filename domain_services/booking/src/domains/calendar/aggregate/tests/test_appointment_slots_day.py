import pytest

from ...dto import ComboBooking
from ..time_slots import TimeSlotsCalculator
from ..types import MatchingBinsMap
from .commons import (
    APPLIANCE_ID,
    BIN_LENGTH,
    PRICE_LIST,
    RESOURCES_BOOKED_OCCUPANCY_MAP,
    SERVICE__APPLIANCE_ONLY__ID,
    SERVICE__WITH_APPLIANCE__ID,
    SERVICE_ID,
    STAFFER_1_ID,
    STAFFER_2_ID,
)

TIME_SLOTS_CALCULATOR = TimeSlotsCalculator(bin_length=BIN_LENGTH)


@pytest.mark.parametrize(
    ["bookings", "expected"],
    [
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE_ID,
                    staffer_id=None,
                ),
            ],
            {
                12: {STAFFER_1_ID},
                16: {STAFFER_1_ID, STAFFER_2_ID},
            },
            id="service without appliance, no staffer selected",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE_ID,
                    staffer_id=STAFFER_2_ID,
                ),
            ],
            {
                16: {STAFFER_2_ID},
            },
            id="service without appliance, staffer selected",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                    staffer_id=None,
                ),
            ],
            {
                12: {STAFFER_1_ID, APPLIANCE_ID},
            },
            id="service with appliance",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                    staffer_id=STAFFER_2_ID,
                ),
            ],
            {},
            id="service with appliance no slots available",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__APPLIANCE_ONLY__ID,
                    staffer_id=None,
                ),
            ],
            {
                12: {APPLIANCE_ID},
            },
            id="service with appliance only",
        ),
    ],
)
def test__find_day_bin_slots(
    bookings: list[ComboBooking],
    expected: MatchingBinsMap,
) -> None:
    actual = TimeSlotsCalculator(  # pylint: disable=protected-access
        bin_length=BIN_LENGTH,
    )._find_day_bin_slots(
        bookings=bookings,
        resources_booked_occupancy_map=RESOURCES_BOOKED_OCCUPANCY_MAP,
        price_list=PRICE_LIST,
    )
    assert actual == expected
