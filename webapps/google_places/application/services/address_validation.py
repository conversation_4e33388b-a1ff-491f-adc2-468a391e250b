from v2.domain.interfaces.settings import SettingsProvider
from v2.infrastructure.translation import Translator
from webapps.google_places.application.dtos.address_validation import (
    AddressValidationRequestData,
    AddressValidationResponse,
    PinLocation,
)
from webapps.google_places.domain.enums import PinLocationStatus
from webapps.google_places.application.ports.address_validation import (
    AbstractAddressValidationService,
)
from webapps.google_places.domain.dtos.address_validation import GoogleAddressValidationResponse
from webapps.google_places.domain.ports.address_validation_client import (
    ValidateAddressParams,
    AddressData,
)
from webapps.google_places.domain.ports.address_validation_gateway import (
    GoogleAddressValidationGateway,
)
from webapps.google_places.domain.services.pin_location_validation_service import (
    PinLocationValidationService,
)
from webapps.google_places.domain.ports.address_validation_service import (
    AbstractAddressValidationDomainService,
)
from webapps.google_places.domain.dtos.shared import Location
from webapps.google_places.domain.dtos.validation_context import PinValidationContext


class AddressValidationService(AbstractAddressValidationService):
    CONTEXT = 'google_places'

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    def __init__(
        self,
        google_gateway: GoogleAddressValidationGateway,
        settings_provider: SettingsProvider,
        pin_location_validation_service: PinLocationValidationService,
        address_validation_service: AbstractAddressValidationDomainService,
        translator: Translator,
    ):
        self._google_gateway = google_gateway
        self._settings_provider = settings_provider
        self._pin_location_validation_service = pin_location_validation_service
        self._address_validation_service = address_validation_service
        self._translator = translator

    def _prepare_validate_address_params(
        self, address_to_validate: AddressValidationRequestData
    ) -> ValidateAddressParams:
        """Prepare the parameters for address validation."""
        country = self._settings_provider.get_settings().api_country
        formatted_address_lines = self._address_validation_service.format_address_lines(
            address_lines=address_to_validate.address_lines,
            address_components=address_to_validate.address_components,
            country=country,
        )
        address_data = AddressData(
            region_code=country,
            postal_code=address_to_validate.address_components.zipcode,
            administrative_area=address_to_validate.administrative_area or "",
            address_lines=formatted_address_lines,
            city=address_to_validate.address_components.city,
        )

        return ValidateAddressParams(
            address=address_data,
            previous_response_id=address_to_validate.previous_response_id or "",
        )

    def _validate_pin_location_with_regional_rules(
        self, address_to_validate: AddressValidationRequestData, validated_location: Location
    ) -> PinLocation:
        if self._address_validation_service.should_skip_pin_location_validation(
            address_lines=address_to_validate.address_lines,
            address_components=address_to_validate.address_components,
        ):
            return PinLocation(status=PinLocationStatus.OK, text="Pin location validation skipped")

        return self._pin_location_validation_service.validate_pin_location(
            user_location=address_to_validate.location,
            validated_address_location=validated_location,
            context=PinValidationContext(
                country_code=self._settings_provider.get_settings().api_country,
            ),
        )

    def _translate_pin_location_text(self, pin_location: PinLocation):
        translated_text = self._translator.gettext(pin_location.text, self.CONTEXT)
        return PinLocation(status=pin_location.status, text=translated_text)

    def validate_address(
        self, address_to_validate: AddressValidationRequestData, session_id: str | None = None
    ) -> AddressValidationResponse:
        validate_address_params = self._prepare_validate_address_params(address_to_validate)
        validation_result: GoogleAddressValidationResponse = self._google_gateway.validate_address(
            validate_address_params=validate_address_params,
            session_id=session_id,
        )

        pin_location = self._validate_pin_location_with_regional_rules(
            address_to_validate=address_to_validate,
            validated_location=validation_result.location,
        )

        is_address_valid = self._address_validation_service.is_address_valid(
            validation_result, pin_location
        )

        return AddressValidationResponse.from_domain(
            validation_result=validation_result,
            is_valid=is_address_valid,
            pin_location=self._translate_pin_location_text(pin_location),
        )
