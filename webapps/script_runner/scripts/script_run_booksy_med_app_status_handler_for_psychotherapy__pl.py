import logging

from django.conf import settings
from country_config.enums import Country
from webapps.public_partners.models.oauth2 import OAuth2Installation
from webapps.script_runner.mixins import ReusableScript
from webapps.script_runner.runners import DBScriptRunner
from lib.db import using_db_for_reads, READ_ONLY_DB
from lib.tools import chunker
from webapps.segment.facade import SegmentFacade
from webapps.business.adapters import CancellationReasonAdapter
from lib.segment_analytics.api import SegmentAnalyticsWrapper
from lib.segment_analytics.context import AnalyticsContext
from lib.segment_analytics.enums import EventType

logger = logging.getLogger('booksy.script_runner')

CHUNK_SIZE = 100


def get_booksy_med_installations():
    return OAuth2Installation.objects.filter(
        deleted__isnull=True,
        application__client_id=settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID,
        business__deleted__isnull=True,
        parent_id__isnull=True,
    ).select_related('business', 'business__owner')


class Script(DBScriptRunner, ReusableScript):
    version = 2

    def run(self):
        if settings.API_COUNTRY != Country.PL:
            return

        logger.warning(
            "Starting booksy_med_app_status_handler execution for psychotherapy businesses"
        )

        with using_db_for_reads(READ_ONLY_DB):
            installations = get_booksy_med_installations()
            total_count = installations.count()

            logger.warning("Found %s businesses with psychotherapy category", total_count)

            processed_count = 0
            error_count = 0

            for installation_chunk in chunker(installations.iterator(), CHUNK_SIZE):
                for installation in installation_chunk:
                    try:
                        analytics_context = AnalyticsContext(
                            business=installation.business,
                            event_type=EventType.BUSINESS,
                        )

                        analytics = SegmentAnalyticsWrapper(analytics_context)

                        service = SegmentFacade(
                            analytics,
                            CancellationReasonAdapter(),
                        )

                        service.booksy_med_app_status(
                            email=installation.business.owner.email,
                            status=installation.status,
                        )

                        processed_count += 1

                        if processed_count % 100 == 0:
                            logger.warning(
                                "Processed %s/%s businesses", processed_count, total_count
                            )

                    except Exception as e:
                        error_count += 1
                        logger.error(
                            "Error processing business %s: %s",
                            installation.business.id,
                            e,
                            exc_info=True,
                        )

        logger.warning(
            "Script completed. Processed: %s, Errors: %s, Total: %s",
            processed_count,
            error_count,
            total_count,
        )
