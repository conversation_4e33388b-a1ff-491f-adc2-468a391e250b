from pathlib import Path

import pytest


@pytest.mark.parametrize(
    ["ref_file", "target_file", "expected_empty"],
    [
        pytest.param(
            "",
            "",
            True,
            id="empty",
        ),
        pytest.param(
            "bitarray==2.9.2",
            "bitarray==2.9.2",
            True,
            id="same content",
        ),
        pytest.param(
            "bitarray==2.9.2",
            "bitarray==2.9.1",
            False,
            id="differing version",
        ),
        pytest.param(
            "bitarray==2.9.2\nDjango==4.2.14",
            "bitarray==2.9.2\npydantic==2.5.2",
            True,
            id="happy path",
        ),
    ],
)
def test__compare_requirmenets(
    tmp_path: Path,
    ref_file: str,
    target_file: str,
    expected_empty: bool,
) -> None:
    """This test is a bit hacky to avoid adding not domain package to monolith requirements"""
    try:
        from ...scripts.compare_requirements import RequirementsDiff, compare_requirmenets

        ref_path = tmp_path / "ref_reqirements.txt"
        target_path = tmp_path / "target_reqirements.txt"
        expected = (
            []
            if expected_empty
            else [
                RequirementsDiff(
                    name="bitarray",
                    ref_version="2.9.2",
                    target_version="2.9.1",
                )
            ]
        )

        with ref_path.open("w") as _file:
            _file.write(ref_file)
        with target_path.open("w") as _file:
            _file.write(target_file)

        actual = compare_requirmenets(ref_path, target_path)

        assert actual == expected
    except ImportError:
        ...
