import pytest

from .....commons.types import ResourceId
from ...dto import ComboBooking
from ..aggregate import CalendarAggregateImpl
from .commons import (
    APPLIANCE_ID,
    PRICE_LIST,
    SERVICE__APPLIANCE_ONLY__ID,
    SERVICE__WITH_APPLIANCE__ID,
    SERVICE_ID,
    STAFFER_1_ID,
    STAFFER_2_ID,
)


@pytest.mark.parametrize(
    ["bookings", "expected"],
    [
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE_ID,
                    staffer_id=None,
                ),
            ],
            {STAFFER_1_ID, STAFFER_2_ID},
            id="no staffer selected",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE_ID,
                    staffer_id=STAFFER_2_ID,
                ),
            ],
            {STAFFER_2_ID},
            id="staffer selected",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                    staffer_id=None,
                ),
            ],
            {STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID},
            id="service with appliance, no staffer selected",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                    staffer_id=STAFFER_2_ID,
                ),
            ],
            {STAFFER_2_ID, APPLIANCE_ID},
            id="service with appliance, staffer selected",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__APPLIANCE_ONLY__ID,
                    staffer_id=None,
                ),
            ],
            {APPLIANCE_ID},
            id="service with appliance only, no staffer selected",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__APPLIANCE_ONLY__ID,
                    staffer_id=STAFFER_2_ID,
                ),
            ],
            {STAFFER_2_ID, APPLIANCE_ID},
            id="service with appliance only, staffer selected",
        ),
    ],
)
def test__get_extract_resources_ids(
    bookings: list[ComboBooking],
    expected: set[ResourceId],
) -> None:
    actual = CalendarAggregateImpl.extract_resources_ids(
        bookings=bookings,
        price_list=PRICE_LIST,
    )
    assert actual == expected
