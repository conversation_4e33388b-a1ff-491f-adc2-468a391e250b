from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, StringFlag


class RemoveFRSuffixFromSMSBodyFlag(BooleanFlag):
    flag_name = 'Feature_RemoveFRSuffixFromSMSBodyFlag'
    adapter = FeatureFlagAdapter.EPPO


class SaveSmsNotificationHistoryViaCeleryFlag(BooleanFlag):
    flag_name = 'Feature_SaveSmsNotificationHistoryViaCelery'
    adapter = FeatureFlagAdapter.LD


class OTPSimpleThrottleRateFlag(StringFlag):
    flag_name = 'Feature_OTPSimpleThrottleRate'
    adapter = FeatureFlagAdapter.EPPO


class OTPPhoneThrottleRateFlag(StringFlag):
    flag_name = 'Feature_OTPPhoneThrottleRate'
    adapter = FeatureFlagAdapter.EPPO


class AdditionalBookingReminderExperiment(StringFlag):
    flag_name = 'Experiment_AdditionalBookingReminder'
    adapter = FeatureFlagAdapter.EPPO


class EnableSendingAdditionalBookingReminderFlag(BooleanFlag):
    flag_name = 'Feature_EnableSendingAdditionalBookingReminder'
    adapter = FeatureFlagAdapter.EPPO


class SendTippingAppetiteExperimentPushFlag(BooleanFlag):
    flag_name = 'Feature_SendTippingAppetiteExperimentPush'
    adapter = FeatureFlagAdapter.EPPO


class DontSendCustomerConfirmationSMSForBBsWithUserFlag(BooleanFlag):
    flag_name = 'Feature_DontSendCustomerConfirmationSMSForBBsBCIsWithUser'
    adapter = FeatureFlagAdapter.EPPO


class RemoveOnboardingNotificationsFlag(BooleanFlag):
    flag_name = 'Feature_RemoveOnboardingNotifications'
    adapter = FeatureFlagAdapter.EPPO


class MoveToSettingsSMSVonageUseRotatingCredentialsFlag(BooleanFlag):
    flag_name = 'Feature_MoveToSettingsSMSVonageUseRotatingCredentials'
    adapter = FeatureFlagAdapter.EPPO


class MoveToSettingsSMSProfilesPerCountryFlag(BooleanFlag):
    flag_name = 'Feature_MoveToSettingsSMSProfilesPerCountryFlag'
    adapter = FeatureFlagAdapter.EPPO


class MoveToEppoSMSServiceNameFlag(BooleanFlag):
    flag_name = 'Feature_MoveToEppoSMSServiceName'
    adapter = FeatureFlagAdapter.EPPO


class SMSServiceProviderFlag(StringFlag):
    flag_name = 'Feature_SMSServiceProvider'
    adapter = FeatureFlagAdapter.EPPO


class NotifyOwnerThroughEmailAboutNewBookingFlag(BooleanFlag):
    """
    Send email notification about a new booking to the owner.
    """

    flag_name = 'Feature_NotifyOwnerThroughEmailAboutNewBooking'
    adapter = FeatureFlagAdapter.EPPO


class DontNotifyOwnerThroughEmailAboutNewBookingFlag(BooleanFlag):
    """
    Reverse logic of the previous flag. We expect that the value of the flag may be read before
    being properly evaluated. We are reversing the behaviour with this flag and adding additional
    debug loggin.
    """

    flag_name = 'Feature_DontNotifyOwnerThroughEmailAboutNewBooking'
    adapter = FeatureFlagAdapter.EPPO


class HandleIterablePushWebhooksFlag(BooleanFlag):
    flag_name = 'Feature_HandleIterablePushWebhooks'
    adapter = FeatureFlagAdapter.EPPO


class DontSendCustomerBookingChangeSMS(BooleanFlag):
    flag_name = 'Feature_DontSendCustomerBookingChangeSMS'
    adapter = FeatureFlagAdapter.EPPO
