from django.conf import settings
from django.urls import include, path

from drf_api.service.apple.views import AppleConnectCustomerView, AppleIDSignInCustomerView
from drf_api.service.booksy_pay.views import (
    BooksyPayDryRunViewSet,
    BooksyPayLateCancellationWindowViewSet,
    BooksyPaySettingsViewSet,
    BooksyPayViewSet,
)
from drf_api.service.business.account.views import BusinessAccountSessionStatusView
from drf_api.service.business.views.business_utils import (
    CheckNewGeolocationView,
    IterableJWTTokenView,
)
from drf_api.service.business.views.business_visibility import MyBusinessVisibilityView
from drf_api.service.customer.views.customer_business_views import (
    BusinessContactInfoView,
    CustomerBusinessPaymentOptionsView,
    BooksyOmnibusConsentContentView,
    BusinessCategoriesView,
)
from drf_api.service.customer.views.customer_favorite_categories_view import (
    CustomerFavoriteCategoriesView,
)
from drf_api.service.customer.views.customer_history_views import CustomerHistoryView
from drf_api.service.customer.views.customer_preferences_views import CustomerPreferencesUpdateView
from drf_api.service.customer.views.customer_quick_sign_in_up_view import CustomerQuickSignInUpView
from drf_api.service.customer.views.email_change_view import CustomerEmailChangeView
from drf_api.service.customer.views.my_booksy_selected_for_you import (
    CustomerMyBooksySelectedForYouView,
)
from drf_api.service.customer.views.people_also_booked_views import PeopleAlsoBookedView
from drf_api.service.facebook.views import (
    FacebookBusinessConnectView,
    FacebookCustomerConnectView,
    FacebookSignInUpBusinessView,
    FacebookSignInUpCustomerView,
    FacebookGraphSignInUpCustomerView,
)
from drf_api.service.family_and_friends.views import (
    FamilyAndFriendsMemberDetailsViewSet,
    FamilyAndFriendsMemberViewSet,
    InvitationViewSet,
    MatchUserInvitationViewSet,
    MemberPhotoViewSet,
    UnlinkViewSet,
)
from drf_api.service.firebase.views import (
    AndroidFirestoreConfigView,
    IosFirestoreConfigView,
    WebFirestoreConfigView,
)
from drf_api.service.migrator.views import MigratorCreatePOSWalletHandler
from drf_api.service.my_booksy.views import MyBooksyPopUpNotificationView
from drf_api.service.notification.webhooks import PushConfirmationWebhookView, VonageWebhookView
from drf_api.service.other.content_reports.views import (
    ReportInappropriateContentBusinessView,
    ReportInappropriateContentCustomerDSAView,
    ReportInappropriateContentCustomerView,
)
from drf_api.service.other.debug.feature_flags import DrfFFView, DrfNoFFView
from drf_api.service.other.debug.views import (
    DebugEppo,
    DebugExceptionView,
    DebugSleepView,
    DebugStatsView,
    DebugTimeoutView,
    health_check,
    liveness_check,
    redis_priority_monitor,
    warmup,
)
from drf_api.service.other.routing.views import ConfigView
from drf_api.service.other.ui_text.views import UITextView
from drf_api.service.splash.views.calendar_splash import (
    CalendarSplashView,
    ChangePrepaymentView,
)
from drf_api.service.splash.views.post_checkout_splash import PostCheckoutSplashView
from drf_api.service.splash.views.profile_and_settings_splash import ProfileAndSettingsSplashView
from drf_api.service.splash.views.ttp_reminder_splash import TTPReminderSplashView
from drf_api.service.unsubscribe.views import UnsubscribeBlastsView, UnsubscribeView
from drf_api.service.user.views import UserAccountDeletionViewSet
from drf_api.service.whats_new.views import BusinessWhatsNewListView, CustomerWhatsNewListView
from drf_api.service.zowie_integration.views import GenerateZowieJWTTokenView
from service.voucher.business_voucher_additional_info import BusinessVoucherAdditionalInfoView
from service.voucher.business_voucher_order_detail import BusinessVoucherOrderDetailView
from service.voucher.business_voucher_orders import BusinessVoucherOrderListingView
from service.voucher.business_voucher_orders_finalize import BusinessVoucherOrdersFinalizeView
from service.voucher.customer_voucher_additional_info import CustomerVoucherAdditionalInfoView
from service.voucher.voucher_simple_redeem import VoucherSimpleRedeemView
from service.voucher.voucher_void import VoucherVoidView
from sre_performance.views import ArchivedBookingChangesView, BranchInfoView
from webapps.billing.views.config import BillingCountryConfigView
from webapps.billing.views.extole_webhook import ExtoleWebhookAPIView
from webapps.booking.v2.application.http_api.view.appointment_compatibility import (
    AppointmentCompatibilityView,
)
from webapps.business.v2.business.presentation.views.my_businesses import MyBusinessesView
from webapps.business.v2.services.presentation.views.service_categories import ServiceCategoriesView
from webapps.business.views.before_churn_path_support import BeforeChurnPathSupportView
from webapps.otp.views import ResendOTPCodeAPIView
from webapps.pos.tipping_experiment.tipping_appetite_splash import (
    TippingAppetiteDecisionView,
    TippingAppetiteSplashView,
)
from webapps.stats_and_reports.v2.infra.staffer_stats_view import StafferStatsView

from webapps.user.v2.application.views.user_exists import UserExistsBusinessView
from webapps.business.views.automation_tests import AutomationTestsSetupView
from webapps.business_customer_info.views.customer_agreements import BusinessCustomerAgreementsView

from webapps.business.views.fizjo import CheckFizjoAccessView
from webapps.business.views.query_hints import (
    FocusOnQueryView,
    ModernQueryHintsView,
    RecommendedTreatmentsView,
)
from webapps.business.views.quick_invite import BusinessCustomerInfoQuickInviteView
from webapps.business.views.retrial import ReTrialAttemptView
from webapps.business.views.service_type import ServiceTypesView
from webapps.business.views.settings_banners import SettingsBannersView
from webapps.text_invite.application.views.text_invite import BusinessInviteTextView
from webapps.business_consents.views import ListUpdateBusinessConsentsAPIView
from webapps.staff_permission_overrides.views import PermissionOverrideDefaultsView
from webapps.google_sign_in.views import (
    GoogleOneTapConnectBusinessView,
    GoogleOneTapConnectCustomerView,
    GoogleOneTapSignInBusinessView,
    GoogleOneTapSignInCustomerView,
)
from webapps.hints.views import HintsListView, StoriesListView
from webapps.profile_completeness.views import PaymentActivatorsTilesView
from webapps.stripe_integration.views import (
    StripeAccountNotificationView,
    StripeConnectNotificationView,
)
from webapps.stripe_terminal.views.hardware import (
    HardwareDetailsView,
    HardwareFeaturesListView,
    HardwareListView,
)
from webapps.stripe_terminal.views.order import OrderDetailsView, OrdersView, ShippingMethodsView
from webapps.stripe_terminal.views.order_payment import OrderPaymentDetailsView, OrderPaymentsView
from webapps.stripe_terminal.views.webhooks import (
    StripeHardwareOrderWebhookView,
    StripeHardwarePaymentWebhookView,
)
from webapps.survey.views import CustomerPollViewSet
from webapps.user.views import ColorPaletteView
from webapps.voucher.views import (
    BusinessVoucherBackgroundListView,
    BusinessVoucherDownloadDetailView,
    CustomerGiftCardViewSet,
    VoucherCheckoutView,
    VoucherConfigView,
    VoucherPurchaseMethodsSettingsView,
)
from webapps.wait_list.views import WaitListViewSet

urlpatterns = [
    path('routing/config/', ConfigView.as_view(), name='config'),
    path('routing/config/billing/', BillingCountryConfigView.as_view(), name='config_billing'),
    path('health_check/', health_check, name='health_check'),
    path('liveness_check/', liveness_check, name='liveness_check'),
    path('warmup/', warmup, name='core-warmup'),
    path('utils/', include('drf_api.service.utils.urls'), name='utils'),
    path('otp/resend/', ResendOTPCodeAPIView.as_view(), name='otp_resend'),
    path(
        'business_api/auth/',
        include('drf_api.service.auth.urls'),
    ),
    path(
        'business_api/firestore/config/web/',
        WebFirestoreConfigView.as_view(),
        name='firestore_config_web',
    ),
    path(
        'business_api/firestore/config/android/',
        AndroidFirestoreConfigView.as_view(),
        name='firestore_config_android',
    ),
    path(
        'business_api/firestore/config/ios/',
        IosFirestoreConfigView.as_view(),
        name='firestore_config_ios',
    ),
    path(
        'priority_queue_monitor/<str:priority_type>/',
        redis_priority_monitor,
        name='priority_queue_monitor',
    ),
    path('other/drf/with_ff/', DrfFFView.as_view(), name='drf_with_ff'),
    path('other/drf/without_ff/', DrfNoFFView.as_view(), name='drf_without_ff'),
    path('storage/', include('drf_api.service.booksy_storage.urls')),
    path(
        'business_api/me/active/',
        BusinessAccountSessionStatusView.as_view(),
        name='business_session_status',
    ),
    path(
        'business_api/me/fizjo/check_access/',
        CheckFizjoAccessView.as_view(),
        name='fizjo_check_access',
    ),
    path(
        'customer_api/quick_sign_in_up/',
        CustomerQuickSignInUpView.as_view(),
        name='customer_quick_sign_in_up',
    ),
    path(
        'customer_api/booksy_med/',
        include('webapps.booksy_med.urls'),
    ),
    path(
        'customer_api/me/email_change/',
        CustomerEmailChangeView.as_view(),
        name='customer_email_change',
    ),
    path(
        'customer_api/me/account_deletion/',
        UserAccountDeletionViewSet.as_view({'get': 'retrieve', 'post': 'create'}),
        name='account_deletion',
    ),
    path(  # TODO I think it's not used, added for testing BookingSource
        'customer_api/me/history/',
        CustomerHistoryView.as_view(),
        name='customer_history',
    ),
    path(
        'customer_api/me/my_booksy/pop_up_notifications/',
        MyBooksyPopUpNotificationView.as_view(),
        name='customer_my_booksy_pop_up_notifications',
    ),
    path(
        'customer_api/me/family_and_friends/members/photo/<int:member_profile_pk>/',
        MemberPhotoViewSet.as_view({'put': 'update', 'delete': 'destroy'}),
        name='customer_family_and_friends_member_photo',
    ),
    path('customer_api/unsubscribe/', UnsubscribeView.as_view()),
    path(
        'customer_api/blasts/unsubscribe/',
        UnsubscribeBlastsView.as_view(),
        name='unsubscribe_blast',
    ),
    path('customer_api/blasts/', include('webapps.message_blast.customer_urls')),
    path(
        'customer_api/businesses/<int:business_id>/voucher_checkout/',
        VoucherCheckoutView.as_view(),
        name='voucher_checkout',
    ),
    path(
        'customer_api/businesses/contact_info/',
        BusinessContactInfoView.as_view(),
        name='business_contact_info',
    ),
    path(
        'customer_api/omnibus_consent_content/',
        BooksyOmnibusConsentContentView.as_view(),
        name='omnibus_consent_content',
    ),
    path(
        'customer_api/me/vouchers_wallet/',
        CustomerGiftCardViewSet.as_view({'get': 'list'}),
        name='customer_vouchers_wallet',
    ),
    path(
        'customer_api/me/vouchers_wallet/<int:pk>/',
        CustomerGiftCardViewSet.as_view({'get': 'retrieve'}),
        name='customer_vouchers_wallet_detail',
    ),
    path(
        'customer_api/me/vouchers_wallet/<int:pk>/download/',
        CustomerGiftCardViewSet.as_view({'get': 'download'}),
        name='customer_vouchers_wallet_detail_download',
    ),
    # region Family and Friends
    path(
        'customer_api/me/family_and_friends/members/',
        FamilyAndFriendsMemberViewSet.as_view({'get': 'retrieve', 'post': 'create'}),
        name='family_and_friends_members',
    ),
    path(
        'customer_api/me/family_and_friends/members/<int:pk>/',
        FamilyAndFriendsMemberDetailsViewSet.as_view({'put': 'update'}),
        name='family_and_friends_member_details',
    ),
    path(
        'customer_api/me/family_and_friends/unlink/',
        UnlinkViewSet.as_view({'put': 'update'}),
        name='family_and_friends_unlink',
    ),
    path(
        'customer_api/me/family_and_friends/members/match_user_invitation/',
        MatchUserInvitationViewSet.as_view({'post': 'update'}),
        name='family_and_friends_match_user',
    ),
    path(
        'customer_api/me/family_and_friends/members/invitations/<str:action_type>/',
        InvitationViewSet.as_view({'post': 'update'}),
        name='family_and_friends_invitation',
    ),
    # endregion Family and Friends
    path(
        'customer_api/me/poll/',
        CustomerPollViewSet.as_view({'get': 'retrieve', 'post': 'create'}),
        name='customer_poll',
    ),
    path(
        'customer_api/me/people_also_booked/',
        PeopleAlsoBookedView.as_view(),
        name='people_also_booked',
    ),
    path(
        'customer_api/my_booksy/recommended_new/',
        CustomerMyBooksySelectedForYouView.as_view(),
        name='customer_my_booksy_selected_for_you',
    ),
    path(
        'customer_api/search/modern_query_hints/',
        ModernQueryHintsView.as_view(),
        name='modern_query_hints',
    ),
    path(
        'customer_api/search/focus_on_query/',
        FocusOnQueryView.as_view(),
        name='focus_on_query',
    ),
    path(
        'customer_api/search/recommended_treatments/',
        RecommendedTreatmentsView.as_view(),
        name='recommended_treatments',
    ),
    path(
        'customer_api/businesses/<int:business_pk>/',
        include('webapps.business.customer_urls'),
    ),
    path(
        'customer_api/businesses/<int:business_pk>/',
        include('webapps.business_customer_info.customer_urls'),
    ),
    path(
        'customer_api/me/businesses/<int:business_pk>/appointments/',
        include('webapps.booking.customer_urls'),
    ),
    path(
        'customer_api/',
        include('webapps.booking.timeslots_urls'),
    ),
    path(
        'customer_api/drafts/',
        include('webapps.booking.v2.application.http_api.urls'),
    ),
    path(
        'customer_api/attention_getters/',
        include('drf_api.service.attention_getters.urls'),
        name='attention_getters',
    ),
    path('stripe_app/', include('webapps.stripe_app.urls')),
    path(
        'business_api/me/iterable/',
        IterableJWTTokenView.as_view(),
        name='iterable_jwt_token',
    ),
    path(
        'business_api/me/stats/businesses/<int:business_pk>/report/',
        include('webapps.stats_and_reports.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/stripe_sdi/',
        include('drf_api.service.stripe_sdi.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/premium_services/',
        include('webapps.premium_services.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/navision/',
        include('drf_api.service.navision.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/billing/',
        include('webapps.billing.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/boost/',
        include('webapps.boost.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/visibility_promotion/',
        include('webapps.visibility_promotion.urls'),
    ),
    path('debug/exception/', DebugExceptionView.as_view()),
    path('debug/timeout/', DebugTimeoutView.as_view()),
    path('debug/sleep/<int:seconds>/', DebugSleepView.as_view()),
    path('debug/stats/', DebugStatsView.as_view(), name='debug-stats'),
    path('debug/eppo/', DebugEppo.as_view()),
    path(
        'business_api/me/businesses/<int:business_pk>/partner_apps/',
        include('drf_api.service.partner_app.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/visibility/',
        MyBusinessVisibilityView.as_view(),
        name='my_business_visibility',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/vouchers/<int:voucher_pk>/download/',
        BusinessVoucherDownloadDetailView.as_view(),
        name='business_voucher_detail_download',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/check_new_geolocation/',
        CheckNewGeolocationView.as_view(),
        name='check_new_geolocation',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/',
        include('drf_api.service.stripe_integration.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/whats_new/',
        BusinessWhatsNewListView.as_view(),
        name='business_whats_new',
    ),
    path(
        'customer_api/me/whats_new/',
        CustomerWhatsNewListView.as_view(),
        name='customer_whats_new',
    ),
    path(
        'business_api/me/businesses/help_center/',
        include('drf_api.service.help_center.deprecated_urls'),
    ),
    path(
        'customer_api/me/businesses/<int:business_id>/payment_options/',
        CustomerBusinessPaymentOptionsView.as_view(),
        name='business_payment_options',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/help_center/',
        include('drf_api.service.help_center.urls'),
    ),
    path(
        'utils/ui_text/<str:text_id>/',
        UITextView.as_view(),
        name='ui_text',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/turntracker/',
        include('webapps.turntracker.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/instagram/',
        include('webapps.instagram_integration.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/wait_list/',
        WaitListViewSet.as_view({'get': 'list'}),
        name='wait_list',
    ),
    path(
        'business_api/me/color_palette/',
        ColorPaletteView.as_view(),
        name='color_palette',
    ),
    path(
        'business_api/me/account_deletion/',
        UserAccountDeletionViewSet.as_view({'get': 'retrieve', 'post': 'create'}),
        name='business_account_deletion',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/navision/', include('webapps.navision.urls')
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/nf525/',
        include('webapps.french_certification.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/calendar/',
        include('drf_api.service.business.calendar.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/settings/banners/',
        SettingsBannersView.as_view(),
        name='settings_banners_view',
    ),
    path(
        (
            'business_api/me/businesses/<int:business_pk>/'
            'pos/transactions/<int:transaction_pk>/splash/'
        ),
        PostCheckoutSplashView.as_view(),
        name='post_checkout_splash',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/ttp_reminder_splash/',
        TTPReminderSplashView.as_view(),
        name='ttp_reminder_splash',
    ),
    path(
        'business_api/me/businesses/<int:business_id>/profile_settings_splash/',
        ProfileAndSettingsSplashView.as_view(),
        name='profile_settings_ttp_promo',
    ),
    # stripe terminal
    path(
        "business_api/me/businesses/<int:business_id>/stripe/terminal/hardware_features/",
        HardwareFeaturesListView.as_view(),
        name='stripe_terminal_hardware_features_list',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/stripe/terminal/hardware/",
        HardwareListView.as_view(),
        name='stripe_terminal_hardware_list',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/stripe/terminal/hardware/<int:hardware_id>/",
        HardwareDetailsView.as_view(),
        name='stripe_terminal_hardware_details',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/stripe/terminal/shipping_methods/",
        ShippingMethodsView.as_view(),
        name='stripe_terminal_shipping_methods',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/stripe/terminal/orders/",
        OrdersView.as_view(),
        name='stripe_terminal_orders',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/stripe/terminal/orders/<int:order_id>/",
        OrderDetailsView.as_view(),
        name='stripe_terminal_order_details',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/"
        "stripe/terminal/orders/<int:order_id>/payments/",
        OrderPaymentsView.as_view(),
        name='stripe_terminal_order_payments',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/"
        "stripe/terminal/orders/<int:order_id>/payments/<int:payment_id>/",
        OrderPaymentDetailsView.as_view(),
        name='stripe_terminal_order_payment_details',
    ),
    path(
        "business_api/me/businesses/<int:business_id>/quick_invite/",
        BusinessCustomerInfoQuickInviteView.as_view(),
        name='business_customer_info_quick_invite',
    ),
    path(
        "business_api/me/businesses/<int:business_pk>/invite_text/",
        BusinessInviteTextView.as_view(),
        name='invite_text',
    ),
    path(
        "business_api/me/businesses/<int:business_pk>/churn_support/",
        BeforeChurnPathSupportView.as_view(),
        name='churn_support',
    ),
    path("stripe/terminal/orders/webhook/", StripeHardwareOrderWebhookView.as_view()),
    path("stripe/terminal/payments/webhook/", StripeHardwarePaymentWebhookView.as_view()),
    path(
        'business_api/me/businesses/<int:business_pk>/customer_agreements/',
        BusinessCustomerAgreementsView.as_view(),
        name='customer_agreements',
    ),
    # payment webhooks
    path(
        'stripe/notifications/connect/',
        StripeConnectNotificationView.as_view(),
        name='stripe_integration_webhook_connect',
    ),
    path(
        'stripe/notifications/account/',
        StripeAccountNotificationView.as_view(),
        name='stripe_integration_webhook_account',
    ),
    path(
        'payments/',
        include('webapps.payments.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/payments/',
        include('webapps.payments.business_urls'),
    ),
    path(
        'customer_api/me/payments/',
        include('webapps.payments.customer_urls'),
    ),
    # notification webhooks
    path(
        'vonage/status/',
        VonageWebhookView.as_view(),
        name='vonage_webhook',
    ),
    path(
        'push_notification/confirm/',
        PushConfirmationWebhookView.as_view(),
        name='push_notification_confirmation_webhook',
    ),
    path('zowie_jwt/', GenerateZowieJWTTokenView.as_view(), name='zowie_jwt'),
    path(
        'business_api/me/businesses/',
        MyBusinessesView.as_view(),
        name='my_businesses',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/',
        include('webapps.business.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/',
        include('webapps.business_calendar.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/calendar/splash/',
        CalendarSplashView.as_view(),
        name='instant_splash',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/calendar/splash/prepayment/',
        ChangePrepaymentView.as_view(),
        name='increase_prepayment_splash',
    ),
    path(
        'customer_api/appointment/<int:appointment_id>/splash/tipping_experiment/',
        TippingAppetiteSplashView.as_view(),
        name='tipping_experiment_splash',
    ),
    path(
        'customer_api/appointment/tipping_experiment/',
        TippingAppetiteDecisionView.as_view(),
        name='tipping_experiment_decision',
    ),
    path('business_api/service_types/', ServiceTypesView.as_view(), name='service_types'),
    path(
        'business_api/me/businesses/<int:business_id>/service_categories/',
        ServiceCategoriesView.as_view(),
        name='service_categories',
    ),
    # Experiment v3
    path('experiment/', include('webapps.experiment_v3.urls')),  # deprecated
    path(
        'business_api/experiment/',
        include('webapps.experiment_v3.business_urls'),
    ),
    path(
        'customer_api/experiment/',
        include('webapps.experiment_v3.customer_urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/qr_codes/',
        include('webapps.qr_code_origami.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/profile_completeness/',
        include('webapps.profile_completeness.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/profile_setup/',
        include('webapps.profile_setup.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/onboarding_space/',
        include('webapps.onboarding_space.presentation.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/',
        include('webapps.staff_management.urls'),
    ),
    path(
        'business_api/me/resources/permission_overrides/defaults/',
        PermissionOverrideDefaultsView.as_view(),
        name='permission_overrides_defaults',
    ),
    # customer information
    path(
        'business_api/me/businesses/<int:business_pk>/',
        include('webapps.business_customer_info.urls'),
    ),
    path(
        # pylint: disable=line-too-long
        'business_api/me/businesses/<int:business_pk>/profile_completeness/payment_activators_tiles/',
        PaymentActivatorsTilesView.as_view(),
        name='payment_activators_tiles',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/voucher_background_images/',
        BusinessVoucherBackgroundListView.as_view(),
        name='voucher_business_images',
    ),
    path(
        'business_api/me/businesses/voucher_config/',
        VoucherConfigView.as_view(),
        name='voucher_config',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/voucher_purchase_methods_settings/',
        VoucherPurchaseMethodsSettingsView.as_view(),
        name='voucher_purchase_methods_settings',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/vouchers/simple_redeem/',
        VoucherSimpleRedeemView.as_view(),
        name='voucher_simple_redeem',
    ),
    # survey
    path('business_api/me/businesses/<int:business_pk>/', include('webapps.survey.urls')),
    # hints and stories
    path(
        'business_api/me/businesses/<int:business_pk>/hints/',
        HintsListView.as_view(),
        name='hints_list',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/stories/',
        StoriesListView.as_view(),
        name='stories_list',
    ),
    path(
        'customer_api/report_inappropriate_content/',
        ReportInappropriateContentCustomerView.as_view(),
        name='report_inappropriate_customer_content',
    ),
    path(
        'customer_api/report_inappropriate_content/v2/',
        ReportInappropriateContentCustomerDSAView.as_view(),
        name='report_inappropriate_customer_content_v2',
    ),
    path(
        'business_api/report_inappropriate_content/',
        ReportInappropriateContentBusinessView.as_view(),
        name='report_inappropriate_business_content',
    ),
    path(
        'versum_migration/',
        include('drf_api.service.versum_migration.urls'),
        name='versum_migration',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/business_consents/',
        ListUpdateBusinessConsentsAPIView.as_view(),
        name='list_update_business_contents',
    ),
    path(
        'business_api/account/exists/',
        UserExistsBusinessView.as_view(),
        name='account_exists_business',
    ),
    path(
        'business_api/account/login/google/one_tap/',
        GoogleOneTapSignInBusinessView.as_view(),
        name='google_one_tap_business',
    ),
    path(
        'customer_api/account/login/facebook/v2/',
        FacebookSignInUpCustomerView.as_view(),
        name='facebook_jwt_sign_in_up_customer',
    ),
    path(
        'customer_api/account/login/facebook/v3/',
        FacebookGraphSignInUpCustomerView.as_view(),
        name='facebook_graph_sign_in_up_customer',
    ),
    path(
        'business_api/account/login/facebook/v2/',
        FacebookSignInUpBusinessView.as_view(),
        name='facebook_jwt_sign_in_up_business',
    ),
    path(
        'customer_api/me/connect/facebook/v2/',
        FacebookCustomerConnectView.as_view(),
        name='facebook_jwt_connect_customer',
    ),
    path(
        'business_api/me/connect/facebook/v2/',
        FacebookBusinessConnectView.as_view(),
        name='facebook_jwt_connect_business',
    ),
    path(
        'customer_api/account/login/google/one_tap/',
        GoogleOneTapSignInCustomerView.as_view(),
        name='google_one_tap_customer',
    ),
    path(
        'customer_api/account/login/apple/v3/',
        AppleIDSignInCustomerView.as_view(),
        name='apple_id_customer',
    ),
    path(
        'customer_api/me/connect/apple/',
        AppleConnectCustomerView.as_view(),
        name='apple_connect_customer',
    ),
    path(
        'customer_api/me/connect/google/',
        GoogleOneTapConnectCustomerView.as_view(),
        name='google_connect_customer',
    ),
    path(
        'business_api/me/connect/google/',
        GoogleOneTapConnectBusinessView.as_view(),
        name='google_connect_business',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/trial_extension/',
        ReTrialAttemptView.as_view(),
        name='trial_extension',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/voucher_additional_info/',
        BusinessVoucherAdditionalInfoView.as_view(),
        name='business_voucher_additional_info',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/voucher_orders/',
        BusinessVoucherOrderListingView.as_view(),
        name='business_voucher_orders',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/voucher_orders/<int:order_id>/finalize/',
        BusinessVoucherOrdersFinalizeView.as_view(),
        name='business_voucher_orders_finalize',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/voucher_orders/<int:order_id>/',
        BusinessVoucherOrderDetailView.as_view(),
        name='business_voucher_order_detail',
    ),
    path(
        'customer_api/me/businesses/<int:business_pk>/voucher_additional_info/',
        CustomerVoucherAdditionalInfoView.as_view(),
        name='customer_voucher_additional_info',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/vouchers/<int:voucher_id>/void/',
        VoucherVoidView.as_view(),
        name='voucher_void',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/pos/migrate_pos/',
        MigratorCreatePOSWalletHandler.as_view(),
        name='migrate_pos',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/pos/booksy_pay_late_cancellation_window/',
        BooksyPayLateCancellationWindowViewSet.as_view({'get': 'retrieve', 'put': 'update'}),
        name='booksy_pay_late_cancellation_window',
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/pos/booksy_pay_settings/',
        BooksyPaySettingsViewSet.as_view({'get': 'retrieve', 'patch': 'partial_update'}),
        name='booksy_pay_settings',
    ),
    path(
        'customer_api/me/appointments/<int:appointment_id>/booksy_pay/',
        BooksyPayViewSet.as_view({'get': 'retrieve', 'post': 'pay'}),
        name='booksy_pay',
    ),
    path(
        'customer_api/me/appointments/<int:appointment_id>/booksy_pay/dry_run/',
        BooksyPayDryRunViewSet.as_view({'post': 'dry_run'}),
        name='booksy_pay_dry_run',
    ),
    path(
        'customer_api/me/appointments/<int:appointment_id>/compatibility/',
        AppointmentCompatibilityView.as_view(),
        name='appointment_compatibility',
    ),
    path(
        'customer_api/businesses/categories/',
        BusinessCategoriesView.as_view(),
        name='customer_business_categories',
    ),
    path(
        'customer_api/me/preferences/',
        CustomerPreferencesUpdateView.as_view(),
        name='customer_preferences',
    ),
    path(
        'customer_api/me/favorite_categories/',
        CustomerFavoriteCategoriesView.as_view({'put': 'update', 'get': 'retrieve'}),
        name='customer_favorite_categories',
    ),
    path(
        "business_api/me/businesses/<int:business_pk>/staffers/<int:staffer_pk>/reports/staffer_stats/",  # pylint: disable=line-too-long
        StafferStatsView.as_view(),
        name="staffer_stats",
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/google_business_profile/',
        include('webapps.google_business_profile.presentation.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/google_places/',
        include('webapps.google_places.presentation.urls'),
    ),
    path(
        'business_api/me/businesses/<int:business_pk>/search/v2/',
        include('service.search.v2.presentation.urls'),
    ),
    path(
        'business_api/me/extole/webhook/',
        ExtoleWebhookAPIView.as_view(),
        name='extole_webhook',
    ),
]


if not settings.LIVE_DEPLOYMENT:
    urlpatterns += [
        path('drf-docs/', include('drf_api.docs.urls')),
        path(
            'other/performance/archived_booking_changes/',
            ArchivedBookingChangesView.as_view(),
            name='archived_booking_changes',
        ),
        path('branch_info/', BranchInfoView.as_view(), name='branch_info'),
        path(
            'automation_tests_setup/',
            AutomationTestsSetupView.as_view(),
            name='automation_tests_setup',
        ),
    ]
