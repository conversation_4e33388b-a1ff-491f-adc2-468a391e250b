from webapps.business_customer_info.enums import BCIGroupName

BLAST_SHORT_BODY_LENGTH_LIMIT = 320
BLAST_TITLE_LENGTH_LIMIT = 256
BLAST_SMS_FR_SHORTCODE = 'STOP au 36179'

SKIP_IN_ES_SEARCH = [
    BCIGroupName.ALL_CUSTOMERS,
    BCIGroupName.NEW_CUSTOMERS,
    BCIGroupName.NOT_BOOKSY_USERS,
]

TILL_NIGHT_SHIFT_MINUTES = {
    'fr': 30,
}


ALWAYS_REQUIRE_GDPR_CONSENT_COUNTRIES = {
    'us': True,
    'ca': True,
}
