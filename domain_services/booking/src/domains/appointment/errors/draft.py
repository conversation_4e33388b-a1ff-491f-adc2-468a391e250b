import uuid

from webapps.booking.enums import BookAgainStatusesEnum


class AppointmentNotFoundError(Exception):
    def __init__(self, appointment_id: int) -> None:
        super().__init__(f"Appointment {appointment_id} not found")


class DraftBookAgainError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Book again error: {message}")


class DraftBookAgainBookingNotPossibleError(Exception):
    def __init__(self, book_again_status: BookAgainStatusesEnum) -> None:
        super().__init__(f"Book again is not possible: {book_again_status}")


class DraftNotFoundError(Exception):
    def __init__(self, draft_id: uuid.UUID) -> None:
        super().__init__(f"Draft {draft_id} not found")


class DraftItemNotFoundError(Exception):
    def __init__(self, draft_item_id: uuid.UUID) -> None:
        super().__init__(f"Draft item {draft_item_id} not found")


class OnlyDraftItemCannotBeRemovedError(Exception):
    def __init__(self, draft_item_id: uuid.UUID) -> None:
        super().__init__(
            f"Draft item {draft_item_id} that is only service for draft cannot be removed"
        )


class DraftVersionMismatchError(Exception):
    def __init__(
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        requested_version: uuid.UUID,
    ) -> None:
        super().__init__(
            f"Draft {draft_id} version {version} "
            f"does not match requested version {requested_version}"
        )


class CreateDraftFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Creating draft failed: {message}")


class ChangeDraftStafferFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Changing draft's staffer failed: {message}")


class ChangeDraftStafferError(Exception):
    def __init__(self, draft_item_id: uuid.UUID, staffer_id: int) -> None:
        super().__init__("Staffer cannot be set")
        self.draft_item_id = draft_item_id
        self.staffer_id = staffer_id


class ChangeDraftStafferNoTimeSlotError(ChangeDraftStafferError): ...


class ChangeDraftStafferInvalidStafferError(ChangeDraftStafferError): ...


class ChangeDraftTimeslotFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Changing draft's timeslot failed: {message}")


class AddDraftServiceFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Adding service to draft failed: {message}")


class DraftBelongsToAnotherUserError(Exception):
    def __init__(self, draft_id: uuid.UUID) -> None:
        super().__init__(f'Draft {draft_id} belongs to another user')


class RemoveDraftItemFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Removing draft item from draft failed: {message}")


class WrongDraftItemsForReorderError(Exception):
    def __init__(self, draft_item_ids: list[uuid.UUID]) -> None:
        super().__init__(
            f"Draft items to reorder {draft_item_ids} differ from existing draft items"
        )


class ReorderFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Reordering draft items failed: {message}")


class ReorderNotPossibleError(Exception):
    def __init__(self) -> None:
        super().__init__("Reordering draft items not possible")


class GetDraftAvailabilityFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Getting availability for draft failed: {message}")


class DraftModifiedForAvailabilityError(Exception):
    def __init__(self, draft_item_id: uuid.UUID):
        super().__init__(f"Draft item was modified {draft_item_id} - can't get availability")
