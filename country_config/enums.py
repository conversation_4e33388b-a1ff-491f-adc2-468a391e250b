from django.utils.translation import gettext_lazy as _

from lib.enums import StrChoicesEnum

_EXTENDED_COUNTRIES = {
    'us': frozenset(
        [
            'pr',  # Puerto Rico
            'vi',  # Virgin Islands
            'gu',  # Guam
            'mp',  # Northern Mariana Islands
            'ws',  # Samoa
        ]
    ),
    'gb': frozenset(
        [
            'im',  # Isle of Man
            'bm',  # Bermuda
            'gg',  # Guernsey
            'je',  # Jersey
            'gi',  # Gibraltar
        ]
    ),
    'fr': frozenset(
        [
            'gf',  # French Guiana
            'gp',  # Guadeloupe
            'mq',  # Martinique
            'yt',  # Mayotte
            're',  # Réunion
        ]
    ),
}


class Country(StrChoicesEnum):
    # FIRST TIER COUNTRIES
    BR = 'br', _('Brasil'), 1
    CA = 'ca', _('Canada'), 1
    ES = 'es', _('Spain'), 1
    FR = 'fr', _('France'), 1
    GB = 'gb', _('Great Britain'), 1
    IE = 'ie', _('Ireland'), 1
    PL = 'pl', _('Poland'), 1
    US = 'us', _('United States'), 1
    ZA = 'za', _('South Africa'), 1

    # SECOND TIER COUNTRIES
    AU = 'au', _('Australia'), 2
    CL = 'cl', _('Chile'), 2
    CO = 'co', _('Colombia'), 2
    NL = 'nl', _('Netherlands'), 2
    MX = 'mx', _('Mexico'), 2
    PT = 'pt', _('Portugal'), 2

    # THIRD TIER COUNTRIES
    AR = 'ar', _('Argentina'), 3
    IT = 'it', _('Italy'), 3

    # UNSUPPORTED COUNTRIES
    NG = 'ng', _('Nigeria'), 4
    DE = 'de', _('Germany'), 4
    IN = 'in', _('India'), 4
    MY = 'my', _('Malaysia'), 4
    RU = 'ru', _('Russia'), 4
    SE = 'se', _('Sweden'), 4
    SG = 'sg', _('Singapore'), 4

    def __new__(cls, value: str, label: str, tier: int):  # pylint: disable=signature-differs
        """Use value for value and save tier for later to generate tier data."""
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.label = label
        obj.tier = tier
        return obj

    @classmethod
    def get_tier(cls, tier: int) -> frozenset:
        return frozenset(member for member in cls.__members__.values() if member.tier == tier)

    @classmethod
    def first_tier(cls) -> frozenset:
        return cls.get_tier(1)

    @classmethod
    def second_tier(cls) -> frozenset:
        return cls.get_tier(2)

    @classmethod
    def third_tier(cls) -> frozenset:
        return cls.get_tier(3)

    @classmethod
    def supported(cls) -> frozenset:
        return cls.first_tier() | cls.second_tier() | cls.third_tier()

    @classmethod
    def non_premium(cls) -> frozenset:
        return cls.second_tier() | cls.third_tier()

    @classmethod
    def open_business_registration(cls) -> frozenset:
        return cls.first_tier() | cls.second_tier()

    @classmethod
    def open_customer_registration(cls) -> frozenset:
        return cls.supported()

    @classmethod
    def huge_countries_premium_tier(cls) -> frozenset:
        """Countries that in first tier but also has huge number of merchants"""
        return frozenset(
            {
                cls.US,
                cls.PL,
                cls.BR,
                cls.GB,
                cls.ES,
            }
        )

    @classmethod
    def get_eu_countries(cls):
        """List of European Union countries"""
        return {
            Country.ES,
            Country.FR,
            Country.IE,
            Country.IT,
            Country.PL,
            Country.PT,
            Country.DE,
            Country.NL,
            Country.SE,
        }

    @classmethod
    def is_eu(cls, country: str) -> bool:
        """Check if given country belongs to the European Union."""
        return country in cls.get_eu_countries()

    def get_extended_countries(self) -> frozenset[str]:
        """Deployments may include nearby colonies & islands."""
        return _EXTENDED_COUNTRIES.get(self.value, frozenset())
