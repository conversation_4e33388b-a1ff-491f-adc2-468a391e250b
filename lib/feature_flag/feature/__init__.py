from lib.enums import StrEnum
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import (
    BooleanFlag,
    IntegerFlag,
    StringFlag,
)


class AuthPasswordChangeRequiredFlag(BooleanFlag):
    flag_name = 'Feature_AuthPasswordChangeRequiredFlag'
    adapter = FeatureFlagAdapter.EPPO


class AutomaticCxAccountDeletion(BooleanFlag):
    flag_name = 'Feature_automatic_CX_account_deletion'
    adapter = FeatureFlagAdapter.LD


class BLIKCalendarBanner(BooleanFlag):
    flag_name = 'Feature_BLIKCalendarBanner'
    adapter = FeatureFlagAdapter.EPPO


class BlikForFreePromoFlag(BooleanFlag):
    flag_name = 'Feature_BlikForFreePromoFlag'
    adapter = FeatureFlagAdapter.EPPO


class BlikRedirectEfortlessKycFlag(BooleanFlag):
    flag_name = 'Feature_BlikRedirectEfortlessKyc'
    adapter = FeatureFlagAdapter.EPPO


class BooksyGiftcardsCheckoutTimeValidationFlag(BooleanFlag):
    flag_name = 'Feature_BooksyGiftcardsCheckoutTimeValidationFlag'
    adapter = FeatureFlagAdapter.EPPO


class BooksyGiftcardsEnabledFlag(BooleanFlag):
    flag_name = 'Feature_BooksyGiftcardsEnabled'
    adapter = FeatureFlagAdapter.LD


class BranchIOBusinessBCRTerminalOrderedTrackingFlag(BooleanFlag):
    flag_name = 'Feature_BranchIOBusinessBCRTerminalOrderedTracking'
    adapter = FeatureFlagAdapter.LD


class BusinessListingImporterFlag(BooleanFlag):
    flag_name = 'Feature_BusinessListingImporterFlag'
    adapter = FeatureFlagAdapter.LD


class BusinessListingTransformInFrance(BooleanFlag):
    flag_name = 'Feature_BusinessListingTransformInFrance'
    adapter = FeatureFlagAdapter.LD


class CustomerQuickSignInUpFlag(BooleanFlag):
    flag_name = 'Feature_CustomerQuickSignInUp'
    adapter = FeatureFlagAdapter.EPPO


class CustomerSendPrivateEmail(BooleanFlag):
    flag_name = 'Feature_CustomerSendPrivateEmail'
    adapter = FeatureFlagAdapter.EPPO


class DisableBGCStatusChangeIfFailedPayment(BooleanFlag):
    flag_name = 'Feature_DisableBGCStatusChangeIfFailedPaymentFlag'
    adapter = FeatureFlagAdapter.EPPO


class DoNotDeleteEmailReceiversInGlobalLogoutFlag(BooleanFlag):
    flag_name = 'Feature_DoNotDeleteEmailReceiversInGlobalLogout'
    adapter = FeatureFlagAdapter.EPPO


class EnableCancellationFeeAuthChangelogServiceFlag(BooleanFlag):
    flag_name = 'Feature_EnableCancellationFeeAuthChangelogService'
    adapter = FeatureFlagAdapter.EPPO


class EnablePBAOnlyAfterAcceptingFeesFlag(BooleanFlag):
    flag_name = 'Feature_EnablePBAOnlyAfterAcceptingFees'
    adapter = FeatureFlagAdapter.LD


class EnableSearchServiceInModernQueryHints(BooleanFlag):
    flag_name = 'Feature_EnableSearchServiceInModernQueryHints'
    adapter = FeatureFlagAdapter.EPPO


class EventSignalLogErrorsFromReceiversFlag(BooleanFlag):
    """
    This feature flag is expected to work a little differently than other flags.
    Its `subject_key` is equal to the name of the event emitted.
    """

    flag_name = "Feature_EventSignalLogErrorsFromReceivers"
    adapter = FeatureFlagAdapter.EPPO


class FrenchCertificationMajorSoftwareVersionFlag(IntegerFlag):
    flag_name = 'Feature_FrenchCertificationMajorSoftwareVersion'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: int = 3) -> int:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class FrenchCertificationVoucherSummaryReportFlag(BooleanFlag):
    flag_name = 'Feature_FrenchCertificationVoucherSummaryReport'
    adapter = FeatureFlagAdapter.EPPO


class IncludeInstagramInMyppFlowFlag(BooleanFlag):
    flag_name = 'Bug_IncludeInstagramInMyppFlow'
    adapter = FeatureFlagAdapter.EPPO


class LogDocumentNotFoundErrorFlag(BooleanFlag):
    flag_name = 'Feature_LogDocumentNotFoundError'
    adapter = FeatureFlagAdapter.EPPO


class LoyaltyProgramFlag(BooleanFlag):
    flag_name = 'Feature_LoyaltyProgram'
    adapter = FeatureFlagAdapter.EPPO


class MarketPayB2BReferralSettingEnabledFlag(BooleanFlag):
    flag_name = 'Feature_MarketPayB2BReferralSettingEnabledFlag'
    adapter = FeatureFlagAdapter.LD


class OptimizeStripeCallsForBannersFlag(BooleanFlag):
    flag_name = 'Feature_OptimizeStripeCallsForBannersFlag'
    adapter = FeatureFlagAdapter.EPPO


class OverrideTotalInVersumAppointmentImportFlag(BooleanFlag):
    flag_name = 'Feature_OverrideTotalInVersumAppointmentImport'
    adapter = FeatureFlagAdapter.LD


class ReindexImagesWithBusinessInAdminFlag(BooleanFlag):
    flag_name = 'Feature_ReindexImagesWithBusinessInAdmin'
    adapter = FeatureFlagAdapter.LD


class ServiceNameReplicationFlag(IntegerFlag):
    flag_name = 'Feature_ServiceNameReplicationFlag'
    adapter = FeatureFlagAdapter.LD


class ServiceTypeAlertGenerationNumberFlag(IntegerFlag):
    flag_name = 'Feature_ServiceTypeAlertGenerationNumber'
    adapter = FeatureFlagAdapter.LD


class ServiceTypeCampaignFlag(BooleanFlag):
    flag_name = 'Feature_ServiceTypeCampaign'
    adapter = FeatureFlagAdapter.LD


class ServiceTypeServicesAndCombosBanner(BooleanFlag):
    flag_name = 'Feature_ServiceTypeServicesAndCombosBanner'
    adapter = FeatureFlagAdapter.LD


class SetSMSLimitForActivePeriodFlag(BooleanFlag):
    flag_name = 'Feature_SetSMSLimitForActivePeriod'
    adapter = FeatureFlagAdapter.EPPO


class ShowBooksyGiftCardsForChosenProvidersFlag(BooleanFlag):
    flag_name = 'Feature_ShowBooksyGiftCardsForChosenProviders'
    adapter = FeatureFlagAdapter.EPPO


class ShowDroppedBooksyGiftCardNotificationOnIosFlag(BooleanFlag):
    flag_name = 'Feature_ShowDroppedBooksyGiftCardNotificationOnIos'
    adapter = FeatureFlagAdapter.EPPO


class SMSProfilesPerCountryFlag(BooleanFlag):
    flag_name = 'Feature_SMSProfilesPerCountry'
    adapter = FeatureFlagAdapter.LD


class SMSServiceNameFlag(StringFlag):
    flag_name = 'Feature_SMSServiceName'
    adapter = FeatureFlagAdapter.LD


class SMSVonageUseRotatingCredentialsFlag(BooleanFlag):
    flag_name = 'Feature_SMSVonageUseRotatingCredentials'
    adapter = FeatureFlagAdapter.LD


class StaffManagementRedesignFlag(BooleanFlag):
    flag_name = 'Feature_StaffManagementRedesign'
    adapter = FeatureFlagAdapter.LD


class ThrottleBusinessReviewsRateRecalculations(BooleanFlag):
    flag_name = "Feature_ThrottleBusinessReviewsRateRecalculations"
    adapter = FeatureFlagAdapter.EPPO


class TrustedClientsTaskAsyncFlag(BooleanFlag):
    flag_name = 'Feature_TrustedClientsTaskAsyncFlag'
    adapter = FeatureFlagAdapter.LD


class TrustedClientsTaskEnabledFlag(BooleanFlag):
    flag_name = 'Feature_TrustedClientsTaskEnabledFlag'
    adapter = FeatureFlagAdapter.LD


class TTPReminderFlag(BooleanFlag):
    flag_name = 'Feature_TTPReminder'
    adapter = FeatureFlagAdapter.EPPO


class TurnTrackerFlag(BooleanFlag):
    flag_name = 'Feature_TurnTracker'
    adapter = FeatureFlagAdapter.EPPO


class UseExplicitRoutingWhenDeletingDocumentsFlag(BooleanFlag):
    flag_name = 'Feature_UseExplicitRoutingWhenDeletingDocuments'
    adapter = FeatureFlagAdapter.EPPO


class UseGetExtraInsteadOfCheckExtra(BooleanFlag):
    flag_name = 'Feature_UseGetExtraInsteadOfCheckExtra'
    adapter = FeatureFlagAdapter.EPPO


class WarningCirclesFlag(BooleanFlag):
    flag_name = 'Feature_WarningCirclesFlag'
    adapter = FeatureFlagAdapter.LD
