from datetime import timedelta
from decimal import Decimal
from unittest.mock import patch

from django.conf import settings
from django.shortcuts import reverse
from rest_framework import status

from drf_api.service.tests.base import AuthenticatedBusinessAPITestCase
from lib.tools import tznow

from webapps.boost.services.next_payment import BoostNextPaymentInfo
from webapps.business.models import Resource
from webapps.invoicing.baker_recipes import user_recipe
from webapps.session.utils import get_user


class TestBoostNextPayment(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.business.has_new_billing = True
        self.business.save()
        self.url = reverse('boost_next_payment', args=(self.business.id,))

    @patch('webapps.billing.interfaces.boost.business_next_payment_info')
    def test_ok(self, mocked_next_payment):
        mocked_next_payment.return_value = BoostNextPaymentInfo(
            business_id=self.business.id,
            total_gross_amount=Decimal('1.23'),
            appointment_count=13,
            currency=settings.CURRENCY_CODE,
        )
        response = self.client.get(self.url, **self.headers)
        start = tznow() - timedelta(days=1)
        period_start = start.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)
        period_end = tznow().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(period_start.isoformat(), response.json().get('period_start'))
        self.assertEqual(period_end.isoformat(), response.json().get('period_end'))
        self.assertEqual('1.23', response.data['total_amount'])
        self.assertEqual(settings.CURRENCY_CODE, response.data['currency'])
        self.assertEqual(13, response.data['appointment_count'])

    def test_has_new_billing_is_false(self):
        self.business.has_new_billing = False
        self.business.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)

    def test_not_authenticated(self):
        self.session.delete()
        get_user.clear_from_cache(user_id=self.user.id)

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)

    def test_not_authorized(self):
        self.business.owner = user_recipe.make()
        self.business.save()
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        self.resource.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)
