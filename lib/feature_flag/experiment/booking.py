from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, StringFlag


class DontSendCustomerConfirmationSMSForBusinessBooking(BooleanFlag):
    flag_name = 'Experiment_DontSendCustomerConfirmationSMSForBusinessBooking'
    adapter = FeatureFlagAdapter.EPPO


class IncentivizeWithGiftCardBackend(StringFlag):
    flag_name = 'Experiment_IncentivizeWithGiftCardBackend'
    adapter = FeatureFlagAdapter.EPPO


class NoShowConfirmationExperiment(StringFlag):
    flag_name = 'Experiment_NoShowConfirmation'
    adapter = FeatureFlagAdapter.EPPO


class SendSMSInNoShowConfirmationExperiment(BooleanFlag):
    flag_name = 'Feature_SendSMSInNoShowConfirmationExperiment'
    adapter = FeatureFlagAdapter.EPPO


class TimeSlotsV2Flag(BooleanFlag):
    flag_name = 'experiment_timeslotsv2'
    adapter = FeatureFlagAdapter.EPPO


class PredefinedReviewsExperiment(StringFlag):
    flag_name = 'Experiment_PredefinedReviews'
    adapter = FeatureFlagAdapter.EPPO


class NailsCampaignOnMyBooksy(StringFlag):
    flag_name = 'Experiment_NailsCampaignOnMyBooksy'
    adapter = FeatureFlagAdapter.EPPO
