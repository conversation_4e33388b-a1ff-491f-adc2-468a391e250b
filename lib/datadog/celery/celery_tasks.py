# pylint: disable=line-too-long

from bo_obs.datadog.enums import BooksyTeams

celery_tasks_with_booksy_teams = {
    'drf_api.service.facebook.tasks.upload_facebook_photo_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'lib.celery_utils.debug_tasks.rdb_high_task': (BooksyTeams.UNASSIGNED,),
    'lib.celery_utils.debug_tasks.rdb_index_task': (BooksyTeams.UNASSIGNED,),
    'lib.elasticsearch.tasks.delete_document_with_backoff_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'lib.email.tasks.send_email_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'lib.email.tasks.send_emails_batch_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'lib.email.tasks.send_emails_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'lib.email.tasks.send_otp_email_task': (BooksyTeams.NO_SHOW_PROTECTION,),
    'lib.es_history.tasks.index_history_records_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'lib.facebook.task.business_facebook_conversion_event_task': (BooksyTeams.UNASSIGNED,),
    'lib.history_model.tasks.add_to_history': (BooksyTeams.UNASSIGNED,),
    'lib.history_model.tasks.save_with_history': (BooksyTeams.UNASSIGNED,),
    'lib.history_model.tasks.update_with_history': (BooksyTeams.UNASSIGNED,),
    'lib.probes.celery_probes.celery_beat_health_check_task': (BooksyTeams.UNASSIGNED,),
    'lib.tasks.eta_scheduler.move_eta_tasks_to_queues_task': (BooksyTeams.UNASSIGNED,),
    'lib.tasks.new_yaml_tasks.task_test_new_celery_type': (BooksyTeams.UNASSIGNED,),
    'service.auto_enable_fast_payouts.tasks.try_enable_fast_payouts_task': (
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'service.auto_enable_fast_payouts.tasks.try_enable_fast_payouts_in_batches_task': (
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'service.auto_enable_ba_deposit.tasks.try_enable_ba_deposit_in_batches_task': (
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'service.auto_enable_ba_deposit.tasks.try_enable_ba_deposit_task': (
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'service.auto_enable_ba_deposit.tasks.try_enable_kip_payment_method_task': (
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'service.booking.experiment_cx_incentives_task.send_experiment_event_if_applicable': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'service.management.commands.generate_swagger.generate_swagger_task': (BooksyTeams.SRE,),
    'webapps.admin_extra.tasks.appsflyer.appsflyer_shortcut_generic_link_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.appsflyer.appsflyer_shortcut_link_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.business_updates.business_churn_switch_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.business_updates.business_freeze_switch_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.business_updates.update_padding_time_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.calendars_import.google_calendar_import_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.calendars_import.i_calendar_import_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.change_bookings.change_bookings_staffer_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.admin_extra.tasks.dry_offline_invoicing.boost_offline_dry_invoice_businesses_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.dry_offline_invoicing.boost_offline_dry_invoicing_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.dry_offline_invoicing.dry_invoicing_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.dry_booksy_billing_invoices_report.dry_booksy_billing_invoices_report_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.dry_booksy_billing_invoices_report.process_one_day_items': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.dry_booksy_billing_invoices_report.prepare_email': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.dry_offline_invoicing.saas_offline_dry_invoice_subscriptions_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.dry_offline_invoicing.saas_offline_dry_invoicing_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.enterprise_data.add_photo_to_business_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_about_us_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_business_logos_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_business_opening_hours_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_business_security_settings_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_business_visibility_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_image_bundles_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_resources_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.batch_update_services_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.calendar_visibility_edit_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.enterprise_data.enterprise_import_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.images_import.images_import_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.images_import.images_import_task_gcp': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.mail_pdf_sender.send_emails_with_pdf_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_billing_business_discounts.mass_billing_business_discounts_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_billing_business_offer.mass_billing_business_offer_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_billing_merchants_switcher.mass_billing_merchants_switcher_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_billing_offer_purchase.mass_billing_offer_purchase_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_billing_offers_changer.mass_billing_offers_changer_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_billing_purchase_flow_switcher.mass_billing_purchase_flow_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_business_retrial_switch.mass_business_retrial_switch_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_business_extend_trial_by_given_days.'
    'mass_business_extend_trial_by_given_days_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.admin_extra.tasks.mass_business_old_fizjo_disable.'
    'mass_business_old_fizjo_disable_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.admin_extra.tasks.stripe_migration_tool.'
    'mass_stripe_migration_tool': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.admin_extra.tasks.mass_offline_to_billing_switch.mass_offline_to_billing_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_revert_business_from_invalid.mass_business_revert_from_invalid_status_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_sms_price_and_limit_changer.mass_billing_sms_changer_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.mass_switch_merchants_payment_processor.mass_switch_merchants_payment_processor_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.okta_users.okta_add_members_to_group_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.admin_extra.tasks.old_subscription_products.mass_subscription_product_import_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.payment_providers.import_stripe_transfer_funds.import_stripe_transfer_funds_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.push_and_notification.create_notification_history': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.admin_extra.tasks.push_and_notification.push_sender_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.push_and_notification.query_and_send_customer_push_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.push_and_notification.send_manual_admin_notification': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.push_and_notification.send_mass_customer_push_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.admin_extra.tasks.push_and_notification.send_notifications_chunk': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.admin_extra.tasks.push_and_notification.send_notifications_packet': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.push_and_notification.send_test_custom_push_email_notification': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.reports.report_inappropriate_content_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.reports.report_inappropriate_content_dsa_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.admin_extra.tasks.send_sms.sms_send_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.send_sms.sms_send_tasks': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.styleseat_import.styleseat_import_single_note_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.styleseat_import.styleseat_import_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.subscription_buyer_tools.batch_update_subscription_buyers_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.subscription_buyer_tools.import_subscription_buyers_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.subscription_import.subscription_batch_edit_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.subscription_import.subscription_import_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.switch_merchants_to_billing.mass_switch_merchants_to_billing_task': (
        BooksyTeams.PROVIDER_CONVERSION,
    ),
    'webapps.admin_extra.tasks.trial_end_helpers.trial_end_change_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.utils.debug_celery_exceptions': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.utils.debug_celery_result_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.utils.set_admin_permissions_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.utils.set_query_count_in_cache': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.vagaro_import.vagaro_file_import_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.versum.import_data_versum_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.versum.versum_file_parser_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.tasks.wholesaler.wholesaler_commodities_file_import_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.tasks.zip_codes_import.zip_codes_areas_import_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.admin_extra.views.blisting_import.import_b_listings_task': (BooksyTeams.UNASSIGNED,),
    'webapps.admin_extra.views.blisting_import.setup_data_and_run_import_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.adyen.tasks.cancel_outdated_3ds_bookings': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.b2b_referral.tasks.create_dyk_notification_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.b2b_referral.tasks.create_finish_invited_notification_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.b2b_referral.tasks.create_finish_referrer_notification_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.b2b_referral.tasks.create_invite_notification_for_setting_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.b2b_referral.tasks.create_signup_notification_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.best_of_booksy.tasks.create_best_of_booksy_certificates': (BooksyTeams.UNASSIGNED,),
    'webapps.best_of_booksy.tasks.create_business_booksy_awards': (BooksyTeams.UNASSIGNED,),
    'webapps.billing.tasks.add_or_update_billing_info_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.auto_churn': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.auto_retry_charges': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.batch_boost_overdue_charge_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.billing.tasks.billing_refund_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.cancel_abandoned_purchase_request_task': (
        BooksyTeams.PROVIDER_CONVERSION,
    ),
    'webapps.billing.tasks.cancel_abandoned_retry_charge_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.cancel_stripe_payment_intent_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.close_subscriptions': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.compute_business_status_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.create_one_off_transaction_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.delete_stripe_payment_method_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.finalize_purchase_subscription_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.finalize_retry_charge_subscription_task': (
        BooksyTeams.PROVIDER_CONVERSION,
    ),
    'webapps.billing.tasks.finalize_stripe_payment_method_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.finish_batch_boost_overdue_charge_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.billing.tasks.initialize_purchase_subscription_task': (
        BooksyTeams.PROVIDER_CONVERSION,
    ),
    'webapps.billing.tasks.initialize_retry_charge_subscription_task': (
        BooksyTeams.PROVIDER_CONVERSION,
    ),
    'webapps.billing.tasks.migrate_stripe_payment_methods_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.migrated_subscription_initial_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.mismatched_report_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.pending_requested_churn': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.process_reward_event_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.purchase_subscription_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.renew_long_subscription_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.requested_churn': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.retry_charge_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.setup_stripe_payment_method_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.switch_billing_cycles_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.switch_cycle_single_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.billing.tasks.update_not_refreshed_businesses_task': (
        BooksyTeams.PROVIDER_CONVERSION,
    ),
    'webapps.booking.experiments.compare_new_slots.tasks.compare_with_timeslot_service_slots_for_sv': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.experiments.compare_new_slots.tasks.compare_with_timeslot_service_slots': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.experiments.record_benchmark_time_slots.tasks.send_benchmark_time_slots': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.tasks.appointment_analytics_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.add_coordinates_to_traveling_appointments_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.tasks.add_reminder_notification_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.bulk_update_appointments': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.compare_with_time_slots_v2_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.check_booking_conflicts_and_send_report_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.tasks.create_late_cancellation_notifications_task': (BooksyTeams.UNASSIGNED,),
    'webapps.booking.tasks.post_business_update_appointment_task': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.PROVIDER_CALENDAR,
    ),
    'webapps.booking.tasks.update_subbooking_internal_service_data_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.tasks.regenerate_deeplinks_task': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.booking.tasks.replan_all_repeating_bookings': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.replan_new_repeating_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.replan_repeating_bookings': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.set_cache_after_bulk_update_appointments': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.tasks.schedule_bulk_update_appointments_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.short_review_pop_up_notification_delete_for_noshow_booking': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.booking.tasks.split_all_repeating_bookings': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.split_new_repeating_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.split_repeating_bookings': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.tasks.update_any_mobile_customer_appointments_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.tasks.update_bci_service_questions_task': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.booking.tasks.update_repeating_by_parent_booking': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.booking.v2.domains.analytics.outer.tasks.send_draft_created_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.booking.v2.domains.appointment.tasks.drafts.remove_old_appointment_drafts': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.boost.tasks.boost_payment_source_change_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.mark_old_clients_as_exempted': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.apply_boost_bans': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.end_boost_bans_for_today': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.end_boost_bans': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.mass_commission_change_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.mass_commission_close_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.mass_revert_or_delete_boost_transactions_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.boost.tasks.set_claim_status_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.boost.tasks.update_boost_ban_sms_status_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.business.actions.after_registration.instantly_invite_clients_after_business_activation': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.business_categories.tasks.assign_business_treatments_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.business.business_categories.tasks.assign_services_treatment_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.business.business_categories.tasks.assign_treatments_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.business.business_categories.tasks.refresh_images_task': (
        BooksyTeams.CUSTOMER_SEARCH,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.block_spammer_accounts': (BooksyTeams.UNASSIGNED,),
    'webapps.business.tasks.boost_set_switch_date_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.business.tasks.boost_status_change_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.business.tasks.boost_status_change_with_date_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.business.tasks.boost_switch_availability_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.business.tasks.business_blocked_overdue_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.business.tasks.business_customer_info_delete_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.business_hidden_in_search_turn_off_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.business_import_customers_task': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.business_import_json_customers': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.business_replan_repeating_bookings': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.business.tasks.business_sms_notification_status_change_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.business.tasks.business_trial_till_blocking_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.business.tasks.business_visible_delay_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.business.tasks.change_bci_web_consents_to_false_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.business_related.tasks.calculate_business_accept_booksy_gift_cards_task': (
        BooksyTeams.NEW_FINANCIAL_SERVICES,
    ),
    'webapps.business.business_categories.tasks.calculate_single_businesscategory_enable_booksy_gift_cards_task': (
        BooksyTeams.NEW_FINANCIAL_SERVICES,
    ),
    'webapps.business.business_categories.tasks.calculate_businesscategory_enable_booksy_gift_cards_task': (
        BooksyTeams.NEW_FINANCIAL_SERVICES,
    ),
    'webapps.business_related.tasks.calculate_single_business_accept_booksy_gift_cards_task': (
        BooksyTeams.NEW_FINANCIAL_SERVICES,
    ),
    'webapps.business.tasks.clean_associations_deleted_variants_staffers_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.business.tasks.cleanup_unused_tags_task': (BooksyTeams.UNASSIGNED,),
    'webapps.business.tasks.compute_es_weight_business_category_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.business.tasks.delete_imported_cutomers_task': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.disable_expired_flash_sale_promotions_task': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.business.tasks.extend_businesses_trial_task': (BooksyTeams.PROVIDER_CONVERSION,),
    'webapps.business.tasks.gdpr_business_data_export_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.business.tasks.happy_hours_incentive_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.hide_test_businesses_on_marketplace_task': (BooksyTeams.UNASSIGNED,),
    'webapps.business.tasks.import_business_gdpr_data_task': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.import_customers_dry_run_with_email_report_task': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.invite_again_existing_customers_task': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.invite_customers_to_booksy': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.last_minute_incentive_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.mass_claim_processing': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.business.tasks.match_business_customers_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.match_users_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.parse_and_import_customers': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.post_business_activate_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.business.tasks.quick_invite_customer_task': (
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.tasks.send_change_details_venue_email': (BooksyTeams.UNASSIGNED,),
    'webapps.business.tasks.send_happy_hours_incentive_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.set_sms_limit_task': (BooksyTeams.UNASSIGNED,),
    'webapps.business.tasks.shift_bookings_to_timezone_task': (BooksyTeams.CUSTOMER_BOOKING),
    'webapps.business.tasks.trust_clients_by_business_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.trust_clients_by_group_business_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.business.tasks.trusted_clients_batch_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.business.tasks.update_business_wallet_task': (BooksyTeams.UNASSIGNED,),
    'webapps.business_consents.tasks.consent_stripe_account_restricted_soon_turn_back_visibility': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.business_related.tasks.send_pdf_with_safety_rules': (BooksyTeams.UNASSIGNED,),
    'webapps.business_related.tasks.update_old_business_invitaion_deeplinks': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.business.v2.business_services.entrypoints.tasks.create_business_services_from_url_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.business.v2.business_services.entrypoints.tasks.upload_screenshot_to_bucket_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.c2b_referral.tasks.change_reward_status_task': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.c2b_referral.tasks.handle_c2b_reward': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.c2b_referral.tasks.send_mail_to_cs_task': (BooksyTeams.UNASSIGNED,),
    'webapps.celery.tasks.async_signal_task_for_receiver': (BooksyTeams.UNASSIGNED,),
    'webapps.celery.tasks.lazy_signal_task_for_receiver': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.celery.tasks.periodic_event_task': (BooksyTeams.UNASSIGNED,),
    'webapps.consents.tasks.create_consents_task': (BooksyTeams.UNASSIGNED,),
    'webapps.consents.tasks.dismiss_consents_task': (BooksyTeams.UNASSIGNED,),
    'webapps.consents.tasks.render_consent_pdf': (BooksyTeams.UNASSIGNED,),
    'webapps.consents.tasks.send_consent_business_email_task': (BooksyTeams.UNASSIGNED,),
    'webapps.consents.tasks.send_consent_customer_email_task': (BooksyTeams.UNASSIGNED,),
    'webapps.consents.tasks.update_appointment_consents_task': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.df_creator.tasks.push_digital_flyer_availability': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.ecommerce.tasks.send_notification_campaign': (BooksyTeams.ECOMMERCE,),
    'webapps.elasticsearch.tasks.BusinessAvailabilityRiverTask': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.CleanOldBusinessAvailabilityTask': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.all_business_availability_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.bulk_update_business_availability_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.elasticsearch.tasks.document_index_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.document_river_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.document_update_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.es_delete_object': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.fix_out_of_sync_docs_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.fix_out_of_sync_single_doc_type_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.elasticsearch.tasks.check_extra_dry_run_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.get_extra_dry_run_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.elasticsearch.tasks.update_business_availability_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.experiment_v3.tasks.check_experiment_watchers': (BooksyTeams.UNASSIGNED,),
    'webapps.experiment_v3.tasks.clear_experiment_activity_status_cache': (BooksyTeams.UNASSIGNED,),
    'webapps.experiment_v3.tasks.update_old_experiment_slots_with_experiment': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.family_and_friends.tasks.expired_invite_notifications.send_expired_invite_notification_task': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.CUSTOMER_ENGAGEMENT,
    ),
    'webapps.family_and_friends.tasks.member_business_customer_info.update_bci_relations_task': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.CUSTOMER_ENGAGEMENT,
    ),
    'webapps.family_and_friends.tasks.member_business_customer_info.update_bci_additional_data': (
        BooksyTeams.CUSTOMER_ENGAGEMENT,
    ),
    'webapps.family_and_friends.tasks.member_business_customer_info.update_inactive_member_bci_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.feeds.facebook.tasks.disconnect_business_from_fbe_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.feeds.facebook.tasks.update_fb_services_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.feeds.google.tasks.notify_conversion_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.french_certification.tasks.batch_close_periods_for_businesses_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.french_certification.tasks.close_periods_for_businesses_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.google_sign_in.tasks.upload_google_photo_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.images.tasks.image_index_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.images.tasks.migrate_photo_to_s3': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.images.tasks.publish_photo_to_portfolio_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.images.tasks.update_image_order_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.invoice.tasks.filter_objects_to_index_task': (BooksyTeams.UNASSIGNED,),
    'webapps.invoice.tasks.find_invoice_objects_to_index_task': (BooksyTeams.UNASSIGNED,),
    'webapps.invoice.tasks.index_invoice_objects_task': (BooksyTeams.UNASSIGNED,),
    'webapps.invoice.tasks.save_invoice_object_task': (BooksyTeams.UNASSIGNED,),
    'webapps.market_pay.tasks.restore_payout_data_task': (BooksyTeams.UNASSIGNED,),
    'webapps.marketing.tasks.clean_sms_invitation_events_task': (BooksyTeams.UNASSIGNED,),
    'webapps.marketplace.cms.tasks.refresh_region_category_task': (BooksyTeams.UNASSIGNED,),
    'webapps.marketplace.cms.tasks.refresh_region_homepage_task': (BooksyTeams.UNASSIGNED,),
    'webapps.marketplace.tasks.boost_celebration_moments_task': (
        BooksyTeams.PROVIDER_CALENDAR,
        BooksyTeams.PROVIDER_MARKETING,
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.marketplace.tasks.boost_pay_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.boost_refund_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.find_merchant_in_braintree_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.import_b_listings_from_lead_db': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.marketplace.tasks.make_sure_merchant_can_pay_for_boost_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.marketplace.tasks.mass_braintree_find_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.mass_check_chargeable': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.mass_set_chargeable_payable_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.pay_marketplace_chunk_transaction_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.marketplace.tasks.pay_marketplace_transaction_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.prepare_boost_trial_notification': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.push_waiting_businesses_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.recalculate_commissions_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.refresh_boost_braintree_processing_transactions_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.marketplace.tasks.rerun_not_enabled_boost': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.resend_push_waiting_businesses_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.retry_boost_claims_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.retry_marketplace_chunk_transaction': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.retry_marketplace_transaction_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.set_chargeable_task': (
        BooksyTeams.PROVIDER_CALENDAR,
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.marketplace.tasks.set_claimed_multiple_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.set_has_braintree_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.marketplace.tasks.update_martketplace_transaction_log_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.marketplace.tasks.update_transacion_row_amount': (
        BooksyTeams.PROVIDER_CALENDAR,
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.message_blast.tasks.process_message_blast_schedules_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.message_blast.tasks.process_message_blast_templates_batch_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.message_blast.tasks.process_message_blast_templates_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.message_blast.tasks.reset_to_default_message_blast_templates_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
    'webapps.message_blast.tasks.send_message_blast_lazy': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.message_blast.tasks.welcome_new_client_river_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.message_blast.tasks.check_msg_for_spam': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.navision.tasks.api.create_invoice_in_navision': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.api.create_not_synced_merchant_in_navision': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.api.create_or_update_merchant_in_navision': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.api.send_not_synced_invoices_to_navision': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.api.sync_changed_invoice_items_task': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.api.sync_invoices_with_errors_task': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.api.send_to_new_sandbox_test_invoices_bc_start_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.base.schedule_invoicing_task': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoices_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.saas_online_based_on_billing_cycle.'
    'create_saas_online_invoice_based_on_billing_cycle_task': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.saas_online_based_on_billing_cycle.'
    'create_saas_online_invoices_based_on_billing_cycle_task': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.saas_online_based_on_billing_cycle.update_invoice_item_as_charged_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.boost_online.create_boost_online_invoice_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.boost_online.create_boost_online_invoices_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.errors.process_error_task': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.errors.process_offline_invoice_error_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.errors.process_online_invoice_error_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.invoicing_summaries.approve_invoices_in_selected_invoice_summary_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.invoicing_summaries.remove_test_invoices_from_invoicing_summary_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.invoicing_summaries.remove_unapproved_invoices_from_summaries_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_batch_boost_invoice_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_batch_saas_invoice_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_boost_invoice_for_migration_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_boost_invoice_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_boost_invoices_for_migration_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_boost_invoices_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_saas_invoice_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_saas_invoices_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_sms_invoice_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.offline.create_offline_sms_invoices_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.tax_rates.fetch_tax_rate_for_zipcode_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.tax_rates.fill_tax_rate_table_from_business_zipcode_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.tax_rates.update_tax_rates_task': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.tax_rates.force_update_tax_rate': (BooksyTeams.FINANCE_AUTOMATION,),
    'webapps.navision.tasks.tax_rates.update_tax_rate_per_state_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.subscription_buyer.fill_subscription_buyer_data_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.subscription_buyer.update_buyer_with_business_data_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.subscription_buyer.create_buyers_based_on_data_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.subscription_buyer.update_buyers_based_on_data_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.subscription_buyer.verify_active_providers_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.subscription_buyer.update_entity_type_task': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.navision.tasks.buyer_merchant_integration.sync_buyer_with_merchant_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.buyer_merchant_integration.sync_subscription_buyers_with_merchant_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.navision.tasks.invoice_details_business_settings.invoice_details_confirmed_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.notification.tasks.add_receiver_filter_to_notification_index_mapping': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.async_send_notification_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.bulk_sms_send_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.custom.reminder_hour_old_values_cleanup': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.instant_notification_schedule_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.notifications_cleanup_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.process_notification_schedules_task_with_cache': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.push._save_history_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.push.apns_push_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.push.delete_expired_push_tokens_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.push.fcm_android_push_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.push.retry_send_push_notification_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.replace_banned_twilio_number': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.retrial.retrial_reset_sms_usage_task': (BooksyTeams.UNASSIGNED,),
    'webapps.notification.tasks.retrieve_stuck_received_notification_schedules_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.retrieve_stuck_started_notification_schedules_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.send_iterable_push_sent_notification': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.set_email_notification_status': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.set_sms_notification_status': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.single_notification_schedule_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.sms_codes.send_sms_registration_code_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.sms_history.save_sms_history_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.notification.tasks.smscodes_cleanup_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.email_codes_cleanup_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.notification.tasks.twilio_resend_registration_code': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.payment_gateway.tasks.initialize_fee_task': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.payment_gateway.tasks.update_payment_fee_amount_task': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.payment_gateway.tasks.synchronize_booksy_wallet_balance_transactions_task': (
        BooksyTeams.NEW_FINANCIAL_SERVICES,
    ),
    'webapps.payment_providers.tasks.capture_payment_task': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.payment_providers.tasks.check_and_update_card_expiry_status_task': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.payment_providers.tasks.check_tokenized_payment_method_menu_warnings_for_customer': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.payment_providers.tasks.mark_cards_invalid_because_of_payment_history': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.payments.payments_service.interfaces.celery.tasks.payments_service_payment_processed_task': (
        BooksyTeams.PAYMENT_PROCESSING,
        BooksyTeams.PAYMENT_NEXUS,
    ),
    'webapps.payments.tasks.send_basket_payment_details_via_email': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.pop_up_notification.tasks.create_what_is_new_family_and_friends_popup_for_all_users': (
        BooksyTeams.CUSTOMER_ENGAGEMENT,
    ),
    'webapps.pop_up_notification.tasks.pop_up_cleanup_task': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.pos.tasks.CancelAllTransactionsOlderThan30days': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.CancelBookingOnDepositFail': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.pos.tasks.ChargeCancellationFeeTransactions': (
        BooksyTeams.PAYMENT_PROCESSING,
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'webapps.pos.tasks.CheckoutPrepaidTransaction': (BooksyTeams.NO_SHOW_PROTECTION,),
    'webapps.pos.tasks.auto_refund_transaction': (BooksyTeams.PAYMENT_NEXUS,),
    'webapps.pos.tasks.CheckoutBooksyPayTransaction': (BooksyTeams.PAYMENT_NEXUS,),
    'webapps.pos.tasks.CloseAllCashRegisters': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.ClosePrepaymentTransactionAtTime': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.pos.tasks.CloseBooksyPayTransactionAtTime': (BooksyTeams.PAYMENT_NEXUS,),
    'webapps.pos.tasks.DisablePrepayments': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.pos.tasks.FetchAutoChargePOSes': (
        BooksyTeams.PAYMENT_PROCESSING,
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'webapps.pos.tasks.POSActivityChange': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.ReleaseAllDepositOnServicesAndBookings': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.pos.tasks.ReleaseDepositOnCancel': (
        BooksyTeams.PAYMENT_PROCESSING,
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'webapps.pos.tasks.ReleaseDepositOnPayment': (
        BooksyTeams.PAYMENT_PROCESSING,
        BooksyTeams.NO_SHOW_PROTECTION,
    ),
    'webapps.pos.tasks.SendReceiptToCustomer': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.SwitchToFraud': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.autopay_for_business_prepayment_task': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.disable_square_integration_task': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.batch_change_default_payment_method_to_pba_task': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.batch_change_pos_plans_task': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.cancel_cfp_task': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.execute_pending_refunds_task': (BooksyTeams.UNASSIGNED,),
    'webapps.pos.tasks.initialize_basket_payments': (BooksyTeams.PAYMENT_PROCESSING,),
    'webapps.pos.tasks.log_transaction_commission_changes_task': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.pos.tasks.recalculate_pos_plans': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.pos.tasks.recalculate_pos_plans_for_payment_type': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.pos.tasks.reissue_gift_card_task': (BooksyTeams.NEW_FINANCIAL_SERVICES,),
    'webapps.pos.tasks.handle_no_show_booksy_gift_card_task': (BooksyTeams.NEW_FINANCIAL_SERVICES,),
    'webapps.pos.tasks.remove_square_payment_method_for_selected_pos_ids': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.pos.actions.basket_payment_details_updated_event_handler_task': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.profile_completeness.tasks.update_profile_completeness_stats_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.public_partners.tasks.notification.notify_business_task': (BooksyTeams.UNASSIGNED,),
    'webapps.public_partners.tasks.notification.notify_client_cmd_task': (BooksyTeams.UNASSIGNED,),
    'webapps.public_partners.tasks.notification.notify_client_task': (BooksyTeams.UNASSIGNED,),
    'webapps.public_partners.tasks.oauth2.clear_oauth2_tokens_task': (BooksyTeams.UNASSIGNED,),
    'webapps.public_partners.tasks.partner_data.create_business_partner_data_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.public_partners.tasks.webhook.deliver_webhook_task': (BooksyTeams.UNASSIGNED,),
    'webapps.public_partners.tasks.webhook.schedule_webhooks_task': (BooksyTeams.UNASSIGNED,),
    'webapps.public_partners.tasks.webhook.send_signal_public_api_task': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.DeletePurchaseRequestsTask': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.InvoiceCreate': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.InvoiceMassSend': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.apple.apple_subscriptions_check_task': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.brain_tree.BraintreeFetchPlansTask': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.brain_tree.delete_old_card_verification_attempts': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.brain_tree.reassign_payment_methods_task': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.brain_tree.refresh_braintree_subscriptions': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.churn.business_churn_task': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.churn.cancel_subscriptions_before_churn_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.churn.clean_cancellation_reasons': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.google.RestoreExpiryNoneSubscription': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.google.acknowledge_google_play_purchase_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.mrr_reports.calculate_mmr_reports': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.apple_google_subs_migration.migrate_apple_google_subscriptions_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.purchase.tasks.apple_google_subs_migration.send_apple_google_subscriptions_task': (
        BooksyTeams.FINANCE_AUTOMATION,
    ),
    'webapps.purchase.tasks.offline.import_offline_transactions_task': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.offline.offline_subscriptions_sync_status_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.offline.switch_agreement_type_task': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.renewing_subscription.renew_one_business': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.renewing_subscription.unify_renewing_subscription_non_churned_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.renewing_subscription.unify_renewing_subscription_status_mismatched_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.renewing_subscription.unify_renewing_subscription_status_overdue_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.renewing_subscription.unify_renewing_subscription_status_paid': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.purchase.tasks.reports.send_double_subscriptions_report': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.segment.SegmentStatusChange': (BooksyTeams.UNASSIGNED,),
    'webapps.purchase.tasks.webhook.ProcessSingleSubscription': (BooksyTeams.UNASSIGNED,),
    'webapps.r_and_d.tasks.calculate_median_of_businesses': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.r_and_d.tasks.check_booking_reactivation': (BooksyTeams.UNASSIGNED,),
    'webapps.r_and_d.tasks.churned_merchant_user_reactivation': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.r_and_d.tasks.follow_up_start_scenario': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.r_and_d.tasks.high_volume_start_scenario': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.r_and_d.tasks.pattern_zip_codes_import_task': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.r_and_d.tasks.reactivate_booking_sms_task': (BooksyTeams.UNASSIGNED,),
    'webapps.r_and_d.tasks.start_scenario_for_found_businesses': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.reviews.import_reviews.dry_run_with_email_report_task': (BooksyTeams.UNASSIGNED,),
    'webapps.reviews.import_reviews.import_reviews_task': (BooksyTeams.UNASSIGNED,),
    'webapps.reviews.import_reviews.setup_data_and_run_import_task': (BooksyTeams.UNASSIGNED,),
    'webapps.reviews.tasks.post_customer_review_task': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.reviews.tasks.recompute_business_reviews_task': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.reviews.tasks.recompute_umbrella_reviews_task': (BooksyTeams.CUSTOMER_ENGAGEMENT,),
    'webapps.search_engine_tuning.tasks.update_addon_tunings_params_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.search_engine_tuning.tasks.update_availability_bulk_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.search_engine_tuning.tasks.update_availability_from_es': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.search_engine_tuning.tasks.update_business_category_tunings_params_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.search_engine_tuning.tasks.update_business_customer_tunings_params_task': (
        BooksyTeams.PROVIDER_CALENDAR,
    ),
    'webapps.search_engine_tuning.tasks.update_business_tuning_params_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.search_engine_tuning.tasks.update_businesses_tuning_params_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.search_engine_tuning.tasks.update_category_tunings_params_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.search_engine_tuning.tasks.update_commodity_tunings_params_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.search_engine_tuning.tasks.update_selected_businesses_tuning_params_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.search_engine_tuning.tasks.update_service_tunings_params_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.search_engine_tuning.tasks.update_service_variant_tunings_params_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.search_engine_tuning.tasks.update_user_tuning_task': (BooksyTeams.UNASSIGNED,),
    'webapps.search_engine_tuning.tasks.update_users_tuning_fast_task': (BooksyTeams.UNASSIGNED,),
    'webapps.search_engine_tuning.tasks.update_users_tuning_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_1st_no_show_for_business_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_1st_paid_status_achieved': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_1st_paid_status_achieved_branchio_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_1st_paid_status_achieved_gtm_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_bb_no_show_for_business_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_bcr_order_received_gtm_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_bcr_order_received_segment_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_bcr_reset_account_verify_gtm_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_bcr_reset_account_verify_segment_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_not_verified_account_gtm_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_not_verified_account_segment_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_pending_account_gtm_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_pending_account_segment_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_verified_account_gtm_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_bcr_stripe_kyc_verified_account_segment_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_bcr_terminal_ordered_branchio_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_bcr_terminal_ordered_gtm_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_bcr_terminal_ordered_segment_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_boost_off_branchio_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.segment.tasks.analytics_boost_on_branchio_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.segment.tasks.analytics_boost_on_off_gtm_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.segment.tasks.analytics_boost_on_off_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.segment.tasks.analytics_business_app_opened_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_categories_updated_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_contact_preferences_updated_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_business_info_updated_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_offline_migration_started_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_business_pba_enabled_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_pos_updated_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_re_trial_eligible_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_registration_completed_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_business_registration_started_task': (
        BooksyTeams.PROVIDER_CALENDAR,
    ),
    'webapps.segment.tasks.analytics_business_report_generated_to_email': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_status_updated_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_subscription_updated_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_user_language_set': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_business_special_offer_create_task': (
        BooksyTeams.PROVIDER_CONVERSION,
    ),
    'webapps.segment.tasks.analytics_cb_created_count_for_business_branchio_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_cb_created_count_in_days_for_business_segment_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.segment.tasks.analytics_cb_created_for_business_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_cb_created_for_customer_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.segment.tasks.analytics_cb_customer_info_updated_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_cb_finished_for_business_task': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.PROVIDER_CALENDAR,
    ),
    'webapps.segment.tasks.analytics_cb_finished_for_customer_task': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.PROVIDER_CALENDAR,
    ),
    'webapps.segment.tasks.analytics_cb_no_show_for_business_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_cb_started_for_customer': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_checkout_transaction_completed_task': (
        BooksyTeams.PROVIDER_CALENDAR,
    ),
    'webapps.segment.tasks.analytics_churn_reason_updated_task': (BooksyTeams.PROVIDER_ENGAGEMENT,),
    'webapps.segment.tasks.analytics_contact_preferences_updated_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_continuous_discovery_business_created_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.segment.tasks.analytics_customer_app_opened_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_customer_registration_completed_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_customer_search_query_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_customer_user_language_set': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_energy_cb_created_for_customer_branchio_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_gtm_onboarding_business_go_live_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.segment.tasks.analytics_import_completed_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.segment.tasks.analytics_invite_all_clicked_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.segment.tasks.analytics_invite_process_completed_branchio_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.segment.tasks.analytics_invite_process_completed_gtm_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.segment.tasks.analytics_invite_process_completed_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.segment.tasks.analytics_location_entered_gtm_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_onboarding_business_go_live_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.segment.tasks.analytics_onboarding_delay_set_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.segment.tasks.analytics_paid_status_achieved_branchio_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_payment_transaction_completed_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_booksy_med_app_status_task': (BooksyTeams.BOOKSY_MED,),
    'webapps.segment.tasks.analytics_basket_payment_completed_task': (
        BooksyTeams.PAYMENT_PROCESSING,
    ),
    'webapps.segment.tasks.analytics_postponed_invites_queued_gtm_task': (
        BooksyTeams.PROVIDER_ONBOARDING,
    ),
    'webapps.segment.tasks.analytics_postponed_invites_sent': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.segment.tasks.analytics_protection_service_enabled_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_quick_invite_sent_task': (BooksyTeams.PROVIDER_ONBOARDING,),
    'webapps.segment.tasks.analytics_review_completed_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.analytics_staffer_created_task': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.segment.tasks.analytics_subscription_payment_succeeded_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.analytics_view_item_list_gtm_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.segment.tasks.create_delayed_gtm_event_auth_data_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.forget_email_gdpr': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.merchant_activity_bulk': (BooksyTeams.DEFAULT_MAINTENANCE,),
    'webapps.segment.tasks.merchant_activity_weekly_report_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.save_100_used_app_version_records_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.save_used_app_version_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.segment_api_appointment_booked_task': (BooksyTeams.PROVIDER_CALENDAR,),
    'webapps.segment.tasks.segment_api_customer_invitation_sent': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.segment_api_overdue_status_started': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.segment_api_subscription_expired': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.segment_api_subscription_reactivated': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.segment_intercom_status_update': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.segment_intercom_status_update_businesses': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.segment_status_change_task': (BooksyTeams.UNASSIGNED,),
    'webapps.segment.tasks.send_analytics_1st_cb_created_for_business_to_facebook': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.segment.tasks.send_analytics_energy_cb_created_for_customer_to_facebook': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.statistics.tasks.aggregate_statistics_batch_task': (BooksyTeams.UNASSIGNED,),
    'webapps.statistics.tasks.aggregate_statistics_river_task': (BooksyTeams.UNASSIGNED,),
    'webapps.statistics.tasks.aggregate_statistics_single_task': (BooksyTeams.UNASSIGNED,),
    'webapps.statistics.tasks.generate_statistics_report': (BooksyTeams.UNASSIGNED,),
    'webapps.statistics.tasks.generate_summary_report': (BooksyTeams.UNASSIGNED,),
    'webapps.stats_and_reports.tasks.send_report_by_email_task': (BooksyTeams.PROVIDER_MARKETING,),
    'webapps.stripe_app.tasks.event_handler_task': (BooksyTeams.UNASSIGNED,),
    'webapps.stripe_integration.tasks.charge_fast_payout_paid_fee_task': (BooksyTeams.UNASSIGNED,),
    'webapps.stripe_integration.tasks.synchronize_stripe_payout_task': (BooksyTeams.UNASSIGNED,),
    'webapps.stripe_integration.tasks.trigger_fast_payout_paid_fees_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.stripe_terminal.tasks.fetch_hardware_orders_shipment_tracking': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.stripe_terminal.tasks.update_order_history_task': (BooksyTeams.UNASSIGNED,),
    'webapps.stripe_terminal.tasks.update_order_payment_status_task': (BooksyTeams.UNASSIGNED,),
    'webapps.stripe_terminal.tasks.update_order_status_task': (BooksyTeams.UNASSIGNED,),
    'webapps.stripe_terminal.tasks.update_payment_intent_device_type_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.structure.tasks.map_regions_to_categories_task': (BooksyTeams.UNASSIGNED,),
    'webapps.structure.tasks.region_index_task': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.apple.authorize_apple_identities_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.user.tasks.apple.authorize_apple_user_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.user.tasks.booking_score.bulk_update_user_booking_score_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.user.tasks.booking_score.update_users_booking_score': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.business_customer_info.update_user_bci': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.business_customer_info.publish_basic_customer_data_changed': (
        BooksyTeams.PROVIDER_ENGAGEMENT,
    ),
    'webapps.user.tasks.business_customer_info.user_claim_bci_task': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.business_customer_info.user_reindex_bci_task': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.cleanup.email_token_cleanup_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.user.tasks.cleanup.session_cleanup_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.user.tasks.comments.reindex_user_comments': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.family_and_friends.update_member_profile_task': (
        BooksyTeams.CUSTOMER_BOOKING,
    ),
    'webapps.user.tasks.gdpr.gdpr_customer_data_export_task': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.profile.bulk_update_profile_language_task': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.sync.sync_user_booksy_auth_from_replica_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.user.tasks.sync.sync_user_booksy_auth_task': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.user.tasks.sync.sync_user_email_in_analytics': (BooksyTeams.CUSTOMER_ONBOARDING,),
    'webapps.user.tasks.sync.user_imported_sync_booksy_auth_task': (
        BooksyTeams.CUSTOMER_ONBOARDING,
    ),
    'webapps.user.tasks.user_deletion.remove_photos_from_buckets': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.user_deletion.send_email_with_deletion_cancellation_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.user.tasks.user_deletion.send_emails_about_user_deletion_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.user.tasks.user_deletion.user_deletion_periodic_task': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.wallet.get_or_create_customer_wallet_task': (BooksyTeams.UNASSIGNED,),
    'webapps.user.tasks.wallet.update_customer_wallet_task': (BooksyTeams.UNASSIGNED,),
    'webapps.utt.tasks.calculate_predictions_confirmation_priority': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.utt.tasks.update_predictions_confirmation_priority': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.versum_migration.tasks.fill_versum_subscription_information_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.versum_migration.tasks.schedule_appointments_sync': (BooksyTeams.UNASSIGNED,),
    'webapps.versum_migration.tasks.versum_migration_customer_registration_completed_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.visibility_promotion.tasks.purchase_visibility_promotion_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.visibility_promotion.tasks.capture_for_order_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.visibility_promotion.tasks.capture_all_orders_task': (BooksyTeams.CUSTOMER_SEARCH,),
    'webapps.visibility_promotion.tasks.deactivate_visibility_promotions_task': (
        BooksyTeams.CUSTOMER_SEARCH,
    ),
    'webapps.voucher.tasks.fill_sold_vouchers_before_migration_threshold_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.voucher.tasks.fix_invalid_voucher_templates_services_task': (BooksyTeams.UNASSIGNED,),
    'webapps.voucher.tasks.log_voucher_changes_task': (BooksyTeams.UNASSIGNED,),
    'webapps.voucher.tasks.migrate_existing_voucher_templates_to_all_services_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.voucher.tasks.migrate_existing_voucher_templates_valid_till_to_never_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.voucher.tasks.migrate_existing_voucher_templates_valid_till_values_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.voucher.tasks.migrate_single_business_vouchers_templates_to_same_value_and_price': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.voucher.tasks.migrate_vouchers_templates_to_same_value_and_price': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.voucher.tasks.remove_unused_redundant_voucher_templates_task': (
        BooksyTeams.NEW_FINANCIAL_SERVICES,
    ),
    'webapps.voucher.tasks.update_voucher_status_task': (BooksyTeams.UNASSIGNED,),
    'webapps.wait_list.tasks.event_query_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.wait_list.tasks.match_waitlist_to_booking': (
        BooksyTeams.CUSTOMER_BOOKING,
        BooksyTeams.PROVIDER_CALENDAR,
    ),
    'webapps.wait_list.tasks.waitlist_scenario_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.warehouse.tasks.business_import_warehouse_commodities_task': (
        BooksyTeams.PROVIDER_ENGAGEMENT,
    ),
    'webapps.warehouse.tasks.import_commodities_dry_run_with_email_report_task': (
        BooksyTeams.UNASSIGNED,
    ),
    'webapps.warehouse.tasks.import_wholesaler_commodities_task': (BooksyTeams.UNASSIGNED,),
    'webapps.warehouse.tasks.parse_and_import_warehouse_commodities': (BooksyTeams.UNASSIGNED,),
    'webapps.warehouse.tasks.send_email_to_supplier_task': (BooksyTeams.UNASSIGNED,),
    'webapps.zoom.tasks.create_missing_zoom_meetings_task': (BooksyTeams.CUSTOMER_BOOKING,),
    'webapps.subdomain_grpc.tasks.create_subdomains_regenerate_deeplinks_task': (
        BooksyTeams.PROVIDER_MARKETING,
    ),
}
