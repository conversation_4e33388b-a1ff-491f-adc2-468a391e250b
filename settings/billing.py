from collections import defaultdict

from country_config.base_info import CURRENCY_CODES
from country_config.enums import Country

from lib.enums import PaymentMethodType, SMSTypeEnum

BILLING_ATTEMPTS_LIMIT_MAX_COUNT = 5
BILLING_ATTEMPTS_LIMIT_RESET_MINUTES = 30


def global_currencies_set():
    return {
        'USD',
    }


COUNTRY__BILLING_CURRENCIES = defaultdict(global_currencies_set)

# Add additional currencies here
LOCAL_ADDITIONAL_CURRENCIES = defaultdict(
    set,
    {
        Country.IT: {
            CURRENCY_CODES[Country.PL],
        }
    },
)

for c in Country.supported():
    COUNTRY__BILLING_CURRENCIES[c].add(CURRENCY_CODES[c.value])
    COUNTRY__BILLING_CURRENCIES[c].update(LOCAL_ADDITIONAL_CURRENCIES[c])

# TODO - fill in
BRAINTREE_PAYMENT_METHODS_BY_COUNTRY = {
    Country.US: (
        PaymentMethodType.CREDIT_CARD,
        PaymentMethodType.PAYPAL,
        PaymentMethodType.VENMO,
    ),
    Country.PL: (PaymentMethodType.CREDIT_CARD,),
    Country.GB: (
        PaymentMethodType.CREDIT_CARD,
        PaymentMethodType.PAYPAL,
    ),
    Country.IE: (
        PaymentMethodType.CREDIT_CARD,
        PaymentMethodType.PAYPAL,
    ),
    Country.ES: (
        PaymentMethodType.CREDIT_CARD,
        PaymentMethodType.PAYPAL,
    ),
    Country.ZA: (PaymentMethodType.CREDIT_CARD,),
    Country.BR: (
        PaymentMethodType.CREDIT_CARD,
        PaymentMethodType.PAYPAL,
    ),
}

BRAINTREE_PAYMENT_METHODS_DEFAULT = (PaymentMethodType.CREDIT_CARD,)

BRAINTREE_PAYMENT_METHODS_ALL = (
    PaymentMethodType.CREDIT_CARD,
    PaymentMethodType.PAYPAL,
    PaymentMethodType.VENMO,
)

SMS_COST_ALERT_DEFAULT_LEVEL = 0

# SMS CONFIG

## like FIRST_N_SMS_LIMITS.NON_TRIAL but for new billing

BILLING_FIRST_N_SMS_LIMITS_NON_TRIAL_DEFAULT = {
    SMSTypeEnum.INVITATION: 6,
    SMSTypeEnum.MARKETING: 0,
    SMSTypeEnum.SYSTEM: None,
}

BILLING_FIRST_N_SMS_LIMITS_NON_TRIAL_PER_COUNTRY = {
    Country.US: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: None,
    },
    Country.CA: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: None,
    },
}


# STRIPE
COUNTRY__BILLING_STRIPE_PAYMENT_METHODS = {}
BILLING_STRIPE_PAYMENT_METHODS_DEFAULT = (PaymentMethodType.CREDIT_CARD,)


# OFFLINE/ONLINE MIGRATION
OFFLINE_TO_ONLINE_MIGRATION_CAMPAIGN_VALID_DAYS = 14
OFFLINE_TO_ONLINE_MIGRATION_CAMPAIGN_BUFFER_HOURS = 12


# STAFFER SASS SETTINGS
BILLING_STAFF_UNLIMITED_QTY = 999


# LONG SUBSCRIPTION MAPPER
BILLING_LONG_SUBSCRIPTION_STAFFER_COUNT_LIMIT = 14


BILLING_COUNTRIES_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION = [
    Country.IE,
    Country.GB,
    Country.US,
    Country.CA,
]

BILLING_COUNTRIES_ALLOW_CHANGE_CARD_NETWORK = [
    Country.FR,
    Country.ES,
]

BILLING_COUNTRIES_AUTO_RETRY_CHARGE_DAYS = {Country.PL: [3, 7, 13, 21]}

BILLING_COUNTRIES_MIGRATED_SUBSCRIPTION_INITIAL_TASK = [Country.PL]
