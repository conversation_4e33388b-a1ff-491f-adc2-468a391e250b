from collections import defaultdict
from datetime import date

from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin import <PERSON>bularIn<PERSON>, SimpleListFilter
from django.core.exceptions import ValidationError
from django.forms.widgets import TextInput
from django.shortcuts import redirect, get_object_or_404
from django.urls import re_path as url, reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from lib.admin_helpers import (
    admin_link,
    BaseModelAdmin,
    NoAddDelMixin,
    NoChangeMixin,
    NoDelMixin,
)
from lib.feature_flag.feature.boost import BoostBanBackendFlag
from lib.tools import SimplePaginator
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin, use_new_permissions
from webapps.billing.permissions import BillingUserPermission
from webapps.booking.enums import AppointmentStatus
from webapps.boost.enums import (
    BoostAppointmentStatus,
    MarketplaceTransactionProgressStatus,
    MarketplaceTransactionStage,
    MarketplaceTransactionType,
    BoostBanLength,
    BoostBanStatus,
    BoostBanNotificationStatus,
    ALLOWED_BOOST_BAN_LENGTHS_FOR_TYPE,
)
from webapps.boost.helpers import is_boost_overdue
from webapps.boost.models import (
    BoostAppointment,
    BoostSettings,
    BoostSettingsHistory,
    BoostBan,
    BoostBanError,
    BoostFraudSuspicion,
)
from webapps.boost.storage import BoostClaimScreenshotStorage
from webapps.boost.tasks import apply_boost_bans, end_boost_bans
from webapps.business.models import Business, BusinessPromotion
from webapps.marketplace.models import (
    MarketplaceClaimDenyReason,
    MarketplaceTransactionRow,
    MarketplaceTransactionStatus,
)
from webapps.marketplace.tasks import (
    boost_refund_task,
    set_chargeable_task,
    set_claimed_multiple_task,
)
from webapps.marketplace.utils import get_status_description
from webapps.survey.models import BoostClaimPollResponse
from webapps.user.groups import GroupNameV2
from webapps.user.tools import get_user_from_django_request


class ClaimAction:
    @staticmethod
    def get_button(obj):
        if obj.status in [
            BoostAppointmentStatus.CLAIMED,
            BoostAppointmentStatus.CLAIM_PENDING,
            BoostAppointmentStatus.CLAIM_OVERDUE,
        ]:
            return ''

        link = reverse('admin:claim_payments', kwargs={'ba_id': obj.id})
        return format_html(
            "<a href='{}' id='claim_marketplace_transaction_row' class='btn btn-danger'>Claim</a>",
            link,
        )

    @staticmethod
    def action_view(request, ba_id):
        boost_appointment = BoostAppointment.objects.get(id=ba_id)
        if boost_appointment.status == BoostAppointmentStatus.CLAIMED:
            messages.error(request, 'Already claimed')
        else:
            boost_appointment.claim_by_admin(request.user.email)
            messages.success(request, 'Status changed to claimed')
        return redirect(reverse('admin:boost_boostappointment_changelist'))

    @staticmethod
    def claim_selected_objects(_, request, queryset):
        if queryset.filter(status=BoostAppointmentStatus.CLAIMED).exists():
            messages.error(request, 'At least one row is already claimed')
            return
        row_ids = queryset.values_list('rows__id', flat=True).distinct()
        set_claimed_multiple_task.delay(list(row_ids), mode='claim', user_email=request.user.email)


class RefundAction:
    @staticmethod
    def get_button(obj):
        if (
            obj is None
            or obj.transaction.status != MarketplaceTransactionProgressStatus.CLOSED
            or obj.status not in (BoostAppointmentStatus.NOSHOW, BoostAppointmentStatus.CLAIMED)
            or all(row.refunded for row in obj.rows.filter(deleted__isnull=True))
        ):
            return ''

        link = reverse('admin:refund_payments', kwargs={'ba_id': obj.id})
        return format_html(
            "<a href='{}' id='refund_marketplace_transaction_row' class='btn btn-danger'>Refund"
            "</a>",
            link,
        )

    @staticmethod
    def action_view(request, ba_id):
        boost_appointment = BoostAppointment.objects.get(id=ba_id)

        transaction_ = boost_appointment.transaction
        status = transaction_.transaction_status

        def is_refundable(boost_appt):
            """returns True if it can be refunded"""
            return (
                BoostAppointment.objects.filter(id=boost_appt.id)
                .annotate_transaction_stage()
                .filter(transaction_stage=MarketplaceTransactionStage.CHARGED)
                .exists()
            )

        if transaction_.status != MarketplaceTransactionProgressStatus.CLOSED:
            messages.error(request, 'Too early to be refunded - transaction is not yet closed')
        elif not is_refundable(boost_appointment):
            messages.error(request, 'Already refunded (or deleted)')
        elif not status or not status.external_id:
            messages.error(request, 'No transaction assigned to this row')
        elif not status.refundable:
            messages.error(request, 'Only settling and settled transactions can be refunded')
        else:
            boost_refund_task.delay(transaction_.id, [ba_id], are_these_boost_appointments=True)
            messages.success(request, 'Transaction refunded')
        return redirect(reverse('admin:boost_boostappointment_changelist'))

    @staticmethod
    def refund_selected_objects(_, request, queryset):  # pylint: disable=too-many-return-statements
        transaction_dict = defaultdict(list)

        if queryset.filter(rows__refunded=True).exists():
            messages.error(request, 'Cannot be refunded: at least one row is already refunded')
            return

        for ba_obj in queryset:
            transaction_ = ba_obj.transaction
            status = transaction_.transaction_status
            if (
                ba_obj.status not in [BoostAppointmentStatus.CLAIMED, BoostAppointmentStatus.NOSHOW]
                or transaction_.status != MarketplaceTransactionProgressStatus.CLOSED
            ):
                messages.error(request, 'Cannot be refunded')
                return
            if not status or not status.external_id:
                messages.error(request, 'No transaction assigned to this BA')
                return
            if not status.refundable:
                messages.error(request, 'Only settling and settled transactions can be refunded')
                return

            transaction_dict[transaction_.id].append(ba_obj.id)

        if not transaction_dict:
            messages.warning(request, 'No refunds to schedule')
            return

        for txn_id, ba_list in transaction_dict.items():
            boost_refund_task.delay(txn_id, ba_list, are_these_boost_appointments=True)

        messages.success(request, 'Refund successfully scheduled for selected BoostAppointments')


class AcceptClaimAction:
    @staticmethod
    def get_button(obj):
        if obj.status != BoostAppointmentStatus.CLAIM_PENDING:
            return ''

        link = reverse('admin:accept_claim', kwargs={'ba_id': obj.id})
        return format_html(
            "<a href='{}' id='accept_claim_marketplace_transaction_row' class='btn btn-info'>"
            "Accept claim</a>",
            link,
        )

    @staticmethod
    def action_view(request, ba_id):
        boost_appointment = BoostAppointment.objects.get(id=ba_id)
        if boost_appointment.status != BoostAppointmentStatus.CLAIM_PENDING:
            messages.error(request, 'Only pending claims can be accepted')
        else:
            boost_appointment.accept_claim(request.user.email)
            messages.success(
                request,
                'Claim accepted. Refund (if needed) is scheduled within the next 20 min.',
            )
        return redirect(reverse('admin:boost_boostappointment_changelist'))

    @staticmethod
    def accept_claim_for_selected_objects(_, request, queryset):
        if queryset.exclude(status=BoostAppointmentStatus.CLAIM_PENDING).exists():
            messages.error(request, 'At least one row is not a pending claim')
            return
        row_ids = queryset.values_list('rows__id', flat=True).distinct()
        set_claimed_multiple_task.delay(list(row_ids), mode='accept', user_email=request.user.email)


class SetPendingAction:
    @staticmethod
    def get_button(obj):
        if obj.status not in (BoostAppointmentStatus.CLAIM_OVERDUE, BoostAppointmentStatus.PAYABLE):
            return ''

        link = reverse('admin:set_pending', kwargs={'ba_id': obj.id})
        return format_html(
            "<a href='{}' id='set_pending_marketplace_transaction_row' class='btn btn-danger'>"
            "Set to pending</a>",
            link,
        )

    @staticmethod
    def action_view(request, ba_id):
        boost_appointment = BoostAppointment.objects.get(id=ba_id)
        if boost_appointment.status == BoostAppointmentStatus.CLAIM_PENDING:
            messages.error(request, 'Already pending')
        else:
            boost_appointment.set_claim_pending(request.user.email)
            messages.success(request, 'Status changed to pending')
        return redirect(reverse('admin:boost_boostappointment_changelist'))

    @staticmethod
    def set_status_to_claim_pending_for_selection(_, request, queryset):
        if queryset.filter(status=BoostAppointmentStatus.CLAIM_PENDING).exists():
            messages.error(request, 'At least one row is already in pending status')
            return
        if queryset.exclude(
            status__in=(
                BoostAppointmentStatus.CLAIM_OVERDUE,
                BoostAppointmentStatus.PAYABLE,
            )
        ).exists():
            messages.error(request, 'At least one row is not payable or claim overdue')
            return

        for ba_obj in queryset.all():
            ba_obj.set_claim_pending(request.user.email)


class ClaimDenyAction:
    @staticmethod
    def get_buttons(obj):
        if obj.status != BoostAppointmentStatus.CLAIM_PENDING:
            return ''

        buttons = []
        for reason in MarketplaceClaimDenyReason.objects.filter(visible=True):
            link = reverse(
                'admin:deny_claim_payments', kwargs={'ba_id': obj.id, 'reason_id': reason.id}
            )
            buttons.append(
                f"<a href='{link}' id='deny_claim_marketplace_transaction_row'"
                f" class='btn btn-danger'>Deny claim: {reason.short_name}</a>"
            )
        return format_html('</br>'.join(buttons))

    @staticmethod
    def action_view(request, ba_id, reason_id):
        boost_appointment = BoostAppointment.objects.get(id=ba_id)
        if boost_appointment.status != BoostAppointmentStatus.CLAIM_PENDING:
            messages.error(request, 'Only pending claims can be denied')
        else:
            boost_appointment.deny_claim(reason_id, request.user.email)
            messages.success(request, 'Status changed to denied')
        return redirect(reverse('admin:boost_boostappointment_changelist'))

    @classmethod
    def _action_factory(cls, claim_reason_id):
        def action(_, request, queryset):
            if queryset.exclude(status=BoostAppointmentStatus.CLAIM_PENDING).exists():
                messages.error(request, 'At least one boost appointment is not pending claim')
                return

            for boost_appointment in queryset.select_related('appointment__booked_for'):
                boost_appointment.deny_claim(claim_reason_id, request.user.email)

        return action

    @classmethod
    def get_all_possible_actions(cls):
        actions = {}

        for claim_reason in MarketplaceClaimDenyReason.objects.filter(visible=True):
            short_description = f'Deny claim: {claim_reason.short_name}'
            claim_reason_id = claim_reason.id

            action = cls._action_factory(claim_reason_id)

            action.short_description = short_description
            action_name = f'deny_claim_{claim_reason_id}'
            actions[action_name] = (action, action_name, short_description)

        return actions


class AcceptNoShowAction:
    @staticmethod
    def get_button(obj):
        appointment = obj.appointment
        business = appointment.business
        if (
            appointment.status != AppointmentStatus.NOSHOW
            or not appointment.payable
            or is_boost_overdue(appointment.booked_till, business)
        ):
            return ''

        link = reverse('admin:accept_noshow', kwargs={'ba_id': obj.id})
        return format_html(
            "<a href='{}' id='accept_noshow' class='btn btn-danger'>Accept no-show</a>",
            link,
        )

    @staticmethod
    def action_view(request, ba_id):
        boost_appointment = BoostAppointment.objects.get(id=ba_id)
        set_chargeable_task.run(boost_appointment.appointment_id, no_show_acceptance_mode=True)

        if (
            BoostAppointment.objects.filter(id=ba_id, appointment__payable=False)
            .exclude(status=BoostAppointmentStatus.OFFLINE)
            .annotate_transaction_stage()
            .filter(transaction_stage=MarketplaceTransactionStage.CHARGED)
            .exists()
        ):
            boost_refund_task.delay(
                boost_appointment.transaction_id, [ba_id], are_these_boost_appointments=True
            )

        return redirect(reverse('admin:boost_boostappointment_changelist'))

    @staticmethod
    def accept_noshow_for_selected_objects(_, request, queryset):
        # set_chargeable_tasks should be called synchronously, function is not fully ready
        # details: BOOS-723
        # do not use this until this task is done
        boost_appointments = queryset.filter(
            appointment__payable=True, appointment__status=AppointmentStatus.NOSHOW
        ).select_related('appointment', 'appointment__business')
        for boost_appt in boost_appointments:
            appt = boost_appt.appointment
            if is_boost_overdue(appt.booked_till, appt.business):
                return
            set_chargeable_task.delay(appt.id, no_show_acceptance_mode=True)


class TransactionStageFilter(SimpleListFilter):
    title = 'TransactionStage'
    parameter_name = 'TransactionStage'

    def lookups(self, request, model_admin):
        return [
            *MarketplaceTransactionStage.choices(),
            ('Pending', 'Pending refund'),
        ]

    def queryset(self, request, queryset):
        if self.value() in MarketplaceTransactionStage.choices_map().keys():
            return queryset.annotate_transaction_stage().filter(transaction_stage=self.value())
        if self.value() == 'Pending':
            return queryset.filter_pending_refunds()
        return queryset


class MarketplaceTransactionRowInline(NoAddDelMixin, admin.TabularInline):
    model = MarketplaceTransactionRow
    fields = readonly_fields = (
        'row_id',
        'subbooking_id',
        'service_name',
        'booking_price',
        'uncapped_amount',
        'refunded',
        'deleted',
    )

    def get_queryset(self, request):
        return self.model.all_objects.get_queryset().select_related(
            'subbooking__service_variant__service'
        )

    @staticmethod
    def row_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)

    @staticmethod
    def service_name(obj):
        if obj.subbooking.service_name:
            return obj.subbooking.service_name
        return obj.subbooking.service_variant.service.name


class BoostAppointmentToStatusInline(NoAddDelMixin, TabularInline):
    model = MarketplaceTransactionStatus.boost_appointments.through
    fields = readonly_fields = (
        'marketplacetransactionstatus_id',
        'status_char',
        'external_id',
        'amount',
        'charge_date',
        'response_code',
        'response_text',
        'braintree_description',
        'decline_type',
        'gateway_rejection_reason',
        'type',
    )

    @staticmethod
    def marketplacetransactionstatus_id(obj):
        return obj.status.id

    @staticmethod
    def status_char(obj):
        return obj.status.status

    @staticmethod
    def external_id(obj):
        return obj.status.external_id

    @staticmethod
    def amount(obj):
        return obj.status.amount

    @staticmethod
    def charge_date(obj):
        return obj.status.charge_date

    @staticmethod
    def response_code(obj):
        return obj.status.response_code

    @staticmethod
    def response_text(obj):
        return obj.status.response_text

    @staticmethod
    def braintree_description(obj):
        return get_status_description(obj.status.response_code)

    @staticmethod
    def decline_type(obj):
        return obj.status.response_type

    @staticmethod
    def gateway_rejection_reason(obj):
        return obj.status.gateway_rejection_reason

    @staticmethod
    def type(obj):
        return dict(MarketplaceTransactionType.choices())[obj.status.type]


class BoostAppointmentAdmin(NoAddDelMixin, NoChangeMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    paginator = SimplePaginator
    model = BoostAppointment
    list_display = (
        'id',
        'appointment_link',
        'appointment_booked_till',
        'business_name_city',
        'business_id',
        'customer_name',
        'gross_amount' if settings.BOOST.GROSS_VALUE else 'amount',
        'appointment_price',
        'status',
        'claim_reason',
        'claim_deny_reason',
        'deleted',
    )
    search_fields = (
        '=id',
        '=appointment_id',
        'boost_promotion__business_id',
        '=boost_promotion_id',
        '=transaction_id',
    )
    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('business', 'business_city'),
                    'appointment',
                    'customer_name_appointments_list',
                    'appointment_time',
                    'appointment_booking_source',
                ),
            },
        ),
        (
            'Boost details',
            {
                'fields': (
                    ('gross_amount', 'net_amount', 'appointment_price'),
                    ('tax_amount', 'tax_rate'),
                    (
                        'commission_percent',
                        'commission',
                        'boost_promotion',
                        'global_claim_percent',
                        'monthly_claim_percent',
                    ),
                ),
            },
        ),
        (
            'Status',
            {
                'fields': (
                    'status',
                    'refunded',
                    'deleted',
                    'transaction',
                ),
            },
        ),
        (
            'Claim',
            {
                'fields': (
                    ('claim_reason', 'claim_explanation'),
                    'claim_images',
                    'claim_deny_reason',
                ),
            },
        ),
    )
    list_filter = ('status', TransactionStageFilter)
    inlines = (
        MarketplaceTransactionRowInline,
        BoostAppointmentToStatusInline,
    )
    actions = [
        ClaimAction.claim_selected_objects,
        RefundAction.refund_selected_objects,
        AcceptClaimAction.accept_claim_for_selected_objects,
        SetPendingAction.set_status_to_claim_pending_for_selection,
        # AcceptNoShowAction.accept_noshow_for_selected_objects,  # to be fixed with BOOS-723
    ]
    list_per_page = 20
    hide_keyword_field = True

    def get_queryset(self, request):
        return self.model.objects.get_queryset().select_related(
            'boost_promotion__business',
            'boost_promotion__commission',
            'appointment__booked_for__user',
            'appointment__booked_for__deny_claim_reason',
            'transaction',
        )

    # ==== fields section ====

    @staticmethod
    def appointment_link(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj.appointment), obj.appointment.id)

    @staticmethod
    def business_name_city(obj):
        business = obj.boost_promotion.business
        return format_html(
            '<a href="{}">{}</a>', admin_link(business), f'{business.name} [{business.city}]'
        )

    @staticmethod
    def business_id(obj):
        return obj.boost_promotion.business_id

    @staticmethod
    def business_city(obj):
        return obj.boost_promotion.business.city

    @staticmethod
    def appointment_booked_till(obj):
        return obj.appointment.booked_till

    @staticmethod
    def business(obj):
        business = obj.boost_promotion.business
        return format_html('<a href="{}">{}</a>', admin_link(business), business)

    @staticmethod
    def appointment_time(obj):
        return f'{obj.appointment.booked_from} - {obj.appointment.booked_till}'

    @staticmethod
    def appointment_booking_source(obj):
        return obj.appointment.source

    @staticmethod
    def customer_name_appointments_list(obj):
        bci = obj.appointment.booked_for
        if not bci:
            return '-'
        if bci.user:
            full_name = f'{bci.user.first_name} {bci.user.last_name}'
        else:
            full_name = f'{bci.first_name} {bci.last_name}'

        return format_html(
            '{}&nbsp&nbsp&nbsp&nbsp<a href="{}">{}</a>',
            full_name,
            f'/admin/{settings.API_COUNTRY}/booking/appointment/?booked_for__id={bci.id}',
            'See all appointments of this client',
        )

    @staticmethod
    def customer_name(obj):
        bci = obj.appointment.booked_for
        if not bci:
            return '-'
        if bci.user:
            return f'{bci.user.first_name} {bci.user.last_name}'
        return f'{bci.first_name} {bci.last_name}'

    @staticmethod
    def net_amount(obj):
        return obj.amount

    @staticmethod
    def commission_percent(obj):
        return obj.boost_promotion.commission.commission

    @staticmethod
    def commission(obj):
        commission = obj.boost_promotion.commission
        return format_html('<a href="{}">{}</a>', admin_link(commission), commission)

    @staticmethod
    def global_claim_percent(obj):
        return f'{obj.boost_promotion.business.get_global_claim_percent()}%'

    @staticmethod
    def monthly_claim_percent(obj):
        return f'{obj.boost_promotion.business.get_monthly_claim_percent()}%'

    @staticmethod
    def claim_reason(obj):
        if obj.claim:
            return obj.claim.choice
        if obj.appointment.booked_for and obj.appointment.booked_for.marketplace_claim:
            return obj.appointment.booked_for.marketplace_claim
        return '-'

    @staticmethod
    def claim_deny_reason(obj):
        booked_for = obj.appointment.booked_for
        if not booked_for:
            return '-'
        claim_deny = booked_for.deny_claim_reason
        return claim_deny.description if claim_deny else '-'

    @staticmethod
    def refunded(obj):
        return obj.is_refunded

    @staticmethod
    def claim_explanation(obj):
        response = BoostClaimPollResponse.objects.filter(
            transaction_row__in=MarketplaceTransactionRow.all_objects.filter(boost_appointment=obj)
        ).first()
        return response.text if response else '-'

    @staticmethod
    def claim_images(obj):
        url_list = BoostClaimScreenshotStorage().get_urls_for_boost_appointment(
            obj.boost_promotion.business_id, obj.id
        )
        html = '<br />'.join(f'<img src="{image_url}"/>' for image_url in url_list)
        return mark_safe(html) or '-'  # nosemgrep: avoid-mark-safe

    # ==== actions section ====

    def modify_actions(self, request, actions):
        actions.update(**ClaimDenyAction.get_all_possible_actions())
        return actions

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.refund_marketplace_transaction_row = RefundAction.get_button(obj)
        self.claim_marketplace_transaction_row = ClaimAction.get_button(obj)
        self.deny_claim_marketplace_transaction_row = ClaimDenyAction.get_buttons(obj)
        self.accept_claim_marketplace_transaction_row = AcceptClaimAction.get_button(obj)
        self.set_pending_marketplace_transaction_row = SetPendingAction.get_button(obj)
        self.accept_noshow = AcceptNoShowAction.get_button(obj)

        return super().get_form(request, obj, change, **kwargs)

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<ba_id>\d+)/claim_payments$',
                self.admin_site.admin_view(ClaimAction.action_view),
                name='claim_payments',
            ),
            url(
                r'^(?P<ba_id>\d+)/accept_claim',
                self.admin_site.admin_view(AcceptClaimAction.action_view),
                name='accept_claim',
            ),
            url(
                r'^(?P<ba_id>\d+)/(?P<reason_id>\d+)/deny_claim_payments$',
                self.admin_site.admin_view(ClaimDenyAction.action_view),
                name='deny_claim_payments',
            ),
            url(
                r'^(?P<ba_id>\d+)/refund_payments$',
                self.admin_site.admin_view(RefundAction.action_view),
                name='refund_payments',
            ),
            url(
                r'^(?P<ba_id>\d+)/set_pending$',
                self.admin_site.admin_view(SetPendingAction.action_view),
                name='set_pending',
            ),
            url(
                r'^(?P<ba_id>\d+)/accept_noshow$',
                self.admin_site.admin_view(AcceptNoShowAction.action_view),
                name='accept_noshow',
            ),
        ]
        return additional_urls + urls


class BoostSettingsForm(forms.ModelForm):
    days_threshold = forms.IntegerField(initial=99)
    total_threshold = forms.IntegerField(initial=9999)
    days_threshold_enabled = forms.BooleanField(
        initial=False,
        required=False,
    )
    total_threshold_enabled = forms.BooleanField(
        initial=False,
        required=False,
    )

    class Meta:
        model = BoostSettings
        fields = (
            'region',
            'days_threshold_enabled',
            'days_threshold',
            'total_threshold_enabled',
            'total_threshold',
        )
        labels = {'total_threshold': f'Total threshold {settings.COUNTRY_CONFIG.currency_code}'}


class BoostSettingsHistoryInline(NoAddDelMixin, admin.TabularInline):
    model = BoostSettingsHistory
    fields = readonly_fields = (
        'created',
        'operator',
        'model',
        'data',
        'metadata',
    )
    ordering = ('-created',)


class BoostSettingsAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    form = BoostSettingsForm
    list_display = (
        'id',
        'region',
    )
    readonly_fields = ('region',)
    raw_id_fields = ('region',)
    inlines = (BoostSettingsHistoryInline,)

    def has_change_permission(self, request, obj=None):
        if use_new_permissions(request.user):
            return super().has_change_permission(request, obj)
        return BillingUserPermission(request).is_billing_admin

    def save_model(self, request, obj, form, change) -> None:
        obj.save(_history={'operator': request.user.id})


class BoostBanAddForm(forms.ModelForm):
    active_from = forms.DateField(disabled=True, initial=date.today)

    class Meta:
        model = BoostBan
        fields = (
            'business',
            'type',
            'length',
            'reason',
            'reason_description',
            'invoice_required',
            'active_from',
        )
        widgets = {'business': TextInput(attrs={'required': True})}

    def clean(self):
        cleaned_data = super().clean()
        business = cleaned_data.get('business')
        if not business:
            raise ValidationError('No business with given id')
        if business.boost_status not in Business.BoostStatus.active_statuses():
            raise ValidationError('Cannot ban business with boost disabled')
        length = cleaned_data.get('length')
        ban_type = cleaned_data.get('type')
        if length not in ALLOWED_BOOST_BAN_LENGTHS_FOR_TYPE.get(ban_type):
            raise ValidationError(f'Cannot use length {length!r} for boost ban type {ban_type!r}')
        return cleaned_data


class BoostBanEditForm(forms.ModelForm):
    class Meta:
        model = BoostBan
        fields = (
            'invoice_paid',
            'length',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if self.instance.status not in (BoostBanStatus.PENDING, BoostBanStatus.ACTIVE):
            self.fields['length'].disabled = True


class BoostBanAdmin(NoDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = BoostBan
    form = BoostBanAddForm
    list_display = (
        'id',
        'business',
        'type',
        'length',
        'status',
        'reason',
        'invoice_required',
        'invoice_paid',
        'active_from',
        'active_till',
        'created_by',
    )
    ordering = ('-created',)
    raw_id_fields = ('business',)
    search_fields = ('=business_id', '=created_by__email')
    list_filter = (
        'status',
        'type',
        'length',
        'reason',
        'active_from',
        'active_till',
        'invoice_required',
    )
    hide_keyword_field = True

    readonly_fields = (
        'business',
        'type',
        'status',
        'active_from',
        'active_till',
        'reason',
        'reason_description',
        'invoice_required',
        'created_by',
        'blocking_commission',
        'notification_status_label',
        'notification_details',
    )

    def has_module_permission(self, request):
        if settings.BOOST.BANS_ENABLED and BoostBanBackendFlag():
            return True
        return False

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<ban_id>\d+)/end_ban',
                self.admin_site.admin_view(self.end_ban_view),
                name='end_ban',
            ),
        ]
        return urls + additional_urls

    def get_form(self, request, obj=None, change=False, **kwargs):
        if obj is None:
            return BoostBanAddForm

        if obj.status == BoostBanStatus.ACTIVE:
            link = reverse('admin:end_ban', kwargs={'ban_id': obj.id})
            self.end_ban_button = format_html(
                "<a href='{}' id='end_ban_button'"
                " onclick='return confirm(\"Are you sure to end this ban?\")'"
                " class='btn btn-warning'>End ban</a>",
                link,
            )
        else:
            self.end_ban_button = ''

        return BoostBanEditForm

    def get_readonly_fields(self, request, obj=None):
        if not obj:
            return []
        return super().get_readonly_fields(request, obj)

    def save_model(self, request, obj, form, change):
        if obj.status in (BoostBanStatus.PENDING, BoostBanStatus.ACTIVE):
            obj.active_till = obj.active_from + BoostBanLength.get_relativedelta(obj.length)
        if not change:
            obj.created_by = get_user_from_django_request(request)

        super().save_model(request, obj, form, change)

        if obj.status == BoostBanStatus.PENDING:
            try:
                apply_boost_bans.run([obj.id])
            except BoostBanError as errors:
                messages.error(request, f'Ban cannot be applied: {errors}')
            else:
                messages.success(request, f'Successfully applied ban {obj}')

    @staticmethod
    def end_ban_view(request, ban_id):
        ban = get_object_or_404(BoostBan, id=ban_id)

        BusinessPromotion.cache_promotion_updater(
            ban.business_id,
            BusinessPromotion.BY_ADMIN,
        )
        try:
            end_boost_bans.run([ban_id])
        except BoostBanError as errors:
            messages.error(request, f'Ban could not be ended: {errors}')
        else:
            messages.success(request, f'Successfully ended ban {ban}')

        return redirect(reverse('admin:boost_boostban_changelist'))

    @admin.display(empty_value='Unknown')
    def notification_status_label(self, obj):
        return BoostBanNotificationStatus.get_label(obj.notification_status)


class BoostFraudSuspicionAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = BoostFraudSuspicion
    hide_keyword_field = True
    list_display = readonly_fields = (
        'id',
        'business_id',
        'warning_type',
        'warning_visible',
        'created',
    )
    search_fields = ('business_id',)
    list_filter = ('warning_visible', 'warning_type')


admin.site.register(BoostAppointment, BoostAppointmentAdmin)
admin.site.register(BoostSettings, BoostSettingsAdmin)
admin.site.register(BoostBan, BoostBanAdmin)
admin.site.register(BoostFraudSuspicion, BoostFraudSuspicionAdmin)
