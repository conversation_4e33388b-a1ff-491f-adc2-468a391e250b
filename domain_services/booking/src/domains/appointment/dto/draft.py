import uuid
from dataclasses import dataclass
from datetime import date, datetime

from django.conf import settings

from ....commons.types import DayMinutes


@dataclass
class DraftItemServiceVariant:
    id: int
    name: str
    price: str | None = None
    duration: int | None = None


@dataclass
class DraftItemService:
    id: int
    name: str
    variant: DraftItemServiceVariant
    price: str | None = None
    omnibus_price: str | None = None


@dataclass
class DraftTimeSlot:
    date: date
    start_time: DayMinutes


@dataclass
class DraftItem:  # pylint: disable=too-many-instance-attributes
    id: uuid.UUID
    service: DraftItemService
    booked_from: datetime
    booked_till: datetime
    wait_time: int
    staffer_id: int | None = None
    requested_staffer_id: int | None = None
    time_slot: DraftTimeSlot | None = None
    price: str | None = None
    discount: str | None = None
    price_before_discount: str | None = None


@dataclass
class Draft:  # pylint: disable=too-many-instance-attributes
    id: uuid.UUID
    version: uuid.UUID
    business_id: int
    booked_from: datetime
    booked_till: datetime
    duration: int
    items: list[DraftItem]
    customer_note: str
    px_timezone: str
    total: str | None = None
    user_id: int | None = None

    @property
    def long_wait_time(self) -> bool:
        return any(
            (item.wait_time > settings.LONG_WAIT_TIME if item.wait_time else False)
            for item in self.items
        )
