# Example configuration
# https://github.com/benoitc/gunicorn/blob/master/examples/example_config.py
#
# Server socket
#
#   bind - The socket to bind.
#
#       A string of the form: 'HOST', 'HOST:PORT', 'unix:PATH'.
#       An IP is a valid HOST.
#
#   backlog - The number of pending connections. This refers
#       to the number of clients that can be waiting to be
#       served. Exceeding this number results in the client
#       getting an error when attempting to connect. It should
#       only affect servers under significant load.
#
#       Must be a positive integer. Generally set in the 64-2048
#       range.
#

bind = [':9094', 'unix:/home/<USER>/code/drf_sock.sock']
backlog = 120  # uwsgi listen = 120

#
# Worker processes
#
#   workers - The number of worker processes that this server
#       should keep alive for handling requests.
#
#       A positive integer generally in the 2-4 x $(NUM_CORES)
#       range. You'll want to vary this a bit to find the best
#       for your particular application's work load.
#
#   worker_class - The type of workers to use.
#
#       The default sync class should handle most 'normal' types of work
#       loads. sync worker does not support persistent connections - each connection is closed
#       after response has been sent
#       (even if you manually add Keep-Alive or Connection: keep-alive header in your application).
#
#       You'll want to read
#       http://docs.gunicorn.org/en/latest/design.html#choosing-a-worker-type
#       for information on when you might want to choose one
#       of the other worker classes.
#
#       A string referring to a Python path to a subclass of
#       gunicorn.workers.base.Worker. The default provided values
#       can be seen at
#       http://docs.gunicorn.org/en/latest/settings.html#worker-class
#
#   worker_connections - For the eventlet and gevent worker classes
#       this limits the maximum number of simultaneous clients that
#       a single process can handle.
#
#       A positive integer generally set to around 1000.
#
#   timeout - If a worker does not notify the master process in this
#       number of seconds it is killed and a new worker is spawned
#       to replace it.
#
#       Generally set to thirty seconds. Only set this noticeably
#       higher if you're sure of the repercussions for sync workers.
#       For the non sync workers it just means that the worker
#       process is still communicating and is not tied to the length
#       of time required to handle a single request.
#
#   keepalive - The number of seconds to wait for the next request
#       on a Keep-Alive HTTP connection.
#
#       Default: 2
#
#       A positive integer. Generally set in the 1-5 seconds range.
#
#   threads - The number of worker threads for handling requests.
#
#       Default: 1
#
#       A positive integer generally in the 2-4 x $(NUM_CORES) range. You’ll want to vary this
#       a bit to find the best for your particular application’s work load.
#       This setting only affects the Gthread worker type.
#
#   max_requests - The maximum number of requests a worker will process before restarting.
#
#       Default: 0
#
#       Any value greater than zero will limit the number of requests a worker will process before
#       automatically restarting. This is a simple method to help limit the damage of memory leaks.
#       If this is set to zero (the default) then the automatic worker restarts are disabled.
#
#   max_requests_jitter - The maximum jitter to add to the max_requests setting.
#
#       Default: 0
#
#       The jitter causes the restart per worker to be randomized by
#       randint(0, max_requests_jitter). This is intended to stagger worker restarts to avoid all
#       workers restarting at the same time.
#
#   graceful_timeout - Timeout for graceful workers restart.
#
#       Default: 30
#
#       After receiving a restart signal, workers have this much time to finish serving requests.
#       Workers still alive after the timeout (starting from the receipt of the restart signal)
#       are force killed.
#

workers = 2  # uwsgi processes
timeout = 20  # uwsgi harakiri

#
# Server mechanics
#
#   daemon - Detach the main Gunicorn process from the controlling
#       terminal with a standard fork/fork sequence.
#
#       True or False
#
#   raw_env - Pass environment variables to the execution environment.
#
#   pidfile - The path to a pid file to write
#
#       A path string or None to not write a pid file.
#
#   user - Switch worker processes to run as this user.
#
#       A valid user id (as an integer) or the name of a user that
#       can be retrieved with a call to pwd.getpwnam(value) or None
#       to not change the worker process user.
#
#   group - Switch worker process to run as this group.
#
#       A valid group id (as an integer) or the name of a user that
#       can be retrieved with a call to pwd.getgrnam(value) or None
#       to change the worker processes group.
#
#   umask - A mask for file permissions written by Gunicorn. Note that
#       this affects unix socket permissions.
#
#       A valid value for the os.umask(mode) call or a string
#       compatible with int(value, 0) (0 means Python guesses
#       the base, so values like "0", "0xFF", "0022" are valid
#       for decimal, hex, and octal representations)
#
#   tmp_upload_dir - A directory to store temporary request data when
#       requests are read. This will most likely be disappearing soon.
#
#       A path to a directory where the process owner can write. Or
#       None to signal that Python should choose one on its own.
#

#
# Server Mechanics 2
#
#   preload_app - Load application code before the worker processes are forked.
#
#       Default: False
#
#       By preloading an application you can save some RAM resources as well as speed up server
#       boot times. Although, if you defer application loading to each worker process, you can
#       reload your application code easily by restarting workers.
#
#   chdir - Change directory to specified directory before loading apps.
#

# preload_app # uwsgi lazy-apps (preload_app=True == lazy_app=False)
chdir = '/home/<USER>/code'

#
#   Logging
#
#   logfile - The path to a log file to write to.
#
#       A path string. "-" means log to stdout.
#
#   loglevel - The granularity of log output
#
#       A string of "debug", "info", "warning", "error", "critical"
#
#   accesslog - The Access log file to write to.
#
#       Default: None
#
#       '-' means log to stdout.
#
#   statsd_host - host:port of the statsd server to log to.
#
#       Default: None
#
#       Instruction to integrate with datadog:
#       https://docs.datadoghq.com/integrations/gunicorn/
#
#   dogstatsd_tags - A comma-delimited list of datadog statsd (dogstatsd) tags to append to
#       statsd metrics.
#

loglevel = 'info'
# statsd_host = ':2999'  # i think it map to 'stats' in uwsgi

#
# Process naming
#
#   proc_name - A base to use with setproctitle to change the way
#       that Gunicorn processes are reported in the system process
#       table. This affects things like 'ps' and 'top'. If you're
#       going to be running more than one instance of Gunicorn you'll
#       probably want to set a name to tell them apart. This requires
#       that you install the setproctitle module.
#
#       A string or None to choose a default of something like 'gunicorn'.
#

#
# Server hooks
#
#   post_fork - Called just after a worker has been forked.
#
#       A callable that takes a server and worker instance
#       as arguments.
#
#   pre_fork - Called just prior to forking the worker subprocess.
#
#       A callable that accepts the same arguments as after_fork
#
#   pre_exec - Called just prior to forking off a secondary
#       master process during things like config reloading.
#
#       A callable that takes a server instance as the sole argument.
#

#
# Security
#
#   limit_request_line - The maximum size of HTTP request line in bytes.
#
#       Default value 4094.
#
#       This parameter is used to limit the allowed size of a client’s HTTP request-line.
#       Since the request-line consists of the HTTP method, URI, and protocol version,
#       this directive places a restriction on the length of a request-URI allowed for
#       a request on the server. A server needs this value to be
#       large enough to hold any of its resource names, including any information that might be
#       passed in the query part of a GET request. Value is a number from 0 (unlimited) to 8190.
#
#   limit_request_fields - Limit the number of HTTP headers fields in a request.
#
#       Default: 100
#
#       This parameter is used to limit the number of headers in a request to prevent DDOS attack.
#       Used with the limit_request_field_size it allows more safety. By default this value is 100
#       and can’t be larger than 32768.
#
#   limit_request_field_size - Limit the allowed size of an HTTP request header field.
#
#       Default: 8190
#
#       Value is a positive number or 0. Setting it to 0 will allow unlimited header field sizes.


#
# Debugging
#
#   spew - Install a trace function that spews every line of Python
#       that is executed when running the server. This is the
#       nuclear option.
#
#       True or False
#
#   reload - Restart workers when code changes. This setting is intended for development.
#       It will cause workers to be restarted whenever application code changes.
#       The reloader is incompatible with application preloading.
#       When using a paste configuration be sure that the server block does not import any
#       application code or the reload will not work as designed.
#

reload = True  # uwsgi py-autoreload


# AND MORE ...  it is not full list of configuration variables
