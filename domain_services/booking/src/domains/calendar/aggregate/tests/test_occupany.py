import pytest

from .....commons.types import ResourceId
from ...dto import Occupancy, ResourceBookedOccupancy, ResourceBookedOccupancyMap
from ..occupancy import OccupancyCalculator
from ..types import (
    AppointmentType,
    AvailabilitySlotsMap,
    BookedSlotsMap,
    OccupancySlotsMap,
    ResourcesOccupancyMap,
)
from .commons import (
    APPLIANCE_ID,
    BIN_LENGTH,
    DAILY_RESOURCES_BOOKED_OCCUPANCY_MAP,
    DAY__NO_WORKING_RESOURCE,
    DAY__PX_NOT_OPERATING,
    DAY_OPERATING,
    EMPTY_OCCUPANCY,
    EXAMPLE_BOOKED_OCCUPANCY,
    FULL_OCCUPANCY,
    RESOURCES_BOOKED_OCCUPANCY_MAP,
    STAFFER_1_ID,
    STAFFER_2_ID,
)

OCCUPANCY_SERVICE = OccupancyCalculator(
    bin_length=BIN_LENGTH,  # bin length increased to 1h for readability
)
WORKING_HOURS_SLOTS = [[6 * BIN_LENGTH, 21 * BIN_LENGTH]]
TIME_OFF_SLOTS__DAY_OFF = [[0, 0]]
EMPTY_DAY_OCCUPANCY = Occupancy("11111 10000 00000 00000 0111")


@pytest.mark.parametrize(
    [
        "resource_ids",
        "resources_working_hours",
        "resources_time_offs",
        "expected",
    ],
    (
        pytest.param([STAFFER_1_ID], {}, None, {STAFFER_1_ID: None}, id="empty"),
        pytest.param(
            [STAFFER_1_ID],
            {STAFFER_1_ID: WORKING_HOURS_SLOTS},
            {STAFFER_1_ID: TIME_OFF_SLOTS__DAY_OFF},
            {STAFFER_1_ID: None},
            id="full day time off",
        ),
        pytest.param(
            [STAFFER_1_ID],
            {STAFFER_1_ID: WORKING_HOURS_SLOTS},
            None,
            {STAFFER_1_ID: EMPTY_DAY_OCCUPANCY},
            id="empty day",
        ),
        pytest.param(
            [STAFFER_1_ID],
            {
                STAFFER_1_ID: [
                    [10 * BIN_LENGTH, 12 * BIN_LENGTH],
                    [13 * BIN_LENGTH, 18 * BIN_LENGTH],
                ]
            },
            {
                STAFFER_1_ID: [[16 * BIN_LENGTH, 20 * BIN_LENGTH]]
            },  # works even if time off extends working hours
            {STAFFER_1_ID: Occupancy("11111 11111 00100 01111 1111")},
            id="empty day - break in working hours and partial time off",
        ),
    ),
)
def test__build_resources_occupancy_map(
    resource_ids: list[ResourceId],
    resources_working_hours: OccupancySlotsMap,
    resources_time_offs: OccupancySlotsMap,
    expected: ResourcesOccupancyMap,
    **_,
) -> None:
    actual = OCCUPANCY_SERVICE.build_resources_occupancy_map(  # pylint: disable=protected-access
        resource_ids=resource_ids,
        resources_working_hours=resources_working_hours,
        resources_time_offs=resources_time_offs,
    )
    assert actual == expected


RESOURCES_OCCUPANCY_MAP = {
    STAFFER_1_ID: EMPTY_DAY_OCCUPANCY,
    STAFFER_2_ID: EMPTY_DAY_OCCUPANCY,
    APPLIANCE_ID: EMPTY_DAY_OCCUPANCY,
}

BOOKED_SLOTS_MAP = {
    STAFFER_1_ID: {
        AppointmentType.CUSTOMER: [
            (9 * BIN_LENGTH, 11 * BIN_LENGTH),
        ],
        AppointmentType.BUSINESS: [
            (20 * BIN_LENGTH, 21 * BIN_LENGTH),
        ],
        AppointmentType.RESERVATION: [
            (15 * BIN_LENGTH, 16 * BIN_LENGTH),
        ],
    },
    STAFFER_2_ID: {
        AppointmentType.CUSTOMER: [
            (9 * BIN_LENGTH, 11 * BIN_LENGTH),
            (10 * BIN_LENGTH, 12 * BIN_LENGTH),
        ],
        AppointmentType.BUSINESS: [
            (20 * BIN_LENGTH, 21 * BIN_LENGTH),
        ],
        AppointmentType.TIMEOFF__DEPRECATED: [
            (15 * BIN_LENGTH, 16 * BIN_LENGTH),
        ],
    },
    APPLIANCE_ID: {
        AppointmentType.CUSTOMER: [
            (9 * BIN_LENGTH, 11 * BIN_LENGTH),
        ],
        AppointmentType.BUSINESS: [
            (20 * BIN_LENGTH, 21 * BIN_LENGTH),
        ],
        AppointmentType.RESERVATION: [
            (15 * BIN_LENGTH, 17 * BIN_LENGTH),
        ],
    },
}


@pytest.mark.parametrize(
    ["resource_ids", "resources_occupancy_map", "expected"],
    [
        pytest.param(
            [STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID],
            RESOURCES_OCCUPANCY_MAP,
            RESOURCES_BOOKED_OCCUPANCY_MAP,
            id="all available staffers",
        ),
        pytest.param(
            [STAFFER_1_ID],
            RESOURCES_OCCUPANCY_MAP,
            {STAFFER_1_ID: EXAMPLE_BOOKED_OCCUPANCY},
            id="staffer selected",
        ),
        pytest.param(
            [STAFFER_1_ID, STAFFER_2_ID],
            {
                STAFFER_1_ID: EMPTY_DAY_OCCUPANCY,
                STAFFER_2_ID: None,
            },
            {
                STAFFER_1_ID: EXAMPLE_BOOKED_OCCUPANCY,
                STAFFER_2_ID: None,
            },
            id="staffer on day off",
        ),
    ],
)
def test__build_booked_occupancy_map(
    resource_ids: list[ResourceId],
    resources_occupancy_map: ResourcesOccupancyMap,
    expected: ResourcesOccupancyMap,
) -> None:
    actual = OCCUPANCY_SERVICE.build_booked_occupancy_map(
        resource_ids=resource_ids,
        resources_occupancy_map=resources_occupancy_map,
        booked_slots_map=BOOKED_SLOTS_MAP,
    )
    assert actual == expected


RESOURCES_WORKING_HOURS = {
    STAFFER_1_ID: WORKING_HOURS_SLOTS,
    STAFFER_2_ID: WORKING_HOURS_SLOTS,
    APPLIANCE_ID: WORKING_HOURS_SLOTS,
}


@pytest.mark.parametrize(
    [
        "resource_ids",
        "resources_working_hours",
        "resources_time_offs",
        "booking_slots_map",
        "expected",
    ],
    [
        pytest.param(
            [STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID],
            RESOURCES_WORKING_HOURS,
            None,
            None,
            {
                STAFFER_1_ID: ResourceBookedOccupancy(
                    booking=EMPTY_DAY_OCCUPANCY,
                    padding=EMPTY_OCCUPANCY,
                ),
                STAFFER_2_ID: ResourceBookedOccupancy(
                    booking=EMPTY_DAY_OCCUPANCY,
                    padding=EMPTY_OCCUPANCY,
                ),
                APPLIANCE_ID: ResourceBookedOccupancy(
                    booking=EMPTY_DAY_OCCUPANCY,
                    padding=EMPTY_OCCUPANCY,
                ),
            },
            id="no bookings",
        ),
        pytest.param(
            [STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID],
            RESOURCES_WORKING_HOURS,
            None,
            BOOKED_SLOTS_MAP,
            RESOURCES_BOOKED_OCCUPANCY_MAP,
            id="happy path",
        ),
        pytest.param(
            [STAFFER_1_ID],
            RESOURCES_WORKING_HOURS,
            None,
            BOOKED_SLOTS_MAP,
            {
                STAFFER_1_ID: EXAMPLE_BOOKED_OCCUPANCY,
            },
            id="happy path for only one staffer",
        ),
        pytest.param(
            [
                STAFFER_1_ID,
                STAFFER_2_ID,
            ],
            RESOURCES_WORKING_HOURS,
            {
                STAFFER_1_ID: [[6 * BIN_LENGTH, 21 * BIN_LENGTH]],
                STAFFER_2_ID: TIME_OFF_SLOTS__DAY_OFF,
            },
            BOOKED_SLOTS_MAP,
            {
                STAFFER_1_ID: ResourceBookedOccupancy(
                    booking=FULL_OCCUPANCY,
                    padding=EMPTY_OCCUPANCY,
                ),
                STAFFER_2_ID: None,
            },
            id="full day time off",
        ),
    ],
)
def test__build_resources_booked_occupancy_map(
    resource_ids: list[ResourceId],
    resources_working_hours: AvailabilitySlotsMap,
    resources_time_offs: OccupancySlotsMap | None,
    booking_slots_map: BookedSlotsMap | None,
    expected: ResourceBookedOccupancyMap | None,
) -> None:
    actual = OCCUPANCY_SERVICE.build_resources_booked_occupancy_map(
        resource_ids=resource_ids,
        resources_working_hours=resources_working_hours,
        resources_time_offs=resources_time_offs,
        booking_slots_map=booking_slots_map,
    )
    assert actual == expected


def test__build_daily_resources_booked_occupancy_map() -> None:
    actual = OCCUPANCY_SERVICE.build_daily_resources_booked_occupancy_map(
        resource_ids=[STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID],
        dates=[
            DAY__PX_NOT_OPERATING,
            DAY__NO_WORKING_RESOURCE,
            DAY_OPERATING,
        ],
        resources_working_hours_map={
            DAY__NO_WORKING_RESOURCE: {
                STAFFER_1_ID: WORKING_HOURS_SLOTS,
                STAFFER_2_ID: WORKING_HOURS_SLOTS,
            },
            DAY_OPERATING: RESOURCES_WORKING_HOURS,
        },
        resources_time_offs_map={
            DAY__NO_WORKING_RESOURCE: {
                STAFFER_1_ID: WORKING_HOURS_SLOTS,
                STAFFER_2_ID: TIME_OFF_SLOTS__DAY_OFF,
            },
        },
        daily_bookings_slots_map={
            DAY_OPERATING: BOOKED_SLOTS_MAP,
        },
    )
    assert actual == DAILY_RESOURCES_BOOKED_OCCUPANCY_MAP
