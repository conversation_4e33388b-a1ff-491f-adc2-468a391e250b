from bo_obs.datadog.enums import BooksyTeams
from django.core.paginator import EmptyPage, Paginator
from django.db.models import Count, F, Q
from django.utils.translation import gettext as _
from rest_framework import status

from lib.serializers import PaginatorSerializer
from service.images.helpers import get_file_from_request_files
from service.images.serializers import (
    CoverImageResponseSerializer,
    CoverImageUploadSerializer,
)
from service.tools import RequestHandler, session
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.images.serializers import ImageSerializer
from webapps.message_blast.enums import MessageBlastImageCategoryType, MessageBlastInternalNames
from webapps.message_blast.helpers import (
    resolve_templates_for_business,
    resolve_templates_for_business_group,
)
from webapps.message_blast.models import (
    MessageBlastGroup,
    MessageBlastImage,
)
from webapps.message_blast.serializers import (
    MessageBlastRequestImageSerializer,
)


class MessageBlastTemplateImageHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """swagger:

        summary: Gets images for message blast
        type: MessageBlastTemplateImageResponse
        parameters:
            - name: business_id
              type: integer
              paramType: path
            - name: business_category
              type: boolean
              paramType: query
              description: whether to return images for business category
              defaultValue: true
            - name: template_internal_name
              type: string
              paramType: query
            - name: group_internal_name
              type: string
              paramType: query
            - name: page
              type: integer
              paramType: query
              description: page to load
              defaultValue: 1
            - name: per_page
              type: integer
              paramType: query
              description: number of items per page
              defaultValue: 12
        :swagger
        """

        business = self.business_with_reception(business_id)

        serializer = MessageBlastRequestImageSerializer(
            data=self._prepare_get_arguments(),
        )

        self.validate_serializer(serializer, flatten=True)

        if serializer.data.get('template_internal_name'):
            template_internal_names = [serializer.validated_data['template_internal_name']]
        else:
            group_list = MessageBlastGroup.objects.filter(
                Q(internal_name=serializer.validated_data['group_internal_name'])
                | Q(parent_group__internal_name=serializer.validated_data['group_internal_name'])
            ).values_list('id', flat=True)

            template_internal_names = [
                x.internal_name
                for x in resolve_templates_for_business_group(
                    group_ids=group_list,
                    business_id=business_id,
                    exclude_own_message=True,
                )
            ]

        images_list = MessageBlastImage.get_images_for_template(
            business=business if serializer.validated_data['business_category'] else None,
            template_internal_names=template_internal_names,
        )

        pager = Paginator(images_list, serializer.validated_data['per_page'])

        try:
            page = pager.page(serializer.validated_data['page'])
            images = list(page.object_list)
        except EmptyPage:
            images = []

        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'images': ImageSerializer(instance=images, many=True).data,
                'images_count': pager.count,
            },
        )


class MessageBlastCustomImageHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True, api_key_required=True)
    def post(self, business_id):
        """swagger:
            summary: Uploads custom image for Message Blast
            type: BusinessImage
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image
                  description: Photo content
                  type: File
                  paramType: form
                  required: true
        :swagger
        """
        business = self.business_with_advanced_staffer(business_id, __only='id')

        mb_custom_photo_file = get_file_from_request_files(self.request)
        serializer = CoverImageUploadSerializer(
            data={
                'business': business.id,
                'category': ImageTypeEnum.MESSAGE_BLAST_PHOTO,
                'image': mb_custom_photo_file,
            },
            context={'business': business},
        )
        self.validate_serializer(serializer)

        custom_image = serializer.save()

        custom_image_data = CoverImageResponseSerializer(instance=custom_image).data
        self.finish_with_json(status.HTTP_201_CREATED, custom_image_data)


class MessageBlastAllTemplateImageHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """swagger:
        summary: Gets all images for message blast
        type: MessageBlastTemplateImageResponse
        parameters:
            - name: business_id
              type: integer
              paramType: path
            - name: page
              type: integer
              paramType: query
              description: page to load
              defaultValue: 1
            - name: per_page
              type: integer
              paramType: query
              description: number of items per page
              defaultValue: 12
        :swagger
        """
        business = self.business_with_reception(business_id)

        serializer = PaginatorSerializer(
            data=self._prepare_get_arguments(),
        )

        self.validate_serializer(serializer, flatten=True)

        search_category = MessageBlastImage.normalize_business_category(
            business.primary_category,
        )

        template_internal_names = []

        for group in resolve_templates_for_business(business_id, True):
            template_internal_names += [
                template.internal_name for template in group.business_templates
            ]

        images_list = list(
            Image.objects.filter(
                business_id=business_id,
                category__in=[ImageTypeEnum.MESSAGE_BLAST_PHOTO, ImageTypeEnum.INSPIRATION],
            ).order_by('-category')
        )

        images_list += [
            x.image
            for x in MessageBlastImage.objects.filter(
                Q(category__isnull=True, template_internal_name__in=template_internal_names)
                | Q(category=search_category, template_internal_name__isnull=True)
            )
            .order_by(F('template_internal_name').asc(nulls_first=True))
            .only('image')
        ]

        pager = Paginator(images_list, serializer.validated_data['per_page'])

        try:
            page = pager.page(serializer.validated_data['page'])
            images = list(page.object_list)
        except EmptyPage:
            images = []

        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'images': ImageSerializer(instance=images, many=True).data,
                'images_count': pager.count,
            },
        )


class MessageBlastImageCategoryTreeHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """swagger:
        summary: Gets image category tree for message blast
        type: MessageBlastImageCategoryTreeResponse
        parameters:
            - name: business_id
              type: integer
              paramType: path
        :swagger
        """
        business = self.business_with_reception(business_id, __only='primary_category')

        images_v2_counts = dict(
            Image.objects.filter(
                business_id=business_id,
                category__in=[ImageTypeEnum.MESSAGE_BLAST_PHOTO, ImageTypeEnum.INSPIRATION],
            )
            .values('category')
            .annotate(count=Count('category'))
            .order_by()
            .values_list('category', 'count')
        )

        template_images_counts = dict(
            MessageBlastImage.objects.filter(
                Q(category__isnull=True)
                & ~Q(template_internal_name=MessageBlastInternalNames.OWN_MESSAGE)
            )
            .values('template_internal_name')
            .annotate(count=Count('template_internal_name'))
            .order_by()
            .values_list('template_internal_name', 'count')
        )

        business_images_count = MessageBlastImage.objects.filter(
            category=MessageBlastImage.normalize_business_category(business.primary_category),
            template_internal_name__isnull=True,
        ).count()

        categories = [
            {
                'id': 1,
                'key': ImageTypeEnum.MESSAGE_BLAST_PHOTO,
                'name': _('Uploaded Images'),
                'type': MessageBlastImageCategoryType.IMAGES_V2,
                'children': [],
                'images_count': images_v2_counts.get(ImageTypeEnum.MESSAGE_BLAST_PHOTO, 0),
            },
            {
                'id': 2,
                'key': ImageTypeEnum.INSPIRATION,
                'name': _('Your Portfolio'),
                'type': MessageBlastImageCategoryType.IMAGES_V2,
                'children': [],
                'images_count': images_v2_counts.get(ImageTypeEnum.INSPIRATION, 0),
            },
            {
                'id': 3,
                'key': MessageBlastImageCategoryType.BUSINESS_CATEGORY,
                'name': _('Business Category'),
                'type': MessageBlastImageCategoryType.BUSINESS_CATEGORY,
                'children': [],
                'images_count': business_images_count,
            },
        ]

        groups = resolve_templates_for_business(business_id, True)
        template_id = len(categories) + len(groups) + 1

        for group in groups:
            children = []

            for template in group.business_templates:
                # Skip templates without images as requested
                if template.internal_name not in template_images_counts:
                    continue

                children.append(
                    {
                        'id': template_id,
                        'key': template.internal_name,
                        'name': template.name,
                        'type': MessageBlastImageCategoryType.TEMPLATE,
                        'children': [],
                        'images_count': template_images_counts.get(template.internal_name, 0),
                    }
                )

                template_id += 1

            categories.append(
                {
                    'id': len(categories) + 1,
                    'key': group.internal_name,
                    'name': group.title,
                    'type': MessageBlastImageCategoryType.GROUP,
                    'children': children,
                    'images_count': sum(x['images_count'] for x in children),
                }
            )

        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'categories': categories,
                'images_count': sum(x['images_count'] for x in categories),
            },
        )
