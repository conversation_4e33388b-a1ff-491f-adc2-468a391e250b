import json
from base64 import urlsafe_b64decode

import binascii
from bo_obs.datadog.enums import BooksyTeams
from django import forms

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.french_certification.consts import TEXT_ENCODING
from webapps.french_certification.signature import is_signature_valid
from webapps.french_certification.utils import json_data_to_signature_chaining_message
from webapps.user.groups import GroupNameV2


class FiscalArchiveSignatureVerificationForm(forms.Form):
    fiscal_archive_file = forms.FileField(label='Fiscal Archive JSON')
    signature_file = forms.FileField(label='Signature file')

    def clean_fiscal_archive_file(self):
        fiscal_archive_file = self.cleaned_data.get('fiscal_archive_file')

        try:
            fiscal_archive_data = json.load(fiscal_archive_file)
        except (json.JSONDecodeError, UnicodeDecodeError) as exc:
            raise forms.ValidationError('Uploaded fiscal archive file is invalid JSON') from exc

        self.cleaned_data['fiscal_archive_data'] = fiscal_archive_data

        return fiscal_archive_file

    def clean_signature_file(self):
        signature_file = self.cleaned_data.get('signature_file')

        try:
            signature_data = json.load(signature_file)
        except (json.JSONDecodeError, UnicodeDecodeError) as exc:
            raise forms.ValidationError('Signature file is invalid json') from exc

        try:
            signature = signature_data['signature']
        except KeyError as exc:
            raise forms.ValidationError('"signature" field is missing in signature file') from exc

        try:
            signature_base64 = signature.encode(TEXT_ENCODING)
            signature_bytes = urlsafe_b64decode(signature_base64)
        except binascii.Error as exc:
            raise forms.ValidationError('Signature content is invalid') from exc

        self.cleaned_data['signature_bytes'] = signature_bytes

        return signature_file


class FiscalArchiveSignatureVerification(GroupPermissionMixin, FormView):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)
    permission_required = ()
    form_class = FiscalArchiveSignatureVerificationForm
    template_name = 'admin/custom_views/fiscal_archive_signature_verification_tool.html'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        context = self.get_context_data(form=form)
        if not form.is_valid():
            context.update({'errors': form.errors})
            return self.render_to_response(context)

        fiscal_archive_data = form.cleaned_data['fiscal_archive_data']
        signature_bytes = form.cleaned_data['signature_bytes']

        message = json_data_to_signature_chaining_message(fiscal_archive_data)
        message = message.encode(TEXT_ENCODING)

        is_valid = is_signature_valid(signature_bytes, message)

        message = 'Signature is valid' if is_valid else 'Signature does not match to fiscal archive'

        context.update(
            {
                'verification_result': {
                    'signature_valid': is_valid,
                    'message': message,
                },
            }
        )

        return self.render_to_response(context)
