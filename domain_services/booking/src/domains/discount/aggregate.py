from decimal import Decimal
from typing import Protocol

from .dto import (
    DiscountDefinition,
    PointInTime,
    TimeSlotDiscount,
)


class DiscountAggregate(Protocol):
    def apply_discount(
        self,
        service_variant_price: Decimal,
        discount_definition: DiscountDefinition,
        time_slots_start: PointInTime,
    ) -> TimeSlotDiscount | None:
        # Check if available discounts apply and choose the best one for each timestamp
        ...
