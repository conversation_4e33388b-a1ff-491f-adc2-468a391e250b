import io
import mimetypes
import os.path
import re
from email.utils import format_datetime

from django.core.files.uploadedfile import (
    InMemoryUploadedFile,
)
from django.core.paginator import Paginator, EmptyPage
from django.db.models import Q, QuerySet
from django.db.transaction import atomic
from django.utils import timezone
from django.utils.translation import gettext as _
from rest_framework.fields import Boolean<PERSON>ield

from bo_obs.datadog.enums import BooksyTeams
from lib.serializers import SendEmailRequest, PaginatorSerializer
from service.consents.serializers import ConsentsListRequestSerializer
from service.mixins.paginator import PaginatorMixin
from service.tools import RequestHandler, session, json_request
from webapps.business_customer_info.bci_merge import ClaimMixin
from webapps.consents.models import Consent, ConsentForm
from webapps.consents.serializers import (
    BusinessConsentSerializer,
    BusinessConsentUpdateSerializer,
    ConsentFormSerializer,
    CustomerConsentPutSecretRequestSerializer,
    CustomerConsentSerializer,
)
from webapps.consents.tasks import (
    send_consent_business_email_task,
    send_consent_customer_email_task,
)
from webapps.family_and_friends.consent_notification import (
    is_consent_notification_not_supported_due_to_family_and_friends,
)


class UploadMixin:
    """
    Helper mixin for treating request body as a single uploaded file.
    """

    _filename_re = re.compile(r'\bfilename="(.*?)"')

    @staticmethod
    def _guess_content_type(signature):
        if signature[:3] == b'\xff\xd8\xff':
            return 'image/jpeg'

        if signature[:4] == b'\x89PNG':
            return 'image/png'

        if signature[:2] == b'BM':
            return 'image/bmp'

        return ''

    def get_uploaded_file(self):
        content_type = self.request.headers.get('Content-Type')
        content_disposition = self.request.headers.get('Content-Disposition')
        content_length = len(self.request.body)

        guessed_content_type = self._guess_content_type(self.request.body[:11])
        if guessed_content_type:
            content_type = guessed_content_type

        guessed_extension = mimetypes.guess_extension(content_type or '') or ''
        if guessed_extension == '.jpe':  # HACK
            guessed_extension = '.jpg'

        match = self._filename_re.match(content_disposition or '')
        if match:
            filename = match.group(1)
            tmp = filename.rsplit('.', 1)
            if len(tmp) == 1 or tmp[1].lower() != guessed_extension.lstrip('.'):
                filename = f'{tmp[0]}{guessed_extension}'
        else:
            filename = f'input{guessed_extension}'

        return InMemoryUploadedFile(
            io.BytesIO(self.request.body), None, filename, content_type, content_length, None
        )


def serve_file(handler, file, content_type=None, content_disposition=None, chunk_size=65536):
    if file.name is None:
        raise RuntimeError('File name is missing')  # pylint: disable=broad-exception-raised

    handler.set_header('Content-Type', content_type or mimetypes.guess_type(file.name)[0] or '')
    handler.set_header(
        'Content-Disposition',
        content_disposition or f'inline; filename="{os.path.basename(file.name)}"',
    )
    handler.set_status(200)

    with file.open('rb') as stream:
        while True:
            chunk = stream.read(chunk_size)
            if not chunk:
                break
            handler.write(chunk)


# pylint: disable=too-many-ancestors
class BusinessConsentFormsHandler(PaginatorMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @json_request
    @session(login_required=True, api_key_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Create a consent form
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: body
                  description: Consent form
                  type: ConsentFormCreate
                  paramType: body
            type: ConsentFormDetailsResponse
        :swagger
        """
        business = self.business_with_manager(business_id)

        serializer = ConsentFormSerializer(
            data=self.data,
            context={
                'business': business,
            },
        )
        self.validate_serializer(serializer)

        serializer.save()

        with timezone.override(business.get_timezone()):
            self.finish_with_json(201, {'consent_form': serializer.data})

    @session(login_required=True, api_key_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get list of active consent forms
            notes: .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
            type: ConsentFormListingResponse
        :swagger
        """
        business = self.business_with_staffer(business_id)

        page, per_page = self.parse_page_values_from_get()

        consent_forms = ConsentForm.objects.filter(
            business=business,
        ).prefetch_related('services')

        pager = Paginator(consent_forms, per_page)
        try:
            object_list = pager.page(page).object_list
        except EmptyPage:
            object_list = []

        serializer = ConsentFormSerializer(
            many=True,
            instance=object_list,
            context={
                'business': business,
            },
        )

        self.finish_with_json(
            200,
            {
                'page': page,
                'per_page': per_page,
                'count': pager.count,
                'consent_forms': serializer.data,
            },
        )


class BusinessConsentFormHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, business_id, form_id):
        """
        swagger:
            summary: Return the consent form
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: form_id
                  type: integer
                  paramType: path
                  description: Consent form id
            type: ConsentFormDetailsResponse
        :swagger
        """
        business = self.business_with_staffer(business_id)

        consent_form = self.get_object_or_404(
            # Allow to lookup historical records
            ConsentForm.all_objects.all(),
            id=form_id,
            business=business,
        )

        serializer = ConsentFormSerializer(
            instance=consent_form,
            context={
                'business': business,
            },
        )

        with timezone.override(business.get_timezone()):
            self.finish_with_json(200, {'consent_form': serializer.data})

    @json_request
    @session(login_required=True, api_key_required=True)
    def put(self, business_id, form_id):
        """
        swagger:
            summary: Update the consent form
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: form_id
                  type: integer
                  paramType: path
                  description: Consent form id
                - name: body
                  description: Consent form
                  type: ConsentFormUpdate
                  paramType: body
            type: ConsentFormDetailsResponse
        :swagger
        """
        business = self.business_with_manager(business_id)

        consent_form = self.get_object_or_404(
            ConsentForm,
            id=form_id,
            business=business,
        )

        serializer = ConsentFormSerializer(
            instance=consent_form,
            data=self.data,
            context={
                'business': business,
            },
        )
        self.validate_serializer(serializer)

        serializer.save()

        with timezone.override(business.get_timezone()):
            self.finish_with_json(200, {'consent_form': serializer.data})

    @session(login_required=True, api_key_required=True)
    def delete(self, business_id, form_id):
        """
        swagger:
            summary: Delete the consent form
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: form_id
                  type: integer
                  paramType: path
                  description: Consent form id
        :swagger
        """
        self.business_with_manager(business_id, __only=['id'])

        consent_form = self.get_object_or_404(
            ConsentForm,
            id=form_id,
            business=business_id,
        )
        consent_form.soft_delete()

        self.finish_with_json(200, {})


class BusinessConsentFormPdfHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, business_id, form_id):
        """
        swagger:
            summary: Download consent pdf.
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: form_id
                  type: integer
                  paramType: path
                  description: Consent form id
            type: string
            format: binary
        :swagger
        """
        self.business_with_staffer(business_id, __only=['id'])

        consent_form = self.get_object_or_404(
            ConsentForm,
            id=form_id,
            business=business_id,
        )

        try:
            with consent_form.render_pdf() as file:
                self.set_header('Cache-Control', 'private')
                self.set_header('Last-Modified', format_datetime(consent_form.updated))

                serve_file(
                    self,
                    file,
                    content_type='application/pdf',
                    content_disposition='attachment; filename="form.pdf"',
                )
        except IOError:
            self.set_status(503)
            self.finish()
            return

        self.finish()


# pylint: disable=too-many-ancestors
class BusinessConsentsHandler(PaginatorMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @json_request
    @session(login_required=True, api_key_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Create a consent to sign.
            notes: >
                Consent will be created after confirming a booking with
                selected services. Use this endpoint if you need to create one
                manually.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: body
                  description: Consent
                  type: ConsentCreate
                  paramType: body
            type: ConsentDetailsResponse
        :swagger
        """
        business = self.business_with_advanced_staffer(business_id)

        serializer = BusinessConsentSerializer(
            data=self.data,
            context={
                'business': business,
            },
        )
        self.validate_serializer(serializer)

        serializer.save()

        with timezone.override(business.get_timezone()):
            self.finish_with_json(201, {'consent': serializer.data})

    @session(login_required=True, api_key_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get list of consents
            notes: .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: customer
                  type: integer
                  paramType: query
                  description: Customer id
                - name: form
                  type: integer
                  paramType: query
                  description: Consent form id
                - name: signed
                  type: boolean
                  paramType: query
                  description: filter signed/unsigned consents
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
            type: ConsentListingResponse
        :swagger
        """
        business = self.business_with_advanced_staffer(business_id)

        data = self._prepare_get_arguments()
        page, per_page = self.parse_page_values_from_get()

        consents = Consent.objects.filter(
            form__business=business,
        )
        if data.get('customer'):
            consents = consents.filter(customer_id=data['customer'])

        if data.get('form'):
            consents = consents.filter(form_id=data['form'])

        if data.get('signed') is not None:
            consents = consents.filter(signed__isnull=data['signed'] in BooleanField.FALSE_VALUES)

        pager = Paginator(consents, per_page)
        try:
            object_list = pager.page(page).object_list
        except EmptyPage:
            object_list = []

        serializer = BusinessConsentSerializer(
            many=True,
            instance=object_list,
            context={
                'business': business,
            },
        )

        with timezone.override(business.get_timezone()):
            self.finish_with_json(
                200,
                {
                    'page': page,
                    'per_page': per_page,
                    'count': pager.count,
                    'consents': serializer.data,
                },
            )


class BusinessConsentHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, business_id, consent_uuid):
        """
        swagger:
            summary: Return consent details.
            notes: >
                Consents store also archived customer and business contact
                details. They're not exposed in API - there has
                not been a need for that yet.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: Consent uuid
            type: ConsentDetailsResponse
        :swagger
        """
        business = self.business_with_staffer(business_id)

        consent = self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            form__business=business,
        )

        serializer = BusinessConsentSerializer(
            instance=consent,
            context={
                'business': business,
            },
        )

        with timezone.override(business.get_timezone()):
            self.finish_with_json(200, {'consent': serializer.data})

    @json_request
    @session(login_required=True, api_key_required=True)
    def put(self, business_id, consent_uuid):
        """
        swagger:
            summary: Edit the consent, if it's not signed yet.
            notes: >
                Uploading signature or photo locks the consent. A non-editable
                consents have "signed" date filled in.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: Consent uuid
                - name: body
                  type: ConsentUpdate
                  paramType: body
                  description: Consent
            type: ConsentDetailsResponse
        :swagger
        """
        business = self.business_with_staffer(business_id)

        with atomic():
            consent = self.get_object_or_404(
                Consent,
                uuid=consent_uuid,
                form__business=business,
            )
            if consent.signed:
                self.finish_with_json(
                    403,
                    {
                        'error': _('Can\'t edit a signed form'),
                    },
                )
                return

            serializer = BusinessConsentUpdateSerializer(
                instance=consent,
                data=self.data,
                context={
                    'business': business,
                },
            )
            self.validate_serializer(serializer)

            serializer.save()

        with timezone.override(business.get_timezone()):
            self.finish_with_json(200, {'consent': serializer.data})

    @session(login_required=True, api_key_required=True)
    def delete(self, business_id, consent_uuid):
        """
        swagger:
            summary: Dismiss consent, if it's not signed yet.
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: Consent uuid
        :swagger
        """
        self.business_with_advanced_staffer(business_id, __only=['id'])

        with atomic():
            consent = self.get_object_or_404(
                Consent,
                uuid=consent_uuid,
                form__business=business_id,
            )
            if consent.signed:
                self.finish_with_json(
                    403,
                    {
                        'error': _('Can\'t edit a signed form'),
                    },
                )
                return

            consent.soft_delete()

        self.finish_with_json(200, {})


# pylint: disable=too-many-ancestors
class BusinessConsentSignHandler(UploadMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    def _can_log_body(self, content_type):
        return False

    @session(login_required=True, api_key_required=True)
    def post(self, business_id, consent_uuid):
        """
        swagger:
            summary: Upload signature or photo and sign the consent.
            notes: >
                Please include header:

                Content-Type: ...
                Content-Disposition: inline; filename="..."
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
                - name: field
                  type: string
                  enum: [signature, photo]
                  defaultValue: signature
                  paramType: query
                  description: >
                      whether uploaded image is a photo of a printed
                      form or just a signature.
            requestBody:
                description: raw image file
                required: true
                content:
                    image/*:
                    schema:
                      type: string
                      format: binary
            type: ConsentDetailsResponse
        :swagger
        """
        business = self.business_with_staffer(business_id)

        field = self.get_argument('field', 'signature')

        if field not in ('signature', 'photo'):
            self.finish_with_json(
                400,
                {
                    'error': f'Invalid field "{field}"',
                },
            )
            return

        consent = self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            form__business=business,
        )
        if consent.signed:
            self.finish_with_json(
                403,
                {
                    'error': _('Can\'t edit a signed form'),
                },
            )
            return

        uploaded_file = self.get_uploaded_file()

        serializer = BusinessConsentUpdateSerializer(
            instance=consent,
            data={
                field: uploaded_file,
            },
            context={
                'business': business,
            },
            partial=True,
        )

        self.validate_serializer(serializer)

        try:
            serializer.save()
        except IOError:
            self.set_status(503)
            self.finish()
            return

        consent.sign()

        with timezone.override(business.get_timezone()):
            self.finish_with_json(200, {'consent': serializer.data})


class BusinessConsentSignatureHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, business_id, consent_uuid):
        """
        swagger:
            summary: Download consent signature.
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
            type: string
            format: binary
        :swagger
        """
        self.business_with_advanced_staffer(business_id, __only=['id'])

        consent = self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            signature__isnull=False,
            form__business=business_id,
            __only=['signature'],
        )

        try:
            file = consent.signature.file
        except ValueError:
            self.set_status(404)
            return

        self.set_header('Cache-Control', 'private')

        serve_file(self, file)

        self.finish()


class BusinessConsentPdfHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, business_id, consent_uuid):
        """
        swagger:
            summary: Download consent pdf.
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
            type: string
            format: binary
        :swagger
        """
        self.business_with_advanced_staffer(business_id, __only=['id'])

        consent = self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            form__business=business_id,
        )

        try:
            if consent.signed and consent.photo:
                consent_pdf = consent.render_photo_pdf()
            else:
                consent_pdf = consent.render_pdf()

            self.set_header('Cache-Control', 'no-store')

            serve_file(
                self,
                consent_pdf,
                content_type='application/pdf',
                content_disposition=f'attachment; filename="{consent_uuid}.pdf"',
            )
        except IOError:
            self.set_status(503)
            self.finish()
            return

        self.finish()


class BusinessConsentNotifyHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def post(self, business_id, consent_uuid):
        """
        swagger:
            summary: Send consent to user app as a pop-up notification.
            notes: >
                Consent must not be signed.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
            type: Errors
        :swagger
        """
        self.business_with_staffer(business_id, __only=['id'])

        with atomic():
            consent = self.get_object_or_404(
                Consent,
                uuid=consent_uuid,
                form__business=business_id,
            )

            if not consent.customer.user:
                self.return_error(
                    [
                        {
                            'code': 'invalid',
                            'description': _('Client does not have Booksy account'),
                        }
                    ]
                )
                return

            if consent.signed:
                self.return_error(
                    [
                        {
                            'code': 'invalid',
                            'description': _('Client already signed the form'),
                        }
                    ]
                )
                return
            if is_consent_notification_not_supported_due_to_family_and_friends(consent):
                self.return_error(
                    [
                        {
                            'code': 'invalid',
                            'description': _('Client does not agree to get notification'),
                        }
                    ]
                )

            if not consent.notify_customer(force=True):
                self.return_error(
                    [
                        {
                            'code': 'internal',
                            'description': _('Failed to send notification'),
                        }
                    ]
                )
                return

        self.finish_with_json(200, {})


class BusinessConsentSendEmailHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @json_request
    @session(login_required=True, api_key_required=True)
    def post(self, business_id, consent_uuid):
        """
        swagger:
            summary: Send consent to staffer email.
            notes: >
                Consent should be signed.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
                - name: body
                  paramType: body
                  description: optional email, defaults to current user
                  type: SendEmailRequest
            type: SendEmailResponse
        :swagger
        """
        self.business_with_advanced_staffer(business_id, __only=['id'])

        self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            form__business_id=business_id,
        )

        serializer = SendEmailRequest(
            data=self.data or {},
            context={
                'email': self.user.email,
            },
        )
        data = self.validate_serializer(serializer)

        send_consent_business_email_task.delay(
            consent_uuid,
            data['email'],
            operator_id=self.user.id,
            language=self.language,
        )

        self.finish_with_json(200, {'email': data['email']})


class CustomerConsentsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @staticmethod
    def filter_queryset(qs: QuerySet, filter_kwargs: dict) -> QuerySet:
        if business := filter_kwargs.get('business'):
            qs = qs.filter(customer__business_id=business)

        if form := filter_kwargs.get('form'):
            qs = qs.filter(form_id=form)

        if signed := filter_kwargs.get('signed') is not None:
            qs = qs.filter(signed__isnull=not signed)
        return qs

    @session(login_required=True, api_key_required=True)
    def get(self):
        """
        swagger:
            summary: Get list of customer consents
            notes: .
            parameters:
                - name: business
                  type: integer
                  paramType: query
                  description: Business id
                - name: form
                  type: integer
                  paramType: query
                  description: Consent form id
                - name: signed
                  type: boolean
                  paramType: query
                  description: filter signed/unsigned consents
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
                - name: secret
                  type: string
                  paramType: query
                  description: Consent secret
            type: ConsentListingResponse
        :swagger
        """
        query_params = self._prepare_get_arguments()
        paginator_serializer = PaginatorSerializer(data=query_params)
        pagination_data = self.validate_serializer(paginator_serializer)

        request_serializer = ConsentsListRequestSerializer(data=query_params)
        request_params = self.validate_serializer(request_serializer)

        qs = Consent.objects.filter(customer__user=self.user)
        if secret := request_params.get('secret'):
            secret_qs = Consent.objects.filter(secret=secret)
            qs = secret_qs.union(qs)

        qs = self.filter_queryset(qs, request_params)

        pager = Paginator(qs, pagination_data['per_page'])
        try:
            object_list = pager.page(pagination_data['page']).object_list
        except EmptyPage:
            object_list = []

        serializer = CustomerConsentSerializer(
            many=True,
            instance=object_list,
        )
        self.finish_with_json(
            200,
            {
                'page': pagination_data['page'],
                'per_page': pagination_data['per_page'],
                'count': pager.count,
                'consents': serializer.data,
            },
        )


# pylint: disable=too-many-ancestors
class CustomerConsentHandler(
    ClaimMixin,
    RequestHandler,
):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, consent_uuid):
        """
        swagger:
            summary: Return consent details.
            notes: >
                Consents store also archived customer and business contact
                details. They're not exposed in API - there has
                not been a need for that yet.
            parameters:
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: Consent uuid
                - name: secret
                  type: string
                  paramType: query
                  description: Consent secret
            type: ConsentDetailsResponse
        :swagger
        """
        data = self._prepare_get_arguments()
        secret = data.get('secret')
        consent = self.get_object_or_404(
            Consent.objects.filter(
                Q(customer__user=self.user) | Q(secret=secret) & Q(secret__isnull=False),
                uuid=consent_uuid,
            ),
        )

        serializer = CustomerConsentSerializer(instance=consent)

        self.finish_with_json(200, {'consent': serializer.data})

    @json_request
    @session(login_required=True, api_key_required=True)
    def put(self, consent_uuid):
        """
        swagger:
            summary: Edit the consent, if it's not signed yet.
            notes: >
                Consents with "signed" date are not editable.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: Consent uuid
                - name: body
                  type: ConsentUpdate
                  paramType: body
                  description: Consent
            type: ConsentDetailsResponse
        :swagger
        """
        request_serializer = CustomerConsentPutSecretRequestSerializer(
            data=self.data,
        )
        self.validate_serializer(request_serializer)
        secret = request_serializer.validated_data.get('secret')
        with atomic():
            consent = self.get_object_or_404(
                Consent.objects.filter(
                    Q(customer__user=self.user) | Q(secret=secret) & Q(secret__isnull=False),
                    uuid=consent_uuid,
                ),
            )
            if consent.signed:
                self.finish_with_json(
                    403,
                    {
                        'error': _('Can\'t edit a signed form'),
                    },
                )
                return

            serializer = CustomerConsentSerializer(
                instance=consent,
                data=self.data,
            )
            self.validate_serializer(serializer)

            serializer.save()
            if secret and str(consent.secret) == secret:
                self.claim_consent(
                    logged_in_user=self.user,
                    consent=consent,
                )
        self.finish_with_json(200, {'consent': serializer.data})


# pylint: disable=abstract-method
class CustomerConsentSignHandler(UploadMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    # Customer may only upload a signature. Photo can only be uploaded
    # through business endpoint.

    def _can_log_body(self, content_type):
        return False

    @session(login_required=True, api_key_required=True)
    def post(self, consent_uuid):
        """
        swagger:
            summary: Upload signature or photo and sign the consent.
            notes: >
                Please include header:

                Content-Type: ...
                Content-Disposition: inline; filename="..."
            parameters:
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
            requestBody:
                description: raw image file
                required: true
                content:
                    image/*:
                    schema:
                      type: string
                      format: binary
            type: ConsentDetailsResponse
        :swagger
        """
        consent = self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            customer__user=self.user,
        )
        if consent.signed:
            self.finish_with_json(
                403,
                {
                    'error': _('Can\'t edit a signed form'),
                },
            )
            return

        serializer = CustomerConsentSerializer(
            instance=consent,
            data={
                'signature': self.get_uploaded_file(),
            },
            partial=True,
        )

        self.validate_serializer(serializer)

        try:
            serializer.save()
        except IOError:
            self.set_status(503)
            self.finish()
            return

        consent.sign()

        self.finish_with_json(200, {'consent': serializer.data})


class CustomerConsentSignatureHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, consent_uuid):
        """
        swagger:
            summary: Download consent signature.
            notes: >
                .
            parameters:
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
            type: string
            format: binary
        :swagger
        """
        consent = self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            signature__isnull=False,
            customer__user=self.user,
            __only=['signature'],
        )

        try:
            file = consent.signature.file
        except ValueError:
            self.set_status(404)
            return

        self.set_header('Cache-Control', 'private')

        serve_file(self, file)

        self.finish()


class CustomerConsentPdfHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def get(self, consent_uuid):
        """
        swagger:
            summary: Download consent pdf.
            notes: >
                Beware that if consent has been signed with a photo,
                this endpoint will return the original pdf for print.
            parameters:
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
            type: string
            format: binary
        :swagger
        """
        consent = self.get_object_or_404(
            Consent,
            uuid=consent_uuid,
            customer__user=self.user,
        )

        try:
            if consent.signed and consent.photo:
                consent_pdf = consent.render_photo_pdf()
            else:
                consent_pdf = consent.render_pdf()

            self.set_header('Cache-Control', 'private')
            self.set_header('Last-Modified', format_datetime(consent.updated))

            serve_file(
                self,
                consent_pdf,
                content_type='application/pdf',
                content_disposition=f'attachment; filename="{consent_uuid}.pdf"',
            )
        except IOError:
            self.set_status(503)
            self.finish()
            return

        self.finish()


class CustomerConsentSendEmailHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @json_request
    @session(login_required=True, api_key_required=True)
    def post(self, consent_uuid):
        """
        swagger:
            summary: Send consent to user email.
            notes: >
                Consent should be signed.
            parameters:
                - name: consent_uuid
                  type: string
                  paramType: path
                  description: consent uuid
                - name: body
                  paramType: body
                  description: optional email, defaults to current user
                  type: SendEmailRequest
            type: SendEmailResponse
        :swagger
        """
        self.get_object_or_404(
            Consent, uuid=consent_uuid, customer__user=self.user, __only=['uuid']
        )

        serializer = SendEmailRequest(
            data=self.data or {},
            context={
                'email': self.user.email,
            },
        )
        data = self.validate_serializer(serializer)

        send_consent_customer_email_task.delay(consent_uuid, data['email'], language=self.language)

        self.finish_with_json(200, {'email': data['email']})
