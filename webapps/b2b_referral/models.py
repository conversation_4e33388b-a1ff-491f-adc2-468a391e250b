# pylint: disable=cyclic-import
import random
import string
from decimal import Decimal
from typing import List, Optional

from django.conf import settings
from django.db import models, transaction
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from lib.db import retry_on_sync_error
from lib.feature_flag.feature import MarketPayB2BReferralSettingEnabledFlag
from lib.models import ArchiveModel, BaseArchiveManager
from lib.tools import create_uuid, format_currency, major_unit, minor_unit

from webapps.b2b_referral.enums import (
    B2BReferralStatus,
    B2BRewardStatus,
    B2BRewardType,
)
from webapps.b2b_referral.events import (
    invited_business_paid_for_subscription_event,
    invited_business_signed_up_event,
)
from webapps.b2b_referral.tasks import (
    create_dyk_notification_task,
    create_finish_invited_notification_task,
    create_finish_referrer_notification_task,
    create_signup_notification_task,
)
from webapps.business.models import Business, MinValueValidator
from webapps.pos.models import OperationFeeMidnightQuerySet, POS
from webapps.profile_completeness.events import step_refer_business
from webapps.structure.models import Region


class B2BReferral(ArchiveModel):

    referrer = models.ForeignKey(
        'business.Business',
        related_name='referrals',
        on_delete=models.DO_NOTHING,
    )
    invited = models.ForeignKey(
        'business.Business',
        related_name='referral_invites',
        on_delete=models.DO_NOTHING,
    )
    status = models.CharField(
        max_length=1,
        choices=B2BReferralStatus.choices(),
        default=B2BReferralStatus.INVITED,
    )

    base_referral_setting = models.ForeignKey(
        'B2BReferralSetting',
        related_name='referrals',
        on_delete=models.PROTECT,
    )

    def __str__(self):
        return f'B2BReferral={self.id} Referrer={self.referrer.id} ' f'Invited={self.invited.id}'

    @cached_property
    def invited_reward(self) -> 'B2BReferralReward':
        """Return reward related to invited business."""
        if hasattr(self, '_invited_reward'):
            return self._invited_reward and self._invited_reward[0]
        return self.rewards.filter(type=B2BRewardType.INVITED).last()

    @cached_property
    def referrer_reward(self) -> 'B2BReferralReward':
        """Return reward related to referrer business."""
        if hasattr(self, '_referrer_reward'):
            return self._referrer_reward and self._referrer_reward[0]
        return self.rewards.filter(type=B2BRewardType.REFERRER).last()

    def _create_invited_reward(self):
        """Creates B2BReferralReward for invited business.

        If MARKET_PAY is on, Prize object will also be created.
        """

        reward_amount = self.base_referral_setting.invited_reward_amount

        if not reward_amount:
            return

        invited_reward = B2BReferralReward.objects.create(
            referral=self,
            type=B2BRewardType.INVITED,
            status=B2BRewardStatus.READY,
            amount=reward_amount,
        )
        if MarketPayB2BReferralSettingEnabledFlag():
            market_pay_global_setting = settings.MARKET_PAY_B2B_REFERRAL_ENABLED
        else:
            market_pay_global_setting = settings.MARKET_PAY_ENABLED

        if self.base_referral_setting.invited_auto_payout and market_pay_global_setting:
            Prize.objects.create(
                amount=minor_unit(reward_amount),
                currency=settings.CURRENCY_CODE,
                referral_reward=invited_reward,
                pos=self.invited.pos,
            )

    def _create_referrer_reward(self):
        """Creates B2BReferralReward for referring business.

        If MARKET_PAY is on, Prize object will also be created.
        """

        reward_amount = self.base_referral_setting.referrer_reward_amount

        if not reward_amount:
            return

        invited_reward = B2BReferralReward.objects.create(
            referral=self,
            type=B2BRewardType.REFERRER,
            status=B2BRewardStatus.READY,
            amount=reward_amount,
        )

        if MarketPayB2BReferralSettingEnabledFlag():
            market_pay_global_setting = settings.MARKET_PAY_B2B_REFERRAL_ENABLED
        else:
            market_pay_global_setting = settings.MARKET_PAY_ENABLED

        if self.base_referral_setting.referrer_auto_payout and market_pay_global_setting:
            Prize.objects.create(
                amount=minor_unit(reward_amount),
                currency=settings.CURRENCY_CODE,
                referral_reward=invited_reward,
                pos=self.referrer.pos,
            )

    @classmethod
    @transaction.atomic
    def create_deal(
        cls,
        referrer: Business,
        invited: Business,
    ) -> 'B2BReferral':
        """Creates referral deal and mark it as INVITED.

        Sends notification to referrer and invited business.

        :param referrer: Business who invites
        :param invited: Business who is invited
        """
        referral = cls.objects.create(
            referrer=referrer,
            invited=invited,
            status=B2BReferralStatus.INVITED,
            base_referral_setting=B2BReferralSetting.get_setting(referrer),
        )
        # profile completeness
        step_refer_business.send(referrer)

        # Notifications 2.0
        create_signup_notification_task.delay(referral.id)
        create_dyk_notification_task.delay(referral.id)

        # Notifications 3.0
        invited_business_signed_up_event.send(referral)

        return referral

    def mark_as_currently_on_trial(self):
        """Marks B2bReferral as CURRENTLY_ON_TRIAL."""
        self.status = B2BReferralStatus.CURRENTLY_ON_TRIAL
        self.save(update_fields=['status'])

    @transaction.atomic
    def mark_as_pending_reward(self):
        """Marks B2BReferral as PENDING_REWARD.

        Creates B2BReferralReward. If MARKET_PAY is on, Prize object will
        also be created.

        Sends notification to referrer and invited business.
        """

        self.status = B2BReferralStatus.PENDING_REWARD
        self.save(update_fields=['status'])

        self._create_invited_reward()
        self._create_referrer_reward()

        # Notifications 2.0
        create_finish_invited_notification_task.delay(self.id)
        create_finish_referrer_notification_task.delay(self.id)

        # Notifications 3.0
        invited_business_paid_for_subscription_event.send(self)

    def mark_as_paid(self):
        """Marks B2BReferral as PAID."""
        self.status = B2BReferralStatus.PAID
        self.save(update_fields=['status'])


class B2BReferralSetting(ArchiveModel):
    DEFAULT_SMS_TEXT = _(
        "Hey you should try out Booksy - it makes online booking easy and lets "
        "you manage your business from anywhere. Use my code {code} and sign up "
        "there {deeplink}"
    )
    DEFAULT_TOPIC_TEXT = _('We want to share the Booksy love')

    referrer_reward_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0, blank=True, validators=[MinValueValidator(0)]
    )
    invited_reward_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0, blank=True, validators=[MinValueValidator(0)]
    )
    business = models.ForeignKey(
        'business.Business',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    region = models.ForeignKey(
        'structure.Region',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    # If business or region should not use referral, set it to False
    referral_enabled = models.BooleanField(default=True)
    share_text = models.TextField(
        help_text="Change the last sentence to: Score {invited_reward_amount} when use my code "
        "{code} {deeplink} if an invited should get any reward together with referrer"
    )
    share_subject = models.TextField()

    # We have ambassadors. Their Referral reward are higher then normal.
    # We don't want to copy their setting to invited businesses.
    # Ambassadors don't want to share info about their profit=
    ambassador = models.BooleanField(default=False)

    referrer_auto_payout = models.BooleanField(default=True)
    invited_auto_payout = models.BooleanField(default=True)

    # Fields used to follow changes of model
    active = models.BooleanField(default=True)
    series_id = models.IntegerField(null=True, blank=True, db_index=True)

    def __str__(self):
        return (
            f'B2BReferralSetting {self.str_type()} '
            f'reward: {self.referrer_reward_amount} | '
            f'{self.invited_reward_amount}'
        )

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ) -> None:

        super().save(force_insert, force_update, using, update_fields, **kwargs)
        if not self.series_id:
            self.series_id = self.id
            super().save(update_fields=['series_id'])

    def str_type(self):
        if self.region:
            return f'region={self.region.id}'
        if self.business:
            return f'business={self.business.id}'
        return 'Invalid configuration'

    @staticmethod
    def get_default_share_text() -> str:
        return B2BReferralSetting.DEFAULT_SMS_TEXT

    @staticmethod
    def get_default_share_subject() -> str:
        return B2BReferralSetting.DEFAULT_TOPIC_TEXT

    @property
    def both_award_flow(self) -> bool:
        """Returns if setting will award single or both businesses"""
        return bool(self.invited_reward_amount == self.referrer_reward_amount)

    def get_affected_businesses(self) -> List[Business]:
        """Returns list of affected business"""
        if self.business:
            return [self.business]

        # Get region and all its children and call them family
        region_ids = [region.id for region in self.region.get_children_with_me()]

        # Get from ReferralSettings all settings which are related to family
        # and get their ids
        regions_with_settings = (
            B2BReferralSetting.objects.filter(
                region_id__in=region_ids,
                active=True,
            )
            .exclude(id=self.id)
            .values_list('region_id', flat=True)
        )

        # Ger from ReferralSettings all settings which are assigned to
        # particular business
        businesses_with_setting = B2BReferralSetting.objects.filter(
            business_id__isnull=False,
            active=True,
        ).values_list('business_id', flat=True)

        # Regions affected by this settings are: regions from family without
        # regions_with_setting
        affected_region_ids = (
            Region.objects.filter(
                id__in=region_ids,
            )
            .exclude(
                id__in=regions_with_settings,
            )
            .values_list('id', flat=True)
        )

        # Solution is Business in affected_regions without businesses which
        # already have their own setting.
        businesses = Business.objects.filter(region_id__in=affected_region_ids).exclude(
            id__in=businesses_with_setting
        )

        return list(businesses)

    @classmethod
    def _get_direct_setting(cls, business: Business) -> Optional['B2BReferralSetting']:
        """Performs direct part of B2BReferralSetting.get_settings method."""

        return cls.objects.filter(
            business=business,
            referral_enabled=True,
            active=True,
        ).first()

    @classmethod
    def _get_region_setting(cls, business: Business) -> Optional['B2BReferralSetting']:
        """Performs region part of B2BReferralSetting.get_settings method."""

        if not business.region:
            return None
        regions = business.region.get_parents_with_me()
        regions = Region.sort_regions(regions)

        referral_settings = cls.objects.filter(
            region__in=regions,
            referral_enabled=True,
            active=True,
        )

        if not referral_settings:
            return None

        return sorted(referral_settings, key=lambda obj: -regions.index(obj.region))[0]

    @classmethod
    def get_setting(cls, business: Business) -> Optional['B2BReferralSetting']:
        """Returns referral settings for business.

        First is checked if setting directly for business_id is existing.
        If not there is check for region.

        :param business:
        :return: setting assigned for business or None
        """

        direct_setting = cls._get_direct_setting(business)

        if direct_setting:
            return direct_setting

        region_setting = cls._get_region_setting(business)

        if region_setting:
            return region_setting

        return None


class B2BReferralCode(ArchiveModel):
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
    )
    # Code format is constant:
    # 4 chars form Business name,
    # 3 chars from Business id,
    # 1 ordinal number(default=1).
    code = models.CharField(max_length=8, unique=True)
    usage_limit = models.IntegerField(default=100)

    def __str__(self):
        return (
            f'B2BReferralCode code={self.code} business={self.business} '
            f'left: {self.usage_limit}'
        )

    @classmethod
    def last_code(cls, business) -> 'B2BReferralCode':
        return cls.objects.filter(business=business).last()

    @classmethod
    @retry_on_sync_error
    def generate_code(
        cls,
        business: [Business],
    ) -> 'B2BReferralCode':

        letters = string.ascii_uppercase + string.digits
        code = ''.join(random.choice(letters) for _i in range(8))

        return cls.objects.create(
            business=business,
            code=code,
        )

    @transaction.atomic
    def invite_business(self, invited: Business):
        """Method performs code usage.

        It creates B2BReferral object and decrease usage_limit of code by 1.
        :param invited: Business which used code.
        """
        self.usage_limit -= 1
        self.save()

        B2BReferral.create_deal(
            referrer=self.business,
            invited=invited,
        )


class B2BReferralReward(ArchiveModel):
    referral = models.ForeignKey(
        B2BReferral,
        related_name='rewards',
        on_delete=models.DO_NOTHING,
    )
    type = models.CharField(
        max_length=1,
        choices=B2BRewardType.choices(),
    )
    status = models.CharField(
        max_length=1,
        choices=B2BRewardStatus.choices(),
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)

    class Meta:
        unique_together = (
            'referral',
            'type',
        )

    def __str__(self):
        return (
            f'B2BReferralReward id={self.id} type={self.get_type_display()} '
            f'business={self.business.id} '
            f'status={self.get_status_display()}'
        )

    @property
    def business(self) -> Business:
        """Gets business depending on B2BRewardType"""
        if self.type == B2BRewardType.INVITED:
            return self.referral.invited

        return self.referral.referrer

    @property
    def pending(self) -> bool:
        """Return if actual status means money are pending."""
        return self.status == B2BRewardStatus.READY

    @property
    def rewarded(self) -> bool:
        """Return if actual status means that money have been already paid."""
        return self.status in [B2BRewardStatus.DEPOSITED, B2BRewardStatus.REWARDED_MANUALLY]

    def mark_as_paid_manually(self):
        from webapps.pop_up_notification.models import B2BReferralNotification

        Prize.objects.filter(
            referral_reward=self,
            settled=False,
        ).update(settled=True)

        self.status = B2BRewardStatus.REWARDED_MANUALLY
        self.save(update_fields=['status'])

        B2BReferralNotification.create_reward_notification(
            business=self.business, amount=self.amount, force_success=True
        )

        if self.type == B2BRewardType.REFERRER:
            self.referral.mark_as_paid()


class PrizeQuerySet(OperationFeeMidnightQuerySet):
    def pending(self):
        return self.filter(
            settled=False,
        )


class Prize(ArchiveModel):
    """It's special model created for Marketpay fund transfer.

    Instance will be created when business will use Marketpay to get its money.
    Fund transfer will aggregate it and pay in one transfer.
    """

    pos = models.ForeignKey(
        POS,
        on_delete=models.DO_NOTHING,
    )
    # This is not pnref for Adyen objects. Its kind of FK for TransferFunds,
    # UUID of Prize
    reference = models.CharField(
        max_length=64,
        default=create_uuid,
    )
    currency = models.CharField(max_length=3)
    amount = models.IntegerField()  # minor unit
    referral_reward = models.ForeignKey(
        B2BReferralReward, related_name='prizes', null=True, on_delete=models.DO_NOTHING
    )
    settled = models.BooleanField(
        default=False,
    )
    payout_reference = models.CharField(
        max_length=64,
        blank=True,
    )
    payout_booking_date = models.DateField(
        null=True,
    )

    objects = BaseArchiveManager.from_queryset(PrizeQuerySet)()

    @property
    def formatted_amount(self) -> Decimal:
        return format_currency(major_unit(self.amount or 0))
