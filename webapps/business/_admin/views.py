# pylint: disable=too-many-lines
# pylint: disable=consider-using-f-string
import datetime
import decimal
import json
import logging
from collections import defaultdict

import pytz
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin import helpers
from django.contrib.admin.utils import display_for_field, display_for_value
from django.contrib.admin.views.main import ChangeList
from django.contrib.messages.views import SuccessMessageMixin
from django.db.models import Count, J<PERSON><PERSON>ield, ManyToManyField
from django.db.models.expressions import RawSQL
from django.db.models.signals import post_save
from django.db.transaction import atomic
from django.forms import BaseInlineFormSet
from django.http import HttpResponseBadRequest, HttpResponsePermanentRedirect
from django.shortcuts import get_object_or_404, redirect
from django.template import loader
from django.urls import re_path as url, resolve, reverse
from django.utils.html import escape, format_html, format_html_join
from django.utils.translation import gettext as _
from django.views.generic.detail import SingleObjectMixin
from django.views.generic.edit import FormView, UpdateView

from country_config import Country
from lib.admin_helpers import (
    admin_link,
    BaseModelAdmin,
    ChangeLogAdminMixin,
    InlineLimitMixin,
    NoAddDelMixin,
    NoAddMixin,
    NoChangeMixin,
    NoRowsInListViewMixin,
    ReadOnlyFieldsMixin,
    ReadOnlyTabular,
    LazyLoadInlineMixin,
)
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.elasticsearch.admin import DocumentAdmin
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature import (
    ReindexImagesWithBusinessInAdminFlag,
)
from lib.feature_flag.feature.admin import ShowPaidDuplicatedAccountCancellationReason
from lib.feature_flag.feature.public_api import PublicAPIBooksyImporterEnabledFlag
from lib.feature_flag.killswitch import (
    UseCeleryInReindexImagesWithBusinessInAdminFlag,
    UseOAuth2ZoomClient,
)
from lib.rivers import River, bump_document
from lib.signals import disconnected_signal_receiver
from lib.tools import (
    CachingPaginator,
    duration_formatter,
    format_currency,
    sget_v2,
    tznow,
)
from lib.widgets.pretty_json import PrettyJSONWidget
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.admin_extra.views.booking_staff import BookingStaffChangeView
from webapps.admin_extra.views.calendar import (
    GoogleCalendarImportView,
    ICalendarImportView,
    VagaroCalendarImportView,
)
from webapps.admin_extra.views.customer import (
    CustomerAllDeleteView,
    CustomerImportedDeleteView,
    CustomerImportListView,
)
from webapps.admin_extra.views.deeplink import GenerateInviteMPDeeplinkView
from webapps.admin_extra.views.generate_staff import GenerateStaffView
from webapps.admin_extra.views.report import RetentionBusinessReports
from webapps.admin_extra.views.review import ReviewImportedDeleteView
from webapps.admin_extra.views.styleseat import (
    StyleSeatImportView,
    StyleSeatLoginView,
    StyleSeatVerificationView,
)
from webapps.billing.models import BillingSubscription, NotificationSMSStatistics
from webapps.billing.models.offline_migration import BillingOfflineMigration
from webapps.billing.permissions import BillingUserPermission
from webapps.billing.tasks import requested_churn
from webapps.booking.enums import AppointmentType
from webapps.booking.models import Appointment, SubBooking
from webapps.boost.models import BoostBan
from webapps.business._admin.filters import (
    BoostIsOnFilter,
    BoostStatusFilter,
    BusinessAccountDeletionRequestedFilter,
    BusinessVisibilityInMarketplaceFilter,
    CategoryFilter,
    CategoryListFilter,
    RentingVenueFilter,
    SmsNotificationStatusFilter,
    StatusFilter,
    SubscriptionExistsFilter,
    TransactionExistsFilter,
    TreatmentExistFilter,
)
from webapps.business._admin.forms import (
    BListingForm,
    BusinessAdminForm,
    BusinessCategoryForm,
    BusinessCustomerInfoForm,
    BusinessLocationForm,
    BusinessPhotoForm,
    BusinessPhotoInlineFormSet,
    CancellationReasonForm,
    CancellationReasonInlineForm,
    DigitalFlyerHashtagInlineFormset,
    FacebookInstagramWidgetSettingsForm,
    FacebookInstagramWidgetSettingsFormSet,
    MarketplaceBusinessFormSet,
    ReTrialAttemptForm,
    RentingVenueForm,
    ResourceModelForm,
    SalonNetworkInlineFormSet,
    ServiceAdminForm,
    ServiceCategoryForm,
    ServiceSuggestionForm,
    ServiceVariantAdminForm,
    UndoChurnForm,
    WeekHoursForm,
)

# pylint: disable=unused-import
from webapps.business._admin.helpers import disable_sms_notifications, enable_sms_notifications
from webapps.business.business_categories.tasks import refresh_images_task
from webapps.business.elasticsearch.business_customer import BusinessCustomerDocument
from webapps.business.enums import CancellationReasonType, CustomData, PriceType, RateType
from webapps.business.events import business_status_changed_event, business_churn_scheduled
from webapps.business.forms.admin import InviteCustomersAdminForm
from webapps.business.history_retriever import (
    BusinessHistoryRetriever,
    CoachingHistoryRetriever,
    HistoryRetriever,
)
from webapps.business.models import (
    BListing,
    Business,
    BusinessPolicyAgreement,
    CancellationReason,
    RentingVenue,
    Resource,
    SalonNetwork,
    Service,
    ServiceAddOn,
    ServiceAddOnUse,
    ServicePromotion,
    ServiceVariant,
    ServiceVariantChangelog,
    VersumToBooksyAgreements,
)
from webapps.business.models.bci import (
    BCIConsentChangelog,
    BCIPatientFile,
    BCITypeDataHistory,
    BusinessCustomerInfo,
    BusinessCustomerInfoHistory,
    BCIVersumAgreement,
)
from webapps.business.models.business_change import BusinessChange
from webapps.business.models.category import BusinessCategory, CategoryTranslation
from webapps.business.models.external import (
    FacebookInstagramWidgetSettings,
)
from webapps.business.receivers import clear_categories_cache
from webapps.business.serializer_fields import convert_to_percent
from webapps.business.tasks import (
    gdpr_business_data_export_task,
    invite_customers_to_booksy,
)
from webapps.business.tools import (
    claim_b_listing,
    create_b_listing_from_business_id,
    generate_icalendar_links,
)
from webapps.business.utils import (
    business_freeze,
    business_undo_churn,
    business_unfreeze,
    set_offline_subscription_expiry,
)
from webapps.business.widgets import (
    BListingCustomDataWidget,
    BusinessCustomDataWidget,
)
from webapps.business_related.models import (
    BooksyGiftCardsSettings,
    SafetyRule,
    SecuritySettings,
)
from webapps.df_creator.models import DigitalFlyerHashtag
from webapps.ecommerce.adapters import EcommercePermissionAdapter
from webapps.images.elasticsearch import ImageDocument
from webapps.images.models import (
    BusinessPlaceholderImage,
    Image as BusinessImage,
)
from webapps.market_pay.serializers import (
    format_payout_status,
    format_status,
)
from webapps.marketplace.models import (
    BoostClientCard,
    MarketplaceBusiness,
    MarketplacePromotionStatus,
)
from webapps.pos.models import BankAccount, POS
from webapps.public_partners.admin import (
    PartnerAppDataInline,
    PartnerAppsAdminInline,
    BooksyImporterAdminMixin,
)
from webapps.public_partners.models import (
    BusinessCustomerInfoMetadata,
    PartnerPermissionBusiness,
    PublicBooksyPartner,
    ResourceMetadata,
    ServiceMetadata,
)
from webapps.purchase.models import (
    Coaching,
    CoachingHistory,
    Subscription,
    SubscriptionHistory,
    SubscriptionTransaction,
)
from webapps.purchase.tasks.churn import business_churn_task
from webapps.reviews.tasks import recompute_business_reviews_task
from webapps.schedule.models import (
    BusinessHours,
    ResourceHours,
)
from webapps.segment.actions import paid_status_achieved_action
from webapps.segment.tasks import (
    analytics_business_info_updated_task,
    analytics_business_re_trial_eligible_task,
)
from webapps.segment.utils import post_first_paid_status_action
from webapps.subdomain_grpc.client import (
    SubdomainGRPC,
    SubdomainGRPCServerError,
    SubdomainGRPCValidationError,
)
from webapps.subdomain_grpc.serializers import SubdomainSerializer
from webapps.user.groups import GroupNameV2, IntranetGroupName
from webapps.user.models import User
from webapps.user.tools import get_user_from_django_request
from webapps.wait_list.models import WaitListDisabled
from webapps.zoom.admin import ZoomBusinessCredentialsInline, ZoomBusinessOAuth2CredentialsInline

logger = logging.getLogger()


class BusinessChangeListMixin:
    @staticmethod
    def get_changelist(request, **kwargs):
        return BusinessChangeList

    def changelist_view(self, request, extra_context=None, **kwargs):
        return super().changelist_view(request, extra_context=extra_context, **kwargs)


class BusinessChangeViewMixin:
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Redirect to custom change views according to whether object is
        business, b-listing or umbrella venue."""
        if request.method == 'GET':
            if isinstance(object_id, str) and not object_id.isdigit():
                return HttpResponseBadRequest()
            business = get_object_or_404(Business, id=object_id)

            if business.is_venue():
                model_class = RentingVenue
            elif business.is_b_listing():
                model_class = BListing
            else:
                model_class = Business

            if model_class != self.model:
                opts = model_class._meta
                return redirect(
                    reverse(f'admin:{opts.app_label}_{opts.model_name}_change', args=(object_id,))
                )

        return super().change_view(
            request, object_id, form_url=form_url, extra_context=extra_context
        )

    class Media:
        js = (
            'admin/js/vendor/jquery/jquery.js',
            'autocomplete_light/select2.min.js',
            'admin/js/jquery.init.js',
        )


class FacebookInstagramWidgetSettingsInline(admin.TabularInline):
    model = FacebookInstagramWidgetSettings
    formset = FacebookInstagramWidgetSettingsFormSet
    form = FacebookInstagramWidgetSettingsForm
    verbose_name_plural = 'Facebook/Instagram widget multiple businesses'
    fields = ('facebook_businesses', 'instagram_businesses')


class CancellationReasonInline(LazyLoadInlineMixin, NoAddMixin, admin.StackedInline):
    """
    Inline for editing CancellationReason
    """

    model = CancellationReason
    verbose_name = 'Cancellation Reason (Churn info)'
    verbose_name_plural = 'Cancellation Reason (Churn info)'
    form = CancellationReasonInlineForm
    # exclude = ['churn_done']

    readonly_fields = ('churn_done', 'deleted', 'payment_source_at_churn')
    classes = ['collapse']
    extra = 0
    can_delete = False
    template = 'admin/churn_inline.html'


class CancellationReasonFilter(admin.SimpleListFilter):
    title = 'Reason of cancel'
    parameter_name = 'cancellation_reason__exact'

    def lookups(self, request, model_admin):
        if ShowPaidDuplicatedAccountCancellationReason():
            return CancellationReasonType.choices()
        return [
            choice
            for choice in CancellationReasonType.choices()
            if choice[0] != CancellationReasonType.PAID_DUPLICATED_ACCOUNT
        ]

    def queryset(self, request, queryset):
        if value := self.value():
            return queryset.filter(cancellation_reason__exact=value)
        return queryset


class CancellationReasonAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = (
        'id',
        'business',
        'cancellation_date',
        'cancellation_reason',
        'business_cancellation_reason',
        'cancellation_info',
        'churn_type',
    )
    readonly_fields = (
        'business',
        'cancellation_date',
        'business_cancellation_reason',
        'reason_additional_info',
        'churn_done',
        'churn_type',
        'operator',
        'payment_source_at_churn',
    )
    ordering = (
        '-id',
        'cancellation_date',
    )
    search_fields = ('business__id', 'cancellation_reason')
    list_filter = (
        CancellationReasonFilter,
        'business_cancellation_reason',
        'churn_type',
    )
    list_select_related = True

    def has_add_permission(self, request):
        return False

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        business_churn_scheduled.send(obj.business, reason_id=obj.id)


class ChurnBusinessAdminView(SuccessMessageMixin, FormView):
    form_class = CancellationReasonForm
    success_url = 'admin:business_business_change'
    template_name = 'admin/custom_views/churn_business.html'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.business_id = None

    def get(self, request, *args, **kwargs):
        self.business_id = kwargs.get('business_id')
        if self.business_id:
            try:
                self.business_id = int(self.business_id)
            except ValueError:
                pass
        return super().get(request, *args, **kwargs)

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        form_kwargs['operator'] = get_user_from_django_request(self.request)
        return form_kwargs

    def get_initial(self):
        initial = self.initial or {}
        if self.business_id:
            try:
                initial['business'] = Business.objects.get(id=self.business_id)
            except Business.DoesNotExist:
                pass

        business = initial.get('business')

        # If it's a business with a new billing, let's set the cancellation
        # date at the end of current billing cycle.
        if (
            business
            and business.has_new_billing
            and business.status == Business.Status.PAID
            and (subscription := BillingSubscription.get_current_subscription(business.pk))
        ):
            cancellation_date = subscription.current_cycle_end
        else:
            cancellation_date = tznow()
        initial['cancellation_date'] = cancellation_date.strftime('%Y-%m-%d')
        return initial

    def get_success_message(self, cleaned_data):
        business = cleaned_data['business']
        churn_message = 'Churn was scheduled' if business.has_new_billing else 'Business was churn'
        return f'{churn_message} {business.name} (business_id:{business.id})'

    def form_valid(self, form):
        # from lib.rivers import bump_document
        cleaned_data = form.cleaned_data
        # Churn Business
        business = cleaned_data['business']
        cancellation_date = cleaned_data['cancellation_date']

        if sub_ids := business.subscriptions.filter(
            start__gte=tznow(), source=Business.PaymentSource.OFFLINE
        ).values_list('id'):
            messages.error(self.request, f'Churn failed due to pending subscriptions:{sub_ids}')
            return redirect(reverse(self.success_url, args=(business.id,)))

        if business.has_new_billing:
            # No atomic transaction is required. Pending requested churns are
            # checked every couple hours by separate task.
            cancellation_reason_obj = form.save()
            requested_churn.run(business_id=business.pk)
        else:
            with atomic():
                cancellation_reason_obj = form.save()
                set_offline_subscription_expiry(business, cancellation_date)
                business_churn_task.run(cancellation_reason_ids=[cancellation_reason_obj.id])
        business_churn_scheduled.send(business, reason_id=cancellation_reason_obj.id)

        # return response with redirect
        response = redirect(
            reverse(
                self.success_url,
                args=(business.id,),
            )
        )
        success_message = self.get_success_message(form.cleaned_data)
        if success_message:
            messages.success(self.request, success_message)

        return response


class UndoChurnBusinessAdminView(FormView):
    form_class = UndoChurnForm
    success_url = 'admin:business_business_change'
    template_name = 'admin/custom_views/generic_form_template.html'

    def get_initial(self):
        initial = self.initial or {}
        initial['business'] = self.kwargs['business_id']
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Undo churn Business'
        context['save_button_title'] = 'Undo churn'
        return context

    @staticmethod
    def get_success_message(cleaned_data):
        business_name = cleaned_data["business"].name
        business_id = cleaned_data["business"].id
        return f'Undo churn successful! {business_name} (business_id:{business_id})'

    def form_valid(self, form):
        # return response with redirect
        response = redirect(
            reverse(
                self.success_url,
                args=(form.cleaned_data['business'].id,),
            )
        )
        success_message = self.get_success_message(
            form.cleaned_data
        )  # pylint: disable=no-value-for-parameter
        if success_message:
            messages.success(self.request, success_message)
        return response

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        if form.is_valid():
            operator = get_user_from_django_request(request)
            endpoint = reverse(
                'admin:undo_churn_business',
                args=(form.cleaned_data['business'].id,),
            )
            success, error_dict, _ = business_undo_churn(form.cleaned_data, operator, endpoint)
            # pylint: disable=no-else-return
            if success:
                return self.form_valid(form)
            else:
                form.add_error(None, error_dict)
        return self.form_invalid(form)


class ResourceInline(ReadOnlyTabular, LazyLoadInlineMixin, admin.TabularInline):
    model = Resource
    classes = ['collapse']  # Django-1.10 :)
    extra = 0
    can_delete = False

    fields = (
        'resource_id',
        'business',
        'name',
        'type',
        'staff_access_level',
        'active',
        'visible',
        'order',
        'services_summary',
        'photo_thumbnail',
        'staff_user_data',
        'created',
        'updated',
        'deleted',
    )
    readonly_fields = fields

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return (
            qs.select_related(
                'staff_user',
                'photo',
            )
            .annotate(
                services_count=Count('services'),
                service_variants_count=Count('service_variants'),
            )
            .order_by('-active', 'order')
        )

    @staticmethod
    def resource_id(resource):
        return format_html('<a href="{}">{}</a>', admin_link(resource), resource.id)

    @staticmethod
    def services_summary(resource):
        if resource.is_staffer:
            return format_html(
                '''
                {} service variants
                (<a href="{}?service_variants__resources__id={}">view</a>)
            ''',
                resource.service_variants_count,
                reverse('admin:business_service_changelist'),
                resource.id,
            )
        return format_html(
            '''
            {} services
            (<a href="{}?resources__id={}">view</a>)
        ''',
            resource.services_count,
            reverse('admin:business_service_changelist'),
            resource.id,
        )

    @staticmethod
    def staff_user_data(resource):
        def _get_resource_user_anchor(resource: Resource) -> str:
            if resource.staff_user_id:
                return (
                    f'<a href="{admin_link(resource.staff_user)}">'
                    f'{escape(resource.get_staff_access_level_display())}:'
                    f' {escape(resource.staff_user.full_name)}</a>'
                )

        return (
            format_html(
                '<br/>'.join(
                    filter(
                        None,
                        (
                            _get_resource_user_anchor(resource),
                            resource.staff_email,
                            resource.staff_cell_phone,
                        ),
                    )
                )
            )
            or '-'
        )

    @staticmethod
    def photo_thumbnail(obj):
        return format_html(
            '<img src="{}" style="width: 100px; height: 100px"/>', obj.photo.thumbnail(100, 100)
        )


class BusinessPolicyAgreementInline(NoAddDelMixin, admin.TabularInline):
    model = BusinessPolicyAgreement
    fields = (
        'created',
        'privacy_policy_agreement',
        'marketing_agreement',
        'partner_marketing_agreement',
        'receiving_messages_consent',
        'new_terms_flow',
        'inspector_first_name',
        'inspector_last_name',
        'inspector_email',
    )
    readonly_fields = (
        'created',
        'privacy_policy_agreement',
        'marketing_agreement',
        'partner_marketing_agreement',
        'receiving_messages_consent',
    )
    exclude = ('deleted',)
    verbose_name = "GDPR Agreement"
    verbose_name_plural = "GDPR Agreements"

    def get_fields(self, request, obj=None):
        if settings.GDPR_ANNEX:
            return self.fields + ('annex_signed',)
        return self.fields


class OfflineToOnlineMigrationConsentInline(NoAddDelMixin, admin.TabularInline):
    model = BillingOfflineMigration
    verbose_name = "Offline to Online migration consent"
    verbose_name_plural = "Offline to Online migration consents"
    fields = [
        'gdpr_and_terms_consent',
        'gdpr_and_terms_consent_date',
        'offline_termination_consent',
        'offline_termination_consent_date',
        'offline_boost_termination_consent',
        'offline_boost_termination_consent_date',
    ]
    readonly_fields = fields

    def gdpr_and_terms_consent(self, obj):
        return obj.business.agreement_exist and bool(obj.business.agreement.reagreed_at)

    gdpr_and_terms_consent.boolean = True

    def gdpr_and_terms_consent_date(self, obj):
        if obj.business.agreement_exist:
            return (
                obj.business.agreement.reagreed_at.isoformat()
                if obj.business.agreement.reagreed_at
                else '-'
            )

    def offline_termination_consent(self, obj):
        return bool(obj.agreed_at)

    offline_termination_consent.boolean = True

    def offline_termination_consent_date(self, obj):
        return obj.agreed_at.isoformat() if obj.agreed_at else '-'

    def offline_boost_termination_consent(self, obj):
        return bool(obj.boost_agreed_at)

    offline_boost_termination_consent.boolean = True

    def offline_boost_termination_consent_date(self, obj):
        return obj.boost_agreed_at.isoformat() if obj.boost_agreed_at else '-'


class ServiceInline(ReadOnlyTabular, LazyLoadInlineMixin, admin.TabularInline):
    model = Service
    classes = ['collapse']  # Django-1.10 :)
    extra = 0
    can_delete = False

    fields = [
        'service_id',
        'name',
        'treatment',
        'service_category',
        'order',
        'active',
        'padding_display',
        'service_variants_list',
        'created',
        'updated',
        'deleted',
    ]
    readonly_fields = [
        'service_id',
        'name',
        'treatment',
        'service_category',
        'order',
        'active',
        'padding_display',
        'service_variants_list',
        'created',
        'updated',
        'deleted',
    ]

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return (
            qs.filter(combo_type__isnull=True)
            .select_related(
                'service_category',
                'treatment__parent',
            )
            .prefetch_related(
                'service_variants',
            )
            .order_by('-active', 'service_category__order', 'order')
        )

    @staticmethod
    def service_id(service):
        return format_html('<a href="{}">{}</a>', admin_link(service), service.id)

    @staticmethod
    def service_variants_list(service):
        return format_html(
            ', '.join(
                [
                    # pylint: disable=consider-using-f-string
                    '<a href="%s">[%s] %sh%sm %s</a>'
                    % (
                        admin_link(variant),
                        variant.id,
                        escape(variant.duration.hours),
                        escape(variant.duration.minutes),
                        escape(variant.format_price()),
                    )
                    for variant in service.service_variants.all()
                ]
            )
        )

    @staticmethod
    def padding_display(obj):
        if obj.padding_type is None:
            return
        return (
            f'{obj.get_padding_type_display()} {obj.padding_time.hours}h{obj.padding_time.minutes}m'
        )


class ServiceAddOnInline(
    LazyLoadInlineMixin, ReadOnlyTabular, InlineLimitMixin, admin.TabularInline
):
    model = ServiceAddOn
    extra = 0
    can_delete = False

    fields = [
        'service_add_on_id',
        'name',
        'services_list',
        'price',
        'price_type',
        'duration_display',
        'is_available_for_customer_booking',
        'max_allowed_quantity',
        'created',
        'updated',
        'deleted',
    ]
    readonly_fields = fields

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related('services').order_by('name')

    @staticmethod
    def service_add_on_id(service_add_on):
        return format_html('<a href="{}">{}</a>', admin_link(service_add_on), service_add_on.id)

    @staticmethod
    def services_list(service_add_on):
        return format_html_join(
            ',',
            '<a href="{}">[{}] {}</a>',
            (
                (admin_link(service), service.id, service.name)
                for service in service_add_on.services.all()
            ),
        )

    @admin.display(description='Duration')
    def duration_display(self, obj):
        if not obj.duration:
            return None
        return f'{obj.duration.hours}h {obj.duration.minutes}m'


class CombosInline(LazyLoadInlineMixin, ReadOnlyTabular, InlineLimitMixin, admin.TabularInline):
    model = Service
    classes = ['collapse']  # Django-1.10 :)
    extra = 0
    can_delete = False
    verbose_name = 'Combo'
    verbose_name_plural = 'Combos'

    fields = [
        'combo_service_id',
        'name',
        'treatment',
        'service_category',
        'is_available_for_customer_booking',
        'service_variants_list',
        'time_between_services',
        'combo_info',
        'combo_type',
        'active',
        'created',
        'updated',
        'deleted',
    ]
    readonly_fields = [
        'combo_service_id',
        'name',
        'treatment',
        'service_category',
        'order',
        'service_variants_list',
        'time_between_services',
        'combo_info',
        'active',
        'created',
        'updated',
        'deleted',
    ]

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return (
            qs.filter(combo_type__isnull=False)
            .select_related(
                'service_category',
                'treatment',
            )
            .prefetch_related(
                'service_variants__combo_children_through__child',
            )
            .order_by('-active', 'service_category__order', 'order')
        )

    @staticmethod
    def combo_service_id(service):
        return format_html('<a href="{}">{}</a>', admin_link(service), service.id)

    @staticmethod
    def service_variants_list(service):
        return format_html(
            '; '.join(
                [
                    # pylint: disable=consider-using-f-string
                    '<a href="%s">[%s] %sh%sm %s %s</a>'
                    % (
                        admin_link(combo.child),
                        combo.child.id,
                        escape(combo.child.duration.hours),
                        escape(combo.child.duration.minutes),
                        escape(combo.child.format_price()),
                        PriceType(escape(combo.child.type)).label,
                    )
                    for combo in service.service_variants.first().combo_children_through.all()
                ]
            )
        )

    @staticmethod
    def time_between_services(service):
        return ', '.join(
            [
                duration_formatter(combo.gap_time, allow_zero=True)
                for combo in service.service_variants.first().combo_children_through.all()
            ][:-1]
        )

    @staticmethod
    def combo_info(service):
        combo = service.service_variants.first()
        return ', '.join(
            [
                duration_formatter(combo.service_duration),
                str(combo.service_price),
                str(PriceType(combo.service_price.price_type).label),
            ]
        )


class ServiceAddOnUseInline(ReadOnlyTabular, InlineLimitMixin, admin.TabularInline):
    model = ServiceAddOnUse
    extra = 0
    can_delete = False

    fields = [
        'service_add_on_use_id',
        'service_addon',
        'business',
        'price',
        'quantity',
        'services_ids',
        'created',
        'updated',
        'deleted',
    ]
    readonly_fields = fields

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('service_addon').order_by('name')

    @staticmethod
    def service_add_on_use_id(service_add_on_use):
        return format_html(
            '<a href="{}">{}</a>', admin_link(service_add_on_use), service_add_on_use.id
        )


class BusinessSubscriptionInline(LazyLoadInlineMixin, ReadOnlyTabular, admin.TabularInline):
    model = Subscription
    classes = ['collapse']  # Django-1.10 :)
    extra = 0
    can_delete = False
    fields = (
        'sub_id',
        'product_name',
        'price',
        'source',
        'duration',
        'renewing',
        'status',
        'trans',
        'external_id',
        'cancel',
    )
    readonly_fields = fields
    ordering = (
        '-expiry',
        '-start',
        '-id',
    )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.select_related('product').prefetch_related('transactions')
        return qs

    @staticmethod
    def sub_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)

    @staticmethod
    def product_name(obj):
        return format_html("<a href='{}'>{}</a>", admin_link(obj.product), obj.product.name)

    @staticmethod
    def price(obj):
        return obj.product.price

    @staticmethod
    def dt_fmt(dt):
        return dt.strftime('%B %d, %Y, %H:%M')

    def duration(self, obj):
        return f'{self.dt_fmt(obj.start)} - {self.dt_fmt(obj.expiry)}'

    @staticmethod
    def status(obj):
        if obj.source == Business.PaymentSource.BRAINTREE:
            return obj.receipt['status']

        return 'Active' if obj.active else 'Inactive'

    def trans(self, obj):
        template = loader.get_template('admin/fields/field__transactions.html')
        transactions = [
            {
                'id': x.id,
                'charged_on': self.dt_fmt(x.charged_on),
                'state': x.get_state_display(),
                'price': f'{x.price_with_discount} {x.get_currency_code()}',
                'type_display': x.get_transaction_type_display() or '',
                'url': admin_link(x),
            }
            for x in obj.transactions.order_by('-charged_on')
        ]
        return template.render(
            {
                'id': obj.id,
                'transactions': transactions,
            }
        )

    trans.short_description = 'Transactions'

    @staticmethod
    def cancel(obj):
        if obj is None or obj.id is None:
            return ''
        return format_html(
            """<a href="{}" onclick="return confirm('Are you sure?')">
                Cancel
            </a>""",
            reverse(
                'admin:cancel_subscription',
                kwargs={'sub_id': obj.id},
            ),
        )

    @staticmethod
    def external_id(obj):
        if obj.source == Business.PaymentSource.PLAY:
            return obj.receipt.get('orderId')
        if obj.source == Business.PaymentSource.BRAINTREE:
            return obj.receipt.get('id')


class MarketplaceBusinessInLine(LazyLoadInlineMixin, admin.StackedInline):
    model = MarketplaceBusiness
    can_delete = False
    formset = MarketplaceBusinessFormSet
    exclude = ('deleted',)
    readonly_fields = (
        'boost_completed_sended',
        'activate_planned_by',
        'deactivate_planned_by',
    )
    verbose_name_plural = "Marketplace business"


class BooksyGiftCardsSettingsBusinessInLine(admin.StackedInline):
    model = BooksyGiftCardsSettings
    can_delete = False
    exclude = ('deleted',)
    readonly_fields = (
        'meets_criteria_to_accept_bgc',
        'is_enabled',
    )
    fields = ('accept_booksy_gift_cards_manually_disabled',) + readonly_fields
    verbose_name_plural = "Booksy Gift Cards Settings"

    @admin.display(description='Meets criteria to accept BGC')
    def meets_criteria_to_accept_bgc(self, obj):
        return obj.accept_booksy_gift_cards


class CoachingInline(LazyLoadInlineMixin, admin.TabularInline):
    model = Coaching
    min_num = 0
    extra = 0
    classes = ['collapse']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        formset._coachings_before_change = {  # pylint: disable=protected-access
            instance.id: CoachingHistory.extract_vars(instance)
            for instance in self.model.objects.filter(business=obj)
        }

        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        field = super().formfield_for_foreignkey(db_field, request, **kwargs)

        if db_field.name == 'subscription':
            resolved = resolve(request.path_info)
            field.queryset = field.queryset.filter(
                business_id=int(resolved.kwargs['object_id']),
                source=Business.PaymentSource.OFFLINE,
            )

        return field


class BraintreeTransactionsInline(LazyLoadInlineMixin, ReadOnlyTabular, admin.TabularInline):
    model = SubscriptionTransaction
    classes = ['collapse']
    extra = 0
    can_delete = False
    fields = (
        'id',
        'charged_on',
        'state',
        'price_with_discount',
    )
    readonly_fields = fields
    verbose_name = 'Braintree non-subscription transaction'
    verbose_name_plural = 'Braintree non-subscription transactions'

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(subscription__isnull=True)


class BusinessHistoryAdmin:
    @staticmethod
    def business_change(obj):
        return BusinessHistoryRetriever(BusinessChange).get_changes(obj)


class CoachingHistoryAdmin:
    @staticmethod
    def coaching_change(obj):
        return CoachingHistoryRetriever(CoachingHistory).get_changes(obj)


class BusinessCustomerInfoHistoryAdmin:
    @staticmethod
    def changes(obj):
        return HistoryRetriever(BusinessCustomerInfoHistory).get_changes(obj)


class BCITypeDataHistoryAdmin:
    @staticmethod
    def changes(obj):
        return HistoryRetriever(BCITypeDataHistory).get_changes(obj)


class MarketplacePromotionStatusInline(ReadOnlyTabular, admin.TabularInline):
    model = MarketplacePromotionStatus
    classes = ['collapse']  # Django-1.10 :)
    extra = 0
    can_delete = False

    fields = (
        'status',
        'created',
    )
    readonly_fields = fields


class BusinessHoursInLineLimiter(BaseInlineFormSet):
    def get_queryset(self):
        qs = super().get_queryset()
        return qs[:2]


class BusinessHoursInline(admin.TabularInline):
    verbose_name = 'Default Opening Hours'
    verbose_name_plural = 'Default Opening Hours'
    model = BusinessHours
    form = WeekHoursForm
    formset = BusinessHoursInLineLimiter
    exclude = ['created', 'updated', 'deleted']
    classes = ['collapse']
    ordering = ('-valid_from',)
    max_num = 2
    extra = 0
    can_delete = False


class SalonNetworkAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    fields = (
        'business',
        'members',
    )
    raw_id_fields = (
        'business',
        'members',
    )


class SafetyRuleInline(admin.TabularInline):
    model = SafetyRule.members.through
    extra = 1
    verbose_name_plural = 'Safety Rules'


class SalonNetworkInline(ReadOnlyTabular, admin.TabularInline):
    """Inline for managing 'You can also book with us here functionality'."""

    verbose_name = 'Salon Network'
    model = SalonNetwork
    exclude = ['created', 'updated', 'deleted']
    classes = ['collapse']
    fields = ('members',)
    raw_id_fields = ('members',)
    formset = SalonNetworkInlineFormSet


class SecuritySettingsInline(ReadOnlyTabular, admin.TabularInline):
    exclude = ['created', 'updated', 'deleted']
    model = SecuritySettings
    verbose_name = 'Security Setting'
    verbose_name_plural = 'Security Settings'
    fields = ('allowed_ips',)

    def has_view_permission(self, request, obj=None):
        if obj:
            return hasattr(obj, 'security_settings')
        return True


class BusinessVersumToBooksyAgreementsInline(ReadOnlyTabular, admin.TabularInline):
    model = VersumToBooksyAgreements
    fields = ('transfer_date', 'transfer_agreement', 'user_email', 'demo_agreement')
    readonly_fields = fields
    classes = ['collapse']

    verbose_name = 'Versum to Booksy Agreement'


class BusinessAdmin(
    NoAddDelMixin,
    NoRowsInListViewMixin,
    BaseModelAdmin,
    BusinessHistoryAdmin,
    CoachingHistoryAdmin,
    BusinessChangeListMixin,
    BusinessChangeViewMixin,
    BooksyImporterAdminMixin,
    GroupPermissionMixin,
    admin.ModelAdmin,
):  # pylint: disable=too-many-ancestors
    inlines = [
        MarketplacePromotionStatusInline,
        BusinessSubscriptionInline,
        PartnerAppsAdminInline,
        MarketplaceBusinessInLine,
        CoachingInline,
        BraintreeTransactionsInline,
        ResourceInline,
        ServiceInline,
        ServiceAddOnInline,
        CombosInline,
        BusinessHoursInline,
        CancellationReasonInline,
        SalonNetworkInline,
        ZoomBusinessCredentialsInline,
        SafetyRuleInline,
        # BusinessPolicyAgreementInline is appended later if needed
        FacebookInstagramWidgetSettingsInline,
        SecuritySettingsInline,
        BusinessVersumToBooksyAgreementsInline,
    ]
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)

    def get_inlines(self, request, obj):
        inlines = super().get_inlines(request, obj)

        if (
            sget_v2(obj, ['booksy_gift_cards_settings'])
            and BooksyGiftCardsSettingsBusinessInLine not in inlines
        ):
            inlines.append(BooksyGiftCardsSettingsBusinessInLine)

        return self.switch_zoom_inlines(inlines)

    @staticmethod
    def switch_zoom_inlines(inlines: list):
        """Used for replacing credentials inlines depending on flag status."""
        if not UseOAuth2ZoomClient():
            return inlines

        if ZoomBusinessOAuth2CredentialsInline in inlines:
            return inlines

        jwt_inline = ZoomBusinessCredentialsInline
        jwt_inline.readonly_fields = jwt_inline.fields
        jwt_inline.verbose_name_plural = ' '.join(
            (
                jwt_inline.verbose_name_plural,
                '!!! DEPRECATED - Use OAauth2 integration below !!!',
            )
        )

        position = inlines.index(jwt_inline)
        inlines.insert(position + 1, ZoomBusinessOAuth2CredentialsInline)
        return inlines

    paginator = CachingPaginator
    change_form_template = 'admin/change_forms/change_form__business.html'

    list_display = [
        'id',
        'name',
        'owner',
        'primary_category',
        'active',
        'status',
        'trial_till',
        'sms_notification_status',
    ]
    list_filter = [
        'active',
        # 'visible',  # need to be replaced
        BusinessVisibilityInMarketplaceFilter,
        StatusFilter,
        'registration_code',
        ('categories', CategoryListFilter),
        SubscriptionExistsFilter,
        TransactionExistsFilter,
        SmsNotificationStatusFilter,
        RentingVenueFilter,
        'include_in_analysis',
        'boost_contact_now',
        'boost_remind_later',
        BusinessAccountDeletionRequestedFilter,
    ]
    raw_id_fields = ['owner', 'buyer']
    readonly_fields = [
        'owner_link',
        'bookings_link',
        'active',
        'created',
        'updated',
        'deleted',
        'status',
        'subscriptions_count',
        'booking_count',
        'marketing_sms_count',
        'active_from',
        'visible_from',
        'paid_from',
        'paid_till',
        'sms_limit_history',
        'sms_limit_alerts',
        'sms_notification_status',
        'kyc_status',
        'braintree_balance',
        'subscription_history',
        'account_number',
        'partner_data_import_uid',
        'registration_source',
        'integrations',
        'churn_info',
        'business_change',
        'coaching_change',
        'contractors',
        'have_agreements',
        'time_zone_name',
        'b_listing_link',
        'has_active_b_listing',
        'overdue_till',
        'reviews_count',
        'reviews_rank_avg',
        'reviews_rank_score',
        # utt
        'business_treatment',
        'primary_category_utt',
        'categories_list_utt',
        'treatments_utt',
        # boost
        'boost_payment_source',
        # billing
        'has_new_billing',
        'new_billing_link',
        # boost
        'boost_ban_status',
    ]

    fieldsets = (
        (
            None,
            {
                'fields': (
                    'owner_link',
                    (
                        'active',
                        'visible',
                    ),
                    ('created', 'updated', 'deleted'),
                    'bookings_link',
                )
            },
        ),
        (
            'Status & Payments',
            {
                'fields': (
                    'status',
                    'active_from',
                    'visible_from',
                    'trial_till',
                    'kyc_status',
                    'has_new_billing',
                    'new_billing_link',
                    ('payment_source', 'subscriptions_count'),
                    'paid_from',
                    'paid_till',
                    'braintree_balance',
                    'registration_code',
                    'booking_count',
                    'marketing_sms_count',
                    'account_number',
                    'invoice_address',
                    'invoice_email',
                    'buyer',
                ),
            },
        ),
        (
            'Business Details',
            {
                'classes': ('collapse',),
                'fields': (
                    'owner',
                    'name',
                    'official_name',
                    'name_short',
                    'manual_boost_score',
                    'description',
                    'region',
                    'time_zone_name',
                    'address',
                    'address2',
                    'city',
                    'zipcode',
                    # 'latitude', 'longitude' will be hidden
                    # position would be used instead
                    ('latitude', 'longitude'),
                    'position',
                    'phone',
                    'alert_phone',
                    (
                        'website',
                        'facebook_link',
                        'instagram_link',
                        'ecommerce_link',
                        'public_email',
                    ),
                    (
                        'credit_cards',
                        'parking',
                        'wheelchair_access',
                        'pricing_level',
                    ),
                    'opening_hours_note',
                ),
            },
        ),
        (
            'Umbrella Venue',
            {
                'classes': ('collapse',),
                'fields': (
                    'renting_venue',
                    'contractor_description',
                ),
            },
        ),
        (
            'Categories & Treatments',
            {
                'classes': ('collapse',),
                'fields': (
                    'primary_category',
                    'categories',
                    'business_treatment',
                ),
            },
        ),
        (
            'Categories & Treatments UTT',
            {
                'classes': ('collapse',),
                'fields': (
                    'primary_category_utt',
                    'categories_list_utt',
                    'treatments_utt',
                ),
            },
        ),
        (
            'Wholesalers',
            {
                'classes': ('collapse',),
                'fields': ('wholesalers',),
            },
        ),
        (
            'Moderation',
            {
                'classes': ('collapse',),
                'fields': (
                    'sms_notification_status',
                    'verification',
                    'moderation_status',
                ),
            },
        ),
        (
            'Boost',
            {
                'classes': ('collapse',),
                'fields': (
                    ('boost_status', 'boost_ban_status'),
                    'boost_payment_source',
                    'has_braintree',
                    'boost_contact_now',
                    'boost_remind_later',
                    'receive_promotion_notifications',
                ),
            },
        ),
        (
            'Business Settings',
            {
                'classes': ('collapse',),
                'fields': (
                    'booking_mode',
                    'booking_max_lead_time',
                    'booking_min_lead_time',
                    'booking_max_modification_time',
                    'locked_limit_hourly',
                    'waitlist_disabled',
                    'consent_form_sms_request',
                ),
            },
        ),
        (
            'Internal Data',
            {
                'classes': ('collapse',),
                'fields': (
                    'importer',
                    'partner_data_import_uid',
                    'partner_integration_disabled',
                    'registration_source',
                    'sms_limit_history',
                    'sms_limit_alerts',
                    'sms_limit',
                    'sms_priority',
                    'integrations',
                    'custom_data',
                    'has_active_b_listing',
                    'b_listing_link',
                    'have_agreements',
                    'seo_region',
                    'package',
                    'overdue_till',
                    'include_in_analysis',
                    'reviews_count',
                    'reviews_rank_avg',
                    'reviews_rank_score',
                ),
            },
        ),
        (
            'Business history',
            {
                'classes': (
                    'collapse',
                    'wide',
                ),
                'fields': (
                    # 'churn_info',
                    'business_change',
                ),
            },
        ),
        (
            'Coaching history',
            {
                'classes': (
                    'collapse',
                    'wide',
                ),
                'fields': ('coaching_change',),
            },
        ),
        (
            'Subscription history',
            {
                'classes': (
                    'collapse',
                    'wide',
                ),
                'fields': ('subscription_history',),
            },
        ),
    )

    form = BusinessAdminForm

    list_per_page = 30
    search_fields = [
        '=id',
        'owner__email',
    ]
    # if we want use `full_text_search`
    # we need to define fields from which create
    # search vector in variable `search_vector_fields`
    search_vector_fields = [
        'name',
        'owner__email',
        'owner__first_name',
        'owner__last_name',
    ]
    show_full_result_count = False
    date_hierarchy = 'created'
    actions_on_bottom = False
    actions_on_top = True
    actions = [
        enable_sms_notifications,  # pylint: disable=used-before-assignment
        'download_sms_history',
        disable_sms_notifications,  # pylint: disable=used-before-assignment
        'business_data_gdpr_export',
    ]
    formfield_overrides = {
        ManyToManyField: {'widget': forms.CheckboxSelectMultiple},
    }

    def save_model(self, request, obj, form, change):
        business_id = obj.id
        analytics_business_info_updated_task.delay(
            business_id=business_id,
            context={'business_id': business_id},
        )

        if 'sms_limit' in form.changed_data:
            obj.set_sms_limit(form.cleaned_data.get('sms_limit'))

        super().save_model(request, obj, form, change)

    def save_formset(self, request, form, formset, change):
        # original logic from base class
        if isinstance(formset, FacebookInstagramWidgetSettingsFormSet):
            super().save_formset(request, form, formset, change)
            return
        # custom logic
        instances = formset.save(commit=False)
        for instance in instances:
            # pylint: disable=protected-access
            if formset.model == MarketplaceBusiness:
                operator = get_user_from_django_request(request)
                fields_modes_dict = {
                    'planned_boost_activate_date': 'activate',
                    'planned_boost_deactivate_date': 'deactivate',
                }
                changed_fields = formset.changed_objects[0][1] if formset.changed_objects else {}
                update_fields = changed_fields.copy()
                for field in set(changed_fields) & set(fields_modes_dict.keys()):
                    mode = fields_modes_dict[field]
                    change_field_name = f'{mode}_planned_by'
                    setattr(instance, change_field_name, operator)
                    update_fields.append(change_field_name)
                metadata = {'endpoint': 'BusinessAdmin'}
                _history = {'operator': operator, 'metadata': metadata}

                params = {'_history': _history}
                if update_fields:
                    params['update_fields'] = update_fields
                instance.save(**params)
            elif formset.model == CancellationReason:
                instance.save()
                business_churn_scheduled.send(instance.business, reason_id=instance.id)
            else:
                instance.save()
                if formset.model == Coaching:
                    before_change = formset._coachings_before_change.get(instance.id, {})
                    CoachingHistory.add(
                        instance,
                        CoachingHistory.extract_vars(instance),
                        before_change,
                        operator=get_user_from_django_request(request),
                    )

        formset.save_m2m()

    @staticmethod
    def owner_link(instance):
        return format_html(
            '<a href="{0}">{1}</a>',
            reverse(
                'admin:user_user_change',
                args=(instance.owner.id,),
            ),
            instance.owner,
        )

    owner_link.short_description = "Owner"

    @staticmethod
    def partner_data_import_uid(instance: Business):
        return instance.partner_data.import_uid if instance.partner_data else '-'

    @staticmethod
    def new_billing_link(instance):
        return format_html(
            '<a href="{0}" target="_blank">Billing details</a>',
            reverse('admin:billing_billingbusiness_change', args=(instance.id,)),
        )

    new_billing_link.short_description = 'Billing details'

    @staticmethod
    def bookings_link(instance):
        return format_html(
            '<a href="{0}?appointment__business__id={1}">{2}</a>',
            reverse(
                'admin:booking_subbooking_changelist',
            ),
            instance.id,
            _("List of bookings"),
        )

    bookings_link.short_description = "Bookings"

    @staticmethod
    @admin.display(description='Has active B Listing')
    def has_active_b_listing(instance):
        b_listing = instance.b_listing
        if b_listing:
            return b_listing.active
        return False

    @staticmethod
    @admin.display(description='B Listing link')
    def b_listing_link(instance):
        if instance.b_listing:
            return format_html(
                '<a href="{0}">{1}</a>',
                reverse(
                    'admin:business_blisting_change',
                    args=(instance.b_listing.id,),
                ),
                instance.b_listing,
            )

    @staticmethod
    def have_agreements(obj):
        return obj.agreement_exist

    have_agreements.boolean = True

    @staticmethod
    def contractors(obj):
        independent_contractor_ids = obj.contractors.values_list('id', flat=True).distinct()
        result = ', '.join(
            [
                # pylint: disable=consider-using-f-string
                "<a href='{}'>{}</a>".format(
                    reverse(
                        'admin:business_business_change',
                        args=(contractor_id,),
                    ),
                    contractor_id,
                )
                for contractor_id in independent_contractor_ids
            ]
        )

        return format_html(result)

    # TODO: Remove
    @staticmethod
    def churn_info(obj):
        if obj.id is None or not obj.cancellation_reason.exists():
            return "Business is active"

        cancel_reason = obj.cancellation_reason.order_by('-cancellation_date').first()
        return CancellationReasonForm(
            instance=cancel_reason,
        ).as_table()

    @staticmethod
    def boost_ban_status(obj):
        if ban := BoostBan.get_current_ban(obj.id):
            return ban
        return '-'

    def get_inline_instances(self, request, obj=None):
        inline_instances = super().get_inline_instances(request, obj)
        # Ugly hack to avoid ADDING BusinessPolicyAgreement in admin
        # (NoAddDelMixin won't work)
        if obj is not None and obj.agreement_exist:
            business_policy_inline = BusinessPolicyAgreementInline(self.model, self.admin_site)
            inline_instances.append(business_policy_inline)

            if hasattr(obj, 'offline_migration'):
                consent_inline = OfflineToOnlineMigrationConsentInline(self.model, self.admin_site)
                inline_instances.append(consent_inline)

        return inline_instances

    def save_related(self, request, form, formsets, change):
        super().save_related(request, form, formsets, change)
        # pylint: disable=protected-access
        BusinessChange.add(
            form.instance,
            form.instance,
            form._business_before_change,
            operator=get_user_from_django_request(request),
            metadata={'endpoint': 'BusinessAdmin'},
            extra_attrs=['get_categories_internal_names'],
        )

        previous_venue_id = form._business_before_change.get(
            'renting_venue_id'
        )  # pylint: disable=protected-access
        if form.instance.renting_venue_id != previous_venue_id:
            # update old venue
            if previous_venue_id is not None:
                # venue was changes
                old_venue = Business.objects.get(id=previous_venue_id)
                old_venue.remove_contractors_categories([form.instance.id])
                old_venue.recalculate_umbrella_rank(create_change=True)

            # update new umbrella venue
            if form.instance.renting_venue:
                # add category to new venue
                form.instance.renting_venue.add_contractors_categories([form.instance.id])
                form.instance.renting_venue.recalculate_umbrella_rank(create_change=True)

        if 'waitlist_disabled' in form.changed_data:
            waitlist_disabled = form.cleaned_data['waitlist_disabled']
            if waitlist_disabled:
                WaitListDisabled.objects.create(
                    business=form.instance,
                    disabled_by_id=request.user.id,
                )
            else:
                WaitListDisabled.objects.filter(business=form.instance).delete()

    def get_extra_business_actions(self, request, obj) -> dict:
        # pylint: disable=no-value-for-parameter
        links = {
            'link_www': self.links(obj),
            'path': request.get_full_path(),
            'edit_status': self.get_status_url(obj),
            'status_refresh': self.get_refresh_status_url(obj),
            'invite_customers_button': self.get_invite_customers(obj),
            'churn_action': self.get_churn_url(obj),
            'customers_imports': self.get_customers_imports(obj),
            'vagaro_file_import': self.get_vagaro_file_import(obj),
            'move_imported_bookings': self.get_move_imported_bookings(obj),
            'google_calendar_import': self.get_google_calendar_import(obj),
            'icalendar_calendar_import': self.get_icalendar_calendar_import(obj),
            'icalendar_path': self.get_icalendar_url(obj),
            'styleseat_import': self.get_styleseat_import(obj),
            'reindex_business': self.get_reindex_business(obj),
            'show_business_document': self.get_show_business_document(obj),
            'recompute_business_reviews': self.get_recompute_business_reviews(obj),
            'change_timezone_button': self.get_change_timezone_button(obj),
            'transform_into_b_listing': self.get_transform_into_b_listing(obj),
            'delete_imported_reviews': self.get_delete_imported_reviews(obj),
            'link_generator': self.get_link_generator(obj),
            'change_bookings_staffer': self.get_change_bookings_staffer(obj),
            'admin_business_reports': self.get_admin_business_reports(obj),
            'switch_notifications': self.get_switch_sms_notifications(obj),
            'generate_mp_invite_deeplink': self.get_generate_mp_invite_deeplink(obj),
        }

        if PublicAPIBooksyImporterEnabledFlag():
            links.update({'switch_booksy_importer': self.get_switch_booksy_importer(obj)})

        if not obj.has_new_billing:
            links.update(
                {
                    'freeze_status': self.get_freeze_status_btn(obj),
                    'braintree_restore': self.get_braintree_restore_url(obj),
                    'subscriptions_restore': self.get_subscriptions_restore_url(obj),
                }
            )
            if settings.CHARGE_FOR_STAFFERS_ENABLED:
                links.update(
                    {
                        'braintree_staff_charges': self.get_staff_charges_url(obj),
                    }
                )
        return links

    # pylint: disable=arguments-differ
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if 'importer' in form.base_fields:
            form.base_fields['importer'].initial = obj and obj.integrations.get('importer')
        if 'categories' in form.base_fields:
            form.base_fields['categories'].widget.can_add_related = False
        if 'wholesalers' in form.base_fields:
            form.base_fields['wholesalers'].widget.can_add_related = False
        if 'latitude' in form.base_fields:
            form.base_fields['latitude'].widget = forms.HiddenInput()
            form.base_fields['latitude'].label = ''
        if 'longitude' in form.base_fields:
            form.base_fields['longitude'].widget = forms.HiddenInput()
            form.base_fields['longitude'].label = ''
        if 'custom_data' in form.base_fields:
            form.base_fields['custom_data'].widget = BusinessCustomDataWidget(request)
        # remember changes for later use in .save_related()
        # pylint: disable=protected-access
        form._business_before_change = BusinessChange.extract_vars(
            obj,
            extra_attrs=['get_categories_internal_names'],
        )
        if (
            'payment_source' in form.base_fields
            and obj.payment_source != Business.PaymentSource.BRAINTREE
            and not BillingUserPermission(request).is_billing_admin
        ):
            choices = form.base_fields['payment_source'].choices
            form.base_fields['payment_source'].choices = [
                choice for choice in choices if choice[0] != Business.PaymentSource.BRAINTREE
            ]

        return form

    @staticmethod
    def get_churn_url(obj=None):
        if obj is None:
            return ''

        if obj.churn_available():
            return f"""
                <a href="{reverse('admin:churn_business', args=(obj.id,))}" class="btn ">
                    Churn business
                </a>
            """
        return ''

    @staticmethod
    def get_icalendar_url(obj=None):
        if obj is None:
            return ''
        return f"""
            <a href="{reverse('admin:generate_icalendar_links', args=(obj.id,))}" class="btn ">
                Generate iCalendar Links
            </a>
        """

    @staticmethod
    def get_change_timezone_button(obj=None):
        if obj is None or (
            len(pytz.country_timezones.get(settings.API_COUNTRY)) < 2
            and settings.API_COUNTRY != Country.GB  # Guernsay
        ):
            return ''
        return format_html(
            """
            <a href="{}" class="btn">
                Change Time Zone
            </a>
        """,
            reverse("admin:business_change_timezone", args=(obj.id,)),
        )

    @staticmethod
    def get_customers_imports(obj=None):
        if obj is None:
            return ''
        return f"""
            <a href="{reverse('admin:customer_imports', args=(obj.id,))}" class="btn ">
                Customers imports
            </a>
        """

    @staticmethod
    def get_vagaro_file_import(obj=None):
        if obj is None:
            return ''
        return f"""
             <a href="{reverse('admin:vagaro_file_import', args=(obj.id,))}" class="btn ">
                 Vagaro File Import
             </a>
         """

    @staticmethod
    def get_move_imported_bookings(obj=None):
        if obj is None:
            return ''
        return f"""
             <a href="{reverse('admin:booking_subbooking_changelist')}?business__id={obj.id}" class="btn ">
                 Move imported bookings
             </a>
         """

    @staticmethod
    def get_styleseat_import(obj=None):
        if obj is None:
            return ''
        return f"""
            <a href="{reverse('admin:styleseat_import', args=(obj.id,))}" class="btn ">
                Styleseat Import
            </a>
        """

    @staticmethod
    def get_change_bookings_staffer(obj=None):
        if obj is None:
            return ''
        return f"""
            <a href="{reverse('admin:change_bookings_staffer', args=(obj.id,))}" class="btn ">
                Change bookings staffer
            </a>
        """

    @staticmethod
    def get_admin_business_reports(obj=None):
        if obj is None:
            return ''
        return f"""
            <a href="{reverse('admin:admin_business_reports', args=(obj.id,))}" class="btn ">Retention report</a>
        """

    @staticmethod
    def get_reindex_business(obj=None):
        if obj is None:
            return ''
        return format_html(
            """
            <a href="{}" class="btn ">
                Reindex Business
            </a>
            """,
            reverse('admin:reindex_business', args=(obj.id,)),
        )

    @staticmethod
    def get_show_business_document(obj=None):
        if obj is None:
            return ''
        obj_id = f"business:{obj.id}"
        return format_html(
            """
            <a href="{}" class="btn ">
                Show BusinessDocument
            </a>
            """,
            reverse("admin:business_BusinessDocument_change", args=(obj_id,)),
        )

    @staticmethod
    def get_recompute_business_reviews(obj=None):
        if obj is None:
            return ''
        return format_html(
            """
            <a href="{}" class="btn ">
                Recompute Business Reviews
            </a>
            """,
            reverse('admin:recompute_business_reviews', args=(obj.id,)),
        )

    @staticmethod
    def get_link_generator(obj=None):
        if obj is None:
            return ''
        return f"""
            <a href="{reverse('admin:purchase_subscriptionoffer_add')}?business_id={obj.id}" class="btn ">
                Link Generator
            </a>
        """

    @staticmethod
    def get_invite_customers(obj=None):
        if obj is None:
            return ''
        return format_html(
            '''
            <a href="{}" class="btn ">
                Invite customers to Booksy
            </a>
            ''',
            reverse('admin:invite_customers', kwargs={'business_id': obj.id}),
        )

    @staticmethod
    def get_google_calendar_import(obj=None):
        if obj is None:
            return ''
        link = reverse('admin:google_calendar_import', args=(obj.id,))
        return f"""<a href="{link}" class="btn ">Google Calendar import</a>"""

    @staticmethod
    def get_icalendar_calendar_import(obj=None):
        if obj is None:
            return ''
        link = reverse('admin:icalendar_calendar_import', args=(obj.id,))
        return f'<a href="{link}" class="btn ">iCalendar (*.ics) import</a>'

    @staticmethod
    def get_transform_into_b_listing(obj):
        if obj is None or not obj.can_transform_into_b_listing():
            return ''
        link = reverse("admin:transform_into_b_listing", args=(obj.id,))
        return f'<a href="{link}" class="btn ">Transform into B Listing</a>'

    @staticmethod
    def get_delete_imported_reviews(obj=None):
        if obj is None:
            return ''
        link = reverse('admin:delete_imported_reviews', args=(obj.id,))
        return f'<a href="{link}" class="btn ">Delete imported reviews</a>'

    @staticmethod
    def get_generate_mp_invite_deeplink(obj):
        if obj is None:
            return ''
        link = reverse('admin:generate_mp_invite_deeplink', args=(obj.id,))
        return f'<a href="{link}" class="btn ">Generate marketplace deeplink</a>'

    @staticmethod
    def get_status_url(obj):
        if obj is None or obj.status in (
            Business.Status.DEMO,
            Business.Status.DEMO_TEMPLATE,
        ):
            return ''

        statuses = {x[0]: x[1] for x in Business.Status.choices()}
        assert '' not in statuses
        statuses[''] = 'original status'

        actions = [Business.Status.BLOCKED if obj.status != Business.Status.BLOCKED else '']

        if obj.trial_blocked_available():
            actions.append(Business.Status.TRIAL_BLOCKED)
        elif obj.status == Business.Status.TRIAL_BLOCKED:
            actions.append(Business.Status.TRIAL)
        elif obj.status == Business.Status.OVERDUE:
            actions.append(Business.Status.BLOCKED_OVERDUE)
        elif obj.status == Business.Status.BLOCKED_OVERDUE:
            actions.append(Business.Status.OVERDUE)

        html = '\n'.join(
            # pylint: disable=consider-using-f-string
            '<a href="%s" class="btn">Switch to %s</a>'
            % (
                reverse(
                    'admin:business_edit_status',
                    kwargs={
                        'business_id': obj.id,
                        'status': status,
                    },
                ),
                statuses[status],
            )
            for status in actions
        )

        return format_html(html)

    @staticmethod
    def get_freeze_status_btn(obj):
        subscriptions = obj.subscriptions.filter(
            source=Business.PaymentSource.OFFLINE,
        )

        active_subsctiption = subscriptions.filter(start__lt=tznow(), expiry__gt=tznow()).last()

        expired_subscription = subscriptions.filter(expiry__lt=tznow()).last()

        if (
            obj.payment_source
            in [
                Business.PaymentSource.UNKNOWN,
                Business.PaymentSource.OFFLINE,
            ]
            and obj.status == Business.Status.PAID
            and active_subsctiption
        ):
            phrase = 'Freeze'
            url_ = reverse('admin:business_freeze', args=(obj.id,))
        elif (
            obj.payment_source
            in [
                Business.PaymentSource.UNKNOWN,
                Business.PaymentSource.OFFLINE,
            ]
            and obj.status == Business.Status.BLOCKED_OVERDUE
            and expired_subscription
        ):
            phrase = 'Unfreeze'
            url_ = reverse('admin:business_unfreeze', args=(obj.id,))
        else:
            return ''

        return format_html('<a href="{}" class="btn">{}</a>', url_, phrase)

    @staticmethod
    def edit_status_view(
        request, business_id, status
    ):  # pylint: disable=too-many-branches,too-many-statements
        biz = get_object_or_404(Business, id=business_id)
        old_status = biz.status
        before = BusinessChange.extract_vars(biz)

        if (
            biz.has_new_billing
            and biz.status in Business.Status.paid_statuses()
            and status != Business.Status.BLOCKED
        ):
            messages.error(
                request,
                'If Business is on New Billing and has Subscription, you can not change status.',
            )
            return redirect(admin_link(biz))

        simple = (
            (old_status == Business.Status.OVERDUE and status == Business.Status.BLOCKED_OVERDUE)
            or (old_status == Business.Status.BLOCKED_OVERDUE and status == Business.Status.OVERDUE)
            or status == Business.Status.BLOCKED
        )
        if status == Business.Status.OVERDUE:
            if biz.id in settings.SUBSCRIPTION_TEST_BUSINESSES:
                # BIL-597
                # We maintain some accounts for testing purposes, such as Apple reviews
                delta = datetime.timedelta(days=180)
            else:
                delta = datetime.timedelta(days=settings.STATUS_FLOW__BLOCKED_OVERDUE_AFTER_DAYS)
            biz.overdue_till = tznow() + delta
            biz.active = True
        elif status == Business.Status.BLOCKED_OVERDUE:
            biz.active = True
        if simple:
            biz.status = status
        elif status == Business.Status.TRIAL_BLOCKED:
            now = tznow()
            biz.status = status
            biz.trial_till = now
            biz.updated = now
            biz.active = False
            biz.save(
                update_fields=[
                    'updated',
                    'status',
                    'trial_till',
                    'active',
                ]
            )
        elif status == Business.Status.TRIAL:
            biz.status = status
            biz.active = True
            biz.trial_till = tznow() + datetime.timedelta(
                days=settings.STATUS_FLOW__TRIAL_EXTENSION
            )

        else:
            # pylint: disable-next=consider-using-f-string
            regex_q = r'"business.status":\s*\[\s*"[^%s]"\s*,\s*"%s"\s*]' % (
                (Business.Status.BLOCKED,) * 2
            )
            try:
                change = BusinessChange.objects.filter(
                    business=biz,
                    data__regex=regex_q,
                ).latest('created')
            except BusinessChange.DoesNotExist:
                last_status = (
                    Business.Status.PAID if biz.subscriptions.exists() else Business.Status.TRIAL
                )
            else:
                last_status = json.loads(change.data)['business.status'][0]

            biz.status = last_status
            biz.save()
            operator = get_user_from_django_request(request)
            BusinessChange.add(
                biz,
                biz,
                before,
                operator=operator,
                metadata={
                    'endpoint': reverse(
                        'admin:business_edit_status',
                        kwargs={
                            'business_id': business_id,
                            'status': last_status,
                        },
                    ),
                },
            )
            from webapps.purchase.tasks import ComputeBusinessStatusTask

            metadata = {
                'user_email': operator.email if operator else '',
                'user_id': operator.id if operator else '',
                'admin_view': 'BusinessAdmin',
            }
            ComputeBusinessStatusTask.run(
                business_id=business_id,
                metadata=metadata,
            )

            messages.success(request, f'Business status changed to {biz.get_status_display()}')
            if (
                old_status in Business.Status.analytics_never_paid_statuses()
                and last_status == Business.Status.PAID
            ):
                post_first_paid_status_action(
                    business_id,
                    {
                        'business_id': business_id,
                    },
                )

            if old_status != last_status and last_status == Business.Status.PAID:
                paid_status_achieved_action(business_id, last_status, old_status)

            return redirect(admin_link(biz))

        biz.save()

        BusinessChange.add(
            biz,
            biz,
            before,
            operator=get_user_from_django_request(request),
            metadata={
                'endpoint': reverse(
                    'admin:business_edit_status',
                    kwargs={'business_id': business_id, 'status': status},
                ),
            },
        )

        messages.success(request, f'Business status changed to {biz.get_status_display()}')
        if old_status in Business.Status.analytics_never_paid_statuses():
            post_first_paid_status_action(
                business_id,
                {
                    'business_id': business_id,
                },
            )
        if old_status != biz.status:
            business_status_changed_event.send(
                business_id,
                new_status=biz.status,
                old_status=old_status,
            )

        return redirect(admin_link(biz))

    @staticmethod
    def get_braintree_restore_url(obj):
        return format_html(
            '<a href="{}" class="btn">Restore Braintree subscriptions</a>',
            reverse('admin:business_restore_braintree', kwargs={'business_id': obj.id}),
        )

    @staticmethod
    def get_subscriptions_restore_url(obj):
        return format_html(
            '<a href="{}" class="btn">Restore subscriptions</a>',
            reverse('admin:business_restore_subscriptions', kwargs={'business_id': obj.id}),
        )

    @staticmethod
    def get_refresh_status_url(obj):
        return format_html(
            '<a href="{}" class="btn">Refresh business status</a>',
            reverse('admin:business_refresh_status', kwargs={'business_id': obj.id}),
        )

    @staticmethod
    def get_staff_charges_url(obj):
        return format_html(
            '<a href="{}" class="btn">Update staff charges in Braintree</a>',
            reverse('admin:business_update_staff_charges', kwargs={'business_id': obj.id}),
        )

    @staticmethod
    def generate_icalendar_links(request, business_id):
        treatwell = PublicBooksyPartner.objects.filter(name='treatwell').first()
        if treatwell:
            business = Business.objects.get(id=business_id)
            permission = PartnerPermissionBusiness.objects.filter(
                business=business, partner=treatwell
            ).first()
            if not permission:
                treatwell.add_business(business)

            success = generate_icalendar_links(business_id)

            if success:
                messages.success(
                    request,
                    'iCalendar(.ics) links generated. '
                    'You can find the calendar links '
                    'in the "Internal Data" section below.',
                )
            else:
                messages.error(
                    request,
                    'An error occurred while generating iCalendar link, please try again later.',
                )
        else:
            messages.warning(request, 'Partner does not exist.')

        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def restore_braintree(request, business_id):
        from webapps.purchase.tasks.brain_tree import refresh_braintree_subscriptions

        refresh_braintree_subscriptions.delay(business_id)
        messages.success(request, 'Please wait up to 5 minutes for restore procedure to finish')
        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def restore_subscriptions(request, business_id):
        from webapps.purchase.tasks.renewing_subscription import (
            renew_one_business,
        )

        renew_one_business.delay(business_id, 'admin restore')
        messages.success(request, 'Subscription/s will be restored soon')
        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def refresh_status(request, business_id):
        from webapps.purchase.tasks import ComputeBusinessStatusTask

        ComputeBusinessStatusTask.delay(
            business_id=business_id,
            metadata={'task': 'admin refresh status'},
        )
        messages.success(request, 'Business status will be refreshed soon')
        return redirect(
            request.GET.get('next')
            or reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def update_staff_charges(request, business_id):
        from webapps.purchase.tasks.brain_tree import update_staff_charges

        update_staff_charges.delay(business_id=business_id)
        messages.success(request, 'Successfully requested staff charges update')
        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def get_switch_sms_notifications(obj=None):
        if obj is None:
            return ''
        if obj.sms_notification_status != Business.SMSStatus.ENABLED:
            # object has sms_notification_status pending or disabled
            phrase = 'Enable'
            fun = 'enable_sms_notifications'
        elif obj.sms_notification_status == Business.SMSStatus.ENABLED:
            # object has sms_notification_status enabled
            phrase = 'Disable'
            fun = 'disable_sms_notifications'
        else:
            raise ValueError('Invalid object sms notification')

        # pylint: disable=possibly-used-before-assignment
        link = reverse(f'admin:{fun}', args=(obj.id,))
        return f'<a href="{link}" class="btn ">{phrase} sms notifications</a>'

    @staticmethod
    def change_sms_status(request, business_id, status):
        business = Business.objects.get(id=business_id)
        before = BusinessChange.extract_vars(business)
        business.sms_notification_status = status
        business.save(update_fields=['sms_notification_status'])
        BusinessChange.add(
            business,
            business,
            before,
            operator=get_user_from_django_request(request),
            metadata={
                'endpoint': reverse(
                    'admin:business_business_change',
                    args=(business_id,),
                ),
            },
        )

    @staticmethod
    def disable_sms_notifications(request, business_id):
        BusinessAdmin.change_sms_status(
            request,
            business_id,
            Business.SMSStatus.DISABLED,
        )
        messages.success(request, 'SMS notifications successfully disabled')

        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def enable_sms_notifications(request, business_id):
        BusinessAdmin.change_sms_status(
            request,
            business_id,
            Business.SMSStatus.ENABLED,
        )
        messages.success(request, 'SMS notifications successfully enabled')

        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def has_elastic_errors(result):
        """Check for Elastic indexing errors in the response.

        An example of response with errors:
        >>> [[doc_type_1, [0, [err_1]]], [doc_type_2, [0, [err_2, err_3]]]]
        Extracted errors:
        >>> [[err_1], [err_2, err_3]]
        This method will return:
        >>> any([[err_1], [err_2, err_3]])
        """
        return any(doc_result[1][1] for doc_result in result)

    @staticmethod
    def reindex_business(request, business_id):
        # we want reindex business immediately here
        # without using celery
        business = Business.objects.get(id=business_id)
        result = business.reindex()

        if BusinessAdmin.has_elastic_errors(result):
            messages.error(request, 'Business reindex failed')
            return redirect(
                reverse(
                    'admin:business_business_change',
                    args=(business_id,),
                )
            )

        user_data = UserData(custom={CustomUserAttributes.BUSINESS_ID: business_id})

        if ReindexImagesWithBusinessInAdminFlag(user_data):
            # Also recalculating business_images status and reindexing all business business_images
            business_images = business.images

            if not business.status == Business.Status.SETUP:
                business_images.filter(active=not business.active).update(
                    active=business.active, bump_instances_to_es=False
                )

            ids_to_reindex = business_images.values_list('id', flat=True)
            ImageDocument.reindex(
                ids=ids_to_reindex,
                use_celery=UseCeleryInReindexImagesWithBusinessInAdminFlag(user_data),
            )

        bcis = business.business_customer_infos.values_list('id', flat=True)
        bump_document(River.BUSINESS_CUSTOMER, list(bcis))

        messages.success(request, 'Business has been reindex')
        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def recompute_business_reviews(request, business_id):
        # pylint: disable=no-value-for-parameter
        recompute_business_reviews_task(business_id=business_id)

        messages.success(request, 'Business reviews have been recomputed')

        return redirect(
            reverse(
                'admin:business_business_change',
                args=(business_id,),
            )
        )

    @staticmethod
    def transform_into_b_listing(request, business_id):
        create_b_listing_from_business_id(business_id)
        messages.success(request, 'Business has been transformed into B Listing')
        return redirect(
            reverse(
                'admin:business_business_changelist',
            )
        )

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<business_id>\d+)/status/(?P<status>[A-Z]?)$',
                self.admin_site.admin_view(self.edit_status_view),
                name='business_edit_status',
            ),
            url(
                r'^(?P<business_id>\d+)/freeze/$',
                self.admin_site.admin_view(self.freeze),
                name='business_freeze',
            ),
            url(
                r'^(?P<business_id>\d+)/unfreeze/$',
                self.admin_site.admin_view(self.unfreeze),
                name='business_unfreeze',
            ),
            url(
                r'^(?P<business_id>\d+)/braintree_restore/$',
                self.admin_site.admin_view(self.restore_braintree),
                name='business_restore_braintree',
            ),
            url(
                r'^(?P<business_id>\d+)/restore_subscriptions/$',
                self.admin_site.admin_view(self.restore_subscriptions),
                name='business_restore_subscriptions',
            ),
            url(
                r'^(?P<business_id>\d+)/refresh_status/$',
                self.admin_site.admin_view(self.refresh_status),
                name='business_refresh_status',
            ),
            url(
                r'^(?P<business_id>\d+)/update_staff_charges/$',
                self.admin_site.admin_view(self.update_staff_charges),
                name='business_update_staff_charges',
            ),
            url(
                r'^(?P<business_id>\d+)/generate_icalendar_links/$',
                self.admin_site.admin_view(self.generate_icalendar_links),
                name='generate_icalendar_links',
            ),
            url(
                r'^(?P<business_id>\d+)/enable_sms_notifications/$',
                self.admin_site.admin_view(self.enable_sms_notifications),
                name='enable_sms_notifications',
            ),
            url(
                r'^(?P<business_id>\d+)/disable_sms_notifications/$',
                self.admin_site.admin_view(self.disable_sms_notifications),
                name='disable_sms_notifications',
            ),
            url(
                r'^(?P<business_id>\d+)/invite_customers/$',
                self.admin_site.admin_view(InviteCustomersAdminView.as_view()),
                name='invite_customers',
            ),
            url(
                r'^(?P<business_id>\d+)/churn_business/$',
                self.admin_site.admin_view(ChurnBusinessAdminView.as_view()),
                name='churn_business',
            ),
            url(
                r'^(?P<business_id>\d+)/undo_churn_business/$',
                self.admin_site.admin_view(UndoChurnBusinessAdminView.as_view()),
                name='undo_churn_business',
            ),
            url(
                r'^(?P<business_id>\d+)/customer_imports/$',
                self.admin_site.admin_view(CustomerImportListView.as_view()),
                name='customer_imports',
            ),
            url(
                r'^(?P<business_id>\d+)/customer_imports/delete/$',
                self.admin_site.admin_view(CustomerImportedDeleteView.as_view()),
                name='customer_imports_delete',
            ),
            url(
                r'^(?P<business_id>\d+)/customer_imports/delete_all/$',
                self.admin_site.admin_view(CustomerAllDeleteView.as_view()),
                name='customer_delete_all',
            ),
            url(
                r'^(?P<business_id>\d+)/change_timezone/$',
                self.admin_site.admin_view(BusinessChangeTimezoneView.as_view()),
                name='business_change_timezone',
            ),
            url(
                r'(?P<business_id>\d+)/vagaro_file_import/$',
                self.admin_site.admin_view(VagaroCalendarImportView.as_view()),
                name='vagaro_file_import',
            ),
            url(
                r'^(?P<business_id>\d+)/google_calendar_import/$',
                self.admin_site.admin_view(GoogleCalendarImportView.as_view()),
                name='google_calendar_import',
            ),
            url(
                r'^(?P<business_id>\d+)/icalendar_calendar_import/$',
                self.admin_site.admin_view(ICalendarImportView.as_view()),
                name='icalendar_calendar_import',
            ),
            url(
                r'^(?P<business_id>\d+)/styleseat_import/$',
                self.admin_site.admin_view(StyleSeatLoginView.as_view()),
                name='styleseat_import',
            ),
            url(
                r'^(?P<business_id>\d+)/styleseat_import_verification/'
                r'(?P<username>.+)/(?P<password>.+)/(?P<tkm_token>.+)/$',
                self.admin_site.admin_view(StyleSeatVerificationView.as_view()),
                name='styleseat_import_verification',
            ),
            url(
                r'^(?P<business_id>\d+)/styleseat_import_final/'
                r'(?P<token>.+)/(?P<provider_id>.+)/(?P<staffers_data>.+)/$',
                self.admin_site.admin_view(StyleSeatImportView.as_view()),
                name='styleseat_import_final',
            ),
            url(
                r'^(?P<business_id>\d+)/reindex_business/$',
                self.admin_site.admin_view(self.reindex_business),
                name='reindex_business',
            ),
            url(
                r'^(?P<business_id>\d+)/recompute_business_reviews/$',
                self.admin_site.admin_view(self.recompute_business_reviews),
                name='recompute_business_reviews',
            ),
            url(
                r'^(?P<business_id>\d+)/transform_into_b_listing/$',
                self.admin_site.admin_view(self.transform_into_b_listing),
                name='transform_into_b_listing',
            ),
            url(
                r'^(?P<business_id>\d+)/generate_mp_invite_deeplink/$',
                self.admin_site.admin_view(GenerateInviteMPDeeplinkView.as_view()),
                name='generate_mp_invite_deeplink',
            ),
            url(
                r'(?P<business_id>\d+)/change_bookings_staffer/$',
                self.admin_site.admin_view(BookingStaffChangeView.as_view()),
                name='change_bookings_staffer',
            ),
            url(
                r'(?P<business_id>\d+)/retention_report/$',
                self.admin_site.admin_view(RetentionBusinessReports.as_view()),
                name='admin_business_reports',
            ),
            url(
                r'(?P<business_id>\d+)/delete_imported_reviews/$',
                self.admin_site.admin_view(ReviewImportedDeleteView.as_view()),
                name='delete_imported_reviews',
            ),
            url(
                r'generate_staffers/$',
                self.admin_site.admin_view(GenerateStaffView.as_view()),
                name='generate_staffers',
            ),
        ]

        return additional_urls + urls

    @staticmethod
    def freeze(request, business_id):
        business = get_object_or_404(Business, id=business_id)
        operator = get_user_from_django_request(request)
        endpoint = reverse('admin:business_freeze', args=(business_id,))
        success = business_freeze(business, operator, endpoint)
        if success:
            messages.success(request, 'Freeze successful')
        else:
            messages.error(request, 'Freeze skipped')
        return redirect(request.META.get('HTTP_REFERER', '/'))

    @staticmethod
    def unfreeze(request, business_id):
        business = get_object_or_404(Business, id=business_id)
        operator = get_user_from_django_request(request)
        endpoint = reverse('admin:business_unfreeze', args=(business_id,))
        success = business_unfreeze(business, operator, endpoint)
        if success:
            customers = list(
                BusinessCustomerInfo.objects.filter(
                    business__id=business.id, visible_in_business=True
                ).values_list('id', flat=True)
            )
            BusinessCustomerDocument.reindex(customers, use_celery=True)
            messages.success(request, 'Unfreeze successful')
        else:
            messages.error(request, 'Unfreeze skipped')
        return redirect(request.META.get('HTTP_REFERER', '/'))

    @staticmethod
    def subscriptions_count(obj):
        count_new = BillingSubscription.objects.filter(business=obj).count()
        if hasattr(obj, 'subscriptions_count'):
            return obj.subscriptions_count + count_new
        return obj.subscriptions.count() + count_new

    subscriptions_count.admin_order_field = 'subscriptions_count'

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def kyc_status(instance):
        account_holder = instance.pos.account_holder
        if account_holder is None:
            return '-'
        return format_html(
            'Status: {}, Payout: {}',
            format_status(account_holder.status),
            format_payout_status(account_holder.payout_allowed),
        )

    kyc_status.short_description = "KYC"

    @staticmethod
    def braintree_balance(obj):
        return float(
            sum(
                decimal.Decimal(x.receipt.get('balance', 0))
                for x in list(filter(lambda x: x.source == 'B', obj.subscriptions.all()))
            )
        )

    @staticmethod
    def subscription_history(obj):
        # pylint: disable=invalid-name
        def row(x):
            link = admin_link(x.subscription)
            ready_json = json.dumps(x.history, indent=4).replace('{', '{{').replace('}', '}}')

            return f'''
                <a href="{link}">{x.subscription}</a><br/>
                Created: {x.created}<br/>
                <pre>{ready_json}</pre>
            '''

        histories = (
            SubscriptionHistory.objects.filter(
                subscription__business=obj,
            )
            .order_by('-created')
            .select_related(
                'subscription',
            )[:20]
        )

        return format_html('<br/>'.join(row(x) for x in histories.iterator()))

    @staticmethod
    def booking_count(obj):
        counts = dict(
            SubBooking.objects.filter(
                appointment__business=obj,
                deleted__isnull=True,
                appointment__type__in=Appointment.TYPES_BOOKABLE,
            )
            .values(
                'appointment__type',
            )
            .annotate(
                Count('appointment__type'),
            )
            .values_list(
                'appointment__type',
                'appointment__type__count',
            )
        )

        cnt_customer = counts.get(AppointmentType.CUSTOMER, 0)
        cnt_business = counts.get(AppointmentType.BUSINESS, 0)

        return format_html(
            'customer: {}<br />business: {}<br />total: {}',
            cnt_customer,
            cnt_business,
            cnt_customer + cnt_business,
        )

    @staticmethod
    def marketing_sms_count(obj):
        sms_summary = NotificationSMSStatistics.sms_summary(obj, periods_amount=1)[0]
        return format_html(
            '<br />'.join(
                [
                    'current period: {}',
                    'limit free: {}',
                    'limit paid: {}',
                    'total parts count (free + paid): {}',
                ]
            ),
            sms_summary.get("period"),
            sms_summary.get("limit_free"),
            sms_summary.get("limit_paid"),
            sms_summary.get("parts_count"),
        )

    @staticmethod
    def account_number(obj):
        number = BankAccount.objects.get(id=POS.objects.get(business_id=obj.id).id)
        return format_html(
            '<a href="{}">({}) {} {} </a>',
            admin_link(number),
            number.type,
            number.routing_number,
            number.account_number,
        )

    @staticmethod
    def download_sms_history(request, queryset):
        from django.http import HttpResponse

        from webapps.notification.models import NotificationHistory

        result = []

        for business in queryset:
            smses = NotificationHistory.sms_history(business.id)
            csv_data = NotificationHistory.sms_history_format_csv(smses)

            # this is one ugly hack!
            if queryset.count() > 1:
                csv_data = f"{business.id},{business.name.encode('utf-8')}\n" + csv_data

            result.append(csv_data)

        download_data = '\n\n'.join(result)

        response = HttpResponse(download_data, content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename=sms-history.csv'
        return response

    download_sms_history.short_description = "Download SMS history"

    @staticmethod
    def business_data_gdpr_export(request):
        # We don't need to evaluate the queryset received, we just need ids
        selected_ids = request.POST.getlist(helpers.ACTION_CHECKBOX_NAME)
        gdpr_business_data_export_task.delay(selected_ids)
        messages.success(
            request, "GDPR data export will be sent to the selected businesses' owners."
        )

    business_data_gdpr_export.short_description = 'Send business data to business owners (GDPR)'

    @staticmethod
    def links(obj):
        links = []
        if obj.active:
            links.append(f'<a href="{obj.get_seo_url()}" target="_blank">www</a>')

        if obj.active and obj.subdomain:
            url = obj.get_seo_url(dummy=False)  # pylint: disable=redefined-outer-name
            links.append(f'<a href="{url}" target="_blank">{url}</a>')

        return format_html('<br/>'.join(links))

    @staticmethod
    def categories_list_utt(instance: Business):
        return list(instance.categories_utt.values_list('internal_name', flat=True))

    @staticmethod
    def primary_category_utt(instance: Business):
        return instance.primary_category.category_utt

    @staticmethod
    def business_treatment(instance: Business):
        return format_html_join(
            '\n',
            '<li>{}</li>',
            (
                (str(treatment),)
                for treatment in instance.treatments.prefetch_related('parent').order_by(
                    'parent__name', 'name'
                )
            ),
        )

    business_treatment.short_description = 'Treatments'


class BusinessDocumentAdmin(GroupPermissionMixin, DocumentAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_per_page = 10
    show_full_result_count = False
    hide_keyword_field = True

    def has_module_permission(self, request):
        return False

    def get_readonly_fields(self, request, obj=None):
        return [
            name for name, field in self.model.fields.items() if not isinstance(field, JSONField)
        ]

    formfield_overrides = {
        JSONField: {'widget': PrettyJSONWidget, 'disabled': True},
    }

    fieldsets = [
        (
            None,
            {
                'fields': [
                    '_id',
                    'name',
                    'updated_index',
                    'active',
                    'visible',
                ],
            },
        ),
        (
            'Other fields',
            {
                'classes': ['collapse'],
                'fields': [
                    'active_from',
                    'booking_mode',
                    'slug',
                    'cover_photo',
                    'thumbnail_photo',
                    'non_cover_image_limited_count',
                    'phone',
                    'business_location',
                    'venue_location',
                    'reviews_rank',
                    'reviews_stars',
                    'reviews_count',
                    'reviews_rank_score',
                    'reviews_rating_score',
                    'popularity',
                    'manual_boost_score',
                    'promotion_boost',
                    'mp_promotion',
                    'conversion',
                    'pricing_level',
                    'website',
                    'facebook_link',
                    'instagram_link',
                    'public_email',
                    'description_keyword',
                    'ecommerce_link',
                    'credit_cards',
                    'parking',
                    'wheelchair_access',
                    'noindex',
                    'promoted',
                    'show_similar_gallery',
                    'hidden_in_search',
                    'hide_top_services',
                    'disable_customer_note',
                    'booking_max_lead_time',
                    'booking_min_lead_time',
                    'booking_max_modification_time',
                    'owner_id',
                    'regions',
                    'timezone_name',
                    'business_primary_category',
                    'business_categories',
                    'treatments',
                    'treatment_count',
                    'female_weight',
                    'primary_female_weight',
                    'primary_category',
                    'categories_utt',
                    'treatments_utt',
                    'female_weight_utt',
                    'primary_female_weight_utt',
                    'subdomain',
                    'service_categories',
                    'open_hours',
                    'opening_hours_note',
                    'pos_pay_by_app_enabled',
                    'deposit_policy',
                    'deposit_cancel_time',
                    'service_fee',
                    'suggest_businesses',
                    'owner_email',
                    'is_renting_venue',
                    'is_b_listing',
                    'b_listing_source_id',
                    'sitemap_url',
                    'top_female_services_ids',
                    'top_male_services_ids',
                    'top_services_ids',
                    'promotions_profitability',
                    'max_discount_rate',
                    'salon_network',
                    'has_online_services',
                    'has_online_vouchers',
                    'has_safety_rules',
                    'traveling',
                    'has_special_offers',
                    'is_financial_institution',
                    'pixel_id',
                    'venue_last_updated',
                    'amenities',
                    'printer_config',
                    'partners',
                    'profile_type',
                    'hidden_on_web',
                    'importer',
                    'awards',
                ],
            },
        ),
        (
            'Availability',
            {
                'classes': ['collapse'],
                'fields': [
                    'availability',
                    'availability_utt',
                ],
            },
        ),
    ]


class BusinessEventAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    fields = [
        'first_no_show',
        'late_cancellation_count',
        'ask_for_message_blast_activation',
    ]
    search_fields = ['=business__id']
    list_filter = [
        'first_no_show',
        'ask_for_message_blast_activation',
    ]
    list_display = [
        'id',
        'business',
        'first_no_show',
        'late_cancellation_count',
        'ask_for_message_blast_activation',
    ]


class TreatmentInline(NoAddDelMixin, admin.TabularInline):
    model = BusinessCategory
    verbose_name = 'Treatment'
    verbose_name_plural = 'Treatments'
    classes = ['collapse']  # Django-1.10 :)
    fields = (
        'treat_id',
        'name',
        'full_name',
        'slug',
    )

    readonly_fields = fields

    @staticmethod
    def treat_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), escape(obj.id))


class DigitalFlyerHashtagsInline(admin.TabularInline):
    model = DigitalFlyerHashtag
    formset = DigitalFlyerHashtagInlineFormset
    verbose_name_plural = 'Digital Flyer Hashtags'
    classes = ['collapse']  # Django-1.10 :)
    extra = 1
    fields = ('id', 'name')
    readonly_fields = ('id',)


class ImagesInline(ReadOnlyTabular, admin.TabularInline):
    model = BusinessPlaceholderImage
    verbose_name_plural = 'Images'
    classes = ['collapse']
    fields = readonly_fields = (
        'image_id',
        'type',
        'image',
        'image_thumb',
    )

    @staticmethod
    def image_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), escape(obj.id))

    @staticmethod
    def image_thumb(obj):
        image_url = f'{obj.image.url}?size=150x150'
        return format_html('<img src="{url}" style="width: 150px"/>', url=image_url)


class CategoryTranslationInline(NoAddDelMixin, admin.TabularInline):
    model = CategoryTranslation
    extra = 1
    fields = (
        'language',
        'translated_name',
    )


class BusinessCategoryAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    change_form_template = 'admin/change_forms/change_form__business_category.html'
    change_list_template = "admin/change_lists/change_list__business_category.html"
    inlines = [TreatmentInline, DigitalFlyerHashtagsInline, ImagesInline, CategoryTranslationInline]
    list_display = [
        'id',
        'type',
        'name',
        'plural_name',
        'parent',
        'slug',
        'full_name',
        'report_name',
        'internal_name',
        'order',
        'f_order',
        'm_order',
        'biz_order',
        'featured',
        'keywords_list',
        'has_online_services',
        'hide_on_home',
        'visible_for_biz',
        'hidden_from_business_search_hints',
        'excluded_from_auto_assignment',
    ]
    list_filter = [
        'type',
        ('parent', CategoryFilter),
        'hidden_from_business_search_hints',
        'excluded_from_auto_assignment',
        'deleted',
    ]
    readonly_fields = [
        'type',
        'name',
        'plural_name',
        'full_name',
        'internal_name',
        'slug',
        'previous_slugs',
        'es_index',
        'parent',
        'group',
        'created',
        'updated',
        'deleted',
    ]
    list_per_page = 200
    search_fields = [
        'slug',
        'name',
        'internal_name',
    ]
    actions = [
        'hide_from_business_search_hints',
        'unhide_from_business_search_hints',
        'exclude_from_auto_assignment',
        'include_in_auto_assignment',
    ]

    form = BusinessCategoryForm

    def has_change_permission(self, request, obj=None):
        has_permission = super().has_change_permission(request, obj=obj)
        return has_permission and (
            request.user.is_superuser or self._is_category_manager(request.user)
        )

    @staticmethod
    def _is_category_manager(user):
        return user.groups.filter(name=IntranetGroupName.BUSINESS_CATEGORY_MANAGER).exists()

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related('parent')

    @admin.display(description='Alternative names')
    def alternative_names_list(self, obj):
        return ', '.join(obj.alternative_names or [])

    @admin.display(description='Keywords')
    def keywords_list(self, obj):
        return ', '.join(obj.keywords or [])

    @staticmethod
    def get_refresh_images(obj=None):
        if obj is None:
            return ''
        return format_html(
            """
            <a href="{}" class="btn ">
                Refresh images
            </a>
            """,
            reverse('admin:refresh_images', args=(obj.id,)),
        )

    # pylint: disable=method-hidden
    def get_form(self, request, obj=None, change=False, **kwargs):
        self.refresh_images = self.get_refresh_images(obj)

        form = super().get_form(request, obj, **kwargs)
        return form

    @staticmethod
    def refresh_images(request, business_category_id=None):  # pylint disable=method-hidden
        business_category_ids = [int(business_category_id)] if business_category_id else None
        refresh_images_task.delay(business_category_ids=business_category_ids)

        messages.success(
            request,
            'Image update has been added to the celery queue and will be completed in 15 minutes.',
        )

        if business_category_id:
            return redirect(
                reverse(
                    'admin:business_businesscategory_change',
                    args=(business_category_id,),
                )
            )
        return redirect(
            reverse(
                'admin:business_businesscategory_changelist',
            )
        )

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<business_category_id>\d+)/refresh_images/$',
                self.admin_site.admin_view(self.refresh_images),
                name='refresh_images',
            ),
            url(
                r'^refresh_images/$',
                self.admin_site.admin_view(self.refresh_images),
                name='refresh_images',
            ),
        ]

        return additional_urls + urls

    def get_extra_business_actions(self, request, obj) -> dict:
        return {
            'refresh_images': self.get_refresh_images(obj),
        }

    @admin.action(permissions=['change'])
    def hide_from_business_search_hints(self, request, queryset):
        self._bulk_change_hidden_from_business_search_hints(request, queryset, True)

    @admin.action(permissions=['change'])
    def unhide_from_business_search_hints(self, request, queryset):
        self._bulk_change_hidden_from_business_search_hints(request, queryset, False)

    def _bulk_change_hidden_from_business_search_hints(self, request, queryset, value):
        with disconnected_signal_receiver(post_save, clear_categories_cache, BusinessCategory):
            for obj in queryset:
                obj.hidden_from_business_search_hints = value
                obj.save(update_fields=['hidden_from_business_search_hints'])
                self.log_change(request, obj, 'Changed hidden_from_business_search_hints.')
        clear_categories_cache()
        self.message_user(request, f'Updated {len(queryset)} object(s).')

    @admin.action(permissions=['change'])
    def exclude_from_auto_assignment(self, request, queryset):
        self._bulk_change_excluded_from_auto_assignment(request, queryset, True)

    @admin.action(permissions=['change'])
    def include_in_auto_assignment(self, request, queryset):
        self._bulk_change_excluded_from_auto_assignment(request, queryset, False)

    def _bulk_change_excluded_from_auto_assignment(self, request, queryset, value):
        with disconnected_signal_receiver(post_save, clear_categories_cache, BusinessCategory):
            for obj in queryset:
                obj.excluded_from_auto_assignment = value
                obj.save(update_fields=['excluded_from_auto_assignment'])
                self.log_change(request, obj, 'Changed excluded_from_auto_assignment.')
        clear_categories_cache()
        self.message_user(request, f'Updated {len(queryset)} object(s).')


class ServiceCategoryAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    form = ServiceCategoryForm
    list_display = ['id', 'business', 'name', 'order']
    search_fields = [
        '=id',
        'business__name',
        '=business__id',
        'business__owner__email',
    ]
    list_per_page = 100
    raw_id_fields = ['business']
    readonly_fields = ['business', 'created', 'updated', 'deleted']


class ServiceAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    form = ServiceAdminForm
    list_display = [
        'id',
        'business',
        'name',
        'treatment',
        'order',
        'active',
        'treatment_no_match',
        'is_online_service',
        'service_code',
    ]
    list_filter = ['active', 'treatment_no_match', TreatmentExistFilter]
    list_per_page = 100
    search_fields = [
        '=id',
        'name',
        'business__name',
        '=business__id',
        'business__owner__email',
        '=treatment__id',
        'treatment__name',
        '=resources__id',
        '=service_variants__resources__id',
    ]
    raw_id_fields = ['business', 'service_category']
    readonly_fields = [
        'business',
        'created',
        'updated',
        'deleted',
        'service_variants_list',
        'treatment_utt',
        'questions',
        'is_treatment_selected_by_user',
        'is_suggested',
        'is_imported',
    ]
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'business',
                    'created',
                    'updated',
                    'deleted',
                    'name',
                    'order',
                    'active',
                    'padding_type',
                    'padding_time',
                    'description',
                    'description_type',
                    'note',
                    'service_category',
                    'tax_rate',
                    'parallel_clients',
                    'gap_time',
                    'color',
                    'wordcloud',
                    'treatment_utt',
                    'treatment_no_match',
                    'is_treatment_selected_by_user',
                    'is_suggested',
                    'is_imported',
                    'import_uid',
                    'questions',
                    'is_available_for_customer_booking',
                    'is_online_service',
                    'service_code',
                    'service_variants_list',
                ),
            },
        ),
        (
            'Treatment',
            {
                'classes': ('collapse',),
                'fields': ('treatment',),
            },
        ),
    )
    inlines = (
        type('ServicePartnerAppDataInline', (PartnerAppDataInline,), {"model": ServiceMetadata}),
    )

    @staticmethod
    def service_variants_list(service):
        return format_html(
            ', '.join(
                [
                    # pylint: disable=consider-using-f-string
                    '<a href="%s">(%s) %sh%sm %s</a>'
                    % (
                        admin_link(variant),
                        variant.id,
                        escape(variant.duration.hours),
                        escape(variant.duration.minutes),
                        escape(variant.format_price()),
                    )
                    for variant in service.service_variants.all()
                ]
            )
        )

    def save_model(self, request, obj, form, change):
        if 'treatment' in form.changed_data:
            obj.is_treatment_selected_by_user = True
        super().save_model(request, obj, form, change)


class ServiceAddOnAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = [
        'id',
        'business_link',
        'name',
        'price',
        'price_type',
        'duration_display',
        'is_available_for_customer_booking',
        'max_allowed_quantity',
    ]
    list_filter = ['is_available_for_customer_booking']
    list_per_page = 100
    search_fields = [
        '=id',
        'name',
        'business__name',
        '=business__id',
        'business__owner__email',
        '=max_allowed_quantity',
    ]
    raw_id_fields = ['services']
    readonly_fields = ['business', 'created', 'updated', 'deleted']
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'created',
                    'updated',
                    'deleted',
                )
            },
        ),
        (
            'Service Add-on',
            {
                'fields': (
                    'business',
                    'name',
                    'price',
                    'price_type',
                    'duration',
                    'is_available_for_customer_booking',
                    'max_allowed_quantity',
                )
            },
        ),
        (
            'Services',
            {
                'classes': ('collapse',),
                'fields': ('services',),
            },
        ),
    )

    @admin.display(description='Duration')
    def duration_display(self, service_add_on):
        if not service_add_on.duration:
            return None
        return f'{service_add_on.duration.hours}h {service_add_on.duration.minutes}m'

    @admin.display(description='Business')
    def business_link(self, service_add_on):
        business = service_add_on.business
        return format_html('<a href="{}">{}</a>', admin_link(business), business)


class ServiceAddOnUseAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = [
        'id',
        'service_addon_link',
        'business_link',
        'price',
        'quantity',
        'services_ids',
    ]
    list_per_page = 100
    search_fields = [
        '=id',
        '=service_addon__id',
        'service_addon__name',
        'business__name',
        '=business__id',
        '=max_allowed_quantity',
        '=quantity',
    ]
    readonly_fields = [
        'business',
        'name',
        'subbooking',
        'service_addon',
        'created',
        'updated',
        'deleted',
    ]
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'created',
                    'updated',
                    'deleted',
                )
            },
        ),
        (
            'Service Add-on Use',
            {
                'fields': (
                    'service_addon',
                    'business',
                    'subbooking',
                    'name',
                    'price',
                    'quantity',
                    'services_ids',
                )
            },
        ),
    )

    @staticmethod
    def _link(obj, link_to):
        model = getattr(obj, link_to)
        if model is None:
            return None
        return format_html('<a href="{}">{}</a>', admin_link(model), model)

    @admin.display(description='Business')
    def business_link(self, service_addon_use):
        return self._link(service_addon_use, 'business')

    @admin.display(description='Service Add-on')
    def service_addon_link(self, service_addon_use):
        return self._link(service_addon_use, 'service_addon')


class ServiceVariantChangelogAdmin(
    NoAddDelMixin, ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = ServiceVariantChangelog
    search_fields = (
        '=service_variant__service__business_id',
        '=service_variant_id',
        '=requested_by_id',
    )
    list_display = ['id', 'date', 'service_variant', 'requested_by']
    list_per_page = 100
    fields = readonly_fields = [
        'id',
        'service_variant',
        'service_variant_version',
        'requested_by',
        'date',
        'data_display',
        'extra_data_display',
    ]
    date_hierarchy = 'created'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('requested_by').order_by('-created')

    @admin.display(description='Date (UTC)', ordering='-created')
    def date(self, obj):
        return obj.created.strftime(settings.DATETIME_FORMAT)

    @admin.display(description='Version')
    def service_variant_version(self, obj):
        return obj.data.get('version') if isinstance(obj.data, dict) else None

    @admin.display(description='Data')
    def data_display(self, obj) -> str:
        if not isinstance(obj.data, dict):
            return ''

        empty_value_display = self.get_empty_value_display()
        form = ServiceVariantAdminForm()
        model_fields = {field.name: field for field in ServiceVariant._meta.fields}
        skip_fields = {'version', 'id'}

        return format_html_join(
            '\n',
            '{}: <strong>{}</strong><br/>',
            (
                (
                    bound_field.label,
                    display_for_field(
                        obj.data.get(bound_field.name),
                        model_fields.get(bound_field.name),
                        empty_value_display,
                    ),
                )
                for bound_field in form
                if bound_field.name not in skip_fields
            ),
        )

    @admin.display(description='Extra data')
    def extra_data_display(self, obj) -> str:
        if not isinstance(obj.data, dict):
            return ''

        empty_value_display = self.get_empty_value_display()
        model_fields = {field.name for field in ServiceVariant._meta.fields}
        skip_fields = {'version', 'id', *model_fields}

        return format_html_join(
            '\n',
            '{}: <strong>{}</strong><br/>',
            (
                (
                    key,
                    display_for_value(
                        value,
                        empty_value_display,
                        boolean=isinstance(value, bool),
                    ),
                )
                for key, value in sorted(list(obj.data.items()))
                if key not in skip_fields
            ),
        )


admin.site.register(ServiceVariantChangelog, ServiceVariantChangelogAdmin)


class ServiceVariantChangelogInline(ReadOnlyTabular, ChangeLogAdminMixin, admin.TabularInline):
    model = ServiceVariantChangelog
    fields = readonly_fields = ['date', 'requested_by', 'diff_display']
    verbose_name = 'Changelog'
    verbose_name_plural = 'Changelog'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('requested_by').order_by('-created')

    @admin.display(description='Date (UTC)')
    def date(self, obj):
        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj),
            obj.created.strftime(settings.DATETIME_FORMAT),
        )


class ServiceVariantAdmin(NoAddDelMixin, ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['id', 'service', 'active', 'duration', 'type', 'price']
    list_filter = ['type', 'active']
    search_fields = (
        '=id',
        '=service__id',
        '=service__business__id',
        'service__business__name',
        'service__business__owner__email',
        'service__name',
    )
    list_per_page = 100
    raw_id_fields = ['service']
    readonly_fields = [
        'service',
        'created',
        'updated',
        'deleted',
        'previous_padding_time',
        'payment_amount',
        'payment_type',
        'last_change_by',
    ]

    form = ServiceVariantAdminForm
    inlines = [
        ServiceVariantChangelogInline,
    ]

    def change_view(self, request, object_id, form_url="", extra_context=None):
        try:
            version = int(request.GET.get('version'))
        except (TypeError, ValueError):
            version = None

        if version:
            changelog = ServiceVariantChangelog.objects.filter(
                service_variant_id=object_id,
                data__version=version,
            ).first()
        else:
            changelog = None

        if changelog:
            return HttpResponsePermanentRedirect(admin_link(changelog))

        return super().change_view(
            request, object_id=object_id, form_url=form_url, extra_context=extra_context
        )

    @staticmethod
    def payment_amount(obj):
        if hasattr(obj, 'payment') and obj.payment.deleted is None and obj.payment.payment_amount:
            amount = obj.payment.payment_amount
            if obj.payment.saving_type == RateType.PERCENTAGE.value:
                price = obj.payment.service_variant.price
                return f"{convert_to_percent(price, amount)}%"
            if obj.payment.saving_type == RateType.AMOUNT.value:
                return format_currency(amount)
            return str(amount)
        return ''

    @staticmethod
    def payment_type(obj):
        return (
            hasattr(obj, 'payment')
            and obj.payment.deleted is None
            and obj.payment.get_payment_type_display()
        ) or ''

    @staticmethod
    def last_change_by(obj: ServiceVariant) -> str:
        """
        Returns string representation of the user that has recently changed
        ServiceVariant.
        """
        recent_change: ServiceVariantChangelog = obj.changelogs.order_by('-created').first()
        return recent_change.requested_by if recent_change else '-'

    @staticmethod
    def previous_padding_time(obj):
        book = (
            SubBooking.objects.filter(
                service_variant_id=obj.id,
            )
            .annotate(
                pad_before=RawSQL(  # nosemgrep: avoid-raw-sql
                    """
                booking_subbooking.booked_from -
                lower(booking_subbooking.padded_booked_range)
            """,
                    (),
                )
            )
            .annotate(
                pad_after=RawSQL(  # nosemgrep: avoid-raw-sql
                    """
                upper(booking_subbooking.padded_booked_range) -
                booking_subbooking.booked_till
            """,
                    (),
                )
            )
            .values('pad_before', 'pad_after')
            .annotate(count=Count('*'))
        )

        return '\n'.join(
            [
                'Before: %d hour(s) %d minute(s) '  # pylint: disable=consider-using-f-string
                'After: %d hour(s) %d minute(s) - %d time(s)'
                % (
                    padding_variant['pad_before'].hours,
                    padding_variant['pad_before'].minutes,
                    padding_variant['pad_after'].hours,
                    padding_variant['pad_after'].minutes,
                    padding_variant['count'],
                )
                for padding_variant in book
            ]
        )


class ResourceHoursInLineLimiter(BaseInlineFormSet):
    def get_queryset(self):
        qs = super().get_queryset()
        return qs[:2]


class ResourceHoursInline(admin.TabularInline):
    verbose_name = 'Default Working Hours'
    verbose_name_plural = 'Default Working Hours'
    model = ResourceHours
    fk_name = 'resource'
    form = WeekHoursForm
    formset = ResourceHoursInLineLimiter
    exclude = ['created', 'updated', 'deleted']
    classes = ['collapse']
    ordering = ('-valid_from',)
    max_num = 2
    extra = 0


class ResourceAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    form = ResourceModelForm
    inlines = [
        ResourceHoursInline,
        type('ResourcePartnerAppDataInline', (PartnerAppDataInline,), {"model": ResourceMetadata}),
    ]
    list_display = [
        'id',
        'created',
        'deleted',
        'business',
        'name',
        'type',
        'staff_access_level',
        'visible',
        'active',
        'staff_user',
    ]
    list_per_page = 100
    search_fields = [
        '=id',
        'name',
        'business__name',
        '=business__id',
        '=business__owner__email',
        '=staff_email',
        '=staff_user__email',
    ]
    list_filter = ['type']
    raw_id_fields = ['business', 'services', 'photo', 'staff_user', 'service_variants']
    readonly_fields = [
        'business',
        'created',
        'updated',
        'deleted',
        'services_list',
    ]

    @staticmethod
    def services_list(resource):
        services = (
            (admin_link(service), service.id, escape(service.name))
            for service in resource.active_services
        )
        return format_html_join(', ', '<a href="{}">[{}] {}</a>', services)

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        if obj.type == Resource.STAFF:
            EcommercePermissionAdapter.update_store_permission(
                obj,
                EcommercePermissionAdapter.is_valid_store_access_level(obj.staff_access_level),
            )


class BCIConsentChangelogAdmin(
    NoAddDelMixin, ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = BCIConsentChangelog
    search_fields = (
        '=requested_by_id',
        '=business_customer_info__business_id',
        '=business_customer_info_id',
    )
    list_display = [
        'id',
        'date',
        'business_customer_info',
        'requested_by',
    ]
    verbose_name = 'BCI Consent Changelog'
    list_per_page = 100
    fields = readonly_fields = [
        'id',
        'business_customer_info',
        'requested_by',
        'date',
        'data_display',
    ]
    date_hierarchy = 'created'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('requested_by').order_by('-created')

    @admin.display(description='Date (UTC)', ordering='-created')
    def date(self, obj):
        return obj.created.strftime(settings.DATETIME_FORMAT)

    @admin.display(description='Data')
    def data_display(self, obj) -> str:
        if not isinstance(obj.data, dict):
            return ''

        empty_value_display = self.get_empty_value_display()
        form = BusinessCustomerInfoForm()
        display_fields = {
            'web_communication_agreement',
        }
        model_fields = {
            field.name: field for field in BusinessCustomerInfo._meta.fields if field.name
        }

        return format_html_join(
            '\n',
            '{}: <strong>{}</strong><br/>',
            (
                (
                    bound_field.label,
                    display_for_field(
                        obj.data.get(bound_field.name),
                        model_fields.get(bound_field.name),
                        empty_value_display,
                    ),
                )
                for bound_field in form
                if bound_field.name in display_fields
            ),
        )


class BCIVersumAgreementInline(NoAddDelMixin, admin.TabularInline):
    verbose_name = 'Versum agreement'
    verbose_name_plural = 'Versum agreements'
    model = BCIVersumAgreement
    classes = ['collapse']
    fields = (
        'agreement_type',
        'sms',
        'email',
    )
    readonly_fields = fields


class BusinessCustomerInfoAdmin(
    NoAddDelMixin,
    BusinessCustomerInfoHistoryAdmin,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    form = BusinessCustomerInfoForm
    inlines = (
        type(
            'BCIPartnerAppDataInline',
            (PartnerAppDataInline,),
            {"model": BusinessCustomerInfoMetadata},
        ),
        BCIVersumAgreementInline,
    )

    def get_queryset(self, request):
        queryset = (
            super()
            .get_queryset(request)
            .select_related('business', 'user')
            .only(
                'id',
                'business__id',
                'business__name',
                'business__owner__email',
                'user__id',
                'user__first_name',
                'user__last_name',
                'user__cell_phone',
                'user__work_phone',
                'user__email',
                'bookmarked',
                'blacklisted',
                'visible_in_business',
                'first_name',
                'last_name',
                'email',
                'cell_phone',
            )
        )
        return queryset

    def invite_bci(self, request, queryset):
        """

        :param request:
        :param queryset:
        :return:
        """
        bci_ids_by_business = defaultdict(list)
        bci_count = 0

        for bci_id, business_id, business_custom_data in queryset.values_list(
            'id', 'business_id', 'business__custom_data'
        ):
            if business_custom_data.get(CustomData.INVITE_CUSTOMERS, True):
                bci_ids_by_business[business_id].append(bci_id)
                bci_count += 1
            else:
                self.message_user(
                    request,
                    f'Business {business_id} do not wish customer invites',
                    messages.WARNING,
                )

        form = InviteCustomersAdminForm(request.POST)
        if not form.is_valid():
            self.message_user(
                request, 'Incorrect report email! No invitations sent.', messages.ERROR
            )
            return

        report_email = form.cleaned_data.get('report_email')
        for business_id, bci_ids in list(bci_ids_by_business.items()):
            invite_customers_to_booksy.delay(bci_ids, business_id, report_email)

        self.message_user(request, _(f'{bci_count} customers were invited to Booksy.'))

    invite_bci.short_description = _('Invite customers to Booksy.')
    change_list_template = 'admin/change_lists/change_list__bci.html'

    search_fields = (
        '=id',
        '=business__id',
        '=user__id',
        'user__email',
        'user__cell_phone',
        'user__first_name',
        'user__last_name',
        'email',
        'first_name',
        'last_name',
        'cell_phone',
    )
    list_filter = [
        'bookmarked',
        'blacklisted',
        'visible_in_business',
        'client_type',
    ]
    list_display = [
        'id',
        'business',
        'user',
        'bookmarked',
        'blacklisted',
        'visible_in_business',
        'first_name',
        'last_name',
        'email',
        'cell_phone',
    ]
    raw_id_fields = ('region', 'user', 'business', 'photo', 'first_appointment', 'type_data')
    exclude = [
        'service_questions',
    ]
    readonly_fields = [
        'business',
        'created',
        'bookmarked_resources',
        'first_appointment',
        'boost_client_card',
        'from_promo',
        'changes',
        'service_questions_json',
    ]
    actions = (invite_bci,)

    @staticmethod
    def from_promo(instance):
        return (
            BoostClientCard.objects.filter(client_card=instance)
            .values_list('from_promo', flat=True)
            .first()
        )

    from_promo.boolean = True

    def save_model(self, request, obj, form, change):
        operator = get_user_from_django_request(request)
        metadata = {'endpoint': 'BusinessCustomerInfoAdmin'}
        _history = {'operator': operator.id, 'metadata': metadata}
        if change and 'web_communication_agreement' in form.changed_data:
            BCIConsentChangelog.create_entry(operator, obj)
        obj.save(_history=_history)

    @staticmethod
    def service_questions_json(obj):
        if not obj.service_questions:
            return '-'

        return json.dumps(obj.service_questions.to_dict(), indent=4)

    service_questions_json.short_description = 'Service questions'


class BusinessFacebookPageAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    search_fields = [
        'id',
        '=business__id',
        'business__name',
        '=extra_businesses__id',
        'extra_businesses__name',
    ]
    list_display = ['id', 'business', 'additional_businesses']
    raw_id_fields = ['business', 'extra_businesses']

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        from django.db.models import Prefetch

        return qs.prefetch_related(
            Prefetch(
                'extra_businesses',
                queryset=Business.objects.all()
                .select_related(
                    'owner',
                )
                .only(
                    'name',
                    'owner__email',
                ),
            )
        )

    @staticmethod
    def additional_businesses(obj):
        return format_html('<br/>'.join([str(escape(biz)) for biz in obj.extra_businesses.all()]))


class BCITypeDataAdmin(BCITypeDataHistoryAdmin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = (
        'id',
        'card_type',
        'created',
        'updated',
        'deleted',
    )
    list_filter = ('card_type',)
    search_fields = ('=id',)
    readonly_fields = (
        'changes',
        'created',
        'updated',
        'deleted',
    )

    def save_model(self, request, obj, form, change):
        operator_id = get_user_from_django_request(request).id
        metadata = {'endpoint': 'BCITypeDataAdmin'}
        _history = {'operator': operator_id, 'metadata': metadata}
        obj.save(_history=_history)


class ServiceSuggestionAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    search_fields = ['=id', 'name', '=treatment__id']
    list_display = ['id', 'treatment', 'name', 'duration_display', 'price']
    form = ServiceSuggestionForm

    @staticmethod
    def duration_display(obj):
        if not obj.duration:
            return None
        return f'{obj.duration.hours}h {obj.duration.minutes}m'

    duration_display.short_description = 'Duration'
    duration_display.admin_order_field = 'duration'


class ServicesWordcloudsAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    search_fields = ['=id', 'name', '=country', 'category__name']
    list_filter = ['country']
    list_display = ['id', 'name', 'category', 'country', 'duration', 'price', 'order']


class BusinessPhotoInline(admin.TabularInline):
    """
    Inline for images in RentingVenue
    """

    verbose_name = 'Umbrella Venue Image'
    verbose_name_plural = 'Umbrella Venue Images'
    model = BusinessImage
    formset = BusinessPhotoInlineFormSet
    form = BusinessPhotoForm
    classes = ['collapse']

    def get_extra(self, request, obj=None, **kwargs):
        return 2 if obj and obj.images.count() == 0 else 0


class BusinessClaimSubdomainView(SingleObjectMixin, FormView):
    class SimpleSubdomainForm(forms.Form):
        business = forms.CharField(
            disabled=True, required=False, widget=forms.TextInput(attrs={'style': 'width: 500px;'})
        )
        subdomain = forms.CharField(
            max_length=SubdomainSerializer._declared_fields[  # pylint: disable=protected-access
                'subdomain'
            ].max_length
        )

    permission_required = 'business.change_business'
    form_class = SimpleSubdomainForm
    template_name = 'admin/custom_views/business_claim_subdomain.html'
    model = Business
    pk_url_kwarg = 'business_id'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.object = None

    @property
    def business(self):
        return self.object

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        return super().post(request, *args, **kwargs)

    def get_initial(self):
        initial = self.initial or {}
        initial['business'] = str(self.business)
        return initial

    def get_success_url(self):
        if self.business.status == Business.Status.VENUE:
            return reverse('admin:business_rentingvenue_change', args=(self.business.id,))
        return reverse('admin:business_business_change', args=(self.business.id,))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['cancel_url'] = self.get_success_url()
        return context

    def form_valid(self, form):
        try:
            SubdomainGRPC.claim(
                data={
                    'business_id': self.business.id,
                    'subdomain': form.cleaned_data['subdomain'],
                    'deeplinks': self.business.generate_subdomain_deeplinks(),
                }
            )
        except SubdomainGRPCValidationError as e:
            for field, errors in e.errors.items():
                for error in errors:
                    new_field = field if field in form.fields else None
                    form.add_error(new_field, error)
        except SubdomainGRPCServerError as e:
            logger.warning('Claim subdomain errors: %s', e.errors)
            for error in e.errors:
                form.add_error(None, error)
        else:
            messages.success(
                self.request,
                self.get_success_message(form.cleaned_data),
            )
            return redirect(self.get_success_url())

        return self.form_invalid(form)

    def get_success_message(self, cleaned_data):
        return f'''
            New subdomain "{cleaned_data['subdomain']}" has been successfully claimed for
            {self.business.name} business
        '''


class RentingVenueAdmin(
    BusinessChangeViewMixin,
    BusinessHistoryAdmin,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    verbose_name = 'Umbrella Venue'
    inlines = [
        BusinessHoursInline,
        BusinessPhotoInline,
    ]
    list_display = [
        'id',
        'name',
        'primary_category',
        'active',
        'visible',
        'links',
    ]
    list_filter = [
        'active',
        'visible',
        ('categories', CategoryListFilter),
    ]
    raw_id_fields = ['region']
    readonly_fields = [
        'go_to_booksy',
        'created',
        'updated',
        'deleted',
        'business_change',
        'integrations',
    ]
    form = RentingVenueForm
    change_form_template = 'admin/change_forms/change_form__renting_venue.html'

    list_per_page = 30
    search_fields = [
        '=id',
        'name',
    ]
    date_hierarchy = 'created'
    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('active', 'visible'),
                    ('created', 'updated', 'deleted'),
                )
            },
        ),
        (
            'Umbrella Venue',
            {
                'fields': (
                    'umbrella_brand_name',
                    'go_to_booksy',
                    'contractors',
                ),
            },
        ),
        (
            'Business Details',
            {
                'classes': ('collapse',),
                'fields': (
                    'name',
                    'official_name',
                    'name_short',
                    'manual_boost_score',
                    'description',
                    'region',
                    'address',
                    'address2',
                    'city',
                    'zipcode',
                    ('latitude', 'longitude'),
                    'position',
                    'phone',
                    'alert_phone',
                    ('website', 'facebook_link', 'instagram_link'),
                    (
                        'parking',
                        'wheelchair_access',
                        'pricing_level',
                    ),
                    'opening_hours_note',
                ),
            },
        ),
        (
            'Categories & Treatments',
            {
                'classes': ('collapse',),
                'fields': (
                    'primary_category',
                    'categories',
                    'treatments',
                ),
            },
        ),
        (
            'Internal Data',
            {
                'classes': ('collapse',),
                'fields': (
                    'integrations',
                    'custom_data',
                    'include_in_analysis',
                ),
            },
        ),
        (
            'Business history',
            {
                'classes': (
                    'collapse',
                    'wide',
                ),
                'fields': ('business_change',),
            },
        ),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._business_before_change = None

    # This method is defined on the ModelAdmin to prevent a TypeError for
    # users with read-only permissions.
    #
    # The GroupPermissionMixin makes all fields read-only for certain users.
    # When rendering a read-only field, Django first looks for a method with
    # that name on the ModelAdmin. If not found, it defaults to accessing the
    # attribute on the model instance itself.
    #
    # In this case, `obj.contractors` is a RelatedManager. The admin's default
    # display logic tries to call it (`obj.contractors()`), which fails with a
    # TypeError because the manager expects a keyword argument.
    #
    # By defining this method on the RentingVenueAdmin, we ensure Django uses
    # this display logic instead of falling back to the model's attribute,
    # thus resolving the error for read-only views.
    @staticmethod
    def contractors(obj):
        return BusinessAdmin.contractors(obj)

    @staticmethod
    def _get_subdomains(business_id):
        business = get_object_or_404(Business, id=business_id)
        return [business.subdomain_data] if business else []

    def change_view(self, request, object_id, form_url='', extra_context=None):
        # pylint: disable=no-value-for-parameter
        if isinstance(object_id, str):
            # sometimes admin send someting like 172337/images/ie-spacer.gif
            # in this variables
            object_id = object_id.strip('/').split('/')[0]
        extra_context = extra_context or {}
        extra_context['subdomains'] = self._get_subdomains(object_id)
        return super().change_view(request, object_id, form_url, extra_context=extra_context)

    def modify_actions(self, request, actions):
        if 'delete_selected' in actions:
            del actions['delete_selected']
        return actions

    @staticmethod
    def _perform_unclaim_all_subdomains(business):
        try:
            response = SubdomainGRPC.unclaim(data={"business_id": business.id})
        except SubdomainGRPCServerError as e:
            logging.error('Subdomain grpc error: %s', e.errors)
        else:
            logging.debug('Unclaim subdomains: %s', response['message'])
            business.clear_subdomain_cache()

    def unclaim_all_subdomains(self, request, venue_id):
        # pylint: disable=no-value-for-parameter disable=unused-variable
        venue = get_object_or_404(Business, id=venue_id)
        self._perform_unclaim_all_subdomains(venue)
        return redirect(reverse('admin:business_rentingvenue_change', args=(venue_id,)))

    # pylint: disable=arguments-differ
    def get_form(self, request, obj=None, **kwargs):
        if obj is not None:
            self._business_before_change = BusinessChange.extract_vars(obj)

        form = super().get_form(request, obj, **kwargs)
        if 'custom_data' in form.base_fields:
            form.base_fields['custom_data'].widget = BusinessCustomDataWidget(request)
        # remember request to for save
        # see save() in RentingVenueForm
        form.request = request
        return form

    def save_related(self, request, form, formsets, change):
        super().save_related(request, form, formsets, change)
        if self._business_before_change:
            BusinessChange.add(
                form.instance,
                form.instance,
                self._business_before_change,
                operator=get_user_from_django_request(request),
                metadata={'endpoint': 'BusinessAdmin'},
            )

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<venue_id>\d+)/unclaim_all_subdomains/$',
                self.admin_site.admin_view(self.unclaim_all_subdomains),
                name="business_rentingvenue_unclaim_all_subdomains",
            ),
            url(
                r'^(?P<business_id>\d+)/claim_new_subdomain/$',
                self.admin_site.admin_view(BusinessClaimSubdomainView.as_view()),
                name='business_claim_new_subdomain',
            ),
        ]
        return additional_urls + urls

    @staticmethod
    def links(obj):
        links = []
        if obj.active:
            links.append(f'<a href="{obj.get_seo_url(dummy=True)}" target="_blank">www</a>')
        return format_html('<br/>'.join(links))

    @staticmethod
    def go_to_booksy(obj):
        if not obj.id:
            return None
        return format_html(
            '<a href="{}" target="_blank">Go to booksy</a>', obj.get_seo_url(dummy=True)
        )

    @staticmethod
    def get_reindex_business(obj=None):
        if obj is None:
            return ''
        return f"""
            <a href="{reverse("admin:reindex_business", args=(obj.id,))}" class="btn ">
                Reindex Business
            </a>
        """

    def get_extra_business_actions(self, request, obj) -> dict:
        # pylint: disable=no-value-for-parameter
        return {
            'link_www': self.links(obj) if obj else '',
            'path': request.get_full_path(),
            'reindex_business': self.get_reindex_business(obj),
        }


class BCIPatientFileAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = BCIPatientFile

    list_display = [
        'id',
        'bci',
        'file_number',
        'national_identity_number',
        'document_type',
        'document_number',
    ]

    readonly_fields = [
        'id',
        'bci',
    ]

    fieldsets = (
        (
            None,
            {
                'fields': [
                    'id',
                    'bci',
                ],
            },
        ),
        (
            'Editable',
            {
                'fields': [
                    'file_number',
                    'gender',
                    'national_identity_number',
                    'document_type',
                    'document_number',
                    'branch',
                    'special_permissions_code',
                ]
            },
        ),
    )


class BListingPhotoInline(admin.TabularInline):
    verbose_name = 'Business Card Image'
    verbose_name_plural = 'Business Card Images'
    model = BusinessImage
    formset = BusinessPhotoInlineFormSet
    form = BusinessPhotoForm
    classes = ['collapse']

    def get_extra(self, request, obj=None, **kwargs):
        return 1 if obj and obj.images.count() == 0 else 0


class BListingAdmin(
    BusinessChangeViewMixin,
    BusinessHistoryAdmin,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    change_form_template = 'admin/change_forms/change_form__business.html'
    delete_confirmation_template = 'admin/change_lists/delete_confirmation__b_listing.html'
    delete_selected_confirmation_template = (
        'admin/change_lists/delete_selected_confirmation__b_listing.html'
    )
    verbose_name = 'B Listing'
    inlines = [
        BListingPhotoInline,
    ]
    list_display = [
        'id',
        'name',
        'primary_category',
        'active',
        'visible',
        'links',
    ]
    list_filter = [
        'active',
        'visible',
        ('categories', CategoryListFilter),
    ]
    raw_id_fields = ['region']
    BUSINESS_DETAILS_INDEX = 1
    readonly_fields = [
        'created',
        'updated',
        'deleted',
        'business_change',
        'source_link',
    ]
    form = BListingForm

    list_per_page = 30
    search_fields = [
        '=id',
        'name',
    ]
    date_hierarchy = 'created'
    fieldsets = (
        (
            None,
            {
                'fields': (
                    (
                        'created',
                        'updated',
                        'deleted',
                        'source_link',
                    ),
                )
            },
        ),
        (
            'Moderation',
            {
                'classes': (),
                'fields': (
                    'active',
                    'visible',
                    'verification',
                    'primary_category',
                    'categories',
                    'treatments',
                ),
            },
        ),
        (
            'Business Details',
            {
                'classes': ('collapse',),
                'fields': (
                    'name',
                    'description',
                    'website',
                    'address',
                    'address2',
                    'city',
                    'region',
                    ('latitude', 'longitude'),
                    'phone',
                    'source_email',
                ),
            },
        ),
        (
            'Internal Data',
            {
                'classes': ('collapse',),
                'fields': (
                    'custom_data',
                    'include_in_analysis',
                ),
            },
        ),
        (
            'Business history',
            {
                'classes': (
                    'collapse',
                    'wide',
                ),
                'fields': ('business_change',),
            },
        ),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._business_before_change = None

    @staticmethod
    def get_claim_b_listing(obj):
        if obj is None or not obj.active:
            return ''
        link = reverse("admin:claim_b_listing", args=(obj.id,))
        return f'<a href="{link}" class="btn ">Claim B Listing</a>'

    # pylint: disable=arguments-differ disable=no-value-for-parameter disable=method-hidden
    def get_form(self, request, obj=None, **kwargs):
        self.claim_b_listing = self.get_claim_b_listing(obj)

        form = super().get_form(request, obj, **kwargs)
        if 'custom_data' in form.base_fields:
            form.base_fields['custom_data'].widget = BListingCustomDataWidget(request)
        return form

    @staticmethod
    def claim_b_listing(request, b_listing_id):  # pylint disable=method-hidden
        instance = BListing.objects.get(id=b_listing_id)
        if instance.source_email:
            claim_b_listing(b_listing_id, instance.owner)
            messages.success(
                request,
                'B Listing has been claimed into Business',
            )
        else:
            messages.error(
                request,
                "Can't claim B Lisitng (which has no Business source) without source_email",
            )
            return redirect(
                reverse(
                    'admin:business_blisting_change',
                    args=(b_listing_id,),
                )
            )
        return redirect(
            reverse(
                'admin:business_blisting_changelist',
            )
        )

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<b_listing_id>\d+)/claim_b_listing/$',
                self.admin_site.admin_view(self.claim_b_listing),
                name='claim_b_listing',
            ),
        ]

        return additional_urls + urls

    @staticmethod
    def source_link(instance):
        source = instance.get_source()
        if not source:
            source = instance.external_source
        if source:
            if isinstance(source, Business):
                reverse_path = 'admin:business_business_change'
                return format_html(
                    '<a href="{0}">{1}</a>',
                    reverse(
                        reverse_path,
                        args=(source.id,),
                    ),
                    source,
                )

    source_link.short_description = "Source"

    @staticmethod
    def links(obj):
        links = []
        if obj.active:
            links.append(f'<a href="{obj.get_seo_url(dummy=True)}" target="_blank">www</a>')
        return format_html('<br/>'.join(links))

    def get_extra_business_actions(self, request, obj) -> dict:
        return {
            'path': request.get_full_path(),
            'claim_b_listing': self.get_claim_b_listing(obj),
        }


class ServicePromotionAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = ServicePromotion

    verbose_name = 'Service Promotion'

    search_fields = ('=business__id', 'business__name')
    list_filter = ['booking_source', 'active', 'type']
    list_display = [
        'id',
        'business',
        'type',
        'created',
        'active',
        'happy_hours_week_day',
        'last_minute_hours',
    ]
    raw_id_fields = ['business']

    readonly_fields = [
        'business',
        'happy_hours_week_day',
        'last_minute_hours',
        'promotion_duration',
        'promotion_end',
        'promotion_options',
        'promotion_start',
        'type',
        'created',
        'updated',
        'user_logs',
    ]

    exclude = ['booking_source']

    fieldsets = (
        (
            None,
            {
                'fields': (
                    'type',
                    'business',
                    'active',
                    'created',
                    'updated',
                    'happy_hours_week_day',
                    'last_minute_hours',
                    'promotion_duration',
                    'promotion_end',
                    'promotion_options',
                    'promotion_start',
                ),
            },
        ),
        ('Logs', {'fields': ('user_logs',)}),
    )

    @staticmethod
    def user_logs(obj):
        result = '<ul>'
        for when, log in sorted(obj.logs.items()):
            what = log.get('action', '').upper()
            user_id = log.get('user_id')
            who = User.objects.get(pk=user_id) if user_id else 'SYSTEM'

            result += f'<li>{when} - <b>{what}</b> by {who}</li>'

        result += '</ul>'
        return format_html(result)

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(
                request,
            )
            .select_related('business', 'booking_source')
        )


class InviteCustomersAdminView(FormView):
    form_class = InviteCustomersAdminForm
    success_url = 'admin:business_business_change'
    template_name = 'admin/custom_views/invite_customers.html'

    def get_context_data(self, **kwargs):
        business = get_object_or_404(Business, id=self.kwargs['business_id'])

        context = super().get_context_data(**kwargs)
        context['can_invite_customers'] = business.custom_data.get(
            CustomData.INVITE_CUSTOMERS, True
        )
        return context

    @staticmethod
    def get_success_message(cleaned_data):
        if 'report_email' in cleaned_data:
            message = f'Success! Report will be sent to {cleaned_data["report_email"]}'
        else:
            message = 'Success! No report was requested'
        return message

    def form_valid(self, form):
        bci_ids = list(
            BusinessCustomerInfo.objects.filter(
                business__id=self.kwargs['business_id'],
                user__isnull=True,
                visible_in_business=True,
            ).values_list('id', flat=True)
        )
        # pylint: disable=no-value-for-parameter
        invite_customers_to_booksy.delay(
            bci_ids, self.kwargs['business_id'], form.cleaned_data.get('report_email')
        )
        messages.success(self.request, self.get_success_message(form.cleaned_data))
        response = redirect(
            reverse(
                self.success_url,
                args=(self.kwargs['business_id'],),
            )
        )
        return response


class BusinessPromotionAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    readonly_fields = list_display = [
        'id',
        'business',
        'enabled_by',
        'disabled_by',
        'commission',
        'commission_id',
        'get_commission_percent',
        'type',
        'promotion_start',
        'promotion_end',
    ]
    search_fields = (
        '=id',
        '=business__id',
        '=commission__id',
        'enabled_by',
        'disabled_by',
        'type',
        'commission',
    )
    list_filter = ['enabled_by', 'disabled_by', 'type', BoostIsOnFilter]

    if not settings.BOOST.AUTO_SUSPENSION:
        list_filter.append(BoostStatusFilter)

    hide_keyword_field = True

    @staticmethod
    def get_commission_percent(obj):
        if not obj.commission:
            return '-'
        return obj.commission.commission

    get_commission_percent.short_description = 'Commission (%)'
    get_commission_percent.admin_order_field = 'commission__commission'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('commission')


class BusinessChangeTimezoneView(SuccessMessageMixin, UpdateView):
    permission_required = 'business.change_business'
    form_class = BusinessLocationForm
    template_name = 'admin/custom_views/business_timezone.html'
    success_url = 'admin:business_business_change'
    model = Business
    pk_url_kwarg = 'business_id'

    _request = None

    def get_success_url(self):
        return reverse(self.success_url, args=(self.object.id,))

    # pylint: disable=no-else-return
    def get_success_message(self, cleaned_data):
        if cleaned_data.get('shift_bookings', False):
            return (
                'Location saved. Bookings will be shifted in a while.'
                '\nAsk Merchant to log out and log in again.'
            )
        else:
            return 'Location saved' '\nAsk Merchant to log out and log in again.'

    def form_valid(self, form):
        business = self.get_object()
        operator = get_user_from_django_request(self._request)
        response = super().form_valid(form)

        BusinessChange.add(
            business,
            form.instance,
            BusinessChange.extract_vars(business),
            operator=operator,
            metadata={'endpoint': 'BusinessChangeTimezoneView'},
        )
        return response

    def post(self, request, *args, **kwargs):
        self._request = request
        return super().post(request, *args, **kwargs)


class BusinessChangeList(ChangeList):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related(
            'owner',
            'primary_category',
        ).only(
            'name',
            'active',
            'status',
            'trial_till',
            'integrations',
            'sms_notification_status',
            'owner__email',
            'primary_category__name',
            'primary_category__internal_name',
            'primary_category__parent',
        )
        return queryset


class AppsFlyerAdmin(NoChangeMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    search_fields = ('=business_id', '=booking_source_id')
    list_display = [
        'business_id',
        'booking_source_id',
        'appsflyer_device_id',
        'advertising_id',
        'appsflyer_user_id',
    ]


class ReTrialAttemptAdmin(NoChangeMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    form = ReTrialAttemptForm
    search_fields = ('=business__id', 'business__name')
    list_display = [
        'id',
        'business',
        'used',
        'switched_from_status',
        'operator',
        'duration',
        'created',
    ]
    readonly_fields = [
        'id',
        'used',
        'switched_from_status',
        'historical_sms_data',
        'operator',
        'duration',
        'created',
    ]
    raw_id_fields = ['business']
    list_select_related = True
    actions = None

    def save_model(self, request, obj, form, change):
        if obj.operator_id is None:
            obj.operator = get_user_from_django_request(request)
        super().save_model(request, obj, form, change)
        if obj.business.status == Business.Status.TRIAL_BLOCKED:
            analytics_business_re_trial_eligible_task.delay(
                context={"business_id": obj.business.id},
            )

    def has_delete_permission(self, request, obj=None):
        # can't delete already used attempt
        if obj and obj.used:
            return False
        return super().has_delete_permission(request, obj)

    def delete_model(self, request, obj):
        obj.soft_delete()
