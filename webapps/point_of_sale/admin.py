from django.contrib import admin
from django.utils.html import format_html

from lib.admin_helpers import (
    BaseModelAdmin,
    HistoryAdminMixin,
    NoAddDelMixin,
    ReadOnlyFieldsMixin,
    ReadOnlyTabular,
    admin_link,
    manual_admin_link,
    NoRowsInListViewMixin,
)
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.business.forms.fields import TableCreator
from webapps.point_of_sale.models import (
    BasketPaymentAnalytics,
    CancellationFeeAuthAnalytics,
    POS,
    Basket,
    BasketHistory,
    BasketItem,
    BasketPayment,
    BasketTip,
    CancellationFeeAuth,
    PaymentMethodVariant,
    POSPlan,
    RelatedBasketItem,
    CancellationFeeAuthChangelog,
    PlatformFeeSettings,
    PlatformFeePricingPlan,
)
from webapps.user.groups import GroupNameV2


class BasketTipInline(ReadOnlyTabular, admin.TabularInline):
    model = BasketTip

    readonly_fields = (
        'uuid',
        'created',
        'updated',
        'deleted',
        'rate',
        'type',
        'amount',
        'staffer_id',
        'source',
    )

    @staticmethod
    def uuid(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)


class BasketPaymentInline(ReadOnlyTabular, admin.StackedInline):
    model = BasketPayment

    readonly_fields = (
        'uuid',
        'created',
        'updated',
        'deleted',
        'amount',
        'payment_method',
        'auto_capture',
        'source',
        'payment_provider_code',
        'balance_transaction_id_url',
        'status',
        'user_id',
        'type',
        'parent_basket_payment_uuid',
        'error_code',
        'operator_id',
        'metadata',
        'action_required_details',
    )
    fields = readonly_fields

    @staticmethod
    def uuid(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)

    @staticmethod
    def parent_basket_payment_uuid(obj):
        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj.parent_basket_payment),
            obj.parent_basket_payment.id,
        )

    def balance_transaction_id_url(self, obj):
        if not obj.balance_transaction_id:
            return "-"
        url = manual_admin_link("payment_gateway", "balancetransaction", obj.balance_transaction_id)
        return format_html('<a href="{}" target="_blank">{}</a>', url, obj.balance_transaction_id)

    balance_transaction_id_url.short_description = 'Balance transaction id'


class BasketItemInline(ReadOnlyTabular, admin.TabularInline):
    model = BasketItem

    readonly_fields = (
        'uuid',
        'created',
        'updated',
        'deleted',
        'type',
        'order',
        'name_line_1',
        'name_line_2',
        'quantity',
        'item_price',
        'discount_rate',
        'discounted_item_price',
        'tax_amount',
        'tax_rate',
        'tax_type',
        'total',
        'net_total',
        'gross_total',
        'discounted_total',
        'net_total_wo_discount',
    )

    @staticmethod
    def uuid(obj):
        return format_html(TableCreator.get_id_link(obj))


# pylint: disable=duplicate-code
class BasketHistoryInline(HistoryAdminMixin, ReadOnlyTabular, admin.TabularInline):
    model = BasketHistory

    ordering = ('-created',)

    fields = (
        'uuid',
        'created',
        'pretty_diff',
    )

    readonly_fields = (
        'uuid',
        'created',
        'pretty_diff',
    )

    @staticmethod
    def uuid(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)


class BasketAdmin(ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = ('id', 'business_id', 'created', 'archived')
    search_fields = ['=id', '=business_id']
    list_filter = [
        'archived',
    ]

    inlines = [
        BasketItemInline,
        BasketTipInline,
        BasketPaymentInline,
        BasketHistoryInline,
    ]

    ordering = ('-created',)


admin.site.register(Basket, BasketAdmin)


class BasketPaymentAdmin(
    NoRowsInListViewMixin, NoAddDelMixin, ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'id',
        'basket',
        'amount',
        'payment_method',
        'payment_provider_code',
        'status',
        'source',
        'created',
    )
    search_fields = [
        '=id',
        '=basket__id',
        '=balance_transaction_id',
        'user_id',
    ]
    list_filter = [
        'payment_method',
        'payment_provider_code',
        'status',
        'source',
    ]
    ordering = ['-created']

    def has_add_permission(self, request, obj=None):
        return False


admin.site.register(BasketPayment, BasketPaymentAdmin)


class RelatedBasketItemInline(NoAddDelMixin, ReadOnlyFieldsMixin, admin.StackedInline):
    model = RelatedBasketItem


class BasketItemAdmin(
    NoRowsInListViewMixin,
    ReadOnlyFieldsMixin,
    ReadOnlyTabular,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'id',
        'basket',
        'created',
    )
    search_fields = ['=id', '=basket__id']

    inlines = [RelatedBasketItemInline]


admin.site.register(BasketItem, BasketItemAdmin)


class BasketTipAdmin(ReadOnlyFieldsMixin, ReadOnlyTabular, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'id',
        'basket',
        'business_id',
        'rate',
        'type',
        'amount',
        'source',
        'staffer_id',
        'created',
        'deleted',
    )
    search_fields = [
        '=id',
        '=basket__id',
    ]

    @staticmethod
    def business_id(obj):
        return obj.basket.business_id

    def get_queryset(self, request):
        """
        Return a QuerySet of all model instances that can be edited by the
        admin site. This is used by changelist_view.
        """
        return BasketTip.all_objects.all().order_by('-created')


admin.site.register(BasketTip, BasketTipAdmin)


class BasketHistoryAdmin(
    NoRowsInListViewMixin,
    HistoryAdminMixin,
    ReadOnlyFieldsMixin,
    ReadOnlyTabular,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    ordering = ('-created',)

    fields = (
        'created',
        'basket_id',
        'formatted_traceback',
        'formatted_data',
        'pretty_diff',
        'raw_diff',
    )

    list_display = (
        'created',
        'id',
        'basket_id',
    )


admin.site.register(BasketHistory, BasketHistoryAdmin)


class PaymentMethodVariantInline(NoAddDelMixin, admin.TabularInline):
    model = PaymentMethodVariant

    fields = (
        'payment_method_type',
        'payment_provider_code',
        'available',
        'enabled',
        'default',
    )


class POSAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = ('id', 'business_id')
    search_fields = ['=id', '=business_id']
    inlines = [PaymentMethodVariantInline]

    def has_add_permission(self, request):
        return False


admin.site.register(POS, POSAdmin)


class POSPlanAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = ('id', 'provider_code', 'payment_method_type', 'default')
    search_fields = ['=id', '=providers__pos__hash__id']
    list_filter = ['provider_code', 'payment_method_type', 'default']

    def has_add_permission(self, request):
        return False


admin.site.register(POSPlan, POSPlanAdmin)


class CancellationFeeAuthChangelogInline(ReadOnlyTabular, admin.TabularInline):
    model = CancellationFeeAuthChangelog

    readonly_fields = (
        'changelog_id',
        'operator_id',
        'old_status',
        'new_status',
        'change_type',
        'function_name',
        'created',
    )

    fields = readonly_fields
    ordering = ('-created',)
    extra = 0
    max_num = 10  # Limit to prevent performance issues

    def changelog_id(self, obj):
        """Display the changelog ID as a clickable link to the changelog detail page."""
        if obj.id:
            return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)
        return '-'

    changelog_id.short_description = 'Changelog ID'
    changelog_id.allow_tags = True

    def change_type(self, obj):
        """Display the change type from metadata."""
        return obj.metadata.get('change_type', '-')

    change_type.short_description = 'Change Type'

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .order_by('-created')
            .only(
                'id',
                'operator_id',
                'old_status',
                'new_status',
                'function_name',
                'created',
                'metadata',
            )
        )


class CancellationFeeAuthAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'created',
        'id',
        'business_id',
        'appointment_id',
        'payment_provider_code',
        'status',
        'basket_id',
        'balance_transaction_id',
    )

    search_fields = ['=id', '=business_id', '=appointment_id', '=basket_id']
    list_filter = ['status', 'payment_provider_code', 'created']
    ordering = ['-created']

    inlines = [CancellationFeeAuthChangelogInline]

    # Performance optimization
    def get_queryset(self, request):
        return (
            CancellationFeeAuth.objects.select_related('basket')  # Optimize basket relationship
            .prefetch_related(
                'changelog',  # Optimize changelog relationship
                'analytics',  # Optimize analytics relationship
            )
            .order_by('-created')
        )

    def has_add_permission(self, request):
        return False


admin.site.register(CancellationFeeAuth, CancellationFeeAuthAdmin)


class BasketPaymentAnalyticsAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'id',
        'basket_payment_id',
        'created',
        'trigger',
        'payment_link',
    )
    search_fields = ['=id', '=basket_payment_id']

    def has_add_permission(self, request):
        return False

    def get_queryset(self, request):
        return BasketPaymentAnalytics.objects.all().order_by('-created')


admin.site.register(BasketPaymentAnalytics, BasketPaymentAnalyticsAdmin)


class CancellationFeeAuthAnalyticsAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'id',
        'cf_auth_id',
        'created',
    )
    search_fields = ['=id', '=cf_auth_id']

    def has_add_permission(self, request):
        return False

    def get_queryset(self, request):
        return CancellationFeeAuthAnalytics.objects.all().order_by('-created')


admin.site.register(CancellationFeeAuthAnalytics, CancellationFeeAuthAnalyticsAdmin)


class CancellationFeeAuthChangelogAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    list_display = (
        'id',
        'cf_auth_id',
        'operator_id',
        'old_status',
        'new_status',
        'function_name',
        'created',
    )
    search_fields = ['=id', '=cf_auth_id', '=operator_id', 'function_name']
    list_filter = ['old_status', 'new_status', 'created']
    readonly_fields = [
        'id',
        'cf_auth',
        'operator_id',
        'old_status',
        'new_status',
        'function_name',
        'metadata',
        'data',
        'traceback',
        'created',
    ]

    def get_queryset(self, request):
        return CancellationFeeAuthChangelog.objects.select_related(
            'cf_auth'
        ).order_by(  # Optimize cf_auth relationship
            '-created'
        )


admin.site.register(CancellationFeeAuthChangelog, CancellationFeeAuthChangelogAdmin)


class PlatformFeeSettingsAdmin(BaseModelAdmin):
    list_display = ('id', 'business_id', 'fee_mode', 'created')
    search_fields = ['=id', '=business_id']
    list_filter = ['fee_mode']
    ordering = ['-created']


admin.site.register(PlatformFeeSettings, PlatformFeeSettingsAdmin)


class PlatformFeePricingPlanAdmin(BaseModelAdmin):
    list_display = ('id', 'scope', 'fee_mode', 'default', 'created')
    search_fields = ['=id', '=business_id']
    list_filter = ['fee_mode', 'default']
    ordering = ['-created']

    def scope(self, obj):
        return obj.business_id if obj.business_id is not None else 'Global'

    scope.short_description = 'Business'


admin.site.register(PlatformFeePricingPlan, PlatformFeePricingPlanAdmin)
