[Subscriptions][1] @booksy/teams/px_conversion
webapps/billing/
webapps/braintree_app/
webapps/stripe_app/
webapps/purchase/
!webapps/purchase/models/subscription_buyer.py
!webapps/purchase/tests/test_subscription_buyer.py
lib/apple
lib/business_consents
lib/facebook
lib/firebase
lib/google_oauth
lib/lifecycle_utils
lib/x_version_compatibility
lib/rate_limiter
service/metrics
webapps/metrics
webapps/b2b_referral
webapps/booksy_med
webapps/banners
service/billing
service/braintree_app
service/purchase


[Cx Booking][1] @booksy/teams/cx_booking
cliapps/booking/
service/booking/
webapps/booking/
webapps/appointment_drafts/
domain_services/booking/
drf_api/service/family_and_friends/
drf_api/service/user
service/customer/book_again.py
service/customer/timeslots.py
service/reviews
webapps/admin_extra/views/booking_staff.py
webapps/admin_extra/views/booking_remove.py
lib/dispatch_context
lib/es_history
lib/interval
lib/widgets
lib/tagmanager
webapps/family_and_friends
webapps/third_tier_wait_list
webapps/pubsub
webapps/pattern
webapps/reviews/

[Px Calendar][1] @booksy/teams/px_calendar
drf_api/service/business/calendar/
drf_api/service/firebase/
service/business/calendars.py
service/business/opening_hours.py
service/resources.py
service/schedule/
webapps/booking/calendar_importer/
webapps/business_calendar/
webapps/public_partners/
webapps/schedule/
webapps/staff_management/
webapps/staff_permission_overrides/
webapps/stats_and_reports/reports/other/staff_performance_report.py
webapps/stats_and_reports/reports/other/staff_revenue_payment_methods_report.py
webapps/stats_and_reports/reports/revenue/staff_revenue_forecast.py
webapps/stats_and_reports/reports/staff
webapps/turntracker/
webapps/wait_list/
lib/history_model
drf_api/service/partner_app/
lib/tasks

[Finance Automation][1] @booksy/teams/finance_automation
drf_api/service/navision/
webapps/admin_extra/tasks/subscription_buyer_tools.py
webapps/admin_extra/tasks/dry_booksy_billing_invoices_report.py
webapps/admin_extra/tasks/dry_offline_invoicing.py
webapps/admin_extra/views/navision/
webapps/admin_extra
webapps/admin_query_fields
webapps/database_publications
webapps/donation
webapps/navision/
webapps/purchase/models/subscription_buyer.py
webapps/purchase/tests/test_subscription_buyer.py
webapps/saml_auth/
webapps/kill_switch
service/invoicing
webapps/invoice
webapps/invoicing


^[Px Calendar Optional] @booksy/teams/px_calendar
# Resource model
webapps/business/models/models.py


[Px Engagement] @booksy/teams/px-engagement
# Dirs
booksy/pubsub/loyalty_program
booksy_pytest_doctest
drf_api/service/splash/
drf_api/service/whats_new/
lib/french_certification
lib/lokalise
lib/redis_client
lib/swagger
protos/booksy/pubsub/loyalty_program
service/base
service/consents
service/invoicing
service/printer_api
service/statics
service/warehouse
webapps/business_consents
webapps/consents
webapps/french_certification
webapps/invoicing
webapps/printer_api
webapps/reports
webapps/statistics
webapps/stats_and_reports
webapps/suit
webapps/warehouse
webapps/whats_new
# Files
.doctestignore
ci_jobs/check_toplevel_ownership.sh
templates/booksy/pdf/french_certification_fiscal_receipt.html
webapps/notification/management/commands/notification_render_email.py
webapps/pubsub/message.py
webapps/pubsub/subscriber.py
webapps/pubsub/tests/test_pubsub_subscribing.py
webapps/pubsub/tests/utils.py
webapps/script_runner/scripts/script__fix_order_of_jets_and_fiscalreceipts__fr.py
webapps/script_runner/scripts/script_bump_software_version__fr.py
webapps/script_runner/tests_scripts/test_script__fix_order_of_jets_and_fiscalreceipts__fr.py
webapps/segment/facade.py
webapps/segment/segment_events.py


[Cx Search][1] @booksy/teams/cx-search
# Boost functionality
cliapps/boost/
service/boost/
webapps/boost/
webapps/marketplace/
settings/boost.py

# Elastisearch
cliapps/es/
lib/elasticsearch/
lib/searchables/
webapps/elasticsearch/
settings/es_countries
settings/synonyms
settings/elasticsearch.py

# Search
service/search/
lib/gcs_dataset
lib/search_service/
webapps/search_engine_tuning/
webapps/business/business_categories
webapps/business/elasticsearch
webapps/business/searchables/

# Geocoding
lib/geocoding
lib/geocoding_v2
service/other/geocoding.py

# Visibility promotion
webapps/visibility_promotion/

# Others
service/marketplace
webapps/structure/
webapps/visibility_promotion

# Feature flags infrastructure (Eppo & LaunchDarkly)
lib/feature_flag/
!lib/feature_flag/feature/
!lib/feature_flag/experiment/
!lib/feature_flag/bug.py
!lib/feature_flag/killswitch.py
!lib/feature_flag/old_experiment.py


[Backend Quality Guild] @booksy/teams/backend-quality-guild
.importlinter
v2/shared/hexagonal/
lib/contrib # to be removed
lib/sensi # to be removed
lib/datadog
lib/fields
lib/celery_utils
lib/probes

[Code Section][1] @booksy/teams/px_marketing
drf_api/service/unsubscribe/
drf_api/service/migrator/
lib/deeplink
lib/gcs
service/b2b_referral/
service/business/business_customer_info_thin.py
service/business/customer_info.py
service/business/invite_again.py
service/business/service_promotions.py
service/business/feature_status
service/partners/
service/df_creator/
service/message_blast/
statics/digital_flyer/
service/best_of_booksy
service/segment
service/marketing
webapps/b2b_acc_deletion/
webapps/best_of_booksy/
webapps/business_customer_info/
webapps/consents/
webapps/df_creator/
webapps/feeds/
webapps/google_business_profile/
webapps/instagram_integration/
webapps/message_blast/
webapps/qr_code/
webapps/qr_code_origami/
webapps/subdomain_grpc/
webapps/segment/
webapps/partners/
webapps/marketing/
lib/contrib
lib/segment_analytics


[Payment Nexus][1] @booksy/teams/payment-nexus
webapps/booksy_pay/
drf_api/service/booksy_pay/
drf_api/service/attention_getters/
webapps/urlinator
webapps/register
webapps/r_and_d
webapps/partners
lib/french_certification
service/mixins
service/other
service/tests
webapps/celery
webapps/premium_services
service/auto_enable_ba_deposit
service/auto_enable_fast_payouts

[Px Onboarding][1] @booksy/teams/provider-onboarding
drf_api/service/business
drf_api/service/facebook
drf_api/service/help_center
drf_api/service/images
drf_api/service/zowie_integration
service/business/account.py
service/business/account_deletion.py
service/business/agreements.py
service/business/create.py
service/business/customer_info.py
service/business/hcaptcha.py
service/business/invite_again.py
service/business/opening_hours.py
service/business/renting_venue.py
service/images
service/intro_screen
service/profile_completeness
service/renting_venue
service/search/v2
service/siwa
service/facebook.py
webapps/business
webapps/business/v2
webapps/business_customer_info/v2
webapps/google_places
webapps/google_sign_in
webapps/help_center
webapps/hints
webapps/images
webapps/onboarding_space
webapps/photo
webapps/profile_completeness
webapps/profile_setup
webapps/qr_code
webapps/qr_code_origami
webapps/survey
webapps/text_invite
webapps/user
webapps/intro_screen
service/business


[Cx Onboarding][1] @booksy/teams/cx_onboarding
drf_api/service/apple
drf_api/service/auth
drf_api/service/customer
drf_api/service/facebook
drf_api/service/my_booksy
drf_api/service/notification
drf_api/service/other/content_reports
drf_api/service/tests
drf_api/service/utils
drf_api/service/utils/views.py
lib/booksy_sms
lib/email
lib/email/tasks.py
service/account.py
service/booking/customer_booking.py
service/booksy_auth
service/c2b_referral
service/customer
service/facebook.py
service/notification
service/other/push.py
service/other/utils.py
service/siwa
webapps/admin_extra/tasks/push_and_notification.py
webapps/admin_extra/tasks/reports.py
webapps/admin_extra/views/password.py
webapps/admin_extra/views/password_reset.py
webapps/allauth_google
webapps/c2b_referral
webapps/google_sign_in
webapps/message_blast/models.py
webapps/message_blast/serializers.py
webapps/message_blast/views.py
webapps/notification
webapps/pop_up_notification
webapps/registrationcode
webapps/session
webapps/user

[Payment Rails][1] @booksy/teams/payment_rails
drf_api/service/stripe_integration
drf_api/service/stripe_sdi
lib/inapp_notifications
lib/payment_gateway
lib/payment_providers
lib/payments
lib/point_of_sale
lib/pos
service/pos
service/stripe_integration
service/market_pay
webapps/adyen
webapps/otp
webapps/partners
webapps/stripe_integration
webapps/stripe_terminal
webapps/payment_gateway
webapps/payment_providers
webapps/payments
webapps/point_of_sale
webapps/pos
webapps/market_pay

[Payment Solutions][1] @booksy/teams/payment-solutions
lib/business
lib/logging
lib/protobuf
lib/pylint_plugins
webapps/commission
webapps/contact_us
webapps/zoom
webapps/utt
webapps/no_show_protection
drf_api/service/booksy_storage


[Payment New Financial Services][1] @booksy/teams/payment_new_financial_services
drf_api/service/other
lib/kafka_events
lib/monkeypatching
lib/tests
lib/test_utils
service/business_related
service/experiment_v3
service/sequencing_number
service/voucher
webapps/booksy_gift_cards
webapps/business_related
webapps/experiment
webapps/experiment_v3
webapps/feedback
webapps/script_runner
webapps/sequencing_number
webapps/voucher
service/management

[Enterprise][1] @booksy/teams/security
webapps/security_blacklist

[KV-Maintenance][1] @booksy/teams/kv_maintenance
drf_api/service/versum_migration
lib/sensi
webapps/versum_migration

[E-commerce][1] @booksy/teams/ecommerce
service/warehouse
webapps/ecommerce
webapps/warehouse
