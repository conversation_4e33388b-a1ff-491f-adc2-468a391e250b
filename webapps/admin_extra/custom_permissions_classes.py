"""
overrides default django classes to corresponding ones inheriting from
PermissionRequiredMixin
"""

from dataclasses import dataclass
from typing import ClassVar, Iterable

from django.conf import settings
from django.contrib.auth.mixins import AccessMixin, PermissionRequiredMixin
from django.core.exceptions import ImproperlyConfigured
from django.views import generic

from lib.cache import lru_booksy_cache
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.admin import UseNewAdminGroupPermissions
from webapps.user.groups import GroupName, GroupNameV2


def use_new_permissions(user):
    """
    Checks if the new group permission model is enabled via Eppo flag.

    When removing the feature flag, please check all usages of this method.
    """
    user_key = str(user.email) if user and user.is_authenticated else 'anonymous'
    return UseNewAdminGroupPermissions(user=UserData(subject_key=user_key))


class View(PermissionRequiredMixin, generic.View):
    def visible(self):
        return self.has_permission()


class FormView(PermissionRequiredMixin, generic.edit.FormView):
    def visible(self):
        return self.has_permission()


class TemplateView(PermissionRequiredMixin, generic.TemplateView):
    def visible(self):
        return self.has_permission()


@dataclass
class PermissionGroups:
    full_access: list[GroupNameV2]
    read_only: list[GroupNameV2]
    legacy_full_access: list[GroupName]


class BaseIsInGroupMixin:
    # if there is more than one group, checks if you belong to at least one
    full_access_groups: ClassVar[Iterable[GroupName | GroupNameV2]]
    read_only_access_groups: ClassVar[Iterable[GroupNameV2]]

    @staticmethod
    @lru_booksy_cache(timeout=settings.USER_ADMIN_GROUPS_CACHE_TIMEOUT)
    def _get_user_group_names(user) -> set[str]:
        return set(user.groups.values_list('name', flat=True))

    @classmethod
    def get_required_groups(cls) -> PermissionGroups:
        full_access_groups = getattr(cls, 'full_access_groups', None)
        read_only_access_groups = getattr(cls, 'read_only_access_groups', None)

        if full_access_groups is None and read_only_access_groups is None:
            raise ImproperlyConfigured(
                f'{cls.__name__} is missing the groups attribute.'
                f' Define {cls.__name__}.full_access_groups or '
                f'{cls.__name__}.read_only_access_groups or override '
                f'{cls.__name__}.get_required_groups().'
            )

        full_access = [
            group for group in full_access_groups or [] if isinstance(group, GroupNameV2)
        ]
        legacy_full_access = [
            group for group in full_access_groups or [] if isinstance(group, GroupName)
        ]
        read_only_access = [
            group for group in read_only_access_groups or [] if isinstance(group, GroupNameV2)
        ]
        return PermissionGroups(
            full_access=full_access,
            read_only=read_only_access,
            legacy_full_access=legacy_full_access,
        )

    @classmethod
    def is_user_in_full_access_groups(cls, user) -> bool | None:
        """
        Checks if a user is in a group with full access.
        Returns:
            - True: if user has access.
            - False: if user does not have access.
            - None: if group-based access is not defined, should fallback to legacy permissions.
        """
        if user.is_superuser:
            return True

        groups = cls.get_required_groups()
        user_group_names = cls._get_user_group_names(user)

        if use_new_permissions(user=user):
            # With FF on, group check is always active.
            # An empty list of groups means no one has access.
            required_groups = {g.value for g in groups.full_access}
            return not required_groups.isdisjoint(user_group_names)

        # With FF off, we only check legacy groups if they are defined.
        if not groups.legacy_full_access:
            return None  # Fallback to super()

        required_legacy_groups = {g.value for g in groups.legacy_full_access}
        return not required_legacy_groups.isdisjoint(user_group_names)

    @classmethod
    def is_user_in_read_only_access_groups(cls, user) -> bool | None:
        """
        Checks if a user is in a group with read-only access.
        Returns:
            - True: if user has access.
            - False: if user does not have access.
            - None: if group-based access is not defined, should fallback to legacy permissions.
        """
        if user.is_superuser:
            return True

        if not use_new_permissions(user=user):
            return None

        groups = cls.get_required_groups()
        user_group_names = cls._get_user_group_names(user)
        # With FF on, group check is always active. An empty list of groups means no one has access.
        required_groups = {g.value for g in groups.read_only}
        return not required_groups.isdisjoint(user_group_names)


class GroupPermissionMixin(BaseIsInGroupMixin, AccessMixin):
    """View mixin that restricts access based on user's groups."""

    def has_view_permission(self, request, obj=None):  # pylint: disable=unused-argument
        has_full_access = self.is_user_in_full_access_groups(request.user)
        has_read_only_access = self.is_user_in_read_only_access_groups(request.user)

        if has_full_access is None and has_read_only_access is None:
            return super().has_view_permission(request, obj=obj)

        return bool(has_full_access) or bool(has_read_only_access)

    def has_change_permission(self, request, obj=None):  # pylint: disable=unused-argument
        has_access = self.is_user_in_full_access_groups(request.user)
        if has_access is None:
            return super().has_change_permission(request, obj=obj)
        return has_access

    def has_add_permission(self, request):
        has_access = self.is_user_in_full_access_groups(request.user)
        if has_access is None:
            return super().has_add_permission(request)
        return has_access

    def has_delete_permission(self, request, obj=None):  # pylint: disable=unused-argument
        has_access = self.is_user_in_full_access_groups(request.user)
        if has_access is None:
            return super().has_delete_permission(request, obj=obj)
        return has_access

    def get_actions(self, request):
        if use_new_permissions(request.user) and not self.is_user_in_full_access_groups(
            request.user
        ):
            return {}
        actions = super().get_actions(request)
        return self.modify_actions(request, actions)

    def modify_actions(self, request, actions):
        """Hook for subclasses to modify actions."""
        return actions

    def dispatch(self, request, *args, **kwargs):
        if self.is_user_in_full_access_groups(request.user) is False:
            return self.handle_no_permission()
        return super().dispatch(request, *args, **kwargs)


class LegacyPermissionBypassMixin:
    """
    Overrides get_permission_required to bypass legacy permission checks
    when the UseNewAdminGroupPermissions feature flag is active for the user.
    This should be placed before other permission-related mixins or views
    in the MRO.
    """

    def get_permission_required(self):
        if use_new_permissions(self.request.user):
            return ()
        return super().get_permission_required()
