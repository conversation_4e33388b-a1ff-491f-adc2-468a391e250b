from django.conf import settings
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin
from webapps.billing.interfaces import boost
from webapps.billing.serializers.boost.overdue import (
    BoostOverdueDetailsSerializer,
    BoostOverdueTotalInfoSerializer,
)
from webapps.billing.views.boost.utils import get_boost_overdue_info
from webapps.business.models import Resource


class BoostOverdueTotalInfoView(
    BusinessViewValidatorMixin,
    BaseBooksySessionGenericAPIView,
):
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = BoostOverdueTotalInfoSerializer

    def get(self, *args, **kwargs):
        business = self.get_business(has_new_billing=True)
        business_boost_info, overdue_info = get_boost_overdue_info(business_id=business.id)

        serializer = self.get_serializer(
            {
                'total_amount': overdue_info.gross_amount,
                'currency': overdue_info.currency,
                'business_boost_info': business_boost_info.to_dict(),
            }
        )

        return Response(
            serializer.data,
            status=status.HTTP_200_OK,
        )


class BoostOverdueAppointmentsView(
    BusinessViewValidatorMixin,
    BaseBooksySessionGenericAPIView,
):
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = BoostOverdueDetailsSerializer

    def get(self, *args, **kwargs):
        business = self.get_business(has_new_billing=True)

        appointments = []
        total_amount = 0
        currency = settings.CURRENCY_CODE

        transactions = boost.overdue_transactions(business_id=business.id)

        for transaction in transactions:
            appointments += (
                [
                    {
                        'amount': appointment.gross_amount,
                        'client_name': appointment.client,
                        'date': appointment.date.date(),
                    }
                    for appointment in transaction.appointments
                ]
                if transaction.appointments
                else []
            )
            total_amount += transaction.gross_amount
            currency = transaction.currency

        serializer = self.get_serializer(
            {
                'total_amount': total_amount,
                'currency': currency,
                'appointments': appointments,
            }
        )

        return Response(
            serializer.data,
            status=status.HTTP_200_OK,
        )
