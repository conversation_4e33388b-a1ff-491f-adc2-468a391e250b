from domain_services.booking.src.domains import Business
from domain_services.booking.src.domains.analytics.dto import (
    MarketingAnalyticsParams,
    ProductAnalyticsParams,
)
from domain_services.booking.src.domains.appointment.dto.draft import Draft
from domain_services.booking.src.domains.business.dto import BusinessCustomerInfo
from domain_services.booking.src.domains.user.dto import User


class MarketingAnalyticsParamsFactory:
    @classmethod
    def create(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls,
        product_analytics_params: ProductAnalyticsParams,
        user: User | None,
        business: Business | None,
        geo_data: dict,
        draft: Draft | None,
        business_customer_info: BusinessCustomerInfo | None,
    ) -> MarketingAnalyticsParams:
        context = {}
        properties = {
            'last_dropped': {
                'booking_source': product_analytics_params.booking_source,
            },
            'business_primary_category': product_analytics_params.business_primary_category,
        }
        cls._add_user_parameters(user, context, properties)
        cls._add_business_parameters(business, properties)
        cls._add_geo_data_parameters(geo_data, properties)
        cls._add_draft_data_parameters(draft, properties)
        cls._add_bci_data_parameters(business_customer_info, properties)
        return MarketingAnalyticsParams(
            context=context,
            properties=properties,
            integrations={},
            traits={},
        )

    @staticmethod
    def _add_user_parameters(user: User | None, context: dict, properties: dict):
        if not user:
            return
        context['session_user_id'] = user.id
        context['locale'] = user.locale
        properties['first_name'] = user.first_name
        properties['last_name'] = user.last_name
        properties['gender_code'] = user.gender

    @staticmethod
    def _add_business_parameters(business: Business, properties: dict):
        properties['last_dropped']['business_deeplink'] = business.deeplink
        properties['last_dropped']['business_name'] = business.name
        properties['city'] = business.city
        properties['state'] = business.state
        properties['zip_code'] = business.zip_code

    @staticmethod
    def _add_geo_data_parameters(geo_data: dict, properties: dict):
        properties['urban_area'] = geo_data['urban_area']
        properties['urban_subarea'] = geo_data['urban_subarea']

    @staticmethod
    def _add_draft_data_parameters(draft: Draft, properties: dict):
        properties['last_dropped']['service_name'] = [item.service.name for item in draft.items]

    @staticmethod
    def _add_bci_data_parameters(bci: BusinessCustomerInfo, properties: dict):
        properties['last_dropped']['client_from_invite'] = bci.invited if bci else False
