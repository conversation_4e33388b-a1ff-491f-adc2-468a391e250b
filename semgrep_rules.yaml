---
rules:
  - id: globals-misuse-code-execution
    message:
      Found request data as an index to 'globals()'. This is extremely dangerous
      because it allows an attacker to execute arbitrary code on the system. Refactor
      your code not to use 'globals()'.
    metadata:
      cwe:
        "CWE-96: Improper Neutralization of Directives in Statically Saved Code ('Static
        Code Injection')"
      owasp: "A1: Injection"
      references:
        - https://github.com/mpirnat/lets-be-bad-guys/blob/d92768fb3ade32956abd53bd6bb06e19d634a084/badguys/vulnerable/views.py#L181-L186
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    patterns:
      - pattern-inside: |
          def $FUNC(...):
            ...
      - pattern-either:
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals().get($DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals().get("..." % $DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals().get(f"...{$DATA}...", ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals().get("...".format(..., $DATA, ...), ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals()[$DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals()["..." % $DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals()[f"...{$DATA}..."]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = globals()["...".format(..., $DATA, ...)]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals().get($DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals().get("..." % $DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals().get(f"...{$DATA}...", ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals().get("...".format(..., $DATA, ...), ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals()[$DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals()["..." % $DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals()[f"...{$DATA}..."]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = globals()["...".format(..., $DATA, ...)]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals().get($DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals().get("..." % $DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals().get(f"...{$DATA}...", ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals().get("...".format(..., $DATA, ...), ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals()[$DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals()["..." % $DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals()[f"...{$DATA}..."]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = globals()["...".format(..., $DATA, ...)]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals().get($DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals().get("..." % $DATA, ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals().get(f"...{$DATA}...", ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals().get("...".format(..., $DATA, ...), ...)
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals()[$DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals()["..." % $DATA]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals()[f"...{$DATA}..."]
              ...
              $INTERM(...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = globals()["...".format(..., $DATA, ...)]
              ...
              $INTERM(...)
  - id: ruby-jwt-exposed-credentials
    languages:
      - ruby
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      references:
        - https://cwe.mitre.org/data/definitions/522.html
      category: security
      technology:
        - jwt
    message:
      Password is exposed through JWT token payload. This is not encrypted and
      the password could be compromised. Do not store passwords in JWT tokens.
    patterns:
      - pattern-inside: |
          require 'jwt'
          ...
      - pattern: |
          $PAYLOAD = {...,password:...,...}
          ...
          JWT.encode($PAYLOAD,...)
    severity: ERROR
  - id: return-in-init
    patterns:
      - pattern-inside: |
          class $A(...):
              ...
      - pattern-inside: |
          def __init__(...):
              ...
      - pattern-not-inside: |
          def __init__(...):
              ...
              def $F(...):
                  ...
      - patterns:
          - pattern: return ...
          - pattern-not: return
          - pattern-not: return None
    message:
      "`return` should never appear inside a class __init__ function. This will
      cause a runtime error."
    languages:
      - python
    severity: ERROR
    metadata:
      category: correctness
      technology:
        - python
  - id: jwt-none-alg
    message:
      Detected use of the 'none' algorithm in a JWT token. The 'none' algorithm
      assumes the integrity of the token has already been verified. This would allow
      a malicious actor to forge a JWT token that will automatically be verified. Do
      not explicitly use the 'none' algorithm. Instead, use an algorithm such as 'HS256'.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.3 Insecue Stateless Session Tokens
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      category: security
      technology:
        - jwt
    languages:
      - javascript
      - typescript
    severity: ERROR
    patterns:
      - pattern-inside: |
          $JWT = require("jsonwebtoken");
          ...
      - pattern: "$JWT.verify($P, $X, {algorithms:[...,'none',...]},...)"
  - id: jruby-xml
    patterns:
      - pattern: "XmlMini.backend = $STR

          "
      - pattern-not: 'XmlMini.backend = "REXML"

          '
    message:
      The JDOM backend for XmlMini has a vulnerability that lets an attacker
      perform a denial of service attack or gain access to files on the application
      server. This affects versions 3.0, but is fixed in versions 3.1.12 and 3.2.13.
      To fix, either upgrade or use XmlMini.backend="REXML".
    metadata:
      references:
        - https://github.com/presidentbeef/brakeman/blob/main/lib/brakeman/checks/check_jruby_xml.rb
      category: security
      technology:
        - ruby
    languages:
      - ruby
    severity: WARNING
  - id: use-of-md5
    message:
      Detected MD5 hash algorithm which is considered insecure. MD5 is not collision
      resistant and is therefore not suitable as a cryptographic signature. Use SHA256
      or SHA3 instead.
    languages:
      - go
    severity: WARNING
    metadata:
      owasp: "A9: Using Components with Known Vulnerabilities"
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      source-rule-url: https://github.com/securego/gosec#available-rules
      category: security
      technology:
        - go
      confidence: MEDIUM
    pattern-either:
      - pattern: "md5.New()

          "
      - pattern: "md5.Sum(...)

          "
  - id: insecure-hash-algorithm-md2
    message:
      Detected MD2 hash algorithm which is considered insecure. This algorithm
      has many known vulnerabilities and has been deprecated. Use SHA256 or SHA3 instead.
    metadata:
      source-rule-url: https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      references:
        - https://tools.ietf.org/html/rfc6149
        - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2009-2409
        - https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html
      category: security
      technology:
        - pycryptodome
    severity: WARNING
    languages:
      - python
    pattern-either:
      - pattern: Crypto.Hash.MD2.new(...)
      - pattern: Cryptodome.Hash.MD2.new (...)
  - id: no-null-cipher
    pattern: new NullCipher(...);
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#NULL_CIPHER
      asvs:
        section: V6 Stored Cryptography Verification Requirements
        control_id: 6.2.5 Insecure Algorithm
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms
        version: "4"
      category: security
      technology:
        - java
    message:
      'NullCipher was detected. This will not encrypt anything; the cipher text
      will be the same as the plain text. Use a valid, secure cipher: Cipher.getInstance("AES/CBC/PKCS7PADDING").
      See https://owasp.org/www-community/Using_the_Java_Cryptographic_Extensions for
      more information.'
    severity: WARNING
    languages:
      - java
  - id: force-ssl-false
    message:
      Checks for configuration setting of force_ssl to false. Force_ssl forces
      usage of HTTPS, which could lead to network interception of unencrypted application
      traffic. To fix, set config.force_ssl = true.
    metadata:
      references:
        - https://github.com/presidentbeef/brakeman/blob/main/lib/brakeman/checks/check_force_ssl.rb
      category: security
      technology:
        - ruby
    languages:
      - ruby
    severity: WARNING
    pattern: config.force_ssl = false
  - id: list-modify-while-iterate
    message:
      It appears that `$LIST` is a list that is being modified while in a for
      loop. This will likely cause a runtime error or an infinite loop.
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: |
          for $ELEMENT in $LIST:
            ...
            $LIST.pop(...)
      - pattern: |
          for $ELEMENT in $LIST:
            ...
            $LIST.push(...)
      - pattern: |
          for $ELEMENT in $LIST:
            ...
            $LIST.append(...)
      - pattern: |
          for $ELEMENT in $LIST:
            ...
            $LIST.extend(...)
    metadata:
      category: correctness
      technology:
        - python
  - id: invalid-port
    message: Detected an invalid port number. Valid ports are 0 through 65535.
    severity: ERROR
    languages:
      - generic
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL3011
      references:
        - https://github.com/hadolint/hadolint/wiki/DL3011
      category: correctness
      technology:
        - dockerfile
    paths:
      include:
        - "*dockerfile*"
        - "*Dockerfile*"
    pattern-either:
      - patterns:
          - pattern: EXPOSE $PORT
          - metavariable-comparison:
              metavariable: "$PORT"
              comparison: "$PORT > 65535"
      - pattern: EXPOSE -$PORT
  - id: hardcoded-http-auth-in-controller
    pattern: |
      class $CONTROLLER < ApplicationController
        ...
        http_basic_authenticate_with ..., :password => "...", ...
      end
    message:
      Detected hardcoded password used in basic authentication in a controller
      class. Including this password in version control could expose this credential.
      Consider refactoring to use environment variables or configuration files.
    severity: ERROR
    metadata:
      cwe: "CWE-798: Use of Hard-coded Credentials"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://github.com/presidentbeef/brakeman/blob/main/docs/warning_types/basic_auth/index.markdown
      category: security
      technology:
        - ruby
    languages:
      - ruby
  - id: eqeq
    patterns:
      - pattern-not-inside: assert $X;
      - pattern-not-inside: "assert $X : $Y;

          "
      - pattern-either:
          - pattern: "$X == $X"
          - pattern: "$X != $X"
      - pattern-not: 1 == 1
    message:
      "`$X == $X` or `$X != $X` is always true. (Unless the value compared is
      a float or double). To test if `$X` is not-a-number, use `Double.isNaN($X)`."
    languages:
      - java
    severity: ERROR
    metadata:
      category: correctness
      technology:
        - java
  - id: des-is-deprecated
    message:
      DES is considered deprecated. AES is the recommended cipher. Upgrade to
      use AES. See https://www.nist.gov/news-events/news/2005/06/nist-withdraws-outdated-data-encryption-standard
      for more information.
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#DES_USAGE
      asvs:
        section: V6 Stored Cryptography Verification Requirements
        control_id: 6.2.5 Insecure Algorithm
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms
        version: "4"
      references:
        - https://www.nist.gov/news-events/news/2005/06/nist-withdraws-outdated-data-encryption-standard
        - https://cheatsheetseries.owasp.org/cheatsheets/Cryptographic_Storage_Cheat_Sheet.html#algorithms
      category: security
      technology:
        - java
    severity: WARNING
    pattern: $CIPHER.getInstance("=~/DES/.*/")
    fix: $CIPHER.getInstance("AES/GCM/NoPadding")
    languages:
      - java
      - kt
  - id: insecure-hash-algorithm-md5
    pattern: cryptography.hazmat.primitives.hashes.MD5(...)
    message:
      Detected MD5 hash algorithm which is considered insecure. MD5 is not collision
      resistant and is therefore not suitable as a cryptographic signature. Use SHA256
      or SHA3 instead.
    metadata:
      source-rule-url: https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      bandit-code: B303
      references:
        - https://tools.ietf.org/html/rfc6151
        - https://crypto.stackexchange.com/questions/44151/how-does-the-flame-malware-take-advantage-of-md5-collision
        - https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html
      category: security
      technology:
        - cryptography
    severity: WARNING
    languages:
      - python
  - id: use-of-md5
    message:
      Detected MD5 hash algorithm which is considered insecure. MD5 is not collision
      resistant and is therefore not suitable as a cryptographic signature. Use SHA256
      or SHA3 instead.
    languages:
      - go
    severity: WARNING
    metadata:
      owasp: "A9: Using Components with Known Vulnerabilities"
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      source-rule-url: https://github.com/securego/gosec#available-rules
      category: security
      technology:
        - go
      confidence: MEDIUM
    pattern-either:
      - pattern: "md5.New()

          "
      - pattern: "md5.Sum(...)

          "
  - id: json-entity-escape
    pattern-either:
      - pattern: "ActiveSupport.escape_html_entities_in_json = false

          "
      - pattern: "config.active_support.escape_html_entities_in_json = false

          "
    message:
      Checks if HTML escaping is globally disabled for JSON output. This could
      lead to XSS.
    metadata:
      source-rule-url: https://github.com/presidentbeef/brakeman/blob/main/lib/brakeman/checks/check_json_entity_escape.rb
      category: security
      technology:
        - ruby
    languages:
      - ruby
    severity: WARNING
  - id: return-in-init
    patterns:
      - pattern-inside: |
          class $A(...):
              ...
      - pattern-inside: |
          def __init__(...):
              ...
      - pattern-not-inside: |
          def __init__(...):
              ...
              def $F(...):
                  ...
      - patterns:
          - pattern: return ...
          - pattern-not: return
          - pattern-not: return None
    message:
      "`return` should never appear inside a class __init__ function. This will
      cause a runtime error."
    languages:
      - python
    severity: ERROR
    metadata:
      category: correctness
      technology:
        - python
  - id: mass-assignment
    languages:
      - python
    severity: WARNING
    message:
      Mass assignment detected. This can result in assignment to model fields
      that are unintended and can be exploited by an attacker. Instead of using '**request.$W',
      assign each field you want to edit individually to prevent mass assignment. You
      can read more about mass assignment at https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html.
    metadata:
      cwe:
        "CWE-915: Improperly Controlled Modification of Dynamically-Determined Object
        Attributes"
      owasp: "A1: Injection"
      owaspapi: "API6: Mass Assignment"
      references:
        - https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html
      category: security
      technology:
        - django
    pattern-either:
      - pattern: "$MODEL.objects.create(**request.$W)"
      - pattern: |
          $OBJ.update(**request.$W)
          ...
          $OBJ.save()
  - id: pprof-debug-exposure
    metadata:
      cwe: "CWE-489: Active Debug Code"
      owasp: "A6: Security Misconfiguration"
      source-rule-url: https://github.com/securego/gosec#available-rules
      references:
        - https://www.farsightsecurity.com/blog/txt-record/go-remote-profiling-20161028/
      category: security
      technology:
        - go
      confidence: MEDIUM
    message:
      The profiling 'pprof' endpoint is automatically exposed on /debug/pprof.
      This could leak information about the server. Instead, use `import "net/http/pprof"`.
      See https://www.farsightsecurity.com/blog/txt-record/go-remote-profiling-20161028/
      for more information and mitigation.
    languages:
      - go
    severity: WARNING
    patterns:
      - pattern-inside: |
          import _ "net/http/pprof"
          ...
      - pattern-inside: |
          func $ANY(...) {
            ...
          }
      - pattern-not-inside: |
          $MUX = http.NewServeMux(...)
          ...
          http.ListenAndServe($ADDR, $MUX)
      - pattern-not: http.ListenAndServe("=~/^localhost.*/", ...)
      - pattern-not: http.ListenAndServe("=~/^127[.]0[.]0[.]1.*/", ...)
      - pattern: http.ListenAndServe(...)
  - id: file-disclosure
    message:
      Special requests can determine whether a file exists on a filesystem that's
      outside the Ruby app's root directory. To fix this, set config.serve_static_assets
      = false.
    metadata:
      references:
        - https://github.com/presidentbeef/brakeman/blob/main/lib/brakeman/checks/check_file_disclosure.rb
        - https://groups.google.com/g/rubyonrails-security/c/23fiuwb1NBA/m/MQVM1-5GkPMJ
      category: security
      technology:
        - ruby
    languages:
      - ruby
    severity: ERROR
    pattern: config.serve_static_assets = true
  - id: insufficient-ec-key-size
    patterns:
      - pattern-inside: cryptography.hazmat.primitives.asymmetric.ec.generate_private_key(...)
      - pattern-either:
          - pattern: cryptography.hazmat.primitives.asymmetric.ec.SECP192R1
          - pattern: cryptography.hazmat.primitives.asymmetric.ec.SECT163K1
          - pattern: cryptography.hazmat.primitives.asymmetric.ec.SECT163R2
    message:
      Detected an insufficient curve size for EC. NIST recommends a key size
      of 224 or higher. For example, use 'ec.SECP256R1'.
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/plugins/weak_cryptographic_key.py
      references:
        - https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-57Pt3r1.pdf
        - https://cryptography.io/en/latest/hazmat/primitives/asymmetric/ec/#elliptic-curves
      category: security
      technology:
        - cryptography
    languages:
      - python
    severity: WARNING
  - id: ssl-mode-no-verify
    pattern: OpenSSL::SSL::VERIFY_NONE
    message:
      Detected SSL that will accept an unverified connection. This makes the
      connections susceptible to man-in-the-middle attacks. Use 'OpenSSL::SSL::VERIFY_PEER'
      instead.
    fix-regex:
      regex: VERIFY_NONE
      replacement: VERIFY_PEER
    severity: WARNING
    languages:
      - ruby
    metadata:
      category: security
      technology:
        - ruby
  - id: hardcoded-jwt-secret
    message:
      "Hardcoded JWT secret or private key is used. This is a Insufficiently
      Protected Credentials weakness: https://cwe.mitre.org/data/definitions/522.html
      Consider using an appropriate security mechanism to protect the credentials (e.g.
      keeping secrets in environment variables: process.env.SECRET)"
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.2 Static API keys or secret
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      category: security
      technology:
        - jwt
    languages:
      - javascript
      - typescript
    severity: ERROR
    patterns:
      - pattern-inside: |
          $JWT = require("jsonwebtoken");
          ...
      - pattern-either:
          - pattern: '$JWT.sign($P, "...", ...);

              '
          - pattern: '$JWT.verify($P, "...", ...);

              '
          - patterns:
              - pattern-inside: |
                  $SECRET = "...";
                  ...
              - pattern-either:
                  - pattern: "$JWT.sign($P, $SECRET, ...)"
                  - pattern: "$JWT.verify($P, $SECRET, ...)"
  - id: react-markdown-insecure-html
    patterns:
      - pattern-either:
          - pattern-inside: |
              $X = require('react-markdown/with-html');
              ...
          - pattern-inside: |
              $X = require('react-markdown');
              ...
          - pattern-inside: |
              import 'react-markdown/with-html';
              ...
          - pattern-inside: |
              import 'react-markdown';
              ...
      - pattern-either:
          - pattern: "<$EL allowDangerousHtml />\n"
          - pattern: "<$EL transformLinkUri=... />\n"
          - pattern: "<$EL transformImageUri=... />\n"
    message:
      Overwriting `transformLinkUri` or `transformImageUri` to something insecure
      or turning `allowDangerousHtml` on, will open code up to XSS vectors.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://www.npmjs.com/package/react-markdown#security
      category: security
      technology:
        - react
    languages:
      - typescript
      - javascript
    severity: WARNING
  - id: use-of-md5
    message:
      Detected MD5 hash algorithm which is considered insecure. MD5 is not collision
      resistant and is therefore not suitable as a cryptographic signature. Use SHA256
      or SHA3 instead.
    languages:
      - go
    severity: WARNING
    metadata:
      owasp: "A9: Using Components with Known Vulnerabilities"
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      source-rule-url: https://github.com/securego/gosec#available-rules
      category: security
      technology:
        - go
      confidence: MEDIUM
    pattern-either:
      - pattern: "md5.New()

          "
      - pattern: "md5.Sum(...)

          "
  - id: copy-from-own-alias
    severity: ERROR
    languages:
      - generic
    message:
      COPY instructions cannot copy from its own alias. The '$REF' alias is used
      before switching to a new image. If you meant to switch to a new image, include
      a new 'FROM' statement. Otherwise, remove the '--from=$REF' from the COPY statement.
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL3023
      references:
        - https://github.com/hadolint/hadolint/wiki/DL3023
      category: correctness
      technology:
        - dockerfile
    paths:
      include:
        - "*dockerfile*"
        - "*Dockerfile*"
    pattern-either:
      - pattern: |
          FROM $IMAGE:$TAG as $REF
          ...
          COPY --from=$REF
          ...
          FROM
      - pattern: |
          FROM $IMAGE:$TAG AS $REF
          ...
          COPY --from=$REF
          ...
          FROM
  - id: ruby-jwt-none-alg
    message:
      Detected use of the 'none' algorithm in a JWT token. The 'none' algorithm
      assumes the integrity of the token has already been verified. This would allow
      a malicious actor to forge a JWT token that will automatically be verified. Do
      not explicitly use the 'none' algorithm. Instead, use an algorithm such as 'HS256'.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      category: security
      technology:
        - jwt
    languages:
      - ruby
    severity: ERROR
    patterns:
      - pattern-inside: |
          require 'jwt'
          ...
      - pattern: "JWT.encode($PAYLOAD, $SECRET, 'none', ...)

          "
  - id: autoescape-disabled
    patterns:
      - pattern-not: jinja2.Environment(..., autoescape=True, ...)
      - pattern-not:
          jinja2.Environment(..., autoescape=jinja2.select_autoescape(...),
          ...)
      - pattern: jinja2.Environment(...)
    fix-regex:
      regex: "(.*)\\)"
      replacement: "\\1, autoescape=True)"
    message:
      Detected a Jinja2 environment without autoescaping. Jinja2 does not autoescape
      by default. This is dangerous if you are rendering to a browser because this allows
      for cross-site scripting (XSS) attacks. If you are in a web context, enable autoescaping
      by setting 'autoescape=True.' You may also consider using 'jinja2.select_autoescape()'
      to only enable automatic escaping for certain file extensions.
    metadata:
      source-rule-url: https://bandit.readthedocs.io/en/latest/plugins/b701_jinja2_autoescape_false.html
      cwe: "CWE-116: Improper Encoding or Escaping of Output"
      owasp: "A6: Security Misconfiguration"
      references:
        - https://jinja.palletsprojects.com/en/2.11.x/api/#basics
      category: security
      technology:
        - jinja2
    languages:
      - python
    severity: WARNING
  - id: user-exec
    message:
      Found user data in a call to 'exec'. This is extremely dangerous because
      it can enable an attacker to execute arbitrary remote code on the system. Instead,
      refactor your code to not use 'eval' and instead use a safe library for the specific
      functionality you need.
    metadata:
      cwe:
        "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code
        ('Eval Injection')"
      owasp: "A1: Injection"
      category: security
      technology:
        - django
      references:
        - https://owasp.org/www-community/attacks/Code_Injection
    patterns:
      - pattern-inside: |
          def $F(...):
            ...
      - pattern-either:
          - pattern: exec(..., request.$W.get(...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              exec(..., $V, ...)
          - pattern: exec(..., request.$W(...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              exec(..., $V, ...)
          - pattern: exec(..., request.$W[...], ...)
          - pattern: |
              $V = request.$W[...]
              ...
              exec(..., $V, ...)
    languages:
      - python
    severity: WARNING
  - id: react-controlled-component-password
    pattern-either:
      - pattern: <$EL type="password" value={this.state.$X} onChange=...  />
      - pattern:
          "React.createElement($EL,{type: 'password', value: this.state.$X, onChange:...},...)

          "
      - pattern: |
          $PARAMS = {type: 'password', value: this.state.$X, onChange:...};
          ...
          React.createElement($EL,$PARAMS,...);
    message: Password can be leaked if CSS injection exists on the page.
    metadata:
      category: security
      technology:
        - react
    languages:
      - typescript
      - javascript
    severity: WARNING
  - id: no-string-eqeq
    languages:
      - java
    patterns:
      - pattern-not: null == (String $Y)
      - pattern: "$X == (String $Y)"
    message:
      Strings should not be compared with '=='. This is a reference comparison
      operator. Use '.equals()' instead.
    severity: WARNING
    metadata:
      category: correctness
      technology:
        - java
  - id: dynamic-httptrace-clienttrace
    message:
      Detected a potentially dynamic ClientTrace. This occurred because semgrep
      could not find a static definition for '$TRACE'. Dynamic ClientTraces are dangerous
      because they deserialize function code to run when certain Request events occur,
      which could lead to code being run without your knowledge. Ensure that your ClientTrace
      is statically defined.
    metadata:
      cwe: "CWE-913: Improper Control of Dynamically-Managed Code Resources"
      owasp: "A8: Insecure Deserialization"
      references:
        - https://github.com/returntocorp/semgrep-rules/issues/518
      category: security
      technology:
        - go
      confidence: MEDIUM
    patterns:
      - pattern-not-inside: |
          package $PACKAGE
          ...
          &httptrace.ClientTrace { ... }
          ...
      - pattern: httptrace.WithClientTrace($ANY, $TRACE)
    severity: WARNING
    languages:
      - go
  - id: bad-deserialization
    patterns:
      - pattern-either:
          - pattern: "YAML.load(...)

              "
          - pattern: "CSV.load(...)

              "
          - pattern: "Marshal.load(...)

              "
          - pattern: "Marshal.restore(...)

              "
          - pattern: "$OBJ.object_load(...)\n"
      - pattern-not: "YAML.load(..., safe: true, ...)

          "
      - pattern-not: 'YAML.load("...", ...)

          '
      - pattern-not-inside: |
          $FILE = File.read("...", ...)
          ...
          YAML.load(..., $FILE, ...)
      - pattern-not-inside: |
          $FILENAME = "..."
          ...
          $FILE = File.read($FILENAME, ...)
          ...
          YAML.load(..., $FILE, ...)
      - pattern-not-inside: 'YAML.load(..., File.read("...", ...), ...)

          '
    message:
      Checks for unsafe deserialization. Objects in Ruby can be serialized into
      strings, then later loaded from strings. However, uses of load and object_load
      can cause remote code execution. Loading user input with YAML, MARSHAL, or CSV
      can potentially be dangerous. Use JSON in a secure fashion instead. However, loading
      YAML from a static file is not dangerous and should not be flagged.
    metadata:
      references:
        - https://groups.google.com/g/rubyonrails-security/c/61bkgvnSGTQ/m/nehwjA8tQ8EJ
        - https://github.com/presidentbeef/brakeman/blob/main/lib/brakeman/checks/check_deserialize.rb
      category: security
      technology:
        - ruby
    languages:
      - ruby
    severity: ERROR
  - id: use-of-md5
    message:
      Detected MD5 hash algorithm which is considered insecure. MD5 is not collision
      resistant and is therefore not suitable as a cryptographic signature. Use SHA256
      or SHA3 instead.
    languages:
      - go
    severity: WARNING
    metadata:
      owasp: "A9: Using Components with Known Vulnerabilities"
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      source-rule-url: https://github.com/securego/gosec#available-rules
      category: security
      technology:
        - go
      confidence: MEDIUM
    pattern-either:
      - pattern: "md5.New()

          "
      - pattern: "md5.Sum(...)

          "
  - id: hardcoded-token
    message:
      Hardcoded AWS access token detected. Attackers can possibly freely read
      this value and gain access to the AWS environment. Instead, use environment variables
      to access tokens (e.g., os.environ.get(...)) or use non version-controlled configuration
      files.
    metadata:
      cwe: "CWE-798: Use of Hard-coded Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://pypi.org/project/flake8-boto3/
      references:
        - https://bento.dev/checks/boto3/hardcoded-access-token/
        - https://aws.amazon.com/blogs/security/what-to-do-if-you-inadvertently-expose-an-aws-access-key/
      category: security
      technology:
        - boto3
    languages:
      - python
    severity: WARNING
    pattern-either:
      - pattern: $W(..., aws_secret_access_key="=~/^[A-Za-z0-9/+=]+$/", ...)
      - pattern: $W(..., aws_access_key_id="=~/^AKI/", ...)
      - pattern: $W(..., aws_session_token="...", ...)
  - id: mass-assignment-protection-disabled
    pattern: "$MODEL.new(params[...], ..., :without_protection => true, ...)"
    message:
      Mass assignment protection disabled for '$MODEL'. This could permit assignment
      to sensitive model fields without intention. Instead, use 'attr_accessible' for
      the model or disable mass assigment using 'config.active_record.whitelist_attributes
      = true'. ':without_protection => true' must be removed for this to take effect.
    metadata:
      cwe:
        "CWE-915: Improperly Controlled Modification of Dynamically-Determined Object
        Attributes"
      owasp: "A1: Injection"
      source-rule-url: https://github.com/presidentbeef/brakeman/blob/main/docs/warning_types/mass_assignment/index.markdown
      category: security
      technology:
        - ruby
    severity: WARNING
    languages:
      - ruby
  - id: user-eval
    message:
      Found user data in a call to 'eval'. This is extremely dangerous because
      it can enable an attacker to execute arbitrary remote code on the system. Instead,
      refactor your code to not use 'eval' and instead use a safe library for the specific
      functionality you need.
    metadata:
      cwe:
        "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code
        ('Eval Injection')"
      owasp: "A1: Injection"
      references:
        - https://nedbatchelder.com/blog/201206/eval_really_is_dangerous.html
        - https://owasp.org/www-community/attacks/Code_Injection
      category: security
      technology:
        - django
    patterns:
      - pattern-inside: |
          def $F(...):
            ...
      - pattern-either:
          - pattern: eval(..., request.$W.get(...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              eval(..., $V, ...)
          - pattern: eval(..., request.$W(...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              eval(..., $V, ...)
          - pattern: eval(..., request.$W[...], ...)
          - pattern: |
              $V = request.$W[...]
              ...
              eval(..., $V, ...)
    languages:
      - python
    severity: WARNING
  - id: sqlalchemy-sql-injection
    patterns:
      - pattern-either:
          - pattern: |
              def $FUNC(...,$VAR,...):
                ...
                $SESSION.query(...).$SQLFUNC("...".$FORMATFUNC(...,$VAR,...))
          - pattern: |
              def $FUNC(...,$VAR,...):
                ...
                $SESSION.query.join(...).$SQLFUNC("...".$FORMATFUNC(...,$VAR,...))
          - pattern: |
              def $FUNC(...,$VAR,...):
                ...
                $SESSION.query.$SQLFUNC("...".$FORMATFUNC(...,$VAR,...))
          - pattern: |
              def $FUNC(...,$VAR,...):
                ...
                query.$SQLFUNC("...".$FORMATFUNC(...,$VAR,...))
      - metavariable-regex:
          metavariable: "$SQLFUNC"
          regex: "(group_by|order_by|distinct|having|filter)"
      - metavariable-regex:
          metavariable: "$FORMATFUNC"
          regex: "(?!bindparams)"
    message:
      Distinct, Having, Group_by, Order_by, and Filter in SQLAlchemy can cause
      sql injections if the developer inputs raw SQL into the before-mentioned clauses.
      This pattern captures relevant cases in which the developer inputs raw SQL into
      the distinct, having, group_by, order_by or filter clauses and injects user-input
      into the raw SQL with any function besides "bindparams". Use bindParams to securely
      bind user-input to SQL statements.
    fix-regex:
      regex: format
      replacement: bindparams
    languages:
      - python
    severity: WARNING
    metadata:
      category: security
      technology:
        - sqlalchemy
  - id: yaml-parsing
    message:
      Detected enabled YAML parsing. This is vulnerable to remote code execution
      in Rails 2.x versions up to 2.3.14. To fix, delete this line.
    fix-regex:
      regex: ActionController.*:yaml
      replacement: " "
    severity: WARNING
    languages:
      - ruby
    pattern: ActionController::Base.param_parsers[Mime::YAML] = :yaml
    metadata:
      category: security
      technology:
        - ruby
  - id: useless-if-conditional
    message: if block checks for the same condition on both branches (`$X`)
    languages:
      - python
    severity: WARNING
    pattern: |
      if $X:
          ...
      elif $X:
          ...
    metadata:
      category: maintainability
      technology:
        - python
  - id: ruby-jwt-hardcoded-secret
    message:
      "Hardcoded JWT secret or private key is used. This is a Insufficiently
      Protected Credentials weakness: https://cwe.mitre.org/data/definitions/522.html
      Consider using an appropriate security mechanism to protect the credentials (e.g.
      keeping secrets in environment variables)"
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      category: security
      technology:
        - jwt
    patterns:
      - pattern-inside: |
          require 'jwt'
          ...
      - pattern-either:
          - pattern: 'JWT.encode($PAYLOAD,"...",...)

              '
          - pattern: 'JWT.decode($PAYLOAD,"...",...)

              '
          - pattern: "JWT.encode($PAYLOAD,nil,...)

              "
          - pattern: "JWT.decode($PAYLOAD,nil,...)

              "
          - pattern: |
              $SECRET = "..."
              ...
              JWT.encode($PAYLOAD,$SECRET,...)
          - pattern: |
              $SECRET = "..."
              ...
              JWT.decode($PAYLOAD,$SECRET,...)
    languages:
      - ruby
    severity: ERROR
  - id: alias-for-html-safe
    message:
      The syntax `<%== ... %>` is an alias for `html_safe`. This means the content
      inside these tags will be rendered as raw HTML. This may expose your application
      to cross-site scripting. If you need raw HTML, prefer using the more explicit
      `html_safe` and be sure to correctly sanitize variables using a library such as
      DOMPurify.
    metadata:
      references:
        - https://medium.com/sumone-technical-blog/a-pretty-way-to-unescape-html-in-a-ruby-on-rails-application-efc22b850027
        - https://stackoverflow.com/questions/4251284/raw-vs-html-safe-vs-h-to-unescape-html#:~:text===
      category: security
      technology:
        - rails
    languages:
      - generic
    paths:
      include:
        - "*.erb"
    severity: WARNING
    patterns:
      - pattern: "<%== ... %>"
      - pattern-not: "<%== $...A.to_json %>"
  - id: avoid-insecure-deserialization
    metadata:
      owasp: "A8: Insecure Deserialization"
      cwe: "CWE-502: Deserialization of Untrusted Data"
      references:
        - https://docs.python.org/3/library/pickle.html
      category: security
      technology:
        - django
    message:
      Avoid using insecure deserialization library, backed by `pickle`, `_pickle`,
      `cpickle`, `dill`, `shelve`, or `yaml`, which are known to lead to remote code
      execution vulnerabilities.
    languages:
      - python
    severity: ERROR
    patterns:
      - pattern-inside: |
          def $X(..., request, ...):
            ...
      - pattern-either:
          - pattern: |
              $VAR = <... request.$Y.get(...) ...>
              ...
              pickle.$FUNC(<... $VAR ...>)
          - pattern: pickle.$FUNC(<... request.$Y.get(...) ...>)
          - pattern: |
              $VAR = <... request.$Y.get(...) ...>
              ...
              _pickle.$FUNC(<... $VAR ...>)
          - pattern: _pickle.$FUNC(<... request.$Y.get(...) ...>)
          - pattern: |
              $VAR = <... request.$Y.get(...) ...>
              ...
              cPickle.$FUNC(<... $VAR ...>)
          - pattern: cPickle.$FUNC(<... request.$Y.get(...) ...>)
          - pattern: |
              $VAR = <... request.$Y.get(...) ...>
              ...
              dill.$FUNC(<... $VAR ...>)
          - pattern: dill.$FUNC(<... request.$Y.get(...) ...>)
          - pattern: |
              $VAR = <... request.$Y.get(...) ...>
              ...
              shelve.$FUNC(<... $VAR ...>)
          - pattern: shelve.$FUNC(<... request.$Y.get(...) ...>)
          - pattern: |
              $VAR = <... request.$Y.get(...) ...>
              ...
              yaml.$FUNC(<... $VAR ...>)
          - pattern: yaml.$FUNC(<... request.$Y.get(...) ...>)
  - id: multiple-cmd-instructions
    severity: ERROR
    languages:
      - generic
    patterns:
      - pattern-not-inside: |
          CMD ...
          ...
          FROM ...
          ...
          CMD ...
      - pattern: |
          CMD ...
          ...
          ...
          CMD ...
    message: Multiple CMD instructions were found. Only the last one will take effect.
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL4003
      references:
        - https://github.com/hadolint/hadolint/wiki/DL4003
        - https://kapeli.com/cheat_sheets/Dockerfile.docset/Contents/Resources/Documents/index#//dash_ref_Instructions/Entry/CMD/0
      category: correctness
      technology:
        - dockerfile
    paths:
      include:
        - "*dockerfile*"
        - "*Dockerfile*"
  - id: eqeq-is-bad
    patterns:
      - pattern-not-inside: assert(...)
      - pattern-either:
          - pattern: "$X == $X"
          - pattern: "$X != $X"
      - pattern-not: 1 == 1
    message:
      Detected useless comparison operation `$X == $X` or `$X != $X`. This will
      always return 'True' or 'False' and therefore is not necessary. Instead, remove
      this comparison operation or use another comparison expression that is not deterministic.
    languages:
      - go
    severity: ERROR
    metadata:
      category: correctness
      technology:
        - go
  - id: filter-skipping
    patterns:
      - pattern-not: '$CALL "=~/.*(/:action.*).*/", $ACTION

          '
      - pattern: '$CALL "=~/.*(/:action.*).*/"

          '
    message:
      Checks for use of action in Ruby routes. This can cause Rails to render
      an arbitrary view if an attacker creates an URL accurately. Affects 3.0 applications.
      Can avoid the vulnerability by providing additional constraints.
    metadata:
      references:
        - https://github.com/presidentbeef/brakeman/blob/main/lib/brakeman/checks/check_filter_skipping.rb
        - https://groups.google.com/g/rubyonrails-security/c/NCCsca7TEtY
      category: security
      technology:
        - ruby
    languages:
      - ruby
    severity: ERROR
  - id: no-strings-as-booleans
    message:
      Using strings as booleans in Python has unexpected results. `"one" and
      "two"` will return "two". `"one" or "two"` will return "one". In Python, strings
      are truthy, and strings with a non-zero length evaluate to True.
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: |
          if <... "..." and ... ...>:
              ...
      - pattern: |
          if <... "..." or ... ...>:
              ...
      - patterns:
          - pattern-not: |
              if $X in "...":
                ...
          - pattern: |
              if "...":
                  ...
    metadata:
      category: correctness
      technology:
        - python
  - id: divide-by-zero
    message:
      Checks for divide by zero. Best practice involves not dividing a variable
      by zero, as this leads to a Ruby ZeroDivisionError.
    metadata:
      references:
        - https://github.com/presidentbeef/brakeman/blob/main/lib/brakeman/checks/check_divide_by_zero.rb
      category: security
      technology:
        - ruby
    languages:
      - ruby
    severity: WARNING
    pattern-either:
      - pattern: "$X / 0\n"
      - pattern: |
          $ZERO = 0
          ...
          $X / $ZERO
  - id: grpc-nodejs-insecure-connection
    message:
      Found an insecure gRPC connection. This creates a connection without encryption
      to a gRPC client/server. A malicious attacker could tamper with the gRPC message,
      which could compromise the machine.
    metadata:
      owasp: "A8: Insecure Deserialization"
      cwe: "CWE-502: Deserialization of Untrusted Data"
      category: security
      technology:
        - grpc
    severity: ERROR
    languages:
      - javascript
      - typescript
    pattern-either:
      - pattern: |
          require('grpc');
          ...
          $GRPC($ADDR,...,$CREDENTIALS.createInsecure(),...);
      - pattern: |-
          require('grpc');
          ...
          $CREDS = <... $CREDENTIALS.createInsecure() ...>;
          ...
          $GRPC($ADDR,...,$CREDS,...);
  - id: insecure-cipher-algorithm-rc2
    message:
      Detected RC2 cipher algorithm which is considered insecure. The algorithm
      has known vulnerabilities and is difficult to use securely. Use AES instead.
    metadata:
      source-rule-url: https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L84
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      bandit-code: B304
      references:
        - https://security.stackexchange.com/questions/93924/is-rc2-cbc-at-all-secure
        - https://sweet32.info/
      category: security
      technology:
        - pycryptodome
    severity: WARNING
    languages:
      - python
    pattern-either:
      - pattern: Cryptodome.Cipher.ARC2.new(...)
      - pattern: Crypto.Cipher.ARC2.new
  - id: exec-injection
    languages:
      - python
    severity: ERROR
    message:
      Detected user data flowing into exec. This is code injection and should
      be avoided.
    metadata:
      cwe:
        "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code
        ('Eval Injection')"
      owasp: "A1: Injection"
      references:
        - https://nedbatchelder.com/blog/201206/exec_really_is_dangerous.html
      category: security
      technology:
        - flask
    pattern-either:
      - patterns:
          - pattern: exec(...)
          - pattern-either:
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    exec(..., <... $ROUTEVAR ...>, ...)
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    $INTERM = <... $ROUTEVAR ...>
                    ...
                    exec(..., <... $INTERM ...>, ...)
      - pattern: exec(..., <... flask.request.$W.get(...) ...>, ...)
      - pattern: exec(..., <... flask.request.$W[...] ...>, ...)
      - pattern: exec(..., <... flask.request.$W(...) ...>, ...)
      - pattern: exec(..., <... flask.request.$W ...>, ...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W.get(...) ...>
              ...
              exec(..., <... $INTERM ...>, ...)
          - pattern: exec(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W[...] ...>
              ...
              exec(..., <... $INTERM ...>, ...)
          - pattern: exec(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W(...) ...>
              ...
              exec(..., <... $INTERM ...>, ...)
          - pattern: exec(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W ...>
              ...
              exec(..., <... $INTERM ...>, ...)
          - pattern: exec(...)
  - id: python-logger-credential-disclosure
    patterns:
      - pattern: "logger.$LOGGER_CALL($FORMAT_STRING,...)

          "
      - metavariable-regex:
          metavariable: "$LOGGER_CALL"
          regex: "(info|error|exception)"
      - metavariable-regex:
          metavariable: "$FORMAT_STRING"
          regex: "(?i).*(api.key|secret|credential|token).*\\%s.*"
    message: Logger call may be exposing a secret credential in $FORMAT_STRING
    severity: WARNING
    languages:
      - python
    metadata:
      category: security
      technology:
        - python
  - id: eval-injection
    languages:
      - python
    severity: ERROR
    message:
      Detected user data flowing into eval. This is code injection and should
      be avoided.
    metadata:
      cwe:
        "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code
        ('Eval Injection')"
      owasp: "A1: Injection"
      references:
        - https://nedbatchelder.com/blog/201206/eval_really_is_dangerous.html
      category: security
      technology:
        - flask
    pattern-either:
      - patterns:
          - pattern: eval(...)
          - pattern-either:
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    eval(..., <... $ROUTEVAR ...>, ...)
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    $INTERM = <... $ROUTEVAR ...>
                    ...
                    eval(..., <... $INTERM ...>, ...)
      - pattern: eval(..., <... flask.request.$W.get(...) ...>, ...)
      - pattern: eval(..., <... flask.request.$W[...] ...>, ...)
      - pattern: eval(..., <... flask.request.$W(...) ...>, ...)
      - pattern: eval(..., <... flask.request.$W ...>, ...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W.get(...) ...>
              ...
              eval(..., <... $INTERM ...>, ...)
          - pattern: eval(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W[...] ...>
              ...
              eval(..., <... $INTERM ...>, ...)
          - pattern: eval(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W(...) ...>
              ...
              eval(..., <... $INTERM ...>, ...)
          - pattern: eval(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W ...>
              ...
              eval(..., <... $INTERM ...>, ...)
          - pattern: eval(...)
  - id: jwt-exposed-credentials
    languages:
      - javascript
      - typescript
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.2 Static API keys or secret
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      references:
        - https://cwe.mitre.org/data/definitions/522.html
      category: security
      technology:
        - jose
        - jwt
    message:
      Password is exposed through JWT token payload. This is not encrypted and
      the password could be compromised. Do not store passwords in JWT tokens.
    severity: ERROR
    pattern-either:
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $T = JWT.sign({password:...},...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $T = JWT.sign({password:...},...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = {password:...};
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = {password:...};
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = {password:...};
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = {password:...};
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P.password = ...;
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P.password = ...;
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = Object.assign(...,{password:...},...);
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = Object.assign(...,{password:...},...);
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = Object.assign(...,{password:...},...);
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = Object.assign(...,{password:...},...);
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $T = JWT.sign(Object.assign(...,{password:...},...),...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $T = JWT.sign(Object.assign(...,{password:...},...),...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $T = JWT.sign({$U:{password:...}},...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $T = JWT.sign({$U:{password:...}},...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = {$U:{password:...}};
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = {$U:{password:...}};
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = {$U:{password:...}};
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = {$U:{password:...}};
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P.$U.password = ...;
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P.$U.password = ...;
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = Object.assign(...,{$U:{password:...}},...);
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $P = Object.assign(...,{$U:{password:...}},...);
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = Object.assign(...,{$U:{password:...}},...);
          ...
          var $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $P = Object.assign(...,{$U:{password:...}},...);
          ...
          $T = JWT.sign($P,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $T = JWT.sign(Object.assign(...,{$U:{password:...}},...),...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          $T = JWT.sign(Object.assign(...,{$U:{password:...}},...),...);
  - id: hardcoded-jwt-key
    metadata:
      cwe: "CWE-798: Use of Hard-coded Credentials"
      owasp: "A2: Broken Authentication"
      category: security
      technology:
        - jwt
      confidence: MEDIUM
    pattern-either:
      - pattern: |
          $X = []byte("...")
          ...
          $Y := $TOKEN.SignedString($X)
      - pattern: '$TOKEN.SignedString([]byte("..."))

          '
    message: JWT token is hardcoded
    languages:
      - go
    severity: WARNING
  - id: potential-dos-via-decompression-bomb
    message:
      Detected a possible denial-of-service via a zip bomb attack. By limiting
      the max bytes read, you can mitigate this attack. `io.CopyN()` can specify a size.
      Refer to https://bomb.codes/ to learn more about this attack and other ways to
      mitigate it.
    severity: WARNING
    languages:
      - go
    patterns:
      - pattern-either:
          - pattern: io.Copy(...)
          - pattern: io.CopyBuffer(...)
      - pattern-either:
          - pattern-inside: |
              gzip.NewReader(...)
              ...
          - pattern-inside: |
              zlib.NewReader(...)
              ...
          - pattern-inside: |
              zlib.NewReaderDict(...)
              ...
          - pattern-inside: |
              bzip2.NewReader(...)
              ...
          - pattern-inside: |
              flate.NewReader(...)
              ...
          - pattern-inside: |
              flate.NewReaderDict(...)
              ...
          - pattern-inside: |
              lzw.NewReader(...)
              ...
          - pattern-inside: |
              tar.NewReader(...)
              ...
          - pattern-inside: |
              zip.NewReader(...)
              ...
          - pattern-inside: |
              zip.OpenReader(...)
              ...
    fix-regex:
      regex: "(.*)(Copy|CopyBuffer)\\((.*?),(.*?)(\\)|,.*\\))"
      replacement: "\\1CopyN(\\3, \\4, 1024*1024*256)"
    metadata:
      cwe: "CWE-400: Uncontrolled Resource Consumption"
      source-rule-url: https://github.com/securego/gosec
      references:
        - https://bomb.codes/
        - https://golang.org/pkg/io/#CopyN
        - https://github.com/securego/gosec/blob/master/rules/decompression-bomb.go
      category: security
      technology:
        - go
      confidence: MEDIUM
  - id: is-not-is-not
    message:
      In Python 'X is not ...' is different from 'X is (not ...)'. In the latter
      the 'not' converts the '...' directly to boolean.
    languages:
      - python
    severity: ERROR
    pattern: "$S is (not ...)"
    metadata:
      category: correctness
      technology:
        - python
  - id: server-dangerous-object-deserialization
    severity: ERROR
    metadata:
      cwe: "CWE-502: Deserialization of Untrusted Data"
      owasp: "A8: Insecure Deserialization"
      references:
        - https://mogwailabs.de/blog/2019/03/attacking-java-rmi-services-after-jep-290/
        - https://frohoff.github.io/appseccali-marshalling-pickles/
      category: security
      technology:
        - rmi
    message:
      Using an arbitrary object ('Object $PARAM') with Java RMI is an insecure
      deserialization vulnerability. This object can be manipulated by a malicious actor
      allowing them to execute code on your system. Instead, use an integer ID to look
      up your object, or consider alternative serialization schemes such as JSON.
    languages:
      - java
    pattern: |
      interface $INTERFACE extends Remote {
        $RETURNTYPE $METHOD(Object $PARAM) throws RemoteException;
      }
  - id: insecure-smtp-connection
    metadata:
      cwe: "CWE-297: Improper Validation of Certificate with Host Mismatch"
      owasp: "A6: Security Misconfiguration"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#INSECURE_SMTP_SSL
      category: security
      technology:
        - java
    message:
      Insecure SMTP connection detected. This connection will trust any SSL certificate.
      Enable certificate verification by setting 'email.setSSLCheckServerIdentity(true)'.
    severity: WARNING
    patterns:
      - pattern-not-inside: |
          $EMAIL.setSSLCheckServerIdentity(true);
          ...
      - pattern-inside: |
          $EMAIL = new SimpleEmail(...);
          ...
      - pattern: "$EMAIL.send(...);"
    languages:
      - java
  - id: weak-ssl-version
    message:
      An insecure SSL version was detected. TLS versions 1.0, 1.1, and all SSL
      versions are considered weak encryption and are deprecated. Use 'ssl.PROTOCOL_TLSv1_2'
      or higher.
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/plugins/insecure_ssl_tls.py#L30
      asvs:
        section: V9 Communications Verification Requirements
        control_id: 9.1.3 Weak TLS
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v91-client-communications-security-requirements
        version: "4"
      references:
        - https://tools.ietf.org/html/rfc7568
        - https://tools.ietf.org/id/draft-ietf-tls-oldversions-deprecate-02.html
        - https://docs.python.org/3/library/ssl.html#ssl.PROTOCOL_TLSv1_2
      category: security
      technology:
        - python
    languages:
      - python
    severity: WARNING
    pattern-either:
      - pattern: ssl.PROTOCOL_SSLv2
      - pattern: ssl.PROTOCOL_SSLv3
      - pattern: ssl.PROTOCOL_TLSv1
      - pattern: ssl.PROTOCOL_TLSv1_1
      - pattern: pyOpenSSL.SSL.SSLv2_METHOD
      - pattern: pyOpenSSL.SSL.SSLv23_METHOD
      - pattern: pyOpenSSL.SSL.SSLv3_METHOD
      - pattern: pyOpenSSL.SSL.TLSv1_METHOD
      - pattern: pyOpenSSL.SSL.TLSv1_1_METHOD
  - id: ssl-v3-is-insecure
    message:
      SSLv3 is insecure because it has known vulnerabilities. Starting with go1.14,
      SSLv3 will be removed. Instead, use 'tls.VersionTLS13'.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A9: Using Components with Known Vulnerabilities"
      source-rule-url: https://github.com/securego/gosec/blob/master/rules/tls_config.go
      references:
        - https://golang.org/doc/go1.14#crypto/tls
        - https://www.us-cert.gov/ncas/alerts/TA14-290A
      category: security
      technology:
        - go
      confidence: HIGH
    languages:
      - go
    severity: WARNING
    fix-regex:
      regex: VersionSSL30
      replacement: VersionTLS13
    pattern: "tls.Config{..., MinVersion: $TLS.VersionSSL30, ...}"
  - id: servletresponse-writer-xss
    message:
      "Cross-site scripting detected in HttpServletResponse writer with variable
      '$VAR'. User input was detected going directly from the HttpServletRequest into
      output. Ensure your data is properly encoded using org.owasp.encoder.Encode.forHtml:
      'Encode.forHtml($VAR)'."
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#XSS_SERVLET
      category: security
      technology:
        - java
    severity: ERROR
    patterns:
      - pattern-inside: "$TYPE $FUNC(..., HttpServletResponse $RESP, ...) { ... }"
      - pattern-inside: "$VAR = $REQ.getParameter(...); ..."
      - pattern-either:
          - pattern: "$RESP.getWriter(...).write(..., $VAR, ...);"
          - pattern: |
              $WRITER = $RESP.getWriter(...);
              ...
              $WRITER.write(..., $VAR, ...);
    languages:
      - java
  - id: xss-html-email-body
    message:
      Found request data in an EmailMessage that is set to use HTML. This is
      dangerous because HTML emails are susceptible to XSS. An attacker could inject
      data into this HTML email, causing XSS.
    metadata:
      cwe:
        "CWE-74: Improper Neutralization of Special Elements in Output Used by a
        Downstream Component ('Injection')"
      owasp: "A1: Injection"
      references:
        - https://www.damonkohler.com/2008/12/email-injection.html
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    patterns:
      - pattern-inside: |
          def $FUNC(...):
            ...
            $EMAIL.content_subtype = "html"
            ...
      - pattern-either:
          - pattern: django.core.mail.EmailMessage($SUBJ, request.$W.get(...), ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.EmailMessage($SUBJ, $DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.EmailMessage($SUBJ, $B.$C(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $B.$C(..., $DATA, ...)
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.EmailMessage($SUBJ, $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.EmailMessage($SUBJ, f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: "$A = django.core.mail.EmailMessage($SUBJ, request.$W.get(...), ...)"
          - pattern: return django.core.mail.EmailMessage($SUBJ, request.$W.get(...), ...)
          - pattern: django.core.mail.EmailMessage($SUBJ, request.$W(...), ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.EmailMessage($SUBJ, $DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.EmailMessage($SUBJ, $B.$C(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $B.$C(..., $DATA, ...)
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.EmailMessage($SUBJ, $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.EmailMessage($SUBJ, f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: "$A = django.core.mail.EmailMessage($SUBJ, request.$W(...), ...)"
          - pattern: return django.core.mail.EmailMessage($SUBJ, request.$W(...), ...)
          - pattern: django.core.mail.EmailMessage($SUBJ, request.$W[...], ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.EmailMessage($SUBJ, $DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.EmailMessage($SUBJ, $B.$C(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $B.$C(..., $DATA, ...)
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.EmailMessage($SUBJ, $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.EmailMessage($SUBJ, f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: "$A = django.core.mail.EmailMessage($SUBJ, request.$W[...], ...)"
          - pattern: return django.core.mail.EmailMessage($SUBJ, request.$W[...], ...)
          - pattern: django.core.mail.EmailMessage($SUBJ, request.$W, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.EmailMessage($SUBJ, $DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.EmailMessage($SUBJ, $B.$C(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $B.$C(..., $DATA, ...)
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.EmailMessage($SUBJ, $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.EmailMessage($SUBJ, f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.EmailMessage($SUBJ, $INTERM, ...)
          - pattern: "$A = django.core.mail.EmailMessage($SUBJ, request.$W, ...)"
          - pattern: return django.core.mail.EmailMessage($SUBJ, request.$W, ...)
  - id: multiple-entrypoint-instructions
    severity: ERROR
    languages:
      - generic
    pattern: |
      ENTRYPOINT ...
      ...
      ENTRYPOINT ...
    message:
      Multiple ENTRYPOINT instructions were found. Only the last one will take
      effect.
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL4004
      references:
        - https://github.com/hadolint/hadolint/wiki/DL4004
        - https://kapeli.com/cheat_sheets/Dockerfile.docset/Contents/Resources/Documents/index#//dash_ref_Instructions/Entry/ENTRYPOINT/0
      category: correctness
      technology:
        - dockerfile
    paths:
      include:
        - "*dockerfile*"
        - "*Dockerfile*"
  - id: insecure-cipher-algorithm-rc4
    pattern: cryptography.hazmat.primitives.ciphers.algorithms.ARC4(...)
    message:
      Detected RC4 cipher algorithm which is considered insecure. The algorithm
      has many known vulnerabilities. Use AES instead.
    metadata:
      source-rule-url: https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L94
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      bandit-code: B304
      references:
        - https://crypto.stackexchange.com/questions/853/google-is-using-rc4-but-isnt-rc4-considered-unsafe
        - https://sweet32.info/
      category: security
      technology:
        - cryptography
    severity: WARNING
    languages:
      - python
  - id: wip-xss-using-responsewriter-and-printf
    patterns:
      - pattern-inside: |
          func $FUNC(..., $W http.ResponseWriter, ...) {
            ...
            var $TEMPLATE = "..."
            ...
            $W.Write([]byte(fmt.$PRINTF($TEMPLATE, ...)), ...)
            ...
          }
      - pattern-either:
          - pattern: |
              $PARAMS = r.URL.Query()
              ...
              $DATA, $ERR := $PARAMS[...]
              ...
              $INTERM = $ANYTHING(..., $DATA, ...)
              ...
              $W.Write([]byte(fmt.$PRINTF(..., $INTERM, ...)))
          - pattern: |
              $PARAMS = r.URL.Query()
              ...
              $DATA, $ERR := $PARAMS[...]
              ...
              $INTERM = $DATA[...]
              ...
              $W.Write([]byte(fmt.$PRINTF(..., $INTERM, ...)))
          - pattern: |
              $DATA, $ERR := r.URL.Query()[...]
              ...
              $INTERM = $DATA[...]
              ...
              $W.Write([]byte(fmt.$PRINTF(..., $INTERM, ...)))
          - pattern: |
              $DATA, $ERR := r.URL.Query()[...]
              ...
              $INTERM = $ANYTHING(..., $DATA, ...)
              ...
              $W.Write([]byte(fmt.$PRINTF(..., $INTERM, ...)))
          - pattern: |
              $PARAMS = r.URL.Query()
              ...
              $DATA, $ERR := $PARAMS[...]
              ...
              $W.Write([]byte(fmt.$PRINTF(..., $DATA, ...)))
    message:
      Found data going from url query parameters into formatted data written
      to ResponseWriter. This could be XSS and should not be done. If you must do this,
      ensure your data is sanitized or escaped.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      category: security
      technology:
        - go
      confidence: MEDIUM
    severity: WARNING
    languages:
      - go
  - id: jwt-none-alg
    message:
      Detected use of the 'none' algorithm in a JWT token. The 'none' algorithm
      assumes the integrity of the token has already been verified. This would allow
      a malicious actor to forge a JWT token that will automatically be verified. Do
      not explicitly use the 'none' algorithm. Instead, use an algorithm such as 'HS256'.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.3 Insecue Stateless Session Tokens
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      category: security
      technology:
        - jose
        - jwt
    languages:
      - javascript
      - typescript
    severity: ERROR
    pattern-either:
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $T = JWT.verify($P, JWK.None,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          $T = JWT.verify($P, JWK.None,...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          JWT.verify($P, JWK.None,...);
  - id: dangerous-template-string
    message:
      Found a template created with string formatting. This is susceptible to
      server-side template injection and cross-site scripting attacks.
    metadata:
      cwe:
        "CWE-96: Improper Neutralization of Directives in Statically Saved Code ('Static
        Code Injection')"
      owasp: "A1: Injection"
      references:
        - https://nvisium.com/blog/2016/03/09/exploring-ssti-in-flask-jinja2.html
        - https://pequalsnp-team.github.io/cheatsheet/flask-jinja2-ssti
      category: security
      technology:
        - flask
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: |
          $V = "...".format(...)
          ...
          flask.render_template_string($V, ...)
      - pattern: |
          $V = "...".format(...)
          ...
          return flask.render_template_string($V, ...), $MORE
      - pattern: |
          $V = "..." % $S
          ...
          flask.render_template_string($V, ...)
      - pattern: |
          $V = "..." % $S
          ...
          return flask.render_template_string($V, ...), $MORE
      - pattern: |
          $V = "..."
          ...
          $V += $O
          ...
          flask.render_template_string($V, ...)
      - pattern: |
          $V = "..."
          ...
          $V += $O
          ...
          return flask.render_template_string($V, ...), $MORE
      - pattern: |
          $V = f"...{$X}..."
          ...
          flask.render_template_string($V, ...)
      - pattern: |
          $V = f"...{$X}..."
          ...
          return flask.render_template_string($V, ...), $CODE
  - id: tls-with-insecure-cipher
    message:
      Detected an insecure CipherSuite via the 'tls' module. This suite is considered
      weak. Use the function 'tls.CipherSuites()' to get a list of good cipher suites.
      See https://golang.org/pkg/crypto/tls/#InsecureCipherSuites for why and what other
      cipher suites to use.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A9: Using Components with Known Vulnerabilities"
      source-rule-url: https://github.com/securego/gosec/blob/master/rules/tls.go
      references:
        - https://golang.org/pkg/crypto/tls/#InsecureCipherSuites
      category: security
      technology:
        - go
      confidence: HIGH
    languages:
      - go
    severity: WARNING
    pattern-either:
      - pattern:
          "tls.Config{..., CipherSuites: []$TYPE{..., tls.TLS_RSA_WITH_RC4_128_SHA,...}}

          "
      - pattern:
          "tls.Config{..., CipherSuites: []$TYPE{..., tls.TLS_RSA_WITH_AES_128_CBC_SHA256,...}}

          "
      - pattern:
          "tls.Config{..., CipherSuites: []$TYPE{..., tls.TLS_ECDHE_ECDSA_WITH_RC4_128_SHA,...}}

          "
      - pattern:
          "tls.Config{..., CipherSuites: []$TYPE{..., tls.TLS_ECDHE_RSA_WITH_RC4_128_SHA,...}}

          "
      - pattern:
          "tls.Config{..., CipherSuites: []$TYPE{..., tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,...}}

          "
      - pattern:
          "tls.Config{..., CipherSuites: []$TYPE{..., tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,...}}

          "
      - pattern: "tls.CipherSuite{..., TLS_RSA_WITH_RC4_128_SHA ,...}

          "
      - pattern: "tls.CipherSuite{..., TLS_RSA_WITH_AES_128_CBC_SHA256 ,...}

          "
      - pattern: "tls.CipherSuite{..., TLS_ECDHE_ECDSA_WITH_RC4_128_SHA ,...}

          "
      - pattern: "tls.CipherSuite{..., TLS_ECDHE_RSA_WITH_RC4_128_SHA ,...}

          "
      - pattern:
          "tls.CipherSuite{..., TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256 ,...}

          "
      - pattern: tls.CipherSuite{..., TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256 ,...}
  - id: ssrf-requests
    languages:
      - python
    severity: ERROR
    message:
      Data from request object is passed to a new server-side request. This could
      lead to a server-side request forgery (SSRF). To mitigate, ensure that schemes
      and hosts are validated against an allowlist, do not forward the response to the
      user, and ensure proper authentication and transport-layer security in the proxied
      request.
    metadata:
      cwe: "CWE-918: Server-Side Request Forgery (SSRF)"
      owasp: "A1: Injection"
      references:
        - https://owasp.org/www-community/attacks/Server_Side_Request_Forgery
      category: security
      technology:
        - flask
    pattern-either:
      - patterns:
          - pattern: requests.$FUNC(...)
          - pattern-either:
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $ROUTE_FUNC(..., $ROUTEVAR, ...):
                    ...
                    requests.$FUNC(..., <... $ROUTEVAR ...>, ...)
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $ROUTE_FUNC(..., $ROUTEVAR, ...):
                    ...
                    $INTERM = <... $ROUTEVAR ...>
                    ...
                    requests.$FUNC(..., <... $INTERM ...>, ...)
      - pattern: requests.$FUNC(..., <... flask.request.$W.get(...) ...>, ...)
      - pattern: requests.$FUNC(..., <... flask.request.$W[...] ...>, ...)
      - pattern: requests.$FUNC(..., <... flask.request.$W(...) ...>, ...)
      - pattern: requests.$FUNC(..., <... flask.request.$W ...>, ...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W.get(...) ...>
              ...
              requests.$FUNC(<... $INTERM ...>, ...)
          - pattern: requests.$FUNC(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W[...] ...>
              ...
              requests.$FUNC(<... $INTERM ...>, ...)
          - pattern: requests.$FUNC(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W(...) ...>
              ...
              requests.$FUNC(<... $INTERM ...>, ...)
          - pattern: requests.$FUNC(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W ...>
              ...
              requests.$FUNC(<... $INTERM ...>, ...)
          - pattern: requests.$FUNC(...)
  - id: insecure-cipher-mode-ecb
    pattern: cryptography.hazmat.primitives.ciphers.modes.ECB(...)
    message:
      Detected ECB cipher mode which is considered insecure. The algorithm can
      potentially leak information about the plaintext. Use CBC mode instead.
    metadata:
      source-rule-url: https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L101
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      bandit-code: B305
      references:
        - https://crypto.stackexchange.com/questions/20941/why-shouldnt-i-use-ecb-encryption
      category: security
      technology:
        - cryptography
    severity: WARNING
    languages:
      - python
  - id: httpservlet-path-traversal
    metadata:
      cwe:
        "CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path
        Traversal')"
      owasp: "A1: Injection"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#PATH_TRAVERSAL_IN
      references:
        - https://www.owasp.org/index.php/Path_Traversal
      category: security
      technology:
        - java
    message:
      Detected a potential path traversal. A malicious actor could control the
      location of this file, to include going backwards in the directory with '../'.
      To address this, ensure that user-controlled variables in file paths are sanitized.
      You may also consider using a utility method such as org.apache.commons.io.FilenameUtils.getName(...)
      to only retrieve the file name from the path.
    patterns:
      - pattern-inside: |
          $RETURNTYPE $FUNC (..., HttpServletRequest $REQ, ...) {
            ...
          }
      - pattern-either:
          - pattern: |
              $VAR = ($TYPE)$REQ.getParameter(...);
              ...
              new File(..., $VAR, ...);
          - pattern: |
              $VAR = $REQ.getParameter(...);
              ...
              new File(..., $VAR, ...);
    severity: ERROR
    languages:
      - java
  - id: unverified-ssl-context
    pattern: ssl._create_unverified_context(...)
    message:
      Unverified SSL context detected. This will permit insecure connections
      without verifying SSL certificates. Use 'ssl.create_default_context()' instead.
    metadata:
      owasp: "A6: Security Misconfiguration"
      cwe: "CWE-295: Improper Certificate Validation"
      references:
        - https://docs.python.org/3/library/ssl.html#ssl-security
        - https://docs.python.org/3/library/http.client.html#http.client.HTTPSConnection
      category: security
      technology:
        - python
    severity: ERROR
    languages:
      - python
  - id: insecure-deserialization
    metadata:
      owasp: "A8: Insecure Deserialization"
      cwe: "CWE-502: Deserialization of Untrusted Data"
      references:
        - https://docs.python.org/3/library/pickle.html
      category: security
      technology:
        - flask
    message:
      Detected the use of an insecure deserialization library in a Flask route.
      These libraries are prone to code execution vulnerabilities. Ensure user data
      does not enter this function. To fix this, try to avoid serializing whole objects.
      Consider instead using a serializer such as JSON.
    languages:
      - python
    severity: ERROR
    patterns:
      - pattern-inside: |
          @app.route(...)
          def $X(...):
            ...
      - pattern-not: $MODULE.$FUNC("...")
      - pattern-not: $MODULE.$FUNC(open("...", ...))
      - pattern-either:
          - pattern: pickle.$FUNC(...)
          - pattern: _pickle.$FUNC(...)
          - pattern: cPickle.$FUNC(...)
          - pattern: dill.$FUNC(...)
          - pattern: shelve.$FUNC(...)
          - pattern: yaml.load(...)
  - id: use-of-sha1
    message:
      Detected SHA1 hash algorithm which is considered insecure. SHA1 is not
      collision resistant and is therefore not suitable as a cryptographic signature.
      Use SHA256 or SHA3 instead.
    languages:
      - java
    severity: WARNING
    metadata:
      owasp: "A9: Using Components with Known Vulnerabilities"
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#WEAK_MESSAGE_DIGEST_SHA1
      asvs:
        section: V6 Stored Cryptography Verification Requirements
        control_id: 6.2.5 Insecure Algorithm
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms
        version: "4"
      category: security
      technology:
        - java
    pattern-either:
      - pattern: 'MessageDigest $VAR = $MD.getInstance("SHA1");

          '
      - pattern: "$DU.getSha1Digest().digest(...)\n"
  - id: hardcoded-jwt-secret
    message:
      "Hardcoded JWT secret or private key is used. This is a Insufficiently
      Protected Credentials weakness: https://cwe.mitre.org/data/definitions/522.html
      Consider using an appropriate security mechanism to protect the credentials (e.g.
      keeping secrets in environment variables: process.env.SECRET)"
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.2 Static API keys or secret
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      category: security
      technology:
        - jose
        - jwt
    languages:
      - javascript
      - typescript
    severity: ERROR
    pattern-either:
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          JWT.verify($P, "...", ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $T = JWT.sign($P, "...", ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $T = JWT.verify($P, "...", ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          JWT.verify($P, JWK.asKey("..."), ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $KEY = JWK.asKey("...");
          ...
          JWT.verify($P, $KEY, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $T = JWT.sign($P, JWK.asKey("..."), ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $T = JWT.verify($P, JWK.asKey("..."), ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $KEY = JWK.asKey("...");
          ...
          var $T = JWT.sign($P, $KEY, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $KEY = JWK.asKey("...");
          ...
          var $T = JWT.verify($P, $KEY, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          JWT.verify($P, $SECRET, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          var $T = JWT.sign($P, $SECRET, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          var $T = JWT.verify($P, $SECRET, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          JWT.verify($P, JWK.asKey($SECRET), ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          var $KEY = JWK.asKey($SECRET);
          ...
          JWT.verify($P, $KEY, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          var $T = JWT.sign($P, JWK.asKey($SECRET), ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          var $KEY = JWK.asKey($SECRET);
          ...
          var $T = JWT.sign($P, $KEY, ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          var $T = JWT.verify($P, JWK.asKey($SECRET), ...);
      - pattern: |
          var $JOSE = require("jose");
          ...
          var { JWK, JWT } = $JOSE;
          ...
          var $SECRET = "...";
          ...
          var $KEY = JWK.asKey($SECRET);
          ...
          var $T = JWT.verify($P, $KEY, ...);
  - id: os-system-injection
    languages:
      - python
    severity: ERROR
    message:
      User data detected in os.system. This could be vulnerable to a command
      injection and should be avoided. If this must be done, use the 'subprocess' module
      instead and pass the arguments as a list.
    metadata:
      cwe:
        "CWE-78: Improper Neutralization of Special Elements used in an OS Command
        ('OS Command Injection')"
      owasp: "A1: Injection"
      references:
        - https://owasp.org/www-community/attacks/Command_Injection
      category: security
      technology:
        - flask
    pattern-either:
      - patterns:
          - pattern: os.system(...)
          - pattern-either:
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    os.system(..., <... $ROUTEVAR ...>, ...)
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    $INTERM = <... $ROUTEVAR ...>
                    ...
                    os.system(..., <... $INTERM ...>, ...)
      - pattern: os.system(..., <... flask.request.$W.get(...) ...>, ...)
      - pattern: os.system(..., <... flask.request.$W[...] ...>, ...)
      - pattern: os.system(..., <... flask.request.$W(...) ...>, ...)
      - pattern: os.system(..., <... flask.request.$W ...>, ...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W.get(...) ...>
              ...
              os.system(<... $INTERM ...>)
          - pattern: os.system(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W[...] ...>
              ...
              os.system(<... $INTERM ...>)
          - pattern: os.system(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W(...) ...>
              ...
              os.system(<... $INTERM ...>)
          - pattern: os.system(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W ...>
              ...
              os.system(<... $INTERM ...>)
          - pattern: os.system(...)
  - id: insecure-module-used
    message:
      Detected use of an insecure cryptographic hashing method. This method is
      known to be broken and easily compromised. Use SHA256 or SHA3 instead.
    metadata:
      owasp: "A9: Using Components with Known Vulnerabilities"
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      source-rule-url: https://github.com/securego/gosec
      references:
        - https://godoc.org/golang.org/x/crypto/sha3
      category: security
      technology:
        - go
      confidence: MEDIUM
    languages:
      - go
    severity: WARNING
    pattern-either:
      - patterns:
          - pattern-inside: |
              import "crypto/md5"
              ...
          - pattern: "md5.$FUNC(...)

              "
      - patterns:
          - pattern-inside: |
              import "crypto/des"
              ...
          - pattern: "des.$FUNC(...)

              "
      - patterns:
          - pattern-inside: |
              import "crypto/sha1"
              ...
          - pattern: "sha1.$FUNC(...)

              "
      - patterns:
          - pattern-inside: |
              import "crypto/rc4"
              ...
          - pattern: "rc4.$FUNC(...)

              "
      - patterns:
          - pattern-inside: |
              import "net/http/cgi"
              ...
          - pattern: "cgi.$FUNC(...)

              "
  - id: use-of-sha1
    message:
      Detected SHA1 hash algorithm which is considered insecure. SHA1 is not
      collision resistant and is therefore not suitable as a cryptographic signature.
      Use SHA256 or SHA3 instead.
    languages:
      - java
    severity: WARNING
    metadata:
      owasp: "A9: Using Components with Known Vulnerabilities"
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#WEAK_MESSAGE_DIGEST_SHA1
      asvs:
        section: V6 Stored Cryptography Verification Requirements
        control_id: 6.2.5 Insecure Algorithm
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms
        version: "4"
      category: security
      technology:
        - java
    pattern-either:
      - pattern: 'MessageDigest $VAR = $MD.getInstance("SHA1");

          '
      - pattern: "$DU.getSha1Digest().digest(...)\n"
  - id: command-injection-os-system
    message:
      Request data detected in os.system. This could be vulnerable to a command
      injection and should be avoided. If this must be done, use the 'subprocess' module
      instead and pass the arguments as a list. See https://owasp.org/www-community/attacks/Command_Injection
      for more information.
    metadata:
      cwe:
        "CWE-78: Improper Neutralization of Special Elements used in an OS Command
        ('OS Command Injection')"
      owasp: "A1: Injection"
      references:
        - https://owasp.org/www-community/attacks/Command_Injection
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    patterns:
      - pattern-inside: |
          def $FUNC(...):
            ...
      - pattern-either:
          - pattern: os.system(..., request.$W.get(...), ...)
          - pattern: os.system(..., $S.format(..., request.$W.get(...), ...), ...)
          - pattern: os.system(..., $S % request.$W.get(...), ...)
          - pattern: os.system(..., f"...{request.$W.get(...)}...", ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              os.system(..., $DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              os.system(..., $STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              os.system(..., $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $STR % $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              os.system(..., f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = f"...{$DATA}..."
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              os.system(..., $STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $STR + $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: "$A = os.system(..., request.$W.get(...), ...)"
          - pattern: "$A = os.system(..., $S.format(..., request.$W.get(...), ...), ...)"
          - pattern: "$A = os.system(..., $S % request.$W.get(...), ...)"
          - pattern: $A = os.system(..., f"...{request.$W.get(...)}...", ...)
          - pattern: return os.system(..., request.$W.get(...), ...)
          - pattern: return os.system(..., $S.format(..., request.$W.get(...), ...), ...)
          - pattern: return os.system(..., $S % request.$W.get(...), ...)
          - pattern: return os.system(..., f"...{request.$W.get(...)}...", ...)
          - pattern: os.system(..., request.$W(...), ...)
          - pattern: os.system(..., $S.format(..., request.$W(...), ...), ...)
          - pattern: os.system(..., $S % request.$W(...), ...)
          - pattern: os.system(..., f"...{request.$W(...)}...", ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              os.system(..., $DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              os.system(..., $STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              os.system(..., $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $STR % $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              os.system(..., f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = f"...{$DATA}..."
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              os.system(..., $STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $STR + $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: "$A = os.system(..., request.$W(...), ...)"
          - pattern: "$A = os.system(..., $S.format(..., request.$W(...), ...), ...)"
          - pattern: "$A = os.system(..., $S % request.$W(...), ...)"
          - pattern: $A = os.system(..., f"...{request.$W(...)}...", ...)
          - pattern: return os.system(..., request.$W(...), ...)
          - pattern: return os.system(..., $S.format(..., request.$W(...), ...), ...)
          - pattern: return os.system(..., $S % request.$W(...), ...)
          - pattern: return os.system(..., f"...{request.$W(...)}...", ...)
          - pattern: os.system(..., request.$W[...], ...)
          - pattern: os.system(..., $S.format(..., request.$W[...], ...), ...)
          - pattern: os.system(..., $S % request.$W[...], ...)
          - pattern: os.system(..., f"...{request.$W[...]}...", ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              os.system(..., $DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              os.system(..., $STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              os.system(..., $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $STR % $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              os.system(..., f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = f"...{$DATA}..."
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              os.system(..., $STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $STR + $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: "$A = os.system(..., request.$W[...], ...)"
          - pattern: "$A = os.system(..., $S.format(..., request.$W[...], ...), ...)"
          - pattern: "$A = os.system(..., $S % request.$W[...], ...)"
          - pattern: $A = os.system(..., f"...{request.$W[...]}...", ...)
          - pattern: return os.system(..., request.$W[...], ...)
          - pattern: return os.system(..., $S.format(..., request.$W[...], ...), ...)
          - pattern: return os.system(..., $S % request.$W[...], ...)
          - pattern: return os.system(..., f"...{request.$W[...]}...", ...)
          - pattern: os.system(..., request.$W, ...)
          - pattern: os.system(..., $S.format(..., request.$W, ...), ...)
          - pattern: os.system(..., $S % request.$W, ...)
          - pattern: os.system(..., f"...{request.$W}...", ...)
          - pattern: |
              $DATA = request.$W
              ...
              os.system(..., $DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              os.system(..., $STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              os.system(..., $STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $STR % $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              os.system(..., f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = f"...{$DATA}..."
              ...
              os.system(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              os.system(..., $STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $STR + $DATA
              ...
              os.system(..., $INTERM, ...)
          - pattern: "$A = os.system(..., request.$W, ...)"
          - pattern: "$A = os.system(..., $S.format(..., request.$W, ...), ...)"
          - pattern: "$A = os.system(..., $S % request.$W, ...)"
          - pattern: $A = os.system(..., f"...{request.$W}...", ...)
          - pattern: return os.system(..., request.$W, ...)
          - pattern: return os.system(..., $S.format(..., request.$W, ...), ...)
          - pattern: return os.system(..., $S % request.$W, ...)
          - pattern: return os.system(..., f"...{request.$W}...", ...)
  - id: permissive-cors
    message:
      https://find-sec-bugs.github.io/bugs.htm#PERMISSIVE_CORS Permissive CORS
      policy will allow a malicious application to communicate with the victim application
      in an inappropriate way, leading to spoofing, data theft, relay and other attacks.
    metadata:
      asvs:
        section: "V14: Configuration Verification Requirements"
        control_id: 14.4.8 Permissive CORS
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x22-V14-Config.md#v144-http-security-headers-requirements
        version: "4"
      category: security
      technology:
        - java
    severity: WARNING
    languages:
      - java
    pattern-either:
      - pattern: |
          HttpServletResponse $RES = ...;
          ...
          $RES.addHeader("=~/access-control-allow-origin/i", "=~/^*|null$/i");
      - pattern: |
          HttpServletResponse $RES = ...;
          ...
          $RES.setHeader("=~/access-control-allow-origin/i", "=~/^*|null$/i");
      - pattern: |
          ServerHttpResponse $RES = ...;
          ...
          $RES.getHeaders().add("=~/access-control-allow-origin/i", "=~/^*|null$/i");
      - pattern: |
          HttpHeaders $HEADERS = ...;
          ...
          $HEADERS.set("=~/access-control-allow-origin/i", "=~/^*|null$/i");
      - pattern: |
          ServerWebExchange $SWE = ...;
          ...
          $SWE.getResponse().getHeaders().add("Access-Control-Allow-Origin", "*");
      - pattern: |
          $X $METHOD(...,HttpServletResponse $RES,...) {
            ...
            $RES.addHeader("=~/access-control-allow-origin/i", "=~/^*|null$/i");
            ...
          }
      - pattern: |
          $X $METHOD(...,HttpServletResponse $RES,...) {
            ...
            $RES.setHeader("=~/access-control-allow-origin/i", "=~/^*|null$/i");
            ...
          }
      - pattern: |
          $X $METHOD(...,ServerHttpResponse $RES,...) {
            ...
            $RES.getHeaders().add("=~/access-control-allow-origin/i", "=~/^*|null$/i");
            ...
          }
      - pattern: |
          $X $METHOD(...,ServerWebExchange $SWE,...) {
            ...
            $SWE.getResponse().getHeaders().add("=~/access-control-allow-origin/i", "=~/^*|null$/i");
            ...
          }
      - pattern: ResponseEntity.$RES().header("=~/access-control-allow-origin/i", "=~/^*|null$/i")
      - pattern: ServerResponse.$RES().header("=~/access-control-allow-origin/i", "=~/^*|null$/i")
  - id: react-dangerouslysetinnerhtml
    pattern-either:
      - pattern: "<$X dangerouslySetInnerHTML=... />\n"
      - pattern: "{dangerouslySetInnerHTML: ...}\n"
    message:
      Detected setting HTML from code. This is risky because it’s easy to inadvertently
      expose your users to a cross-site scripting (XSS) attack. This can lead to attackers
      accessing sensitive information. Instead, do this without dangerouslySetInnerHTML
      or use DOMPurify to santize your HTML.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://reactjs.org/docs/dom-elements.html#dangerouslysetinnerhtml
      category: security
      technology:
        - react
    languages:
      - typescript
      - javascript
    severity: WARNING
  - id: useless-if-conditional
    message:
      Detected an if block that checks for the same condition on both branches
      (`$X`). The second condition check is useless as it is the same as the first,
      and therefore can be removed from the code,
    languages:
      - go
    severity: WARNING
    pattern: |
      if ($X) {
          ...
      } else if ($X) {
          ...
      }
    metadata:
      category: maintainability
      technology:
        - go
  - id: no-static-initialization-vector
    message:
      Initialization Vectors (IVs) for block ciphers should be randomly generated
      each time they are used. Using a static IV means the same plaintext encrypts to
      the same ciphertext every time, weakening the strength of the encryption.
    metadata:
      cwe: "CWE-329: Not Using a Random IV with CBC Mode"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#STATIC_IV
      asvs:
        section: V6 Stored Cryptography Verification Requirements
        control_id: 6.2.5 Insecure Algorithm
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms
        version: "4"
      references:
        - https://cwe.mitre.org/data/definitions/329.html
      category: security
      technology:
        - java
    severity: WARNING
    languages:
      - java
    pattern-either:
      - pattern: |
          byte[] $IV = {
              ...
          };
          ...
          new IvParameterSpec($IV, ...);
      - pattern: |
          class $CLASS {
              byte[] $IV = {
                  ...
              };
              ...
              $METHOD(...) {
                  ...
                  new IvParameterSpec($IV, ...);
                  ...
              }
          }
  - id: unrestricted-request-mapping
    patterns:
      - pattern: |
          @RequestMapping(...)
          $RETURNTYPE $METHOD(...) { ... }
      - pattern-not-inside: |
          @RequestMapping(..., method = $X, ...)
          $RETURNTYPE $METHOD(...) { ... }
    message:
      Detected a method annotated with 'RequestMapping' that does not specify
      the HTTP method. CSRF protections are not enabled for GET, HEAD, TRACE, or OPTIONS,
      and by default all HTTP methods are allowed when the HTTP method is not explicitly
      specified. This means that a method that performs state changes could be vulnerable
      to CSRF attacks. To mitigate, add the 'method' field and specify the HTTP method
      (such as 'RequestMethod.POST').
    severity: WARNING
    metadata:
      cwe: "CWE-352: Cross-Site Request Forgery (CSRF)"
      owasp: "A6: Security Misconfiguration"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING
      references:
        - https://find-sec-bugs.github.io/bugs.htm#SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING
      category: security
      technology:
        - spring
    languages:
      - java
  - id: avoid-ssh-insecure-ignore-host-key
    message:
      Disabled host key verification detected. This allows man-in-the-middle
      attacks. Use the 'golang.org/x/crypto/ssh/knownhosts' package to do host key verification.
      See https://skarlso.github.io/2019/02/17/go-ssh-with-host-key-verification/ to
      learn more about the problem and how to fix it.
    metadata:
      cwe: "CWE-322: Key Exchange without Entity Authentication"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://github.com/securego/gosec
      references:
        - https://skarlso.github.io/2019/02/17/go-ssh-with-host-key-verification/
        - https://gist.github.com/Skarlso/34321a230cf0245018288686c9e70b2d
      category: security
      technology:
        - go
      confidence: MEDIUM
    languages:
      - go
    severity: WARNING
    pattern: ssh.InsecureIgnoreHostKey()
  - id: no-auth-over-http
    fix-regex:
      regex: http:\/\/
      replacement: https://
      count: 1
    message:
      Authentication detected over HTTP. HTTP does not provide any encryption
      or protection for these authentication credentials. This may expose these credentials
      to unauthorized parties. Use 'https://' instead.
    metadata:
      cwe: "CWE-523: Unprotected Transport of Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://pypi.org/project/flake8-flask/
      references:
        - https://blog.r2c.dev/2020/bento-check-no-auth-over-http/
        - https://bento.dev/checks/requests/no-auth-over-http/
      category: security
      technology:
        - requests
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: requests.$W("=~/http:\/\/.*/", ..., auth=$X, ...)
      - pattern: |
          $URL = "=~/http:\/\/.../"
          ...
          requests.$W($URL, ..., auth=$X, ...)
  - id: insecure-cipher-algorithm-rc4
    pattern: cryptography.hazmat.primitives.ciphers.algorithms.ARC4(...)
    message:
      Detected RC4 cipher algorithm which is considered insecure. The algorithm
      has many known vulnerabilities. Use AES instead.
    metadata:
      source-rule-url: https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L94
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      bandit-code: B304
      references:
        - https://crypto.stackexchange.com/questions/853/google-is-using-rc4-but-isnt-rc4-considered-unsafe
        - https://sweet32.info/
      category: security
      technology:
        - cryptography
    severity: WARNING
    languages:
      - python
  - id: path-traversal-inside-zip-extraction
    message: File traversal when extracting zip archive
    metadata:
      cwe:
        "CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path
        Traversal')"
      source_rule_url: https://github.com/securego/gosec/issues/205
      category: security
      technology:
        - go
      confidence: MEDIUM
    languages:
      - go
    severity: WARNING
    pattern: |
      reader, $ERR := zip.OpenReader($ARCHIVE)
      ...
      for _, $FILE := range reader.File {
        ...
        path := filepath.Join($TARGET, $FILE.Name)
        ...
      }
  - id: listen-eval
    languages:
      - python
    message:
      Because portions of the logging configuration are passed through eval(),
      use of this function may open its users to a security risk. While the function
      only binds to a socket on localhost, and so does not accept connections from remote
      machines, there are scenarios where untrusted code could be run under the account
      of the process which calls listen(). To avoid this happening, use the `verify()`
      argument to `listen()` to prevent unrecognized configurations.
    metadata:
      cwe:
        "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code
        ('Eval Injection')"
      owasp: "A6: Security Misconfiguration"
      references:
        - https://docs.python.org/3/library/logging.config.html?highlight=security#logging.config.listen
      category: security
      technology:
        - python
    severity: WARNING
    pattern: logging.config.listen(...)
  - id: xss-send-mail-html-message
    message:
      Found request data in 'send_mail(...)' that uses 'html_message'. This is
      dangerous because HTML emails are susceptible to XSS. An attacker could inject
      data into this HTML email, causing XSS.
    metadata:
      cwe:
        "CWE-74: Improper Neutralization of Special Elements in Output Used by a
        Downstream Component ('Injection')"
      owasp: "A1: Injection"
      references:
        - https://www.damonkohler.com/2008/12/email-injection.html
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    patterns:
      - pattern-inside: |
          def $FUNC(...):
            ...
      - pattern-either:
          - pattern: django.core.mail.send_mail(..., html_message=request.$W.get(...), ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.send_mail(..., html_message=$DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.send_mail(..., html_message=$STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.send_mail(..., html_message=$STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.send_mail(..., html_message=f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              django.core.mail.send_mail(..., html_message=$STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = $STR + $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern:
              "$A = django.core.mail.send_mail(..., html_message=request.$W.get(...),
              ...)"
          - pattern:
              return django.core.mail.send_mail(..., html_message=request.$W.get(...),
              ...)
          - pattern: django.core.mail.send_mail(..., html_message=request.$W(...), ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.send_mail(..., html_message=$DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.send_mail(..., html_message=$STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.send_mail(..., html_message=$STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.send_mail(..., html_message=f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              django.core.mail.send_mail(..., html_message=$STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = $STR + $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern:
              "$A = django.core.mail.send_mail(..., html_message=request.$W(...),
              ...)"
          - pattern:
              return django.core.mail.send_mail(..., html_message=request.$W(...),
              ...)
          - pattern: django.core.mail.send_mail(..., html_message=request.$W[...], ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.send_mail(..., html_message=$DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.send_mail(..., html_message=$STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.send_mail(..., html_message=$STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.send_mail(..., html_message=f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              django.core.mail.send_mail(..., html_message=$STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = $STR + $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern:
              "$A = django.core.mail.send_mail(..., html_message=request.$W[...],
              ...)"
          - pattern:
              return django.core.mail.send_mail(..., html_message=request.$W[...],
              ...)
          - pattern: django.core.mail.send_mail(..., html_message=request.$W, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.send_mail(..., html_message=$DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.send_mail(..., html_message=$STR.format(..., $DATA, ...), ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $STR.format(..., $DATA, ...)
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.send_mail(..., html_message=$STR % $DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $STR % $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.send_mail(..., html_message=f"...{$DATA}...", ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = f"...{$DATA}..."
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              django.core.mail.send_mail(..., html_message=$STR + $DATA, ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = $STR + $DATA
              ...
              django.core.mail.send_mail(..., html_message=$INTERM, ...)
          - pattern: "$A = django.core.mail.send_mail(..., html_message=request.$W, ...)"
          - pattern: return django.core.mail.send_mail(..., html_message=request.$W, ...)
  - id: jax-rs-path-traversal
    metadata:
      owasp: "A1: Injection"
      cwe:
        "CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path
        Traversal')"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#PATH_TRAVERSAL_IN
      references:
        - https://www.owasp.org/index.php/Path_Traversal
      category: security
      technology:
        - jax-rs
    message:
      Detected a potential path traversal. A malicious actor could control the
      location of this file, to include going backwards in the directory with '../'.
      To address this, ensure that user-controlled variables in file paths are sanitized.
      You may also consider using a utility method such as org.apache.commons.io.FilenameUtils.getName(...)
      to only retrieve the file name from the path.
    severity: WARNING
    languages:
      - java
    pattern-either:
      - pattern: |
          $RETURNTYPE $FUNC (..., @PathParam(...) $TYPE $VAR, ...) {
            ...
            new File(..., $VAR, ...);
            ...
          }
      - pattern: |-
          $RETURNTYPE $FUNC (..., @javax.ws.rs.PathParam(...) $TYPE $VAR, ...) {
            ...
            new File(..., $VAR, ...);
            ...
          }
  - id: alias-must-be-unique
    severity: ERROR
    languages:
      - generic
    patterns:
      - pattern-either:
          - pattern: |
              FROM ... as $REF
              ...
              ...
              FROM ... as $REF
          - pattern: |
              FROM ... AS $REF
              ...
              ...
              FROM ... AS $REF
      - pattern-not-inside: |
          FROM ... as $REF
          ...
          ...
          FROM ... as $REF-
      - pattern-not-inside: |
          FROM ... AS $REF
          ...
          ...
          FROM ... AS $REF-
    paths:
      include:
        - "*dockerfile*"
        - "*Dockerfile*"
    message:
      Image aliases must have a unique name, and '$REF' is used twice. Use another
      name for '$REF'.
    metadata:
      source-rule-url: https://github.com/hadolint/hadolint/wiki/DL3024
      references:
        - https://github.com/hadolint/hadolint/wiki/DL3024
      category: correctness
      technology:
        - dockerfile
  - id: use-defused-xml
    fix-regex:
      regex: xml
      replacement: defusedxml
    metadata:
      owasp: "A4: XML External Entities (XXE)"
      cwe: "CWE-611: Improper Restriction of XML External Entity Reference"
      references:
        - https://docs.python.org/3/library/xml.html
        - https://github.com/tiran/defusedxml
      category: security
      technology:
        - python
    message:
      Found use of the native Python XML libraries, which is vulnerable to XML
      external entity (XXE) attacks and can lead to discolsure of confidential data.
      The Python documentation recommends the 'defusedxml' library instead.
    languages:
      - python
    severity: ERROR
    pattern: import xml
  - id: disabled-cert-validation
    message:
      Certificate verification has been explicitly disabled. This permits insecure
      connections to insecure servers. Re-enable certification validation.
    metadata:
      cwe: "CWE-295: Improper Certificate Validation"
      owasp: "A3: Sensitive Data Exposure"
      references:
        - https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib
      category: security
      technology:
        - requests
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: requests.put(..., verify=False, ...)
      - pattern: requests.patch(..., verify=False, ...)
      - pattern: requests.delete(..., verify=False, ...)
      - pattern: requests.head(..., verify=False, ...)
      - pattern: requests.options(..., verify=False, ...)
      - pattern: requests.request(..., verify=False, ...)
      - pattern: requests.get(..., verify=False, ...)
      - pattern: requests.post(..., verify=False, ...)
  - id: xmlinputfactory-external-entities-enabled
    severity: ERROR
    metadata:
      cwe: "CWE-611: Improper Restriction of XML External Entity Reference"
      owasp: "A4: XML External Entities (XXE)"
      asvs:
        section: V5 Validation, Sanitization and Encoding
        control_id: 5.5.2 Insecue XML Deserialization
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v55-deserialization-prevention
        version: "4"
      references:
        - https://www.blackhat.com/docs/us-15/materials/us-15-Wang-FileCry-The-New-Age-Of-XXE-java-wp.pdf
      category: security
      technology:
        - java
    message:
      XML external entities are enabled for this XMLInputFactory. This is vulnerable
      to XML external entity attacks. Disable external entities by setting "javax.xml.stream.isSupportingExternalEntities"
      to false.
    pattern:
      $XMLFACTORY.setProperty("javax.xml.stream.isSupportingExternalEntities",
      true);
    languages:
      - java
  - id: jwt-exposed-credentials
    languages:
      - javascript
      - typescript
    metadata:
      cwe: "CWE-522: Insufficiently Protected Credentials"
      owasp: "A2: Broken Authentication"
      source-rule-url: https://r2c.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.5.2 Static API keys or secret
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v35-token-based-session-management
        version: "4"
      references:
        - https://cwe.mitre.org/data/definitions/522.html
      category: security
      technology:
        - jwt
    message:
      Password is exposed through JWT token payload. This is not encrypted and
      the password could be compromised. Do not store passwords in JWT tokens.
    severity: ERROR
    patterns:
      - pattern-inside: |
          $JWT = require("jsonwebtoken");
          ...
      - pattern-either:
          - pattern: "$JWT.sign({password:...},...);\n"
          - pattern: |
              $P = {password:...};
              ...
              $JWT.sign($P,...);
          - pattern: |
              $P.password = ...;
              ...
              $JWT.sign($P,...);
          - pattern: |
              $P = Object.assign(...,{password:...},...);
              ...
              $JWT.sign($P,...);
          - pattern: "$JWT.sign(Object.assign(...,{password:...},...),...);\n"
          - pattern: "$JWT.sign({$U:{password:...}},...);\n"
          - pattern: |
              $P = {$U:{password:...}};
              ...
              $JWT.sign($P,...);
          - pattern: |
              $P.$U.password = ...;
              ...
              $JWT.sign($P,...);
          - pattern: |
              $P = Object.assign(...,{$U:{password:...}},...);
              ...
              $JWT.sign($P,...);
          - pattern: "$JWT.sign(Object.assign(...,{$U:{password:...}},...),...);\n"
  - id: insecure-hostname-verifier
    message:
      Insecure HostnameVerifier implementation detected. This will accept any
      SSL certificate with any hostname, which creates the possibility for man-in-the-middle
      attacks.
    metadata:
      cwe: "CWE-295: Improper Certificate Validation"
      owasp: "A6: Security Misconfiguration"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#WEAK_HOSTNAME_VERIFIER
      asvs:
        section: V9 Communications Verification Requirements
        control_id: 9.2.1 Weak TLS
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements
        version: "4"
      category: security
      technology:
        - java
    severity: WARNING
    languages:
      - java
    pattern-either:
      - pattern: |
          class $CLASS implements HostnameVerifier {
            ...
            public boolean verify(...) { return true; }
          }
      - pattern: |-
          new HostnameVerifier(...){
            public boolean verify(...) {
              return true;
            }
          }
      - pattern: import org.apache.http.conn.ssl.NoopHostnameVerifier;
  - id: default-mutable-list
    message:
      "Function $F mutates default list $D. Python only instantiates default
      function arguments once and shares the instance across the function calls. If
      the default function argument is mutated, that will modify the instance used by
      all future function calls. This can cause unexpected results, or lead to security
      vulnerabilities whereby one function consumer can view or modify the data of another
      function consumer. Instead, use a default argument (like None) to indicate that
      no argument was provided and instantiate a new list at that time. For example:
      `if $D is None: $D = []`."
    languages:
      - python
    severity: ERROR
    pattern-either:
      - patterns:
          - pattern: |
              def $F(..., $D=[], ...):
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = []
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = [...]
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = list(...)
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = copy.deepcopy($D)
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = copy.copy($D)
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = list.copy($D)
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = $D[:]
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = [... for ... in ...]
                ...
                $D.append(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = $D or []
                ...
                $D.append(...)
          - pattern-not-inside: |
              def $A(...):
                ...
                def $F(..., $D=[], ...):
                  ...
                  $D.append(...)
      - patterns:
          - pattern: |
              def $F(..., $D=[], ...):
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = []
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = [...]
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = list(...)
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = copy.deepcopy($D)
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = copy.copy($D)
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = list.copy($D)
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = $D[:]
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = [... for ... in ...]
                ...
                $D.extend(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = $D or []
                ...
                $D.extend(...)
          - pattern-not-inside: |
              def $A(...):
                ...
                def $F(..., $D=[], ...):
                  ...
                  $D.extend(...)
      - patterns:
          - pattern: |
              def $F(..., $D=[], ...):
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = []
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = [...]
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = list(...)
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = copy.deepcopy($D)
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = copy.copy($D)
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = list.copy($D)
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = $D[:]
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = [... for ... in ...]
                ...
                $D.insert(...)
          - pattern-not: |
              def $F(..., $D=[], ...):
                ...
                $D = $D or []
                ...
                $D.insert(...)
          - pattern-not-inside: |
              def $A(...):
                ...
                def $F(..., $D=[], ...):
                  ...
                  $D.insert(...)
    metadata:
      category: correctness
      technology:
        - python
  - id: weak-ssl-context
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
      source_rule_url: https://find-sec-bugs.github.io/bugs.htm#SSL_CONTEXT
      references:
        - https://tools.ietf.org/html/rfc7568
        - https://tools.ietf.org/id/draft-ietf-tls-oldversions-deprecate-02.html
      category: security
      technology:
        - java
    message:
      An insecure SSL context was detected. TLS versions 1.0, 1.1, and all SSL
      versions are considered weak encryption and are deprecated. Use SSLContext.getInstance("TLSv1.2")
      for the best security.
    severity: WARNING
    languages:
      - java
    patterns:
      - pattern-not: SSLContext.getInstance("TLSv1.3")
      - pattern-not: SSLContext.getInstance("TLSv1.2")
      - pattern: SSLContext.getInstance("...")
    fix-regex:
      regex: "(.*?)\\.getInstance\\(.*?\\)"
      replacement: \1.getInstance("TLSv1.2")
  - id: grpc-server-insecure-connection
    metadata:
      cwe: "CWE-300: Channel Accessible by Non-Endpoint"
      references:
        - https://blog.gopheracademy.com/advent-2019/go-grps-and-tls/#connection-without-encryption
      category: security
      technology:
        - grpc
      confidence: HIGH
    message:
      Found an insecure gRPC server without 'grpc.Creds()' or options with credentials.
      This allows for a connection without encryption to this server. A malicious attacker
      could tamper with the gRPC message, which could compromise the machine. Include
      credentials derived from an SSL certificate in order to create a secure gRPC connection.
      You can create credentials using 'credentials.NewServerTLSFromFile("cert.pem",
      "cert.key")'.
    languages:
      - go
    severity: ERROR
    patterns:
      - pattern-not: grpc.NewServer(..., grpc.Creds(...), ...)
      - pattern-not-inside: |
          $OPTS := []grpc.ServerOption{
            ...,
            grpc.Creds(credentials.NewClientTLSFromCert(...)),
            ...,
          }
          grpc.NewServer($OPTS...)
      - pattern-not-inside: |
          $CREDS := credentials.NewClientTLSFromCert(...)
          ...
          $OPTS := []grpc.ServerOption{
            ...,
            $CREDS,
            ...,
          }
          grpc.NewServer($OPTS...)
      - pattern: grpc.NewServer(...)
  - id: insecure-trust-manager
    metadata:
      cwe: "CWE-295: Improper Certificate Validation"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#WEAK_TRUST_MANAGER
      asvs:
        section: V9 Communications Verification Requirements
        control_id: 9.2.1 Weak TLS
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements
        version: "4"
      references:
        - https://stackoverflow.com/questions/2642777/trusting-all-certificates-using-httpclient-over-https
      category: security
      technology:
        - java
    message:
      Detected empty trust manager implementations. This is dangerous because
      it accepts any certificate, enabling man-in-the-middle attacks. Consider using
      a KeyStore and TrustManagerFactory instead. See https://stackoverflow.com/questions/2642777/trusting-all-certificates-using-httpclient-over-https
      for more information.
    severity: WARNING
    languages:
      - java
    patterns:
      - pattern-either:
          - pattern-inside: |
              class $CLASS implements X509TrustManager {
                ...
              }
          - pattern-inside: |
              new X509TrustManager() {
                ...
              }
          - pattern-inside: |
              class $CLASS implements X509ExtendedTrustManager {
                ...
              }
          - pattern-inside: |
              new X509ExtendedTrustManager() {
                ...
              }
      - pattern-not: public void checkClientTrusted(...) { $SOMETHING; }
      - pattern-not: public void checkServerTrusted(...) { $SOMETHING; }
      - pattern-either:
          - pattern: public void checkClientTrusted(...) {}
          - pattern: public void checkServerTrusted(...) {}
          - pattern: public X509Certificate[] getAcceptedIssuers(...) { return null; }
  - id: insecure-cipher-algorithm-rc4
    pattern: cryptography.hazmat.primitives.ciphers.algorithms.ARC4(...)
    message:
      Detected RC4 cipher algorithm which is considered insecure. The algorithm
      has many known vulnerabilities. Use AES instead.
    metadata:
      source-rule-url: https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L94
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
      bandit-code: B304
      references:
        - https://crypto.stackexchange.com/questions/853/google-is-using-rc4-but-isnt-rc4-considered-unsafe
        - https://sweet32.info/
      category: security
      technology:
        - cryptography
    severity: WARNING
    languages:
      - python
  - id: path-traversal-open
    languages:
      - python
    severity: ERROR
    message:
      Found request data in a call to 'open'. Ensure the request data is validated
      or sanitized, otherwise it could result in path traversal attacks.
    metadata:
      cwe:
        "CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path
        Traversal')"
      owasp: "A1: Injection"
      references:
        - https://owasp.org/www-community/attacks/Path_Traversal
      category: security
      technology:
        - flask
    pattern-either:
      - patterns:
          - pattern: open(...)
          - pattern-either:
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    open(..., <... $ROUTEVAR ...>, ...)
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    with open(..., <... $ROUTEVAR ...>, ...) as $FD:
                      ...
              - pattern-inside: |
                  @$APP.route($ROUTE, ...)
                  def $FUNC(..., $ROUTEVAR, ...):
                    ...
                    $INTERM = <... $ROUTEVAR ...>
                    ...
                    open(..., <... $INTERM ...>, ...)
      - pattern: open(..., <... flask.request.$W.get(...) ...>, ...)
      - pattern: open(..., <... flask.request.$W[...] ...>, ...)
      - pattern: open(..., <... flask.request.$W(...) ...>, ...)
      - pattern: open(..., <... flask.request.$W ...>, ...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W.get(...) ...>
              ...
              open(<... $INTERM ...>, ...)
          - pattern: open(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W[...] ...>
              ...
              open(<... $INTERM ...>, ...)
          - pattern: open(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W(...) ...>
              ...
              open(<... $INTERM ...>, ...)
          - pattern: open(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W ...>
              ...
              open(<... $INTERM ...>, ...)
          - pattern: open(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W.get(...) ...>
              ...
              with open(<... $INTERM ...>, ...) as $F:
                ...
          - pattern: open(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W[...] ...>
              ...
              with open(<... $INTERM ...>, ...) as $F:
                ...
          - pattern: open(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W(...) ...>
              ...
              with open(<... $INTERM ...>, ...) as $F:
                ...
          - pattern: open(...)
      - patterns:
          - pattern-inside: |
              $INTERM = <... flask.request.$W ...>
              ...
              with open(<... $INTERM ...>, ...) as $F:
                ...
          - pattern: open(...)
  - id: use-defused-xmlrpc
    pattern-either:
      - pattern: import xmlrpclib
      - pattern: import SimpleXMLRPCServer
      - pattern: import xmlrpc
    message:
      Detected use of xmlrpc. xmlrpc is not inherently safe from vulnerabilities.
      Use defusedxml.xmlrpc instead.
    metadata:
      cwe:
        "CWE-776: Improper Restriction of Recursive Entity References in DTDs ('XML
        Entity Expansion')"
      owasp: "A4: XML External Entities (XXE)"
      source-rule-url: https://github.com/PyCQA/bandit/blob/07f84cb5f5e7c1055e6feaa0fe93afa471de0ac3/bandit/blacklists/imports.py#L160
      references:
        - https://pypi.org/project/defusedxml/
        - https://docs.python.org/3/library/xml.html#xml-vulnerabilities
      category: security
      technology:
        - python
    severity: ERROR
    languages:
      - python
  - id: grpc-client-insecure-connection
    metadata:
      cwe: "CWE-300: Channel Accessible by Non-Endpoint"
      references:
        - https://blog.gopheracademy.com/advent-2019/go-grps-and-tls/#connection-without-encryption
      category: security
      technology:
        - grpc
      confidence: HIGH
    message:
      "Found an insecure gRPC connection using 'grpc.WithInsecure()'. This
      creates a connection without encryption to a gRPC server. A malicious attacker
      could tamper with the gRPC message, which could compromise the machine. Instead,
      establish a secure connection with an SSL certificate using the 'grpc.WithTransportCredentials()'
      function. You can create a create credentials using a 'tls.Config{}' struct
      with 'credentials.NewTLS()'. The final fix looks like this: 'grpc.WithTransportCredentials(credentials.NewTLS(<config>))'."
    languages:
      - go
    severity: ERROR
    pattern: "$GRPC.Dial($ADDR, ..., $GRPC.WithInsecure(...), ...)"
    fix-regex:
      regex: "(.*)WithInsecure\\(.*?\\)"
      replacement: "\\1WithTransportCredentials(credentials.NewTLS(<your_tls_config_here>))"
  - id: use-none-for-password-default
    message:
      "'$VAR' is using the empty string as its default and is being used to set
      the password on '$MODEL'. If you meant to set an unusable password, set the default
      value to 'None' or call 'set_unusable_password()'."
    metadata:
      cwe: "CWE-521: Weak Password Requirements"
      owasp: "A2: Broken Authentication"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/contrib/auth/#django.contrib.auth.models.User.set_password
      category: security
      technology:
        - django
    fix-regex:
      regex: (def.*|request.*)(""|'')
      replacement: "\\1None"
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: |
          $VAR = request.$W.get($X, "")
          ...
          $MODEL.set_password($VAR)
          ...
          $MODEL.save(...)
      - pattern: |
          def $F(..., $VAR="", ...):
            ...
            $MODEL.set_password($VAR)
  - id: default-mutable-dict
    message:
      "Function $F mutates default dict $D. Python only instantiates default
      function arguments once and shares the instance across the function calls. If
      the default function argument is mutated, that will modify the instance used by
      all future function calls. This can cause unexpected results, or lead to security
      vulnerabilities whereby one function consumer can view or modify the data of another
      function consumer. Instead, use a default argument (like None) to indicate that
      no argument was provided and instantiate a new dictionary at that time. For example:
      `if $D is None: $D = {}`."
    languages:
      - python
    severity: ERROR
    pattern-either:
      - patterns:
          - pattern: |
              def $F(..., $D={}, ...):
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = {}
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = dict(...)
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = $D.copy()
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = copy.deepcopy($D)
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = copy.copy($D)
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = dict.copy($D)
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = {... for ... in ...}
                ...
                $D[...] = ...
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = $D or {}
                ...
                $D[...] = ...
          - pattern-not-inside: |
              def $A(...):
                ...
                def $F(..., $D={}, ...):
                  ...
                  $D[...] = ...
      - patterns:
          - pattern: |
              def $F(..., $D={}, ...):
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = {}
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = dict(...)
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = $D.copy()
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = copy.deepcopy($D)
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = copy.copy($D)
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = dict.copy($D)
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = {... for ... in ...}
                ...
                $D.update(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = $D or {}
                ...
                $D.update(...)
          - pattern-not-inside: |
              def $A(...):
                ...
                def $F(..., $D={}, ...):
                  ...
                  $D.update(...)
      - patterns:
          - pattern: |
              def $F(..., $D={}, ...):
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = {}
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = dict(...)
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = $D.copy()
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = copy.deepcopy($D)
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = copy.copy($D)
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = dict.copy($D)
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = {... for ... in ...}
                ...
                $D.setdefault(...)
          - pattern-not: |
              def $F(..., $D={}, ...):
                ...
                $D = $D or {}
                ...
                $D.setdefault(...)
          - pattern-not-inside: |
              def $A(...):
                ...
                def $F(..., $D={}, ...):
                  ...
                  $D.setdefault(...)
    metadata:
      category: correctness
      technology:
        - python
  - id: defaulthttpclient-is-deprecated
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
      source-rule-url: https://find-sec-bugs.github.io/bugs.htm#DEFAULT_HTTP_CLIENT
      asvs:
        section: V9 Communications Verification Requirements
        control_id: 9.1.3 Weak TLS
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v91-client-communications-security-requirements
        version: "4"
      category: security
      technology:
        - java
    message:
      DefaultHttpClient is deprecated. Further, it does not support connections
      using TLS1.2, which makes using DefaultHttpClient a security hazard. Use SystemDefaultHttpClient
      instead, which supports TLS1.2.
    severity: WARNING
    languages:
      - java
    pattern: new DefaultHttpClient(...);
    fix-regex:
      regex: DefaultHttpClient
      replacement: SystemDefaultHttpClient
  - id: path-traversal-file-name
    message:
      Data from request is passed to a file name `$FILE`.  This is a path traversal
      vulnerability, which can lead to sensitive data being leaked.  To mitigate, consider
      using os.path.abspath or os.path.realpath or the pathlib library.
    metadata:
      cwe:
        "CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path
        Traversal')"
      owasp: "A1: Injection"
      references:
        - https://owasp.org/www-community/attacks/Path_Traversal
      category: security
      technology:
        - django
    patterns:
      - pattern-inside: |
          def $F(...):
            ...
      - pattern-not-inside: |
          def $F(...):
            ...
            os.path.realpath(...)
            ...
      - pattern-not-inside: |
          def $F(...):
            ...
            os.path.abspath(...)
            ...
      - pattern-either:
          - pattern: |
              $V = request.$W.get($X)
              ...
              $FILE % ($V)
          - pattern: |
              $V = request.$W[$X]
              ...
              $FILE % ($V)
          - pattern: |
              $V = request.$W($X)
              ...
              $FILE % ($V)
          - pattern: |
              $V = request.$W
              ...
              $FILE % ($V)
              # match format use cases
          - pattern: |
              $V = request.$W.get($X)
              ...
              $FILE.format(..., $V, ...)
          - pattern: |
              $V = request.$W[$X]
              ...
              $FILE.format(..., $V, ...)
          - pattern: |
              $V = request.$W($X)
              ...
              $FILE.format(..., $V, ...)
          - pattern: |
              $V = request.$W
              ...
              $FILE.format(..., $V, ...)
      - metavariable-regex:
          metavariable: "$FILE"
          regex: ".*\\.(log|zip|txt|csv|xml|html).*"
    languages:
      - python
    severity: WARNING
  - id: yaml_deserialize
    patterns:
      - pattern-inside: |
          require('js-yaml')
          ...
      - pattern: "$X.load(...)\n"
    message:
      User controlled data in 'yaml.load()' function can result in Remote Code
      Injection.
    languages:
      - javascript
    severity: ERROR
    metadata:
      owasp: "A8: Insecure Deserialization"
      cwe: "CWE-502: Deserialization of Untrusted Data"
  - id: handlebars_safestring
    pattern-either:
      - pattern: "$X.SafeString(...)"
      - pattern: new Handlebars.SafeString(...)
    message:
      Handlebars SafeString will not escape the data passed through it. Untrusted
      user input passing through SafeString can cause XSS.
    languages:
      - javascript
    severity: ERROR
    metadata:
      owasp: "A1: Injection"
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
  - id: handlebars_safestring
    pattern-either:
      - pattern: "$X.SafeString(...)"
      - pattern: new Handlebars.SafeString(...)
    message:
      Handlebars SafeString will not escape the data passed through it. Untrusted
      user input passing through SafeString can cause XSS.
    languages:
      - javascript
    severity: ERROR
    metadata:
      owasp: "A1: Injection"
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
  - id: avoid-pyyaml-load
    metadata:
      owasp: "A8: Insecure Deserialization"
      cwe: "CWE-502: Deserialization of Untrusted Data"
      references:
        - https://github.com/yaml/pyyaml/wiki/PyYAML-yaml.load(input)-Deprecation
        - https://nvd.nist.gov/vuln/detail/CVE-2017-18342
      category: security
      technology:
        - pyyaml
    languages:
      - python
    message:
      Avoid using `load()`. `PyYAML.load` can create arbitrary Python objects.
      A malicious actor could exploit this to run arbitrary code. Use `safe_load()`
      instead.
    fix-regex:
      regex: load
      replacement: safe_load
      count: 1
    severity: ERROR
    patterns:
      - pattern-inside: |
          import yaml
          ...
      - pattern-not-inside: |
          $YAML = ruamel.yaml.YAML(...)
          ...
      - pattern-not: yaml.load(..., Loader=yaml.CSafeLoader, ...)
      - pattern-not: yaml.load(..., Loader=yaml.SafeLoader, ...)
      - pattern-not: yaml.load_all(..., Loader=yaml.CSafeLoader, ...)
      - pattern-not: yaml.load_all(..., Loader=yaml.SafeLoader, ...)
      - pattern-either:
          - pattern: yaml.load(...)
          - pattern: yaml.load_all(...)
  - id: avoid-pickle
    metadata:
      owasp: "A8: Insecure Deserialization"
      cwe: "CWE-502: Deserialization of Untrusted Data"
      references:
        - https://docs.python.org/3/library/pickle.html
        - https://davidhamann.de/2020/04/05/exploiting-python-pickle/
      category: security
      technology:
        - python
    languages:
      - python
    message:
      Avoid using `pickle`, which is known to lead to code execution vulnerabilities.
      When unpickling, the serialized data could be manipulated to run arbitrary code.
      Instead, consider serializing the relevant data as JSON or a similar text-based
      serialization format.
    severity: WARNING
    pattern-either:
      - pattern: pickle.$FUNC(...)
      - pattern: _pickle.$FUNC(...)
  - id: unchecked-subprocess-call
    patterns:
      - pattern: subprocess.call(...)
      - pattern-not-inside: "$S = subprocess.call(...)"
      - pattern-not-inside: subprocess.call(...) == $X
      - pattern-not-inside: return subprocess.call(...)
    fix: subprocess.check_call(...)
    message:
      This is not checking the return value of this subprocess call; if it fails
      no exception will be raised. Consider subprocess.check_call() instead
    languages:
      - python
    severity: WARNING
    metadata:
      category: correctness
      technology:
        - python
  - id: raise-not-base-exception
    message:
      In Python3, a runtime `TypeError` will be thrown if you attempt to raise
      an object or class which does not inherit from `BaseException`
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: raise "..."
      - pattern: |
          $X: BaseException
          raise $X(...)
      - patterns:
          - pattern: raise $EXCEPTION
          - metavariable-regex:
              metavariable: "$EXCEPTION"
              regex: "[0-9]*\\.?[0-9]+"
    metadata:
      category: correctness
      technology:
        - python
  - id: http-not-https-connection
    message:
      Detected HTTPConnectionPool. This will transmit data in cleartext. It is
      recommended to use HTTPSConnectionPool instead for to encrypt communications.
    metadata:
      cwe: "CWE-319: Cleartext Transmission of Sensitive Information"
      owasp: "A3: Sensitive Data Exposure"
      references:
        - https://urllib3.readthedocs.io/en/1.2.1/pools.html#urllib3.connectionpool.HTTPSConnectionPool
      category: security
      technology:
        - python
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: urllib3.HTTPConnectionPool(...)
      - pattern: urllib3.connectionpool.HTTPConnectionPool(...)
  - id: use-sys-exit
    languages:
      - python
    message:
      Use `sys.exit` over the python shell `exit` built-in. `exit` is a helper
      for the interactive shell and may not be available on all Python implementations.
      https://stackoverflow.com/questions/6501121/difference-between-exit-and-sys-exit-in-python
    patterns:
      - pattern: exit(...)
      - pattern-not: sys.exit(...)
    severity: WARNING
    fix: sys.exit($X)
    metadata:
      category: correctness
      technology:
        - python
  - id: avoid-pickle
    metadata:
      owasp: "A8: Insecure Deserialization"
      cwe: "CWE-502: Deserialization of Untrusted Data"
      references:
        - https://docs.python.org/3/library/pickle.html
        - https://davidhamann.de/2020/04/05/exploiting-python-pickle/
      category: security
      technology:
        - python
    languages:
      - python
    message:
      Avoid using `pickle`, which is known to lead to code execution vulnerabilities.
      When unpickling, the serialized data could be manipulated to run arbitrary code.
      Instead, consider serializing the relevant data as JSON or a similar text-based
      serialization format.
    severity: WARNING
    pattern-either:
      - pattern: pickle.$FUNC(...)
      - pattern: _pickle.$FUNC(...)
  - id: marshal-usage
    languages:
      - python
    message:
      "The marshal module is not intended to be secure against erroneous or maliciously
      constructed data. Never unmarshal data received from an untrusted or unauthenticated
      source. See more details: https://docs.python.org/3/library/marshal.html?highlight=security"
    metadata:
      cwe: "CWE-502: Deserialization of Untrusted Data"
      owasp: "A8: Insecure Deserialization"
      references:
        - https://docs.python.org/3/library/marshal.html?highlight=security
      category: security
      technology:
        - python
    pattern-either:
      - pattern: marshal.dump(...)
      - pattern: marshal.dumps(...)
      - pattern: marshal.load(...)
      - pattern: marshal.loads(...)
    severity: WARNING
  - id: missing-throttle-config
    patterns:
      - pattern-not-inside: |
          REST_FRAMEWORK = {
            ...,
            "DEFAULT_THROTTLE_RATES": ...
          }
      - pattern: "REST_FRAMEWORK = ...

          "
    message:
      Django REST framework configuration is missing default rate- limiting options.
      This could inadvertently allow resource starvation or Denial of Service (DoS)
      attacks. Add 'DEFAULT_THROTTLE_CLASSES' and 'DEFAULT_THROTTLE_RATES' to add rate-limiting
      to your application.
    metadata:
      owasp: "A6: Security Misconfiguration"
      cwe: "CWE-400: Uncontrolled Resource Consumption"
      references:
        - https://www.django-rest-framework.org/api-guide/throttling/#setting-the-throttling-policy
      category: security
      technology:
        - django
    severity: WARNING
    languages:
      - python
  - id: no-csrf-exempt
    pattern: |
      @django.views.decorators.csrf.csrf_exempt
      def $R(...):
        ...
    message:
      Detected usage of @csrf_exempt, which indicates that there is no CSRF token
      set for this route. This could lead to an attacker manipulating the user's account
      and exfiltration of private data. Instead, create a function without this decorator.
    metadata:
      cwe: "CWE-352: Cross-Site Request Forgery (CSRF)"
      owasp: "A6: Security Misconfiguration"
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
  - id: use-earliest-or-latest
    message:
      Looks like you are only accessing first element of an ordered QuerySet.
      Use `latest()` or `earliest()` instead. See https://docs.djangoproject.com/en/3.0/ref/models/querysets/#django.db.models.query.QuerySet.latest
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: "$X.objects.order_by(...)[0]"
      - pattern: "$X.objects.$FUNC(...).order_by(...)[0]"
      - pattern: "$X.objects.$FUNC(...).$FILTER(...).order_by(...)[0]"
    metadata:
      category: performance
      technology:
        - django
  - id: avoid-raw-sql
    message: "Detected the use of 'RawSQL' or 'raw' indicating the execution of
      a non-parameterized SQL query. This could lead to a SQL injection and therefore
      protected information could be leaked. Instead, use Django ORM and parameterized
      queries before raw SQL. An example of using the Django ORM is: `People.objects.get(name='Bob')`"
    metadata:
      source-rule-url: https://bandit.readthedocs.io/en/latest/plugins/b611_django_rawsql_used.html
      cwe:
        "CWE-89: Improper Neutralization of Special Elements used in an SQL Command
        ('SQL Injection')"
      owasp: "A1: Injection"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/models/expressions/#raw-sql-expressions
        - https://blog.r2c.dev/2020/preventing-sql-injection-a-django-authors-perspective/
      category: security
      technology:
        - django
    languages:
      - python
    severity: ERROR
    patterns:
      - pattern-either:
          - pattern: "$MODEL.objects.raw($QUERY, ...)"
          - pattern: django.db.models.expressions.RawSQL(...)
      - pattern-not: $MODEL.objects.raw("...")
      - pattern-not: django.db.models.expressions.RawSQL("...")
  - id: use-onetoonefield
    patterns:
      - pattern-inside: |
          class $M(...):
            ...
      - pattern: "$F = django.db.models.ForeignKey(..., unique=True, ...)"
    message:
      Use 'django.db.models.OneToOneField' instead of 'ForeignKey' with unique=True.
      'OneToOneField' is used to create one-to-one relationships.
    languages:
      - python
    severity: WARNING
    metadata:
      category: best-practice
      technology:
        - django
  - id: avoid-query-set-extra
    message:
      QuerySet.extra' does not provide safeguards against SQL injection and requires
      very careful use. SQL injection can lead to critical data being stolen by attackers.
      Instead of using '.extra', use the Django ORM and parameterized queries such as
      `People.objects.get(name='Bob')`.
    metadata:
      source-rule-url: https://bandit.readthedocs.io/en/latest/plugins/b610_django_extra_used.html
      cwe:
        "CWE-89: Improper Neutralization of Special Elements used in an SQL Command
        ('SQL Injection')"
      owasp:
        - A01:2017 - Injection
        - A03:2021 - Injection
      references:
        - https://docs.djangoproject.com/en/3.0/ref/models/querysets/#django.db.models.query.QuerySet.extra
        - https://blog.r2c.dev/2020/preventing-sql-injection-a-django-authors-perspective/
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    patterns:
      - pattern: "$MODEL.extra(...)"
      - pattern-not-inside: '$MODEL.extra(select = {$KEY: "..."})'
  - id: user-eval-format-string
    message:
      Found user data in a call to 'eval'. This is extremely dangerous because
      it can enable an attacker to execute remote code. See https://owasp.org/www-community/attacks/Code_Injection
      for more information.
    metadata:
      cwe:
        "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code
        ('Eval Injection')"
      owasp: "A1: Injection"
      references:
        - https://nedbatchelder.com/blog/201206/eval_really_is_dangerous.html
      category: security
      technology:
        - django
    patterns:
      - pattern-inside: |
          def $F(...):
            ...
      - pattern-either:
          - pattern: eval(..., $STR % request.$W.get(...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              eval(..., $STR % $V, ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              $S = $STR % $V
              ...
              eval(..., $S, ...)
          - pattern: eval(..., "..." % request.$W(...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              eval(..., $STR % $V, ...)
          - pattern: |
              $V = request.$W(...)
              ...
              $S = $STR % $V
              ...
              eval(..., $S, ...)
          - pattern: eval(..., $STR % request.$W[...], ...)
          - pattern: |
              $V = request.$W[...]
              ...
              eval(..., $STR % $V, ...)
          - pattern: |
              $V = request.$W[...]
              ...
              $S = $STR % $V
              ...
              eval(..., $S, ...)
          - pattern: eval(..., $STR.format(..., request.$W.get(...), ...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              eval(..., $STR.format(..., $V, ...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              $S = $STR.format(..., $V, ...)
              ...
              eval(..., $S, ...)
          - pattern: eval(..., $STR.format(..., request.$W(...), ...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              eval(..., $STR.format(..., $V, ...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              $S = $STR.format(..., $V, ...)
              ...
              eval(..., $S, ...)
          - pattern: eval(..., $STR.format(..., request.$W[...], ...), ...)
          - pattern: |
              $V = request.$W[...]
              ...
              eval(..., $STR.format(..., $V, ...), ...)
          - pattern: |
              $V = request.$W[...]
              ...
              $S = $STR.format(..., $V, ...)
              ...
              eval(..., $S, ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              eval(..., f"...{$V}...", ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              $S = f"...{$V}..."
              ...
              eval(..., $S, ...)
          - pattern: |
              $V = request.$W(...)
              ...
              eval(..., f"...{$V}...", ...)
          - pattern: |
              $V = request.$W(...)
              ...
              $S = f"...{$V}..."
              ...
              eval(..., $S, ...)
          - pattern: |
              $V = request.$W[...]
              ...
              eval(..., f"...{$V}...", ...)
          - pattern: |
              $V = request.$W[...]
              ...
              $S = f"...{$V}..."
              ...
              eval(..., $S, ...)
    languages:
      - python
    severity: WARNING
  - id: extends-custom-expression
    languages:
      - python
    message:
      "Found extension of custom expression: $CLASS. Extending expressions in
      this way could inadvertently lead to a SQL injection vulnerability, which can
      result in attackers exfiltrating sensitive data. Instead, ensure no user input
      enters this function or that user input is properly sanitized."
    metadata:
      cwe:
        "CWE-89: Improper Neutralization of Special Elements used in an SQL Command
        ('SQL Injection')"
      owasp: "A1: Injection"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/models/expressions/#avoiding-sql-injection
        - https://blog.r2c.dev/2020/preventing-sql-injection-a-django-authors-perspective/
      category: security
      technology:
        - django
    severity: WARNING
    pattern-either:
      - pattern: |
          class $CLASS(..., django.db.models.Func, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Func, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Expression, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Expression, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Value, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Value, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.DurationValue, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.DurationValue, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.RawSQL, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.RawSQL, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Star, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Star, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Random, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Random, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Col, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Col, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Ref, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Ref, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.ExpressionList, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.ExpressionList, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.ExpressionWrapper, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.ExpressionWrapper, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.When, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.When, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Case, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Case, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Subquery, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Subquery, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Exists, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Exists, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.Window, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.Window, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.WindowFrame, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.WindowFrame, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.RowRange, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.RowRange, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.ValueRange, ...):
              ...
      - pattern: |
          class $CLASS(..., django.db.models.expressions.ValueRange, ...):
              ...
  - id: use-json-response
    patterns:
      - pattern-inside: |
          def $X(...):
            ...
      - pattern: |
          $Y = json.dumps(...)
          ...
          django.http.HttpResponse($Y, ...)
    message: Use JsonResponse instead
    languages:
      - python
    severity: ERROR
    metadata:
      category: best-practice
      technology:
        - django
  - id: user-exec-format-string
    message:
      Found user data in a call to 'exec'. This is extremely dangerous because
      it can enable an attacker to execute arbitrary remote code on the system. Instead,
      refactor your code to not use 'eval' and instead use a safe library for the specific
      functionality you need.
    metadata:
      cwe:
        "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code
        ('Eval Injection')"
      owasp: "A1: Injection"
      category: security
      technology:
        - django
      references:
        - https://owasp.org/www-community/attacks/Code_Injection
    patterns:
      - pattern-inside: |
          def $F(...):
            ...
      - pattern-either:
          - pattern: exec(..., $STR % request.$W.get(...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              exec(..., $STR % $V, ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              $S = $STR % $V
              ...
              exec(..., $S, ...)
          - pattern: exec(..., "..." % request.$W(...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              exec(..., $STR % $V, ...)
          - pattern: |
              $V = request.$W(...)
              ...
              $S = $STR % $V
              ...
              exec(..., $S, ...)
          - pattern: exec(..., $STR % request.$W[...], ...)
          - pattern: |
              $V = request.$W[...]
              ...
              exec(..., $STR % $V, ...)
          - pattern: |
              $V = request.$W[...]
              ...
              $S = $STR % $V
              ...
              exec(..., $S, ...)
          - pattern: exec(..., $STR.format(..., request.$W.get(...), ...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              exec(..., $STR.format(..., $V, ...), ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              $S = $STR.format(..., $V, ...)
              ...
              exec(..., $S, ...)
          - pattern: exec(..., $STR.format(..., request.$W(...), ...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              exec(..., $STR.format(..., $V, ...), ...)
          - pattern: |
              $V = request.$W(...)
              ...
              $S = $STR.format(..., $V, ...)
              ...
              exec(..., $S, ...)
          - pattern: exec(..., $STR.format(..., request.$W[...], ...), ...)
          - pattern: |
              $V = request.$W[...]
              ...
              exec(..., $STR.format(..., $V, ...), ...)
          - pattern: |
              $V = request.$W[...]
              ...
              $S = $STR.format(..., $V, ...)
              ...
              exec(..., $S, ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              exec(..., f"...{$V}...", ...)
          - pattern: |
              $V = request.$W.get(...)
              ...
              $S = f"...{$V}..."
              ...
              exec(..., $S, ...)
          - pattern: |
              $V = request.$W(...)
              ...
              exec(..., f"...{$V}...", ...)
          - pattern: |
              $V = request.$W(...)
              ...
              $S = f"...{$V}..."
              ...
              exec(..., $S, ...)
          - pattern: |
              $V = request.$W[...]
              ...
              exec(..., f"...{$V}...", ...)
          - pattern: |
              $V = request.$W[...]
              ...
              $S = f"...{$V}..."
              ...
              exec(..., $S, ...)
          - pattern:
              exec(..., base64.decodestring($S.format(..., request.$W.get(...), ...),
              ...), ...)
          - pattern: exec(..., base64.decodestring($S % request.$W.get(...), ...), ...)
          - pattern:
              exec(..., base64.decodestring(f"...{request.$W.get(...)}...", ...),
              ...)
          - pattern: exec(..., base64.decodestring(request.$W.get(...), ...), ...)
          - pattern:
              exec(..., base64.decodestring(bytes($S.format(..., request.$W.get(...),
              ...), ...), ...), ...)
          - pattern:
              exec(..., base64.decodestring(bytes($S % request.$W.get(...), ...),
              ...), ...)
          - pattern:
              exec(..., base64.decodestring(bytes(f"...{request.$W.get(...)}...",
              ...), ...), ...)
          - pattern:
              exec(..., base64.decodestring(bytes(request.$W.get(...), ...), ...),
              ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              exec(..., base64.decodestring($DATA, ...), ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = base64.decodestring($DATA, ...)
              ...
              exec(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              exec(..., base64.decodestring(bytes($DATA, ...), ...), ...)
          - pattern: |
              $DATA = request.$W.get(...)
              ...
              $INTERM = base64.decodestring(bytes($DATA, ...), ...)
              ...
              exec(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              exec(..., base64.decodestring($DATA, ...), ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = base64.decodestring($DATA, ...)
              ...
              exec(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              exec(..., base64.decodestring(bytes($DATA, ...), ...), ...)
          - pattern: |
              $DATA = request.$W(...)
              ...
              $INTERM = base64.decodestring(bytes($DATA, ...), ...)
              ...
              exec(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              exec(..., base64.decodestring($DATA, ...), ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = base64.decodestring($DATA, ...)
              ...
              exec(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              exec(..., base64.decodestring(bytes($DATA, ...), ...), ...)
          - pattern: |
              $DATA = request.$W[...]
              ...
              $INTERM = base64.decodestring(bytes($DATA, ...), ...)
              ...
              exec(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              exec(..., base64.decodestring($DATA, ...), ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = base64.decodestring($DATA, ...)
              ...
              exec(..., $INTERM, ...)
          - pattern: |
              $DATA = request.$W
              ...
              exec(..., base64.decodestring(bytes($DATA, ...), ...), ...)
          - pattern: |
              $DATA = request.$W
              ...
              $INTERM = base64.decodestring(bytes($DATA, ...), ...)
              ...
              exec(..., $INTERM, ...)
    languages:
      - python
    severity: WARNING
  - id: use-django-environ
    patterns:
      - pattern-not-inside: |
          import environ
          ...
      - pattern-either:
          - pattern: |
              import django
              ...
              import os
              ...
              $FOO = $M.environ[...]
          - pattern: |
              import os
              ...
              import django
              ...
              $FOO = $M.environ[...]
    message:
      You are using environment variables inside django app. Use `django-environ`
      as it a better alternative for deployment.
    languages:
      - python
    severity: ERROR
    metadata:
      category: best-practice
      technology:
        - django
  - id: custom-expression-as-sql
    languages:
      - python
    message:
      Detected a Custom Expression ''$EXPRESSION'' calling ''as_sql(...).'' This
      could lead to SQL injection, which can result in attackers exfiltrating sensitive
      data. Instead, ensure no user input enters this function or that user input is
      properly sanitized.
    metadata:
      cwe:
        "CWE-89: Improper Neutralization of Special Elements used in an SQL Command
        ('SQL Injection')"
      owasp: "A1: Injection"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/models/expressions/#django.db.models.Func.as_sql
        - https://blog.r2c.dev/2020/preventing-sql-injection-a-django-authors-perspective/
      category: security
      technology:
        - django
    pattern: "$EXPRESSION.as_sql(...)"
    severity: WARNING
  - id: use-count-method
    message:
      Looks like you need to determine the number of records. Django provides
      the count() method which is more efficient than .len(). See https://docs.djangoproject.com/en/3.0/ref/models/querysets/
    languages:
      - python
    severity: ERROR
    pattern-either:
      - pattern: "$X.objects.$FUNC(...).len()"
      - pattern: "$X.objects.$FUNC(...).$FILTER().len()"
      - pattern: "$X.objects.$FUNC(...).$FILTER().$UPDATE(...).len()"
    metadata:
      category: performance
      technology:
        - django
  - id: django-secure-set-cookie
    patterns:
      - pattern-either:
          - pattern-inside: |
              import django.http.HttpResponse
              ...
          - pattern-inside: |
              import django.shortcuts.render
              ...
      - pattern-not-inside: |
          LANGUAGE_QUERY_PARAMETER = 'language'
          ...
          def set_language(request):
              ...
          # Exclude vendored contrib/messages/storage/cookie.py
      - pattern-not-inside: |
          class CookieStorage(django.contrib.messages.storage.base.BaseStorage):
              ...
          # Exclude cookies handled by vendored middleware
      - pattern-not: response.set_cookie(django.conf.settings.SESSION_COOKIE_NAME, ...)
      - pattern-not: response.set_cookie(django.conf.settings.CSRF_COOKIE_NAME, ...)
      - pattern-not: response.set_cookie(django.conf.settings.LANGUAGE_COOKIE_NAME, ...)
      - pattern-not:
          response.set_cookie(rest_framework_jwt.settings.api_settings.JWT_AUTH_COOKIE,
          ...)
      - pattern-not: response.set_cookie(..., secure=$A, httponly=$B, samesite=$C, ...)
      - pattern-not: response.set_cookie(..., **$A)
      - pattern: response.set_cookie(...)
    message:
      Django cookies should be handled securely by setting secure=True, httponly=True,
      and samesite='Lax' in response.set_cookie(...). If your situation calls for different
      settings, explicitly disable the setting. If you want to send the cookie over
      http, set secure=False.  If you want to let client-side JavaScript read the cookie,
      set httponly=False. If you want to attach cookies to requests for external sites,
      set samesite=None.
    metadata:
      cwe: "CWE-614: Sensitive Cookie in HTTPS Session Without 'Secure' Attribute"
      owasp: "A3: Sensitive Data Exposure"
      asvs:
        section: "V3: Session Management Verification Requirements"
        control_id: 3.4 Missing Cookie Attributes
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v34-cookie-based-session-management
        version: "4"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/request-response/#django.http.HttpResponse.set_cookie
        - https://blog.r2c.dev/2020/bento-check-keeping-cookies-safe-in-flask/
        - https://bento.dev/checks/flask/secure-set-cookie/
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
  - id: template-autoescape-off
    message:
      Detected a template block where autoescaping is explicitly disabled with
      '{% autoescape off %}'. This allows rendering of raw HTML in this segment. Turn
      autoescaping on to prevent cross-site scripting (XSS). If you must do this, consider
      instead, using `mark_safe` in Python code.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.1/ref/templates/builtins/#autoescape
      category: security
      technology:
        - django
    languages:
      - regex
    paths:
      include:
        - "*.html"
    severity: WARNING
    pattern-regex: "{%\\s+autoescape\\s+off\\s+%}"
  - id: template-unescaped-with-safe
    message:
      Detected a segment of a Flask template where autoescaping is explicitly
      disabled with '| safe' filter. This allows rendering of raw HTML in this segment.
      Ensure no user data is rendered here, otherwise this is a cross-site scripting
      (XSS) vulnerability.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://flask.palletsprojects.com/en/1.1.x/security/#cross-site-scripting-xss
      category: security
      technology:
        - flask
    languages:
      - regex
    paths:
      include:
        - "*.html"
    severity: WARNING
    pattern-regex: "{{.*?\\|\\s*safe(\\s*}})?"
  - id: filter-with-is-safe
    message:
      Detected Django filters flagged with 'is_safe'. 'is_safe' tells Django
      not to apply escaping on the value returned by this filter (although the input
      is escaped). Used improperly, 'is_safe' could expose your application to cross-site
      scripting (XSS) vulnerabilities. Ensure this filter does not 1) add HTML characters,
      2) remove characters, or 3) use external data in any way. Consider instead removing
      'is_safe' and explicitly marking safe content with 'mark_safe()'.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.1/topics/security/#cross-site-scripting-xss-protection
        - https://docs.djangoproject.com/en/3.1/howto/custom-template-tags/#filters-and-auto-escaping
        - https://stackoverflow.com/questions/7665512/why-use-is-safe
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    pattern: |-
      @register.filter(..., is_safe=True, ...)
      def $FILTER(...):
        ...
  - id: context-autoescape-off
    message:
      "Detected a Context with autoescape disabled. If you are rendering any
      web pages, this exposes your application to cross-site scripting (XSS) vulnerabilities.
      Remove 'autoescape: False' or set it to 'True'."
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.1/ref/settings/#templates
        - https://docs.djangoproject.com/en/3.1/topics/templates/#django.template.backends.django.DjangoTemplates
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    fix-regex:
      regex: "(autoescape.*?)False"
      replacement: "\\1True"
    pattern-either:
      - pattern: '{..., "autoescape": False, ...}'
      - pattern: $D["autoescape"] = False
  - id: globals-as-template-context
    languages:
      - python
    message:
      'Using ''globals()'' as a context to ''render(...)'' is extremely dangerous.
      This exposes Python functions to the template that were not meant to be exposed.
      An attacker could use these functions to execute code that was not intended to
      run and could compromise the application. (This is server-side template injection
      (SSTI)). Do not use ''globals()''. Instead, specify each variable in a dictionary
      or ''django.template.Context'' object, like ''{"var1": "hello"}'' and use that
      instead.'
    metadata:
      category: security
      cwe:
        "CWE-96: Improper Neutralization of Directives in Statically Saved Code ('Static
        Code Injection')"
      owasp: "A1: Injection"
      references:
        - https://docs.djangoproject.com/en/3.2/ref/settings/#templates
        - https://docs.djangoproject.com/en/3.2/topics/templates/#django.template.backends.django.DjangoTemplates
        - https://docs.djangoproject.com/en/3.2/ref/templates/api/#rendering-a-context
      technology:
        - django
    pattern-either:
      - pattern: django.shortcuts.render(..., globals(...), ...)
      - pattern: django.template.Template.render(..., globals(...), ...)
      - patterns:
          - pattern-inside: |
              $CONTEXT = globals(...)
              ...
          - pattern-either:
              - pattern: django.shortcuts.render(..., $CONTEXT, ...)
              - pattern: django.template.Template.render(..., $CONTEXT, ...)
    severity: ERROR
  - id: html-safe
    message:
      "`html_safe()` add the `__html__` magic method to the provided class. The
      `__html__` method indicates to the Django template engine that the value is 'safe'
      for rendering. This means that normal HTML escaping will not be applied to the
      return value. This exposes your application to cross-site scripting (XSS) vulnerabilities.
      If you need to render raw HTML, consider instead using `mark_safe()` which more
      clearly marks the intent to render raw HTML than a class with a magic method."
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.0/_modules/django/utils/html/#html_safe
        - https://gist.github.com/minusworld/7885d8a81dba3ea2d1e4b8fd3c218ef5
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    pattern-either:
      - pattern: django.utils.html.html_safe(...)
      - pattern: |
          @django.utils.html.html_safe
          class $CLASS(...):
            ...
  - id: locals-as-template-context
    languages:
      - python
    message:
      'Using ''locals()'' as a context to ''render(...)'' is extremely dangerous.
      This exposes Python functions to the template that were not meant to be exposed.
      An attacker could use these functions to execute code that was not intended to
      run and could compromise the application. (This is server-side template injection
      (SSTI)). Do not use ''locals()''. Instead, specify each variable in a dictionary
      or ''django.template.Context'' object, like ''{"var1": "hello"}'' and use that
      instead.'
    metadata:
      category: security
      cwe:
        "CWE-96: Improper Neutralization of Directives in Statically Saved Code ('Static
        Code Injection')"
      owasp: "A1: Injection"
      references:
        - https://docs.djangoproject.com/en/3.2/ref/settings/#templates
        - https://docs.djangoproject.com/en/3.2/topics/templates/#django.template.backends.django.DjangoTemplates
        - https://docs.djangoproject.com/en/3.2/ref/templates/api/#rendering-a-context
      technology:
        - django
    pattern-either:
      - pattern: django.shortcuts.render(..., locals(...), ...)
      - pattern: django.template.Template.render(..., locals(...), ...)
      - patterns:
          - pattern-inside: |
              $CONTEXT = locals(...)
              ...
          - pattern-either:
              - pattern: django.shortcuts.render(..., $CONTEXT, ...)
              - pattern: django.template.Template.render(..., $CONTEXT, ...)
    severity: ERROR
  - id: formathtml-fstring-parameter
    message:
      Passing a formatted string as first parameter to `format_html` disables
      the proper encoding of variables. Any HTML in the first parameter is not encoded.
      Using a formatted string as first parameter obscures which parameters are encoded.
      Correct use of `format_html` is passing a static format string as first parameter,
      and the variables to substitute as subsequent parameters.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.2/ref/utils/#django.utils.html.format_html
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    pattern-either:
      - pattern: format_html(<... f"..." ...>, ...)
      - pattern: format_html("..." % ..., ...)
      - pattern: format_html("...".format(...), ...)
  - id: avoid-mark-safe
    patterns:
      - pattern-not-inside: django.utils.html.format_html(...)
      - pattern-not: django.utils.safestring.mark_safe("...")
      - pattern: django.utils.safestring.mark_safe(...)
    message:
      '''mark_safe()'' is used to mark a string as "safe" for HTML output. This
      disables escaping and could therefore subject the content to XSS attacks. Use
      ''django.utils.html.format_html()'' to build HTML for rendering instead.'
    metadata:
      source-rule-url: https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe
        - https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
  - id: global-autoescape-off
    message:
      "Autoescape is globally disbaled for this Django application. If you are
      rendering any web pages, this exposes your application to cross-site scripting
      (XSS) vulnerabilities. Remove 'autoescape: False' or set it to 'True'."
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.1/ref/settings/#templates
        - https://docs.djangoproject.com/en/3.1/topics/templates/#django.template.backends.django.DjangoTemplates
      category: security
      technology:
        - django
    languages:
      - python
    severity: WARNING
    pattern: "{..., 'BACKEND': ..., 'OPTIONS': {..., 'autoescape': False, ...}, ...}\n"
    fix-regex:
      regex: "(autoescape.*?)False"
      replacement: "\\1True"
  - id: var-in-script-tag
    languages:
      - generic
    severity: ERROR
    message:
      Detected a template variable used in a script tag. Although template variables
      are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS)
      attacks when used directly in JavaScript. If you need this data on the rendered
      page, consider placing it in the HTML portion (outside of a script tag). Alternatively,
      use a JavaScript-specific encoder, such as the one available in OWASP ESAPI. For
      Django, you may also consider using the 'json_script' template tag and retrieving
      the data in your script by using the element ID (e.g., `document.getElementById`).
    patterns:
      - pattern-inside: "<script ...> ... </script>"
      - pattern: "{{ ... }}"
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://adamj.eu/tech/2020/02/18/safely-including-data-for-javascript-in-a-django-template/?utm_campaign=Django%2BNewsletter&utm_medium=rss&utm_source=Django_Newsletter_12A
        - https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough
        - https://github.com/ESAPI/owasp-esapi-js
      category: security
      technology:
        - django
  - id: password-empty-string
    message:
      "'$VAR' is the empty string and is being used to set the password on '$MODEL'.
      If you meant to set an unusable password, set the password to None or call 'set_unusable_password()'."
    metadata:
      cwe: "CWE-521: Weak Password Requirements"
      owasp: "A2: Broken Authentication"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/contrib/auth/#django.contrib.auth.models.User.set_password
      category: security
      technology:
        - django
    patterns:
      - pattern-either:
          - pattern: |
              $MODEL.set_password($EMPTY)
              ...
              $MODEL.save()
          - pattern: |
              $VAR = $EMPTY
              ...
              $MODEL.set_password($VAR)
              ...
              $MODEL.save()
      - metavariable-regex:
          metavariable: "$EMPTY"
          regex: (\'\'|\"\")
    languages:
      - python
    severity: ERROR
  - id: template-var-unescaped-with-safeseq
    message:
      Detected a template variable where autoescaping is explicitly disabled
      with '| safeseq' filter. This allows rendering of raw HTML in this segment. Ensure
      no user data is rendered here, otherwise this is a cross-site scripting (XSS)
      vulnerability. If you must do this, use `mark_safe` in your Python code.
    metadata:
      cwe:
        "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site
        Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
      references:
        - https://docs.djangoproject.com/en/3.0/ref/templates/builtins/#safeseq
      category: security
      technology:
        - django
    languages:
      - regex
    paths:
      include:
        - "*.html"
    severity: WARNING
    pattern-regex: "{{.*?\\|\\s+safeseq(\\s+}})?"
  # https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/insecure-file-permissions.yaml
  # should subsume B103 but we have to set the flag --dangerously-allow-arbitrary-code-execution-from-rules
  # - using metavariable-based approach for the time being
  - id: bandit.B103
    patterns:
      - pattern: os.chmod(...,$MASK)
      - metavariable-regex:
          metavariable: "$MASK"
          regex: "(0x..f|0o..[2,3,7]|stat.S_IXGRP|stat.S_IWOTH)"
    message: "Chmod setting a permissive mask on file."
    metadata:
      cwe: "CWE-732: Incorrect Permission Assignment for Critical Resource"
      owasp: "A6: Security Misconfiguration"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/hardcoded-password-default-argument.yaml
  # hardcoded-password-default-argument
  - id: bandit.B107
    patterns:
      - pattern: |
          def $FUNC(..., password="...", ...):
            ...
    message: |
      Hardcoded password is used as a default argument to '$FUNC'. This could be dangerous if
      a real password is not supplied.
    metadata:
      cwe: "CWE-259: Use of Hard-coded Password"
      owasp: "A3: Broken Authentication and Session Management"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/best-practice/hardcoded-tmp-path.yaml
  # hardcoded-tmp-path
  - id: bandit.B108-1
    pattern: open("=~/^\/tmp.*/", ...)
    message: |
      Detected hardcoded temp directory. Consider using 'tempfile.TemporaryFile' instead.
    metadata:
      cwe: "CWE-377: Insecure Temporary File"
    severity: WARNING
    languages: [python]
  - id: bandit.B108-2
    patterns:
      - pattern: open("=~/^\/(dev|var).*/", ...)
    message: |
      Probable insecure usage of temp file/directory.
    metadata:
      cwe: "CWE-377: Insecure Temporary File"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/flask/security/audit/debug-enabled.yaml
  # debug-enabled
  - id: bandit.B201
    patterns:
      - pattern-inside: |
          import flask
          ...
      - pattern: $APP.run(..., debug=True, ...)
    message: |
      Detected Flask app with debug=True. Do not deploy to production with this flag enabled
      as it will leak sensitive information. Instead, consider using Flask configuration
      variables or setting 'debug' using system environment variables.
    metadata:
      cwe: "CWE-489: Active Debug Code"
      owasp: "A6: Security Misconfiguration"
    severity: WARNING
    languages: [python]
  - id: bandit.B301-2
    pattern: cPickle.$FUNC(...)
    message: |
      Avoid using `cPickle`, which is known to lead to code execution vulnerabilities.
      When unpickling, the serialized data could be manipulated to run arbitrary code.
      Instead, consider serializing the relevant data as JSON or a similar text-based
      serialization format.
    metadata:
      cwe: "CWE-502: Deserialization of Untrusted Data"
      owasp: "A8: Insecure Deserialization"
    severity: WARNING
    languages: [python]
  # avoid-dill
  - id: bandit.B301-3
    pattern-either:
      - pattern: dill.$FUNC(...)
    message: |
      Avoid using `dill`, which uses `pickle`, which is known to lead to code execution vulnerabilities.
      When unpickling, the serialized data could be manipulated to run arbitrary code.
      Instead, consider serializing the relevant data as JSON or a similar text-based
      serialization format.
    metadata:
      cwe: "CWE-502: Deserialization of Untrusted Data"
      owasp: "A8: Insecure Deserialization"
    languages: [python]
    severity: WARNING
  # avoid-shelve
  - id: bandit.B301-4
    pattern-either:
      - pattern: shelve.$FUNC(...)
    message: |
      Avoid using `shelve`, which uses `pickle`, which is known to lead to code execution vulnerabilities.
      When unpickling, the serialized data could be manipulated to run arbitrary code.
      Instead, consider serializing the relevant data as JSON or a similar text-based
      serialization format.
    metadata:
      cwe: "CWE-502: Deserialization of Untrusted Data"
      owasp: "A8: Insecure Deserialization"
    severity: WARNING
    languages: [python]
  # insecure-hash-algorithm-sha1
  - id: bandit.B303-2
    pattern: hashlib.sha1(...)
    message: |
      Detected SHA1 hash algorithm which is considered insecure. SHA1 is not
      collision resistant and is therefore not suitable as a cryptographic
      signature. Use SHA256 or SHA3 instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-hash-algorithm-md4
  - id: bandit.B303-4
    patterns:
      - pattern-either:
          - pattern: Crypto.Hash.MD4.new(...)
          - pattern: Cryptodome.Hash.MD4.new (...)
    message: |
      Detected MD4 hash algorithm which is considered insecure. This algorithm
      has many known vulnerabilities and has been deprecated. Use SHA256 or SHA3 instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-hash-algorithm-sha1
  - id: bandit.B303-6
    patterns:
      - pattern-either:
          - pattern: Crypto.Hash.SHA.new(...)
          - pattern: Cryptodome.Hash.SHA.new (...)
    message: |
      Detected SHA1 hash algorithm which is considered insecure. SHA1 is not
      collision resistant and is therefore not suitable as a cryptographic
      signature. Use SHA256 or SHA3 instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-hash-algorithm-md4
  - id: bandit.B304-2
    patterns:
      - pattern-either:
          - pattern: Crypto.Hash.MD4.new(...)
          - pattern: Cryptodome.Hash.MD4.new (...)
    message: |
      Detected MD4 hash algorithm which is considered insecure. This algorithm
      has many known vulnerabilities and has been deprecated. Use SHA256 or SHA3 instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-hash-algorithm-sha1
  - id: bandit.B304-4
    patterns:
      - pattern-either:
          - pattern: Crypto.Hash.SHA.new(...)
          - pattern: Cryptodome.Hash.SHA.new (...)
    message: |
      Detected SHA1 hash algorithm which is considered insecure. SHA1 is not
      collision resistant and is therefore not suitable as a cryptographic
      signature. Use SHA256 or SHA3 instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-cipher-algorithm-rc4
  - id: bandit.B304-6
    patterns:
      - pattern-either:
          - pattern: Cryptodome.Cipher.ARC4.new(...)
          - pattern: Crypto.Cipher.ARC4.new(...)
    message: |
      Detected RC4 cipher algorithm which is considered insecure. The algorithm has many
      known vulnerabilities. Use AES instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-cipher-algorithm-blowfish
  - id: bandit.B304-7
    patterns:
      - pattern-either:
          - pattern: Cryptodome.Cipher.Blowfish.new(...)
          - pattern: Crypto.Cipher.Blowfish.new(...)
    message: |
      Detected Blowfish cipher algorithm which is considered insecure. The algorithm has many
      known vulnerabilities. Use AES instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-cipher-algorithm-des
  - id: bandit.B304-8
    patterns:
      - pattern-either:
          - pattern: Cryptodome.Cipher.DES.new(...)
          - pattern: Crypto.Cipher.DES.new(...)
    message: |
      Detected DES cipher algorithm which is considered insecure. The algorithm is
      considered weak and has been deprecated. Use AES instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-cipher-algorithm-xor
  - id: bandit.B304-9
    patterns:
      - pattern-either:
          - pattern: Cryptodome.Cipher.XOR.new(...)
          - pattern: Crypto.Cipher.XOR.new(...)
    message: |
      Detected XOR cipher algorithm which is considered insecure. This algorithm
      is not cryptographically secure and can be reversed easily. Use AES instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-cipher-algorithm-rc4
  - id: bandit.B304-10
    pattern: cryptography.hazmat.primitives.ciphers.algorithms.ARC4(...)
    message: |
      Detected RC4 cipher algorithm which is considered insecure. The algorithm has many
      known vulnerabilities. Use AES instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-cipher-algorithm-blowfish
  - id: bandit.B304-11
    pattern: cryptography.hazmat.primitives.ciphers.algorithms.Blowfish(...)
    message: |
      Detected Blowfish cipher algorithm which is considered insecure. The algorithm has many
      known vulnerabilities. Use AES instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # insecure-cipher-algorithm-idea
  - id: bandit.B304-12
    pattern: cryptography.hazmat.primitives.ciphers.algorithms.IDEA(...)
    message: |
      Detected IDEA cipher algorithm which is considered insecure. The algorithm is
      considered weak and has been deprecated. Use AES instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/correctness/tempfile/mktemp.yaml
  # tempfile-insecure
  - id: bandit.B306
    pattern: tempfile.mktemp(...)
    message: |
      Use tempfile.NamedTemporaryFile instead. From the official Python documentation: THIS FUNCTION IS UNSAFE AND SHOULD
      NOT BE USED. The file name may refer to a file that did not exist at some point, but by the time you get around to creating
      it, someone else may have beaten you to the punch.
    metadata:
      cwe: "CWE-377: Insecure Temporary File"
      owasp: "A3: Sensitive Data Exposure"
    languages: [python]
    severity: ERROR
  # source (modified): https://semgrep.dev/c/p/bandit
  # python.lang.security.audit.eval-detected.eval-detected
  - id: bandit.B307
    patterns:
      - pattern: eval(...)
    message: |
      Detected the use of eval(). eval() can be dangerous if used to evaluate
      dynamic content. If this content can be input from outside the program, this
      may be a code injection vulnerability. Ensure evaluated content is not definable
      by external sources. Consider using safer ast.literal_eval.
    metadata:
      cwe: "CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"
      owasp: "A1: Injection"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/httpsconnection-detected.yaml
  # httpsconnection-detected
  - id: bandit.B309
    patterns:
      - pattern-either:
          - pattern: httplib.HTTPSConnection(...)
          - pattern: http.client.HTTPSConnection(...)
          - pattern: six.moves.http_client.HTTPSConnection(...)
    message: |
      The HTTPSConnection API has changed frequently with minor releases of Python.
      Ensure you are using the API for your version of Python securely.
      For example, Python 3 versions prior to 3.4.3 will not verify SSL certificates by default.
      See https://docs.python.org/3/library/http.client.html#http.client.HTTPSConnection
      for more information.
    metadata:
      cwe: "CWE-295: Improper Certificate Validation"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/dynamic-urllib-use-detected.yaml
  # dynamic-urllib-use-detected
  - id: bandit.B310-1
    patterns:
      - pattern-not: urllib.$W("...")
      - pattern-not: urllib.request.$W("...")
      - pattern-not: $OPENER.$W("...")
      - pattern-either:
          - patterns:
              - pattern-either:
                  - pattern: urllib.urlopen(...)
                  - pattern: urllib.request.urlopen(...)
                  - pattern: urllib.urlretrieve(...)
                  - pattern: urllib.request.urlretrieve(...)
          - patterns:
              - pattern-either:
                  - pattern-inside: |
                      $OPENER = urllib.URLopener(...)
                      ...
                  - pattern-inside: |
                      $OPENER = urllib.request.URLopener(...)
                      ...
                  - pattern-inside: |
                      $OPENER = urllib.FancyURLopener(...)
                      ...
                  - pattern-inside: |
                      $OPENER = urllib.request.FancyURLopener(...)
                      ...
              - pattern-either:
                  - pattern: $OPENER.open(...)
                  - pattern: $OPENER.retrieve(...)
    message: >-
      Detected a dynamic value being used with urllib. urllib supports 'file://' schemes,
      so a dynamic value controlled by a malicious actor may allow them to read arbitrary files.
      Audit uses of urllib calls to ensure user data cannot control the URLs, or consider
      using the 'requests' library instead.
    metadata:
      cwe: "CWE-939: Improper Authorization in Handler for Custom URL Scheme"
      owasp: "A5: Broken Access Control"
    severity: WARNING
    languages: [python]
  - id: bandit.B310-2
    pattern-either:
      - pattern: urllib2.urlopen(...)
      - pattern: urllib2.Request(...)
      - pattern: urllib.URLopener(...)
      - pattern: urllib.FancyURLopener(...)
      - pattern: urllib.request.FancyURLopener(...)
      - pattern: urllib.request.urlopen(...)
      - pattern: urllib.request.URLopener(...)
      - pattern: six.moves.urllib.request.urlopen(...)
      - pattern: six.moves.urllib.request.urlretrieve(...)
      - pattern: six.moves.urllib.request.URLopener(...)
      - pattern: six.moves.urllib.request.FancyURLopener(...)
    message: >-
      Detected a dynamic value being used with urllib. urllib supports 'file://' schemes,
      so a dynamic value controlled by a malicious actor may allow them to read arbitrary files.
      Audit uses of urllib calls to ensure user data cannot control the URLs, or consider
      using the 'requests' library instead.
    metadata:
      cwe: "CWE-939: Improper Authorization in Handler for Custom URL Scheme"
      owasp: "A5: Broken Access Control"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/telnetlib.yaml
  # telnetlib
  - id: bandit.B312
    pattern: telnetlib.$ANYTHING(...)
    message: |
      Telnet does not encrypt communications. Use SSH instead.
    metadata:
      cwe: "CWE-319: Cleartext Transmission of Sensitive Information"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # source (modified): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/use-defused-xml.yaml
  - id: bandit.B313.B314.B315.B316.B318.B319.B320.B405.B406.B407.B408.B409.B410
    pattern-either:
      - pattern: import xml
      - pattern: import lxml
        # B313
      - pattern: xml.etree.cElementTree.fromstring(...)
      - pattern: xml.etree.cElementTree.parse(...)
      - pattern: xml.etree.cElementTree.iterparse(...)
      - pattern: xml.etree.cElementTree.XMLParser(...)
        # B314
      - pattern: xml.etree.ElementTree.fromstring(...)
      - pattern: xml.etree.ElementTree.parse(...)
      - pattern: xml.etree.ElementTree.iterparse(...)
      - pattern: xml.etree.ElementTree.XMLParser(...)
        # B315
      - pattern: xml.sax.expatreader.create_parser(...)
        # B316
      - pattern: xml.dom.expatbuilder.parse(...)
      - pattern: xml.dom.expatbuilder.parseString(...)
        # B318
      - pattern: xml.dom.minidom.parseString(...)
      - pattern: xml.dom.minidom.parse(...)
        # B319
      - pattern: xml.dom.pulldom.parseString(...)
      - pattern: xml.dom.pulldom.parse(...)
        # B320
      - pattern: lxml.etree.fromstring(...)
      - pattern: lxml.etree.RestrictedElement(...)
      - pattern: lxml.etree.GlobalParserTLS(...)
      - pattern: lxml.etree.getDefaultParser(...)
      - pattern: lxml.etree.check_docinfo(...)
    metadata:
      cwe: "CWE-611: Improper Restriction of XML External Entity Reference"
      owasp: "A4: XML External Entities (XXE)"
    message: |
      Found use of the native Python XML libraries, which is vulnerable to XML external entity (XXE)
      attacks. The Python documentation recommends the 'defusedxml' library instead. Use 'defusedxml'.
      See https://github.com/tiran/defusedxml for more information.
    severity: ERROR
    languages: [python]
  # source (original): https://semgrep.dev/c/p/bandit
  # contrib.dlint.dlint-equivalent.insecure-xml-use
  - id: bandit.B317
    pattern-either:
      - patterns:
          - pattern: xml.$ANYTHING
          - pattern-not: xml.sax.saxutils
          - pattern-not: xml.etree.ElementTree.Element
          - pattern-not: xml.etree.ElementTree.SubElement
      - pattern: lxml.$ANYTHING
      - pattern: xmlrpclib.$ANYTHING
    message: Insecure XML parsing functionality, prefer 'defusedxml'
    metadata:
      cwe: "CWE-611: Improper Restriction of XML External Entity Reference"
      owasp: "A4: XML External Entities (XXE)"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/ftplib.yaml
  # ftplib
  - id: bandit.B321
    pattern: ftplib.$ANYTHING(...)
    message: |
      FTP does not encrypt communications by default. This can lead to sensitive
      data being exposed. Ensure use of FTP here does not expose sensitive data.
    metadata:
      cwe: "CWE-319: Cleartext Transmission of Sensitive Information"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # source (modified): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/insecure-hash-function.yaml
  # insecure-hash-function
  - id: bandit.B324
    patterns:
      - pattern-either:
          - pattern: hashlib.new("=~/[M|m][D|d][4|5]/", ...)
          - pattern: hashlib.new(..., name="=~/[M|m][D|d][4|5]/", ...)
          - pattern: hashlib.new('sha1')
          - pattern: hashlib.new(..., name='SHA1')
          - pattern: hashlib.new('sha', string='test')
          - pattern: hashlib.new(name='SHA', string='test')
    message: |
      Detected use of an insecure MD4 or MD5 hash function.
      These functions have known vulnerabilities and are considered deprecated.
      Consider using 'SHA256' or a similar function instead.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # source (modified): https://semgrep.dev/c/p/bandit
  # contrib.dlint.dlint-equivalent.insecure-os-temp-use
  - id: bandit.B325
    pattern-either:
      - pattern: os.tempnam(...)
      - pattern: os.tmpnam(...)
    message: The Python 'os' tempnam|tmpnam functions are vulnerable to symlink attacks
    metadata:
      cwe: "CWE-377: Insecure Temporary File"
    severity: WARNING
    languages: [python]
  - id: bandit.B401
    patterns:
      - pattern: import telnetlib
    message: |
      Telnet-related functions are being called. Telnet is considered insecure. Use SSH or some other encrypted protocol.
    metadata:
      cwe: "CWE-319: Cleartext Transmission of Sensitive Information"
      owasp: "A3: Sensitive Data Exposure"
    severity: ERROR
    languages: [python]
  - id: bandit.B402
    patterns:
      - pattern-either:
          - pattern: import ftplib
          - pattern: from ftplib import FTP
          - pattern: ftplib.FTP(...)
    message: |
      functions are being called. FTP is considered insecure. Use SSH/SFTP/SCP or some other encrypted protocol
    metadata:
      cwe: "CWE-319: Cleartext Transmission of Sensitive Information"
      owasp: "A3: Sensitive Data Exposure"
    severity: ERROR
    languages: [python]
  - id: bandit.B403
    patterns:
      - pattern-either:
          - pattern: import pickle
          - pattern: import cPickle
    message: |
      Consider possible security implications associated with pickle module.
    metadata:
      cwe: "CWE-502: Deserialization of Untrusted Data"
      owasp: "A8: Insecure Deserialization"
    severity: ERROR
    languages: [python]
  - id: bandit.B411
    pattern-either:
      - pattern: import xmlrpclib
    message: |
      Consider possible security implications associated with xmlrpclib module.
    metadata:
      cwe: "CWE-502: Deserialization of Untrusted Data"
      owasp: "A8: Insecure Deserialization"
    severity: ERROR
    languages: [python]
  - id: bandit.B412
    pattern-either:
      - pattern: wsgiref.handlers.CGIHandler(...)
      - pattern: twisted.web.twcgi.CGIDirectory(...)
    message: |
      Consider possible security implications associated with httpoxy module.
    metadata:
      cwe: "CWE-284: Improper Access Control"
      owasp: "A5: Broken Access Control"
    severity: ERROR
    languages: [python]
  - id: bandit.B413
    pattern-either:
      - pattern: import pycryto
      - pattern: import Crypto.Cipher
      - pattern: import Crypto.Hash
      - pattern: import Crypto.IO
      - pattern: import Crypto.Protocol
      - pattern: import Crypto.PublicKey
      - pattern: import Crypto.Random
      - pattern: import Crypto.Signature
      - pattern: import Crypto.Util
    message: |
      Consider possible security implications associated with pycrypto module.
    metadata:
      cwe: "CWE-327: Use of a Broken or Risky Cryptographic Algorithm"
      owasp: "A3: Sensitive Data Exposure"
    severity: ERROR
    languages: [python]
  - id: bandit.B504
    patterns:
      - pattern: ssl.wrap_socket()
    message: |
      ssl.wrap_socket call with no SSL/TLS protocol version specified, the default SSLv23 could be insecure, possible security issue.
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
    severity: INFO
    languages: [python]
  # source (modified/combined): https://github.com/returntocorp/semgrep-rules/tree/develop/python/cryptography/security/insufficient*-size/insufficient*-size.yaml
  - id: bandit.B505-1
    patterns:
      - pattern-either:
          - pattern: cryptography.hazmat.primitives.asymmetric.rsa.generate_private_key(..., key_size=$SIZE, ...)
          - pattern: cryptography.hazmat.primitives.asymmetric.rsa.generate_private_key($EXP, $SIZE, ...)
          - pattern: cryptography.hazmat.primitives.asymmetric.rsa.generate_private_key($SIZE, ...)
          - pattern: cryptography.hazmat.primitives.asymmetric.dsa.generate_private_key(..., key_size=$SIZE, ...)
          - pattern: cryptography.hazmat.primitives.asymmetric.dsa.generate_private_key($EXP, $SIZE, ...)
          - pattern: cryptography.hazmat.primitives.asymmetric.dsa.generate_private_key($SIZE, ...)
          - pattern: Crypto.PublicKey.RSA.generate($SIZE, ...)
          - pattern: Crypto.PublicKey.DSA.generate($SIZE, ...)
          - pattern: Cryptodome.PublicKey.DSA.generate($SIZE, ...)
          - pattern: Cryptodome.PublicKey.RSA.generate($SIZE, ...)
          - pattern: Crypto.PublicKey.DSA.generate(bits=$SIZE, ...)
          - pattern: Cryptodome.PublicKey.DSA.generate(bits=$SIZE, ...)
          - pattern: pycrypto_rsa.generate(bits=$SIZE, ...)
          - pattern: pycrypto_dsa.generate(bits=$SIZE, ...)
          - pattern: pycryptodomex_rsa.generate(bits=$SIZE, ...)
          - pattern: pycryptodomex_rsa.generate($SIZE, ...)
          - pattern: pycryptodomex_dsa.generate(bits=$SIZE, ...)
          - pattern: pycryptodomex_dsa.generate($SIZE, ...)
      - metavariable-comparison:
          metavariable: $SIZE
          comparison: $SIZE < 2048
    message: |
      Detected an insufficient key size for DSA. NIST recommends
      a key size of 2048 or higher.
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  - id: bandit.B505-2
    patterns:
      - pattern-inside: cryptography.hazmat.primitives.asymmetric.ec.generate_private_key(...)
    message: |
      Detected an insufficient curve size for EC. NIST recommends
      a key size of 224 or higher. For example, use 'ec.SECP256R1'.
    metadata:
      cwe: "CWE-326: Inadequate Encryption Strength"
      owasp: "A3: Sensitive Data Exposure"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/paramiko-implicit-trust-host-key.yaml
  # paramiko-implicit-trust-host-key
  - id: bandit.B507
    patterns:
      - pattern-inside: |
          $CLIENT = paramiko.client.SSHClient(...)
          ...
          $CLIENT.set_missing_host_key_policy(...)
      - pattern-either:
          - pattern: paramiko.client.AutoAddPolicy
          - pattern: paramiko.client.WarningPolicy
    message: |
      Detected a paramiko host key policy that implicitly trusts a server's
      host key. Host keys should be verified to ensure the connection
      is not to a malicious server. Use RejectPolicy or a custom subclass
      instead.
    metadata:
      cwe: "CWE-322: Key Exchange without Entity Authentication"
      owasp: "A5: Broken Access Control"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/paramiko/paramiko-exec-command.yaml
  # paramiko-exec-command
  - id: bandit.B601
    patterns:
      - pattern-inside: |
          import paramiko
          ...
      - pattern: $CLIENT.exec_command(...)
    message: |
      Unverified SSL context detected. This will permit insecure connections without verifying
      SSL certificates. Use 'ssl.create_default_context()' instead.
    metadata:
      cwe: "CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"
      owasp: "A1: Injection"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/subprocess-shell-true.yaml
  # subprocess-shell-true
  - id: bandit.B602
    patterns:
      - pattern-not: subprocess.$FUNC(..., shell=False, ...)
      - pattern-not: subprocess.$FUNC(..., shell=False)
      - pattern-not: subprocess.$FUNC(..., shell=0)
      - pattern-not: subprocess.$FUNC(..., shell=[])
      - pattern-not: subprocess.$FUNC(..., shell={})
      - pattern-not: subprocess.$FUNC(..., shell=None)
      - pattern-either:
          - pattern: subprocess.$FUNC(..., shell=True, ...)
          - pattern: subprocess.$FUNC(..., shell='True', ...)
          - pattern: subprocess.$FUNC(..., shell='False', ...)
          - pattern: subprocess.$FUNC(..., shell='None', ...)
          - pattern: subprocess.$FUNC(..., shell=$X, ...)
    message: |
      Found 'subprocess' function '$FUNC' with 'shell=True'. This is dangerous because this call will spawn
      the command using a shell process. Doing so propagates current shell settings and variables, which
      makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.
    metadata:
      cwe: "CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"
      owasp: "A1: Injection"
    severity: ERROR
    languages: [python]
  # source (modified): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/dangerous-system-call.yaml
  # dangerous-system-call
  - id: bandit.B603
    patterns:
      - pattern-either:
          - pattern: subprocess.$FUNC([...])
          - pattern: subprocess.$FUNC([...], shell=False)
          - pattern: subprocess.$FUNC(..., shell=False)
          - pattern: subprocess.$FUNC([...], shell=0)
          - pattern: subprocess.$FUNC(..., shell=0)
          - pattern: subprocess.$FUNC([...], shell=[])
          - pattern: subprocess.$FUNC(..., shell=[])
          - pattern: subprocess.$FUNC([...], shell={})
          - pattern: subprocess.$FUNC(..., shell={})
          - pattern: subprocess.$FUNC([...], shell=None)
          - pattern: subprocess.$FUNC(..., shell=None)
    message: |
      subprocess call - check for execution of untrusted input
    metadata:
      cwe: "CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"
      owasp: "A1: Injection"
    severity: WARNING
    languages: [python]
  - id: bandit.B604
    patterns:
      - pattern-not: subprocess.$FUNC(..., shell=True, ...)
      - pattern: $FOO(..., shell=True, ...)
    message: |
      subprocess call - check for execution of untrusted input
    metadata:
      cwe: "CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"
      owasp: "A1: Injection"
    severity: INFO
    languages: [python]
  - id: bandit.B605
    patterns:
      - pattern-either:
          - pattern: os.system(...)
          - pattern: os.popen(...)
          - pattern: os.popen2(...)
          - pattern: os.popen3(...)
          - pattern: os.popen4(...)
          - pattern: popen2.popen2(...)
          - pattern: popen2.popen3(...)
          - pattern: popen2.popen4(...)
          - pattern: popen2.Popen3(...)
          - pattern: popen2.Popen4(...)
          - pattern: commands.getoutput(...)
          - pattern: commands.getstatusoutput("")
    message: |
      Starting a process with a shell: Seems safe, but may be changed in the
      future, consider rewriting without shell
    metadata:
      cwe: "CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"
      owasp: "A1: Injection"
    severity: INFO
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/dangerous-spawn-process.yaml
  # dangerous-spawn-process
  - id: bandit.B606
    patterns:
      - pattern-either:
          - patterns:
              - pattern-not: os.$W("...", ...)
              - pattern-either:
                  - pattern: os.execl(...)
                  - pattern: os.execle(...)
                  - pattern: os.execlp(...)
                  - pattern: os.execlpe(...)
                  - pattern: os.execv(...)
                  - pattern: os.execve(...)
                  - pattern: os.execvp(...)
                  - pattern: os.execvpe(...)
                  - pattern: os.startfile(...)
          - patterns:
              - pattern-not: os.$W($MODE, "...", ...)
              - pattern-either:
                  - pattern: os.spawnl(...)
                  - pattern: os.spawnle(...)
                  - pattern: os.spawnlp(...)
                  - pattern: os.spawnlpe(...)
                  - pattern: os.spawnv(...)
                  - pattern: os.spawnve(...)
                  - pattern: os.spawnvp(...)
                  - pattern: os.spawnvpe(...)
    message: |
      Found dynamic content when spawning a process. This is dangerous if external
      data can reach this function call because it allows a malicious actor to
      execute commands. Ensure no external data reaches here.
    metadata:
      cwe: "CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"
      owasp: "A1: Injection"
    severity: WARNING
    languages: [python]
  - id: bandit.B607
    patterns:
      - pattern-either:
          - pattern: subprocess.Popen($BIN, shell=False)
          - pattern: subprocess.Popen([$BIN, ...], shell=False)
          - pattern: os.system($BIN, shell=False)
          - pattern: os.system([$BIN, ...], shell=False)
          - pattern: popen2.Popen3($BIN, shell=False)
          - pattern: popen2.Popen3([$BIN, ...], shell=False)
          - pattern: popen2.Popen4($BIN, shell=False)
          - pattern: popen2.Popen4([$BIN, ...], shell=False)
          - pattern: commands.getoutput($BIN, shell=False)
          - pattern: commands.getoutput([$BIN, ...], shell=False)
          - pattern: commands.getstatusoutput($BIN, shell=False)
          - pattern: commands.getstatusoutput([$BIN, ...], shell=False)
      - metavariable-regex:
          metavariable: "$BIN"
          regex: "^['\"][^/\\.][^:].*['\"]"
    message: |
      Starting a process with a partial executable path
    metadata:
      cwe: "CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"
      owasp: "A1: Injection"
    severity: INFO
    languages: [python]
  # source (modified): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/formatted-sql-query.yaml
  # formatted-sql-query
  - id: bandit.B608
    patterns:
      - pattern-either:
          - pattern: $DB.execute("..." % ...)
          - pattern: $DB.execute("...".format(...))
          - pattern: $DB.execute(f"...")
          - pattern: $DB.execute("..." + $V + "...")
          - patterns:
              - pattern-either:
                  - pattern-inside: |
                      $SQL = "..." % ...
                      ...
                  - pattern-inside: |
                      $SQL = "...".format(...)
                      ...
                  - pattern-inside: |
                      $SQL = f"...{$X}..."
                      ...
              - pattern: $DB.execute($SQL)
    message: |
      Detected possible formatted SQL query. Use parameterized queries instead.
    metadata:
      cwe: "CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"
      owasp: "A1: Injection"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/system-wildcard-detected.yaml
  # system-wildcard-detected
  - id: bandit.B609
    patterns:
      - pattern-either:
          - pattern-inside: os.system("...")
          - pattern-inside: os.popen("...")
          - pattern-inside: os.popen2("...")
          - pattern-inside: os.popen3("...")
          - pattern-inside: os.popen4("...")
          - pattern-inside: subprocess.$W(..., shell=True, ...)
      - pattern-regex: (tar|chmod|chown|rsync)(.*?)\*
    message: |
      Detected use of the wildcard character in a system call that spawns a shell.
      This subjects the wildcard to normal shell expansion, which can have unintended consequences
      if there exist any non-standard file names. Consider a file named '-e sh script.sh' -- this
      will execute a script when 'rsync' is called. See
      https://www.defensecode.com/public/DefenseCode_Unix_WildCards_Gone_Wild.txt
      for more information.
    metadata:
      cwe: "CWE-155: Improper Neutralization of Wildcards or Matching Symbols"
      owasp: "A1: Injection"
    severity: WARNING
    languages: [python]
  # source (original): https://github.com/returntocorp/semgrep-rules/blob/develop/python/lang/security/audit/mako-templates-detected.yaml
  # mako-templates-detected
  - id: bandit.B702
    pattern: mako.template.Template(...)
    message: |
      Mako templates do not provide a global HTML escaping mechanism.
      This means you must escape all sensitive data in your templates
      using '| u' for URL escaping or '| h' for HTML escaping.
      If you are using Mako to serve web content, consider using
      a system such as Jinja2 which enables global escaping.
    metadata:
      cwe: "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
    severity: INFO
    languages: [python]
  # source (modified): https://github.com/returntocorp/semgrep-rules/blob/develop/python/django/security/audit/avoid-mark-safe.yaml
  # avoid-mark-safe
  - id: bandit.B703
    patterns:
      - pattern-either:
          - pattern: django.utils.safestring.SafeText(...)
          - pattern: django.utils.safestring.SafeUnicode(...)
          - pattern: django.utils.safestring.SafeString(...)
          - pattern: django.utils.safestring.SafeBytes(...)
    message: |
      'mark_safe()' is used to mark a string as "safe" for HTML output.
      This disables escaping and could therefore subject the content to
      XSS attacks. Use 'django.utils.html.format_html()' to build HTML
      for rendering instead.
    metadata:
      cwe: "CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"
      owasp: "A7: Cross-Site Scripting (XSS)"
    severity: WARNING
    languages: [python]
