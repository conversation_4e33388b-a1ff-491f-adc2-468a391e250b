FROM europe-west1-docker.pkg.dev/bks-ar-pkg/images/python:3.11.13-slim-bookworm
# Upgrade base image system packages as those are separate from packages defined
# in requirements.txt files and bumping a package version there does not always
# fix the vulnerabilities found by dependency audit.
RUN pip install --upgrade \
    setuptools==78.1.1;

ENV TIMEZONE UTC
RUN ln -snf /usr/share/zoneinfo/$TIMEZONE /etc/localtime && \
    echo $TIMEZONE > /etc/timezone

# install system deps
RUN apt-get update && apt-get install --no-install-recommends -y \
    build-essential \
    bash \
    telnet \
    tmux \
    vim \
    less \
    htop \
    strace \
    curl \
    ca-certificates  \
    gnupg \
    git \
    procps \
    wget \
    net-tools \
    tcpdump \
    iproute2 \
    # core specific packages
    gettext \
    libgettextpo-dev \
    redis=5:7.0.15-1~deb12u5 \
    libffi-dev \
    libgdk-pixbuf2.0-0 \
    libgeos-dev \
    libpangocairo-1.0-0 \
    openssl \
    xmlsec1 \
    libpcre3-dev \
    apache2-utils \
    libexpat1 \
    libtasn1-6=4.19.0-2+deb12u1 \
    gnutls-bin=3.7.9-2+deb12u5 \
    perl=5.36.0-7+deb12u3 \
    systemd=252.39-1~deb12u1 \
    passwd=1:4.13+dfsg1-1+deb12u1 \
    libgssapi-krb5-2=1.20.1-2+deb12u4 \
    libkrb5-3=1.20.1-2+deb12u4 \
    libkrb5support0=1.20.1-2+deb12u4

# install postgresql-client-14 to match our postgres version
RUN curl https://www.postgresql.org/media/keys/ACCC4CF8.asc \
    | gpg --dearmor \
    | tee /etc/apt/trusted.gpg.d/apt.postgresql.org.gpg >/dev/null
RUN sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt/ bookworm-pgdg main" \
    > /etc/apt/sources.list.d/postgresql.list'
RUN apt update && apt install -y \
    libpq-dev \
    postgresql-client-14

# clean apt stuff to save space
RUN apt-get clean  \
    && rm -rf /var/lib/apt/lists/*

# core important variables
ENV BASE_PATH "/opt"
ENV PYTHONPATH "${BASE_PATH}:/root/.local/bin"
ENV PATH "${PATH}:${BASE_PATH}:/root/.local/bin"
ENV DJANGO_SETTINGS_MODULE=settings
# SRE-2378: the env var set here prevents ddtrace from pushing traces to localhost when using custom instrumentation
ENV DD_TRACE_ENABLED "false"

# PY-1257 PY-1264 - remove this after all proto files are updated
# https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates'
ENV PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

# Added remporarely due to lock in datadog
ENV DD_TRACE_SPAN_AGGREGATOR_RLOCK=True

# install uv
RUN pip install uv==0.8.6
ENV UV_KEYRING_PROVIDER=subprocess
ENV UV_BREAK_SYSTEM_PACKAGES=True
ENV UV_COMPILE_BYTECODE=True
ENV UV_NO_CACHE=True

# AR auth requirements
COPY requirements-pre-init.txt /opt/requirements-pre-init.txt
RUN uv pip install \
    --prefix ~/.local \
    --no-cache-dir \
    -r "${BASE_PATH}/requirements-pre-init.txt"

# install initial python deps
COPY requirements-initial.txt /opt/requirements-initial.txt

RUN uv pip install \
    --prefix ~/.local \
    --no-cache-dir \
    -r "${BASE_PATH}/requirements-initial.txt"

ADD https://github.com/che0/uwping/releases/download/14.0/uwping /usr/local/bin/uwping
RUN chmod +x /usr/local/bin/uwping

# install python requirements
COPY requirements.txt /opt/requirements.txt
COPY requirements-booking.txt /opt/requirements-booking.txt
RUN uv pip install \
    --prefix ~/.local \
    --no-build-isolation \
    --no-cache-dir \
    -r /opt/requirements.txt \
    -r /opt/requirements-booking.txt

COPY requirements-booksy.txt /opt/requirements-booksy.txt
RUN uv pip install \
    --prefix ~/.local \
    --no-cache-dir \
    -r /opt/requirements-booksy.txt

RUN uwsgi --build-plugin https://github.com/Datadog/uwsgi-dogstatsd

#Create version file
ARG CI_COMMIT_REF_NAME
ARG CI_COMMIT_SHORT_SHA
RUN echo "{\"branch\": \"${CI_COMMIT_REF_NAME}\", \"commit\": \"${CI_COMMIT_SHORT_SHA}\"}" > /opt/version_file.json

#Create datadog source code connection
ARG CI_PROJECT_URL
ARG CI_COMMIT_SHA
ENV DD_GIT_REPOSITORY_URL=${CI_PROJECT_URL}
ENV DD_GIT_COMMIT_SHA=${CI_COMMIT_SHA}
ENV DD_GIT_COMMIT_SHORT_SHA=${CI_COMMIT_SHORT_SHA}


COPY . /opt

# uWSGI will listen on this port
EXPOSE 3000
