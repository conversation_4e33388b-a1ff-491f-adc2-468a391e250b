import typing as t

from rest_framework import serializers, fields


class EmptyRequestSerializer(serializers.Serializer):
    """
    Ability to send an empty request in the Swagger using GenericViewSet
    """


class EmptyResponseSerializer(serializers.Serializer):
    """
    Serializer that returns empty json
    """

    def to_representation(self, _):
        return {}


class SchemaTypeSerializerMethodField(serializers.SerializerMethodField):
    """
    SerializerMethodField with defined output schema field.
    """

    def __init__(
        self,
        schema_field: t.Callable = fields.Cha<PERSON><PERSON><PERSON>,
        field_kwargs: t.Optional[dict] = None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.schema_field = schema_field
        self.field_kwargs = field_kwargs or {}
