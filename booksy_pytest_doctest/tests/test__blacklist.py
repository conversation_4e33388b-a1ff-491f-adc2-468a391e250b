from pathlib import Path
from unittest.mock import Mock

import pytest

from booksy_pytest_doctest._blacklist import Blacklist
from booksy_pytest_doctest._reader import Ignore


class TestBlacklist:
    @staticmethod
    @pytest.fixture
    def mock_item() -> Mock:
        return Mock(spec_set=pytest.Item)

    @staticmethod
    @pytest.fixture
    def sut(tmp_path: Path) -> Blacklist:
        sut = Blacklist()
        sut.reset(
            tmp_path,
            [
                Ignore(collect="collect.py"),
                Ignore(testid="test.py::foobar.Foo.add"),
            ],
        )
        return sut

    def test_doesnt_match_neither_path_nor_item(self, mock_item: Mock):
        sut = Blacklist()
        assert not sut.match_path(Path().absolute())
        assert not sut.match_item(mock_item)

    def test_matches_path(self, tmp_path: Path, sut: Blacklist):
        assert sut.match_path(tmp_path / "collect.py")
        assert not sut.match_path(Path(tmp_path / "test.py"))

    @pytest.mark.parametrize(
        "nodeid,match",
        [
            ("collect.py", False),
            ("test.py", False),
            ("test.py::foobar", False),
            ("test.py::foobar.Foo", False),
            ("test.py::foobar.Foo.del", False),
            ("test.py::foobar.Foo.add", True),
        ],
    )
    def test_matching_item(
        self,
        sut: Blacklist,
        mock_item: Mock,
        nodeid: str,
        match: bool,
    ):
        mock_item.nodeid = nodeid
        assert sut.match_item(mock_item) is match
