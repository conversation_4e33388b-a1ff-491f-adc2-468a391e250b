from typing import Any, Dict

from django.conf import settings

from domain_services.booking.src.domains import Business
from domain_services.booking.src.domains.analytics.dto import ProductAnalyticsParams
from domain_services.booking.src.domains.appointment.dto.draft import Draft


def truncate(param: str | None) -> str | None:
    if not param:
        return
    return param[:100]


class ProductAnalyticsParamsFactory:
    @staticmethod
    def create(
        data: Dict[str, Any],
        business: Business,
        draft: Draft | None = None,
        user_id: int | None = None,
        appointment_id: int | None = None,
    ) -> ProductAnalyticsParams:
        return ProductAnalyticsParams(
            draft_id=str(draft.id) if draft else None,
            business_id=business.id,
            user_id=user_id,
            fingerprint=data.get('fingerprint'),
            country_code=settings.API_COUNTRY,
            business_primary_category=business.primary_category,
            booking_source=truncate(data.get('booking_source')),
            traffic_source=truncate(data.get('traffic_source')),
            traffic_channel=truncate(data.get('traffic_channel')),
            traffic_initial_referrer=truncate(data.get('traffic_initial_referrer')),
            traffic_referrer=truncate(data.get('traffic_referrer')),
            task_id=truncate(data.get('task_id')),
            platform=truncate(data.get('platform')),
            app_version=truncate(data.get('app_version')),
            device_type=truncate(data.get('device_type')),
            appointment_id=appointment_id,
        )
