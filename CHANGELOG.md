# API Changes

*WARNING: changelog has not been updated since version 1.1.1*


## Version 2

This version is fully compatible with 1.1.1, but works only with new URL
structure /api/{cc}/{ver}/ instead of HTTP headers.

## Version 1.1.1

### Bookmarks
City regions are now returned in response containing bookmarked businesses.
Following fields unrelated to bookmarks have been removed from response:
- booksy_business
- tempimage
- reviews
- tileboxes

### Registration Codes
A new field 'registration_code' has been added to handlers:
- /business_api/account/ (POST)
- /business_api/me/businesses/ (POST)
Field is not required, but its value is validated against DB
[Ticket 18172](https://pm.sensisoft.com/issues/18172)

### TileBoxes

##### Customer API:
TileBoxes may be received in business search response by setting the "include_tileboxes" flag to "1".
In such case they are available under key "tileboxes" in the response.

##### Business API:
TileBoxes and TileBoxLocations may be created and searched for using endpoints:

    * GET /business_api/tilebox_locations/ - for searching TileBoxLocations
    * POST /business_api/tilebox_locations/ - creating TileBoxLocations
    * GET /business_api/tileboxes/ - searching for TileBoxes
    * POST /business_api/tileboxes/ - creating TileBoxes


### Business Booking ###

A new parameter `overbooking` has been added to following Business Booking handlers:

 - POST /business_api/me/businesses/{business_id}/bookings/ (Create Booking by Business)

 - PUT /business_api/me/bookings/{booking_id}/ (Update Booking by Business)

Use `overbooking` param to specify if conflicts should be detected.
If there is a conflict a `409 CONFLICT` status is returned
and booking is not created or modified.

In previous versions Business Booking always overbooked - that's why
for backward compatibility `overbooking` param is set to `true` by default.

A correct Business Booking scenario is as follows:

* booking is sent with overbooking set to `false`

* if there is a conflict, user is asked to confirm

* if user confirms, booking is sent with overbooking set to `true`

Example request with overbooking turned off:

<pre>
{
    "overbooking": false,
    "_version": 1415182536309,
    "booked_from": "2014-11-05T14:40",
    "booked_till": "2014-11-05T14:50",
    "resources": [1167, 1168],
    "service_variant_id": 24668,
    "customer_name": "Zenek Z.",
    "customer_email": "<EMAIL>",
    "customer_phone": "666 666 666",
    "business_note": "Tego pana obslugujemy"
}
</pre>

### Business search returns location details

If BusinessSearch is conducted using location_name or location_id,
then the response will contain detailed data of Region that was used
to search Businesses in.


### Business search returns categories ids

In response to [Ticket 16899](https://redmine.sensisoft.com/issues/16899)

Change affects endpoint /customer_api/businesses

Each Business returned in response has a list of it's categories ids, under key `business_category_ids`

### Business search by first letter of name

Endpoint GET /customer_api/businesses/ (and its friends) have new query param: `first_letter`, allowing searching by first letter of name.

Details:

 - case-insensitive

 - Polish letters aware, i.e. searching for "A" return also "A with ogonek"

 - '#' search for any character which is not latin or polish letter (so this includes also French or Czech extension to latin alphabet)


### Optional booking mode

In response to the [Ticket 16793](https://redmine.sensisoft.com/issues/16793) "Ukrycie wyboru trybu, defaultowo ma byc Automatic" (eng: Hiding booking mode, default value should be Automatic).

Changes affect only endpoint `POST /business_api/me/businesses/{business_id}/activate/`:

 - making JSON field `booking_mode` optional with default value of "A".

 - verify if JSON field `booking_mode` exist then is one of 'A', 'M' or 'S'.

 - exception `code` has been changed from `required` to `invalid`.

### Business search response changes

#### Search Modifications

Each SearchModification contains a type, a reason for modification,
lists of search params have been added or removed and a dict containing
updated values of search params that have been changed.

In version 1.1.0 there were added two search modifications: open_for_fallback and widen_search.

This time a third possible SearchModification was added:

##### location_found

If you have searched using 'location_name' and it matches exactly one region, then search is modified to use this Region's ID as 'location_id' parameter.
(note that if 'location_name' is ambiguous then no search is performed and user is asked to be more specific).

This logic was present from version 1.0.0, but there was no indication that a Region was found. Now, response contains a SearchModification object with this information.

Example response:
<pre>
{
  ...
  "modifications": [
    {
      "added": [
        "location_id"
      ],
      "reason": "location_id found for given location_name",
      "removed": [
        "location_name"
      ],
      "type": "location_found",
      "values": {
        "location_name": "",
        "location_id": 10236
      }
    }
  ]
}
</pre>

### Business booking POST/PUT overbooking `POST /business_api/me/businesses/{business_id}/bookings/` `PUT /business_api/me/bookings/{booking_id}/`

In 1.1.0 bookings for default are overbooked, for new version we are adding parameter `overbooking=true/false`.
Default for overbooking is changed from `True` to `False`, param `overbooking` can overwrite this default option.

This change is made due to help business to not overwrite other bookings and occupied timeslots.


## Version 1.1.0 FREEZED on 01.08.2014

### Business calendar response changes

[Ticket 15836](https://redmine.sensisoft.com/issues/15836)

A `GET` request to URL:

<pre>
/business_api/me/businesses/1/calendar?business_id=1&end_date=2014-08-03&start_date=2014-07-27
</pre>

returns response in new format:

<pre>
{
  "start_date": "2014-07-29",
  "end_date": "2014-08-06",
  
  "bookings": {
    "76": {
      "_version": 1406614595032,
      "type": "B",
      "customer": {
        "name": ""
      },
      "status": "A",
      "id": 76,
      "service": {
        "name": "new booking"
      },
      "booked_from": "2014-07-30T09:35",
      "booked_till": "2014-07-30T10:30"
    },
    ...
  },
  
  "reservations": {
    "72": {
      "id": 72,
      "reserved_till": "2014-07-29T11:30",
      "_version": 1406528308794,
      "reserved_from": "2014-07-29T11:00"
    },
    ...
  },
  
  "time_offs": {
    "73": {
      "_version": 1406528408072,
      "off_till": "2014-07-29T23:59",
      "type": "T",
      "id": 73,
      "off_from": "2014-07-29T00:00",
      "status": "A"
    }
    ...
  },
  
  "resources": [
    {
      "id": 1,
      "name": "P K Zet",
      "type": "S",
      
      "bookings": {
        "2014-07-29": [71],
        "2014-07-30": [76],
        ...
      },
      
      "reservations": {
        "2014-07-29": [72],
        "2014-07-30": [],
        ...
      },
      
      "free_hours": {
        "2014-07-31": [
          {
            "hour_till": "09:00",
            "hour_from": "00:00"
          },
          {
            "hour_till": "23:59",
            "hour_from": "17:00"
          }
        ],
        ...
      },
      
      "working_hours": {
        "2014-07-31": [
          {
            "hour_till": "17:00",
            "hour_from": "09:00"
          }
        ],
        ...
      }
      
    },
    ...
  ]
}
</pre>


### Business details timezone added

[Ticket 15836](https://redmine.sensisoft.com/issues/15836)

A `GET` request to URL:

<pre>
/business_api/me/businesses/{business_id}/
</pre>

Added `timezone` to business details response. Timezone is formated as `-0400`.


### Business search response changes

[Ticket 15478](https://redmine.sensisoft.com/issues/15478)

A `GET` request to business search URL

<pre>
/customer_api/businesses?businesses_page=1&query=&location_name=mia&sort_order=score
</pre>

will now always return a unified response (no 303 redirects, in particular):

<pre>
{
  "businesses_count": 0,
  "businesses": [],
  "specify_regions": [
    {
      "url": "http://localhost:8888/customer_api/businesses?location_id=31333&distance_unit=mi&sort_order=score&businesses_page=1",
      "level": null,
      "full_name": "Miamitown, OH",
      "id": "31333",
      "name": "Miamitown"
    },
    ...
  ],
  "businesses_per_page": 20,
  "modifications": []
}
</pre>

Also, the *no businesses found* response changes from

<pre>
{
  "no_results": {
    "reason": "no_results" 
  }
}
</pre>

to

<pre>
{
  "businesses_per_page": 20,
  "businesses_count": 0,
  "businesses": [],
  "modifications": []
}
</pre>

#### Search Modifications

[Ticket 15232](https://redmine.sensisoft.com/issues/15232)
Another new feature is a SearchModifications list under the key "modifications".

Each SearchModification contains a type, a reason for modification,
lists of search params have been added or removed and a dict containing
updated values of search params that have been changed.

There are currently two types of search modifications:

##### open_for_fallback

If you have searched using 'available_for' and there were no results, your search will be modified - 'open_for' query param will be set to the value of 'available_for' query param and 'available_for' will become empty.

Example response:
<pre>
{
  ...
  "modifications": [
    {
      "added": [
        "open_for"
      ],
      "reason": "no results",
      "removed": [
        "available_for"
      ],
      "type": "open_for_fallback",
      "values": {
        "available_for": "",
        "open_for": "2014-08-14"
      }
    }
  ]
}
</pre>

##### widen_search
If you have searched using 'location_id' and there were less then full page of results, your search will be modified - 'location_geo' will be populated by geo coordinates of a region specified in 'location_id' query param and 'location_id' will become empty. 'distance_radius' query param will be set to the lowest value among the valid ones, that would guarantee a full page of results.

Example response:
<pre>
{
  ...
  "modifications": [
    {
      "added": [
        "location_geo",
        "distance_unit",
        "distance_radius"
      ],
      "reason": "not enough results",
      "removed": [
        "location_id"
      ],
      "type": "widen_search",
      "values": {
        "distance_radius": 50,
        "distance_unit": "mi",
        "location_geo": "40.75205,-73.994517",
        "location_id": ""
      }
    }
  ]
}
</pre>

### Facebook Page Tab App support

[Ticket 15055](https://redmine.sensisoft.com/issues/15055)

Two new operations have been added, to support adding Booksy Widget to Facebook
Pages.

* PUT /business_api/me/businesses/{business_id}/fb_page_app/

Associate Facebook Pages with Businesses

Request:

/business_api/me/businesses/1/fb_page_app/

<pre>
{
  "facebook_pages": [
    *********,
    *********
  ]
}
</pre>

Response:
<pre>
{
  "facebook_pages": [
    {
      "status": "created",
      "id": *********
    },
    {
      "status": "updated",
      "id": *********
    }
  ]
}
</pre>

* GET /customer_api/businesses/fb_page_app/{fb_page_id}/

Return business_id associated with given Facebook Page ID

Request:

/customer_api/businesses/fb_page_app/*********/

Response:
<pre>
{
  "business_id": 1
}
</pre>

### Routing & Config

[Ticket 15172](https://redmine.sensisoft.com/issues/15172)

[Specification](https://docs.google.com/document/d/1hm-oHjvx-TrE_uuo1SmfTn_7yCDjjkMXXB0WG9l70_0/edit?usp=sharing)

Previously a request to `/config` resulted in response like this one

<pre>
{
  "supported_regions": [
    {
      "id": 7,
      "name": "USA"
    }
  ],
  "distance_unit": "mi",
  "currency": {
    "symbol_precedes": true,
    "locale": "en_US.UTF-8"
  }
}
</pre>

Now the response is split into two parts:

* First, make a request to either `us alfa` or `us live` to `/routing/router/`
  getting the API url in response
  
<pre>
  {
      "api": "https://api-booksy-us-beta.sensisoft.com"
  }
</pre>

* Second, call API url with the path `/routing/config/` to get the
  configuration:
  
<pre>
{
    "currency": [
        {
            "code": "PLN ",
            "decimal_separator": ",",
            "group_separator": ".",
            "symbol": "zł",
            "decimal_length": 2,
            "precedes": 0,
            "negative_sign": "-"
        }
    ],
    "supported_regions": [
        {
            "name": "USA",
            "id": 7
        }
    ],
    "widget": "http://localhost:8700",
    "default_locale": "pl_PL",
    "js-utils": "http://localhost:8080/js-utils",
    "locale": {
        "pl_PL": {
            "long_date_ym": "MMMM yyyy",
            "long_date_ymd": "MMMM dd yyyy",
            "time_hm": "HH:mm a",
            "date_ymwd": "E, MMM dd yyyy",
            "long_date_y": "yyyy",
            "distance_unit": "mi",
            "date_ym": "MMM yyyy",
            "time_hms": "HH:mm:ss a",
            "date_ymd": "MM/dd/yyyy",
            "default_currency": "zł",
            "long_date_ymwd": "EEEE, MMMM dd yyyy",
            "date_y": "yyyy",
            "first_day_of_the_week": 0
        }
    },
    "distance_unit": "mi",
    "web-biz": "http://localhost:8601"
    "thumbnails": [
        "306x189",
        "250x250",
        "100x100",
        "50x50"
     ],
}
</pre>

### Notifications `GET`, `PUT` requests

[Ticket 15398](https://redmine.sensisoft.com/issues/15398)

Old `PUT` request to

<pre>
/business_api/me/businesses/<business_id>/notifications/ios/<token>/
/business_api/me/businesses/<business_id>/notifications/android/<token>/
/business_api/me/businesses/<business_id>/notifications/email/<email>/
</pre>

had this form:

<pre>
    {
        "notifications": [
            {
                "code": "biz_booking_status_change",
                "resources": [
                    2115
                ]
            }
        ]
    }
</pre>

Since specifying resources per notification type is not supported in any of the
client applications, a global `resources` field is present instead. The new
`PUT` request has a global `options` key with a `resources` list inside:

<pre>
    {
      ...
      "options": {
        "resources": [2115]
      },
      ...
    }
</pre>

Also the `GET` response for

<pre>
    /business_api/me/businesses/<business_id>/notifications
</pre>

changes from

<pre>
    {
      "notifications": [
        {
          "code": "biz_booking_status_change",
          "name": "Booking status changes",
          "types": [
            {
              "active": "true",
              "type": "P" 
            },
            {
              "active": "true",
              "type": "E" 
            }
          ],
          "description": "Notify me when my booking is confirmed, declined or otherwise changes a status" 
        }
      ],
      "recievers": [
        {
          "identifier": "<EMAIL>",
          "type": "E",
          "language": "en",
          "business": 1169,
          "options": {
            "biz_booking_status_change": "{\"code\": \"biz_booking_status_change\"}" 
          }
        },
        {
          "identifier": "6f41d5c4e6696e5de837194f39d768cb0c0bef75d37fabacda4015617b381eee",
          "type": "P",
          "language": "en",
          "business": 1169,
          "options": {
            "device": "ios",
            "biz_booking_status_change": "{\"sound\": \"default\", \"code\": \"biz_booking_status_change\"}" 
          }
        },
        {
          "identifier": "APA91bFVRjmxN2AtSc4Ma-OnklItl49Uv0HxW8nPHRtU-En-G-7pHuXETDaNBqpcrPDY2bcaA69B_f3bGvEJt3uHuhtY-avvphShXpk4JPReTZTI9lri4wpvJ8t3Nsn46wDe40CQ5TPXUxZOGWKr551G561VDItYEb5HQwlLxQj43EZvilbJHdE",
          "type": "P",
          "language": "en",
          "business": 1169,
          "options": {
            "device": "android" 
          }
        }
      ]
    }
</pre>

to

<pre>
    {
      ...
      "recievers": [
        {
          "identifier": "<EMAIL>",
          "type": "E",
          "language": "en",
          "business": 1169,
          "options": {
            "resources": [2115]
          }
        },
      ...
      ]
    }
</pre>

### Services `any_resource`, `any_staff` removal

[Ticket 12818](https://redmine.sensisoft.com/issues/12818)

Fields `any_resource`, `any_staff` become deprecated in service definition.
The request/response for services changes: the list under `resources` key in
request/response always specifies *all* selected resources performing this
service -- no more *any staff/resource*.

## Version 1.0.1

### Business search "did you mean" 303 response

[Ticket 15477](https://redmine.sensisoft.com/issues/15477)

A `GET` request to URL

<pre>
/customer_api/businesses?businesses_page=1&query=&location_name=mia&sort_order=score
</pre>

will (as before) return status 303, but now the `Location` header is filled.
It is thus safe to turn on client 303 redirect following.
