{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "baseBranches": ["main"], "assignees": ["wojciech.kietschke"], "assigneesSampleSize": 1, "reviewers": ["wojciech.kietschke", "mateusz.k<PERSON>", "kamil.krakowski"], "reviewersSampleSize": 2, "constraints": {"python": "3.11"}, "commitMessagePrefix": "chore(deps): renovate", "prConcurrentLimit": 0, "prHourlyLimit": 3, "rebaseWhen": "auto", "automergeType": "pr", "platformAutomerge": true, "packageRules": [{"groupName": "all patch updates", "updateTypes": ["patch"], "excludePackageNames": ["dictor"], "automerge": true}, {"groupName": "AWS Client packages", "matchPackageNames": ["awscli", "boto3", "botocore", "s3transfer"]}, {"groupName": "Celery packages", "matchPackageNames": ["celery", "billiard", "kombu", "pytest-celery"]}, {"groupName": "Elasticsearch packages", "matchPackagePrefixes": ["elasticsearch"]}, {"groupName": "Glom packages", "matchPackageNames": ["glom", "boltons", "face"]}, {"groupName": "grpcio and protobuf packages", "matchPackageNames": ["grpcio", "grpcio-health-checking", "grpcio-status", "grpcio-tools", "protobuf", "pylint-protobuf", "types-protobuf"]}, {"groupName": "pyasn1 packages", "matchPackageNames": ["pyasn1", "pyasn1_modules"]}, {"groupName": "Pyflakes packages", "matchPackageNames": ["flake8", "pycodestyle", "pyflakes"]}, {"groupName": "Pylint packages", "matchPackageNames": ["pylint", "astroid"]}, {"groupName": "disabled packages", "packageNames": ["ddtrace"], "enabled": false}, {"groupName": "ddtrace", "description": "disable automerge for ddtrace", "packageNames": ["ddtrace"], "automerge": false}]}