"""
Customization of Pytest integration with doctest.

This plugin enables doctest for an existing codebase making sure that

  1. Files that can't be collected, due to e.g. import errors, can be ignored.
  2. Existing tests that fail can be skipped and fixed in the future.

Note, that doctest MUST be run in a separate job as custom behavior of this
plugin can effectively IGNORE some Python files.
"""

from pathlib import Path

import pytest

from booksy_pytest_doctest._blacklist import Blacklist
from booksy_pytest_doctest._marker import Marker
from booksy_pytest_doctest._reader import DoctestignoreReader

marker = Marker("doctest", doc="marks tests for doctest examples")
blacklist = Blacklist()


def pytest_configure(config: pytest.Config) -> None:
    cfgdir = config.rootpath
    if config.inipath:
        cfgdir = config.inipath.parent

    config.addinivalue_line("markers", str(marker))
    blacklist.reset(cfgdir, DoctestignoreReader(cfgdir / ".doctestignore").read())


def pytest_ignore_collect(
    collection_path: Path,
    path,
    config: pytest.Config,
) -> bool | None:
    # Some files fail on collection level as they probably have not been imported
    # for a long time. We need to ignore them!
    ignore = True
    force_accept = False
    let_others_decide = None

    if blacklist.match_path(collection_path):
        return ignore

    return let_others_decide


def pytest_itemcollected(item: pytest.Item) -> None:
    if isinstance(item, pytest.DoctestItem):
        if blacklist.match_item(item):
            item.add_marker(pytest.mark.skip("doctestignore"))
        item.add_marker(marker.name, append=True)
