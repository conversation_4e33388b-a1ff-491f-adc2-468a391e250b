from django_socio_grpc.proto_serializers import ProtoSerializer
from rest_framework import serializers

from merger_grpc.proto.appointment_pb2 import (
    AppointmentDetails,
    AppointmentUpdate,
    AppointmentsSyncRequest,
    AppointmentsSyncResponse,
    TimeSlotsQueryRequest,
    TimeSlotsQueryResponse,
)
from lib.protobuf.fields import DeprecatedTimestampDateField, DeprecatedTimestampDateTimeField


class TimeSlotsQueryRequestSerializer(ProtoSerializer):
    class Meta:
        proto_class = TimeSlotsQueryRequest

    class SubBookingSerializer(ProtoSerializer):
        class Meta:
            proto_class = TimeSlotsQueryRequest.SubBooking

        service_variant_id = serializers.IntegerField()
        staffer_id = serializers.ListField(
            child=serializers.IntegerField(),
            required=False,
        )

    business_id = serializers.IntegerField()
    omit_appointment_import_id = serializers.CharField(required=False, allow_blank=True)
    subbookings = SubBookingSerializer(many=True)
    start_date = DeprecatedTimestampDateField()
    end_date = DeprecatedTimestampDateField()


class StaffersAvailability(ProtoSerializer):
    staffer_id = serializers.IntegerField()
    slots = serializers.ListField(child=DeprecatedTimestampDateTimeField())


class TimeSlotsQueryResponseSerializer(ProtoSerializer):
    class Meta:
        proto_class = TimeSlotsQueryResponse

    business_id = serializers.IntegerField()
    staffers_availability = StaffersAvailability(many=True)


class SubBookingSerializer(ProtoSerializer):
    id = serializers.IntegerField(required=False)
    booked_from = DeprecatedTimestampDateTimeField(required=False)
    booked_till = DeprecatedTimestampDateTimeField(required=False)
    staffer_id = serializers.IntegerField(required=False)
    appliance_id = serializers.IntegerField(required=False)
    service_variant_id = serializers.IntegerField()
    _available_staffer_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
    )


class AppointmentRequiredPaymentSerializer(ProtoSerializer):
    prepayment = serializers.DecimalField(max_digits=10, decimal_places=2)
    payment_url = serializers.CharField(required=False, allow_blank=True)


class AppointmentSerializer(ProtoSerializer):
    class Meta:
        proto_class = AppointmentDetails

    business_id = serializers.IntegerField()
    id = serializers.IntegerField(required=False)
    import_id = serializers.CharField(required=False, allow_blank=True)
    status = serializers.CharField(required=False, allow_blank=True)
    customer_name = serializers.CharField(required=False, allow_blank=True)
    customer_phone = serializers.CharField(required=False, allow_blank=True)
    customer_email = serializers.CharField(required=False, allow_blank=True)
    customer_note = serializers.CharField(required=False, allow_blank=True)
    booked_for_id = serializers.IntegerField(required=False)
    subbookings = SubBookingSerializer(many=True)
    _required_payment = AppointmentRequiredPaymentSerializer(required=False)
    _from_subdomain = serializers.BooleanField(required=False)


class AppointmentUpdateSerializer(ProtoSerializer):
    class Meta:
        proto_class = AppointmentUpdate

    business_id = serializers.IntegerField()
    id = serializers.IntegerField()
    import_id = serializers.CharField()
    status = serializers.CharField(required=False, allow_blank=True)
    customer_note = serializers.CharField(required=False, allow_blank=True)


class AppointmentsSyncRequestSerializer(ProtoSerializer):
    class Meta:
        proto_class = AppointmentsSyncRequest

    business_id = serializers.IntegerField()
    booksy_customer_id = serializers.IntegerField()
    partner_customer_id = serializers.IntegerField()


class AppointmentsSyncResponseSerializer(ProtoSerializer):
    class Meta:
        proto_class = AppointmentsSyncResponse
