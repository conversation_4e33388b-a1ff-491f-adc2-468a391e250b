# /tmp/otel-collector-config.yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: :4317
      http:
        endpoint: :4318
processors:
  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 700
    check_interval: 5s
  cumulativetodelta:  # for NR
  transform: # for NR
    traces:
      queries:
        - truncate_all(attributes, 4095)
        - truncate_all(resource.attributes, 4095)
    logs:
      queries:
        - truncate_all(attributes, 4095)
        - truncate_all(resource.attributes, 4095)
extensions:
  health_check:
  zpages:
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 500
exporters:
#  otlp:
#    endpoint: tempo-eu-west-0.grafana.net:443
#    headers:
#      authorization: --secret--
  otlp:  # new relic
    endpoint: https://otlp.eu01.nr-data.net:4317
    headers:
      api-key: --secret--
  datadog:
    api:
      key: --secret--
      site: datadoghq.eu
  otlp/lightstep:
    endpoint: ingest.lightstep.com:443
    headers: { "lightstep-access-token": --secret-- }
  logging:
    loglevel: debug
service:
  extensions: [health_check, zpages, memory_ballast]
  pipelines:
    metrics:
      receivers: [otlp]
      exporters: [logging, datadog, otlp/lightstep, otlp]
      processors: [cumulativetodelta, batch]
    traces:
      receivers: [otlp]
      exporters: [logging, datadog, otlp/lightstep, otlp]
      processors: [memory_limiter, transform, batch]
    logs:
      receivers: [otlp]
      exporters: [otlp/lightstep]
      processors: [transform, batch]
