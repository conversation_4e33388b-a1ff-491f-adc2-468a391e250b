from bo_obs.datadog.enums import BooksyTeams
from django.contrib.messages.views import SuccessMessageMixin

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.report import BooksyGiftCardReportForm
from webapps.user.groups import GroupNameV2


class BooksyGiftCardsFinancialReportView(SuccessMessageMixin, GroupPermissionMixin, FormView):
    full_access_groups = (GroupNameV2.PAYMENTS_AND_CHECKOUTS,)
    booksy_teams = (BooksyTeams.NEW_FINANCIAL_SERVICES,)

    permission_required = 'user.create_reports'
    form_class = BooksyGiftCardReportForm
    template_name = 'admin/custom_views/booksy_gift_cards_report.html'
    success_url = 'booksy_gift_card_report'

    def get(self, request, *args, **kwargs):
        self._admin_user = request.user.user
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self._admin_user = request.user.user
        return super().post(request, *args, **kwargs)

    def get_initial(self):
        initial = self.initial or {}
        initial['email'] = self._admin_user.email
        return initial

    def get_success_message(self, cleaned_data):
        email = cleaned_data['email']
        message = f'Report sent to {email}'
        return message

    def form_valid(self, form):
        from webapps.statistics.tasks import generate_summary_report

        generate_summary_report.delay(
            email=form.cleaned_data['email'],
            start_date=form.cleaned_data['start_date'].isoformat(),
            end_date=form.cleaned_data['end_date'].isoformat(),
            report_type='booksy_gift_cards_summary',
        )
        return super().form_valid(form)
