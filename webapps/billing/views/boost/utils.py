from decimal import Decimal

from django.conf import settings

from webapps.billing.interfaces import boost
from webapps.boost.apis.overdue_transactions import BoostOverdueInfo
from webapps.boost.services.status import BusinessBoostStatus


def get_boost_overdue_info(business_id: int) -> tuple[BusinessBoostStatus, BoostOverdueInfo]:
    business_boost_info = boost.business_boost_info(business_id=business_id)
    if not business_boost_info.promoted_before:
        overdue_info = BoostOverdueInfo(
            business_id=business_id,
            gross_amount=Decimal(0),
            currency=settings.CURRENCY_CODE,
        )
    else:
        overdue_info = boost.total_overdue_info(business_id=business_id)
    return business_boost_info, overdue_info
