from dataclasses import dataclass
from functools import lru_cache
from typing import Op<PERSON>, Tuple

from country_config.base_info import (
    BASE_INFO,
    CS_EMAILS,
    CS_EMAILS_BIZ,
    CS_PHONES,
    CS_WORKING_HOURS,
    CURRENCY_CODES,
    CURRENCY_DATA,
    CURRENCY_LOCALES,
    DEFAULT_TIME_ZONES,
    FIRST_DAY_OF_WEEK,
    LANGUAGE_CODES,
    REPORT_EMAILS,
)
from country_config.enums import Country
from country_config.features import SERVICE_SUGGESTIONS


@dataclass(init=False)
class CountryConfig:  # pylint: disable=too-many-instance-attributes
    """A dataclass storing per country configuration.

    All django settings that are related to country code and
    not server instance should be migrated to this class.

    """

    country_code: Country

    # locales
    language_code: str
    currency_locale: str
    currency_code: str
    currency_data: dict
    default_time_zone: str
    first_day_of_week: int

    # base info
    name: str
    latitude: float
    longitude: float
    zoom: int
    address_format: str
    use_state_code: bool
    no_zipcodes: bool
    zipcode_regexp: Optional[str]
    line_1_format: str
    line_1_regexp: str
    street_regexp: str
    multiline_address_format: list[str]

    # customer support phones
    cs_phone: str
    cs_working_hours: Tuple[str, str]
    cs_email: str
    report_email: str

    # features
    service_suggestion_subset: Optional[dict]

    @lru_cache(maxsize=len(Country))
    def __new__(cls, country_code: Country):
        """Only create once per country and cache."""
        if country_code not in cls.supported_countries():
            raise ValueError('country is not supported')
        return super().__new__(cls)

    def __init__(self, country_code: Country):
        if hasattr(self, 'country_code'):
            # already initialized and fetched from _cache
            # no need to run __init__ again
            return
        self.country_code = country_code

        # locales
        self.language_code = LANGUAGE_CODES[country_code]
        self.currency_locale = CURRENCY_LOCALES[country_code]
        self.currency_code = CURRENCY_CODES[country_code]
        self.currency_data = CURRENCY_DATA[country_code]
        self.default_time_zone = DEFAULT_TIME_ZONES.get(country_code, 'UTC')
        self.first_day_of_week = FIRST_DAY_OF_WEEK[country_code]

        # base info
        info = BASE_INFO[country_code]
        self.name = info['name']
        self.latitude = info['latitude']
        self.longitude = info['longitude']
        self.zoom = info['zoom']
        self.address_format = info['address_format']
        self.multiline_address_format = info['multiline_address_format']
        self.use_state_code = info.get('use_state_code', False)
        self.no_zipcodes = info['no_zipcodes']
        self.zipcode_regexp = info['zipcode_regexp']
        self.line_1_format = info.get('line_1_format')
        self.line_1_regexp = info.get('line_1_regexp')
        self.street_regexp = info.get('street_regexp')

        # customer support phones
        self.cs_phone = CS_PHONES.get(country_code, CS_PHONES[Country.US])
        self.cs_working_hours = CS_WORKING_HOURS.get(
            country_code,
            CS_WORKING_HOURS[Country.US],
        )
        self.cs_email = CS_EMAILS.get(country_code, CS_EMAILS['default'])
        self.cs_email_biz = CS_EMAILS_BIZ.get(country_code, CS_EMAILS_BIZ['default'])
        self.report_email = REPORT_EMAILS.get(
            country_code,
            REPORT_EMAILS['default'],
        )

        # features
        self.service_suggestion_subset = SERVICE_SUGGESTIONS.get(country_code)

    @classmethod
    def supported_countries(cls):
        return frozenset(BASE_INFO)
