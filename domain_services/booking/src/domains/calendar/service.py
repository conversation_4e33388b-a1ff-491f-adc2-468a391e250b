from datetime import date
from typing import Protocol

from ...commons.types import DayMinutes, ResourceId
from ..appointment.dto.draft import DraftTimeSlot
from ..business.service import BusinessService
from .aggregate import CalendarAggregate
from .dto import PossibleSlotsMap, StaffersAvailability, TimeSlotsQuery
from .repository import CalendarRepository


class CalendarService(Protocol):
    def find_time_slots(
        self,
        time_slots_query: TimeSlotsQuery,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
        include_empty_days: bool = False,
    ) -> PossibleSlotsMap: ...

    def find_first_free_time_slot(
        self,
        time_slots_query: TimeSlotsQuery,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
    ) -> PossibleSlotsMap: ...

    def check_staffers_availability(
        self,
        time_slots_query: TimeSlotsQuery,
        staffer_ids: set[ResourceId],
        current_time_slot: DraftTimeSlot,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
    ) -> StaffersAvailability: ...


class CalendarServiceImpl(CalendarService):
    _business_service: BusinessService
    _calendar_repository: CalendarRepository
    _calendar_aggregate: CalendarAggregate

    def __init__(
        self,
        business_service: BusinessService,
        calendar_repository: CalendarRepository,
        calendar_aggregate: CalendarAggregate,
    ) -> None:
        self._business_service = business_service
        self._calendar_repository = calendar_repository
        self._calendar_aggregate = calendar_aggregate

    def find_time_slots(
        self,
        time_slots_query: TimeSlotsQuery,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
        include_empty_days: bool = False,
    ) -> PossibleSlotsMap:
        price_list = self._business_service.get_price_list(time_slots_query.business_id)
        resource_ids = self._calendar_aggregate.extract_resources_ids(
            time_slots_query.bookings, price_list
        )
        daily_resources_booked_occupancy_map = (
            self._calendar_repository.get_daily_resources_booked_occupancy_map(
                business_id=time_slots_query.business_id,
                resource_ids=resource_ids,
                start_date=time_slots_query.start_date,
                end_date=time_slots_query.end_date,
            )
        )

        daily_slots_map = self._calendar_aggregate.find_time_slots(
            bookings=time_slots_query.bookings,
            price_list=price_list,
            daily_resources_booked_occupancy_map=daily_resources_booked_occupancy_map,
            minutes_offsets_map=minutes_offsets_map,
            include_empty_days=include_empty_days,
        )

        return daily_slots_map

    def find_first_free_time_slot(
        self,
        time_slots_query: TimeSlotsQuery,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
    ) -> PossibleSlotsMap:
        price_list = self._business_service.get_price_list(time_slots_query.business_id)
        daily_resources_booked_occupancy_map = (
            self._calendar_repository.get_daily_resources_booked_occupancy_map(
                business_id=time_slots_query.business_id,
                resource_ids=self._calendar_aggregate.extract_resources_ids(
                    time_slots_query.bookings, price_list
                ),
                start_date=time_slots_query.start_date,
                end_date=time_slots_query.end_date,
            )
        )

        daily_slots_map = self._calendar_aggregate.find_time_slots(
            bookings=time_slots_query.bookings,
            price_list=price_list,
            daily_resources_booked_occupancy_map=daily_resources_booked_occupancy_map,
            minutes_offsets_map=minutes_offsets_map,
            find_only_first=True,
        )

        return daily_slots_map
