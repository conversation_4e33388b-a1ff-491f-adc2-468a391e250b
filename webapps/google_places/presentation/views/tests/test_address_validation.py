# pylint: disable=line-too-long
from unittest.mock import patch
import pytest
from django.test import override_settings
from django.urls import reverse
from rest_framework import status
from parameterized import parameterized

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.google_places.domain.enums import Country
from webapps.google_places.infrastructure.apis.mock_data.address_validation_response import (
    ADDRESS_VALIDATION_MOCK_RESPONSE,
)
from webapps.google_places.infrastructure.apis.mock_data.address_validation_response_pl import (
    ADDRESS_VALIDATION_MOCK_RESPONSE_PL,
)
from webapps.google_places.infrastructure.apis.mock_data.address_validation_response_confirm_add_subpremises import (
    ADDRESS_VALIDATION_MOCK_RESPONSE_CONFIRM_ADD_SUBPREMISES,
)
from webapps.google_places.domain.ports.address_validation_client import ApiResponse

from webapps.user.baker_recipes import user_recipe


@pytest.mark.django_db
class TestAddressValidationView(BaseBusinessApiTestCase):
    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        super().setUp()

    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_success_integration(self, mock_api_client):
        """Integration test that goes through the full flow:
        view → service → gateway → converter → API client.

        This tests that ValidateAddressComponentsConverter correctly processes
        the Address Validation API response format."""

        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE
        )

        url = reverse(
            "google_places_validate_address",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        data = {
            'address_lines': ['930 Woodbourne Road PA'],
            'administrative_area': 'PA',
            'location': {'latitude': 40.192821, 'longitude': -74.891032},
            'address_components': {
                'city': 'Langhorne',
                'country': 'USA',
                'zipcode': '19047',
                'subpremise': 'PA',
            },
            'previous_response_id': '9721c868-031f-435e-99bc-1a42625fada6',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        self.assertIn('possible_next_action', response.data)
        self.assertIn('is_valid', response.data)
        self.assertIn('formatted_address', response.data)
        self.assertIn('address_complete', response.data)
        self.assertIn('has_replaced_components', response.data)
        self.assertIn('missing_components', response.data)
        self.assertIn('unconfirmed_components', response.data)
        self.assertIn('replaced_components', response.data)
        self.assertIn('address_components', response.data)
        self.assertIn('address_lines', response.data)
        self.assertIn('pin_location', response.data)
        self.assertIn('response_id', response.data)
        self.assertIn('location', response.data)

        self.assertEqual('CONFIRM', response.data['possible_next_action'])
        self.assertTrue(response.data['is_valid'])
        self.assertEqual(
            '930 Woodbourne Road PA, Langhorne, PA 19047-1305, USA',
            response.data['formatted_address'],
        )
        self.assertTrue(response.data['address_complete'])
        self.assertTrue(response.data['has_replaced_components'])
        self.assertEqual('18a6f738-441f-466c-a39c-9617a6a5d924', response.data['response_id'])

        mock_api_client.assert_called_once()

        address_components = response.data['address_components']
        self.assertEqual('930', address_components['street_number'])
        self.assertEqual('Woodbourne Road', address_components['street'])
        self.assertEqual('Langhorne', address_components['city'])
        self.assertEqual('USA', address_components['country'])
        self.assertEqual('19047', address_components['zipcode'])
        self.assertEqual('PA', address_components['subpremise'])

        self.assertEqual(['Langhorne'], response.data['replaced_components'])
        self.assertEqual(['subpremise'], response.data['unconfirmed_components'])

        location = response.data['location']
        self.assertIn('latitude', location)
        self.assertIn('longitude', location)
        self.assertEqual(40.192821, location['latitude'])
        self.assertEqual(-74.891032, location['longitude'])

        mock_api_client.assert_called_once()

    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_polish_address_integration(self, mock_api_client):
        """Integration test with Polish address data to test ValidateAddressComponentsConverter
        with different address formats and missing components."""

        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE_PL
        )

        url = reverse(
            "google_places_validate_address",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        data = {
            'address_lines': ['Aleje Jerozolimskie'],
            'administrative_area': 'Mazowieckie',
            'location': {'latitude': 52.1858312, 'longitude': 20.9047982},
            'address_components': {
                'city': 'Warszawa',
                'country': 'Poland',
                'zipcode': '02-495',
                'subpremise': '',
            },
            'previous_response_id': 'test-polish-address-id',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        self.assertEqual('FIX', response.data['possible_next_action'])
        self.assertFalse(response.data['is_valid'])
        self.assertEqual(
            'Aleje Jerozolimskie, 02-495 Warszawa, Polska',
            response.data['formatted_address'],
        )
        self.assertFalse(response.data['address_complete'])
        self.assertFalse(response.data['has_replaced_components'])
        self.assertEqual('34faa851-64a6-47c8-9db9-49a94bb7cc16', response.data['response_id'])

        mock_api_client.assert_called_once()

        address_components = response.data['address_components']
        self.assertEqual('', address_components['street_number'])
        self.assertEqual('Aleje Jerozolimskie', address_components['street'])
        self.assertEqual('Warszawa', address_components['city'])
        self.assertEqual('Polska', address_components['country'])
        self.assertEqual('02-495', address_components['zipcode'])
        self.assertEqual('', address_components['subpremise'])

        self.assertEqual(['street_number'], response.data['missing_components'])
        self.assertEqual([], response.data['replaced_components'])
        self.assertEqual([], response.data['unconfirmed_components'])

        location = response.data['location']
        self.assertIn('latitude', location)
        self.assertIn('longitude', location)
        self.assertEqual(52.1858312, location['latitude'])
        self.assertEqual(20.9047982, location['longitude'])

    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_pin_location_error_integration(self, mock_api_client):
        """Integration test that tests ERROR status in pin location validation
        by using coordinates far from the mock response location."""

        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE
        )

        url = reverse(
            "google_places_validate_address",
            kwargs={
                "business_pk": self.business.id,
            },
        )

        data = {
            'address_lines': ['930 Woodbourne Road PA'],
            'administrative_area': 'PA',
            'location': {'latitude': 40.5, 'longitude': -75.5},  # Far from mock response
            'address_components': {
                'city': 'Langhorne',
                'country': 'USA',
                'zipcode': '19047',
                'subpremise': 'PA',
            },
            'previous_response_id': 'test-pin-location-error-id',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        self.assertEqual('CONFIRM', response.data['possible_next_action'])
        # Even though possible_next_action is CONFIRM, is_valid should be False due to pin location ERROR
        self.assertFalse(response.data['is_valid'])
        self.assertEqual(
            '930 Woodbourne Road PA, Langhorne, PA 19047-1305, USA',
            response.data['formatted_address'],
        )
        mock_api_client.assert_called_once()

        pin_location = response.data['pin_location']
        self.assertEqual('ERROR', pin_location['status'])
        self.assertIn('over 1500 feet', pin_location['text'])

        location = response.data['location']
        self.assertIn('latitude', location)
        self.assertIn('longitude', location)
        self.assertEqual(40.192821, location['latitude'])
        self.assertEqual(-74.891032, location['longitude'])

    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_pin_location_warn_integration(self, mock_api_client):
        """Integration test that tests WARN status in pin location validation.
        is_valid should be True when pin location status is WARN."""

        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE
        )

        url = reverse(
            "google_places_validate_address",
            kwargs={
                "business_pk": self.business.id,
            },
        )

        # Coordinates are ~430 feet away from the mock response location, which should trigger a WARN
        data = {
            'address_lines': ['930 Woodbourne Road PA'],
            'administrative_area': 'PA',
            'location': {'latitude': 40.194, 'longitude': -74.891},
            'address_components': {
                'city': 'Langhorne',
                'country': 'USA',
                'zipcode': '19047',
                'subpremise': 'PA',
            },
            'previous_response_id': 'test-pin-location-warn-id',
        }
        response = self.client.post(url, data=data)
        self.assertEqual(status.HTTP_200_OK, response.status_code)

        self.assertTrue(response.data['is_valid'])

        mock_api_client.assert_called_once()

        pin_location = response.data['pin_location']
        self.assertEqual('WARN', pin_location['status'])
        self.assertIn('over 300 feet', pin_location['text'])

        location = response.data['location']
        self.assertIn('latitude', location)
        self.assertIn('longitude', location)
        self.assertEqual(40.192821, location['latitude'])
        self.assertEqual(-74.891032, location['longitude'])

    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_address_formatting_us_style(self, mock_api_client):
        """Tests that street numbers are properly formatted for preceding countries (e.g., US)."""
        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE
        )
        url = reverse("google_places_validate_address", kwargs={"business_pk": self.business.id})

        data = {
            'address_lines': ['Woodbourne Road'],
            'administrative_area': 'PA',
            'location': {'latitude': 40.192821, 'longitude': -74.891032},
            'address_components': {
                'street_number': '930',
                'city': 'Test City',
                'country': 'USA',
                'zipcode': '12345',
                'subpremise': '',
            },
            'previous_response_id': 'test-formatting-us-id',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        mock_api_client.assert_called_once()

        call_args = mock_api_client.call_args
        called_params = call_args[0][0]
        self.assertEqual(['930 Woodbourne Road'], called_params.address.address_lines)

    @override_settings(API_COUNTRY=Country.PL)
    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_address_formatting_pl_style(self, mock_api_client):
        """Tests that street numbers are properly formatted for following countries (e.g., PL)."""
        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE_PL
        )

        url = reverse("google_places_validate_address", kwargs={"business_pk": self.business.id})

        data = {
            'address_lines': ['Aleje Jerozolimskie'],
            'administrative_area': 'Mazowieckie',
            'location': {'latitude': 52.1858312, 'longitude': 20.9047982},
            'address_components': {
                'street_number': '123',
                'city': 'Warszawa',
                'country': 'Poland',
                'zipcode': '02-495',
                'subpremise': '',
            },
            'previous_response_id': 'test-formatting-pl-id',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        mock_api_client.assert_called_once()

        call_args = mock_api_client.call_args
        called_params = call_args[0][0]
        self.assertEqual(['Aleje Jerozolimskie 123'], called_params.address.address_lines)

    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_confirm_add_subpremises_integration(self, mock_api_client):
        """Integration test that tests CONFIRM_ADD_SUBPREMISES possible next action."""

        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE_CONFIRM_ADD_SUBPREMISES
        )

        url = reverse(
            "google_places_validate_address",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        data = {
            'address_lines': ['123 Main Street'],
            'administrative_area': 'NY',
            'location': {'latitude': 40.7505, 'longitude': -73.9934},
            'address_components': {
                'city': 'New York',
                'country': 'USA',
                'zipcode': '10001',
                'subpremise': '',
            },
            'previous_response_id': 'test-confirm-add-subpremises-id',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        self.assertEqual('CONFIRM_ADD_SUBPREMISES', response.data['possible_next_action'])
        self.assertTrue(response.data['is_valid'])

        mock_api_client.assert_called_once()

    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_subpremise_formatting_us_style_integration(
        self,
        mock_api_client,
    ):
        """Integration test that verifies subpremise formatting works with US-style addresses."""
        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE
        )

        url = reverse("google_places_validate_address", kwargs={"business_pk": self.business.id})

        data = {
            'address_lines': ['Main Street'],
            'administrative_area': 'NY',
            'location': {'latitude': 40.7505, 'longitude': -73.9934},
            'address_components': {
                'street_number': '123',
                'city': 'New York',
                'country': 'USA',
                'zipcode': '10001',
                'subpremise': 'Apt 4B',
            },
            'previous_response_id': 'test-subpremise-formatting-us-id',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        mock_api_client.assert_called_once()

        call_args = mock_api_client.call_args
        called_params = call_args[0][0]
        self.assertEqual(['123 Main Street', 'Apt 4B'], called_params.address.address_lines)

    @override_settings(API_COUNTRY=Country.PL)
    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_post_subpremise_formatting_pl_style_integration(self, mock_api_client):
        """Integration test that verifies subpremise formatting works with Polish-style addresses."""
        mock_api_client.return_value = ApiResponse(
            status_code=200, data=ADDRESS_VALIDATION_MOCK_RESPONSE_PL
        )

        url = reverse("google_places_validate_address", kwargs={"business_pk": self.business.id})

        data = {
            'address_lines': ['Aleje Jerozolimskie'],
            'administrative_area': 'Mazowieckie',
            'location': {'latitude': 52.1858312, 'longitude': 20.9047982},
            'address_components': {
                'street_number': '98',
                'city': 'Warszawa',
                'country': 'Poland',
                'zipcode': '02-495',
                'subpremise': 'Unit 5',
            },
            'previous_response_id': 'test-subpremise-formatting-pl-id',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        mock_api_client.assert_called_once()

        call_args = mock_api_client.call_args
        called_params = call_args[0][0]
        self.assertEqual(['Aleje Jerozolimskie 98', 'Unit 5'], called_params.address.address_lines)

    @parameterized.expand(
        [
            (
                'en-us',
                Country.US,
                'The pin is over 300 feet from the address.',
                ADDRESS_VALIDATION_MOCK_RESPONSE,
                (40.1928210, -74.8898950),
            ),
            (
                'pl-pl',
                Country.PL,
                'Pinezka znajduje się ponad 100 metrów od adresu.',
                ADDRESS_VALIDATION_MOCK_RESPONSE_PL,
                (52.1858312, 20.9070868),
            ),
            (
                'en-us',
                Country.PL,
                'The pin is over 100 meters from the address.',
                ADDRESS_VALIDATION_MOCK_RESPONSE_PL,
                (52.1858312, 20.9070868),
            ),
        ]
    )
    @patch(
        'webapps.google_places.infrastructure.apis.address_validation_api.GoogleAddressValidationClient.validate_address'
    )
    def test_pin_text_translation(
        self,
        language_code,
        api_country,
        expected_translated_text,
        mock_response_data,
        user_pin_location,
        mock_api_client,
    ):
        """Tests that pin location messages are properly translated based on language settings."""
        mock_api_client.return_value = ApiResponse(status_code=200, data=mock_response_data)

        url = reverse("google_places_validate_address", kwargs={"business_pk": self.business.id})

        data = {
            'address_lines': ['123 Test Address'],
            'administrative_area': 'Test Area',
            'location': {'latitude': user_pin_location[0], 'longitude': user_pin_location[1]},
            'address_components': {
                'street_number': '123',
                'city': 'Test City',
                'country': api_country.value,
                'zipcode': '12345',
                'subpremise': '',
            },
            'previous_response_id': 'test-translation-id',
        }

        with override_settings(LANGUAGE_CODE=language_code, API_COUNTRY=api_country):
            response = self.client.post(url, data=data)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        mock_api_client.assert_called_once()

        pin_location = response.data['pin_location']
        self.assertEqual(expected_translated_text, pin_location['text'])
