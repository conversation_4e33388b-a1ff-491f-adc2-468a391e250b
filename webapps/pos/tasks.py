import datetime
import json
import logging
from itertools import islice
from typing import List, Optional

from dacite import from_dict
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.db import DatabaseError
from django.db import transaction as db_transaction
from django.db.models import (
    Exists,
    F,
    OuterRef,
    Prefetch,
    prefetch_related_objects,
)
from django.db.models import QuerySet
from django.utils.translation import gettext as _

from lib import jinja_renderer
from lib.celery_tools import (
    PostTransactionTaskRetryException,
    celery_task,
    post_transaction_task,
)
from lib.db import (
    READ_ONLY_DB,
    retry_on_sync_error,
    using_db_for_reads,
)
from lib.email import send_email
from lib.locks import (
    BusinessActionLock,
    HandleNoShowBGCTransfer,
    RedlockError,
)
from lib.payment_providers.entities import DeviceDataEntity
from lib.pos.entities import HandleGiftCardTransactionEntity
from lib.tools import (
    tznow,
)
from lib.payment_providers.entities import DeviceDataDict
from webapps.booking.enums import (
    <PERSON>ppoint<PERSON><PERSON><PERSON><PERSON>,
    WhoMakesChange,
)
from webapps.business.models import ServiceVariantPayment, ServiceVariantChangelog
from webapps.notification.models import NotificationHistory
from webapps.point_of_sale.services.basket_payment_analytics import BasketPaymentAnalyticsService
from webapps.pos.booksy_gift_cards.grpc_client import BooksyGiftCardsClient
from webapps.pos.booksy_gift_cards.protobufs.booksy_gift_cards_pb2 import (  # pylint: disable=no-name-in-module
    ReissueReason,
)
from webapps.pos.enums import (
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
    ReissueGiftCardReasonEnum,
)
from webapps.pos.enums.consts import SPLITTING_STATUSES
from webapps.stripe_integration.services import (
    SynchronizeService,
)

logger = logging.getLogger('booksy.pos_task')


@post_transaction_task
def ReleaseDepositOnPayment(deposit_id):  # pylint: disable=invalid-name
    """Release deposit on successful payment for bookings with deposit.

    If customer cancels booking it should be triggered only if it was done
    within business policy (pos.deposit_cancel_time).

    :param deposit_id: ID of deposit Transaction to release.
    :returns: reason, deposit_id, status_code

    """
    from webapps.pos.models import Transaction
    from webapps.pos.provider import get_payment_provider

    deposit = Transaction.objects.filter(id=deposit_id).first()
    if deposit is None:
        return 'no_deposit', deposit_id, None

    latest_receipt_status_code = deposit.latest_receipt.status_code
    if latest_receipt_status_code not in [
        receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
        receipt_status.DEPOSIT_CHARGE_FAILED,
    ]:
        return 'bad_status', deposit_id, None

    payment_row = deposit.latest_receipt.payment_rows.get()
    provider = get_payment_provider(
        codename=payment_row.provider,
        txn=deposit,
    )
    deposit = provider.cancel_deposit(deposit, payment_row)

    return 'ok', deposit_id, latest_receipt_status_code


@post_transaction_task
def ReleaseDepositOnCancel(appointment_id):  # pylint: disable=invalid-name
    """Release deposit on booking cancel (or decline).

    If customer cancels booking it should be triggered only if it was done
    within business policy (pos.deposit_cancel_time).

    :param appointment_id: Appointment ID.

    """
    from webapps.pos.models import Transaction
    from webapps.pos.provider import get_payment_provider

    deposit = (
        Transaction.objects.filter(
            appointment_id=appointment_id,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            latest_receipt__status_code__in=[
                receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
                receipt_status.DEPOSIT_CHARGE_FAILED,
            ],
        )
        .select_related('latest_receipt')
        .last()
    )
    if deposit is None:
        return

    # cancel deposit - should be only 1 provider
    provider = get_payment_provider(
        codename=deposit.latest_receipt.payment_rows.get().provider,
        txn=deposit,
    )
    provider.cancel_deposit(deposit)


def close_transaction(
    txn: 'Transaction',
    log_note: str,
    handle_gift_card_transaction_details: HandleGiftCardTransactionEntity | None = None,
) -> None:
    """Closes transaction with:
    * prepayment -> sets PaymentSuccess status (used in CheckoutPrepaidTransaction)
    * Booksy Pay -> sets PaymentSuccess status (used in CheckoutBooksyPayTransaction)
    * Booksy Gift Card:
        - sets PaymentCanceled status if appointment canceled or (no-show and charge_bgc is False)
        - sets PaymentSuccess status if appointment no-show and charge_bgc is True
    """
    from webapps.pos.models import PaymentRowChange
    from webapps.register.models import Register

    # Closing Transaction
    temp_register = None
    register = txn.pos.registers.filter(is_open=True).first()

    if not register and txn.pos.registers_enabled:
        temp_register = Register(
            pos=txn.pos,
            is_open=True,
            opened_by=txn.pos.business.owner,
            opening_cash=0,
        )
        temp_register.save()

    txn.register = register if register else temp_register
    txn.operator = txn.pos.business.owner
    txn.save(update_fields=['register', 'operator'])
    final_status = receipt_status.PAYMENT_SUCCESS
    if handle_gift_card_transaction_details and any(
        [
            handle_gift_card_transaction_details.appointment_status
            != AppointmentStatus.NOSHOW.value,
            not handle_gift_card_transaction_details.charge_bgc,
        ]
    ):
        final_status = receipt_status.PAYMENT_CANCELED
    txn.latest_receipt.payment_rows.get().update_status(
        status=final_status,
        log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
        log_note=log_note,
    )

    if temp_register:
        temp_register.is_open = False
        temp_register.closed_by = txn.pos.business.owner
        temp_register.closed_at = txn.pos.business.tznow

        # save RegisterClosingAmounts
        for summary in temp_register.all_summaries:
            if summary.countable:
                summary.save()

        temp_register.save()


def retrieve_transaction(
    transaction_id=None, appointment_id=None, business_id=None, customer_user_id=None
) -> tuple[Optional['Transaction'], Optional[str]]:
    from webapps.pos.models import Transaction

    if bool(transaction_id) == bool(appointment_id):  # XOR
        msg = 'transaction_id xor booking required'
        logger.error('[POS_TASK]: %s', msg)
        return None, msg

    if transaction_id:
        txn = Transaction.objects.get(id=transaction_id)
    elif appointment_id:
        if bool(business_id) == bool(customer_user_id):  # XOR
            msg = 'Business xor Customer_user required'
            logger.error('[POS_TASK]: %s', msg)
            return None, msg

        txn = (
            Transaction.objects.by_appointment_id(appointment_id)
            .filter(transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT)
            .select_related(
                'pos',
                'latest_receipt',
            )
            .last()
        )
    else:
        txn = None

    return txn, None


@post_transaction_task
def CheckoutPrepaidTransaction(  # pylint: disable=invalid-name, too-many-return-statements, too-many-branches
    transaction_id=None, appointment_id=None, business_id=None, customer_user_id=None
):
    from webapps.pos.provider import get_payment_provider

    txn, error_msg = retrieve_transaction(
        transaction_id, appointment_id, business_id, customer_user_id
    )

    if error_msg:
        return error_msg

    if txn is None:
        return 'no_transaction', None

    if txn.latest_receipt.status_code not in [
        receipt_status.PREPAYMENT_SUCCESS,
        receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
    ]:
        return 'bad_status', txn.id

    if txn.latest_receipt.status_code == receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS:
        pr = txn.latest_receipt.payment_rows.get()
        # cancel prepayment - should be only 1 provider
        provider = get_payment_provider(
            codename=pr.provider,
            txn=txn,
        )

        provider.cancel_payment(pr)
        return 'ok', None

    close_transaction(txn, log_note='CheckoutPrepaidTransaction')
    return 'ok', txn.id


@post_transaction_task
def CheckoutBooksyPayTransaction(  # pylint: disable=invalid-name
    transaction_id=None, appointment_id=None, business_id=None, customer_user_id=None
):
    txn, error_msg = retrieve_transaction(
        transaction_id, appointment_id, business_id, customer_user_id
    )

    if error_msg:
        return error_msg

    if txn is None:
        return 'no_transaction', None

    if txn.latest_receipt.status_code != receipt_status.BOOKSY_PAY_SUCCESS:
        return 'bad_status', txn.id

    close_transaction(txn, log_note='CheckoutBooksyPayTransaction')
    return 'ok', txn.id


@post_transaction_task
def CancelBookingOnDepositFail(deposit_id, _instance=None):  # pylint: disable=invalid-name
    """Cancel booking if deposit authorization failed.

    :param transaction_id: ID of deposit Transaction
    :param _instance: Transaction instance - use it only in .run(),
                      because it's not pickleable.
    :returns: success, reason

    """
    assert deposit_id or _instance and not (deposit_id and _instance)

    # get deposit
    from webapps.pos.models import Transaction

    if deposit_id:
        deposit = Transaction.objects.filter(id=deposit_id).first()
    else:
        deposit = _instance
    if deposit is None:
        return False, 'no_deposit'
    if deposit.latest_receipt.status_code != receipt_status.DEPOSIT_AUTHORISATION_FAILED:
        return False, 'bad_status'
    if not deposit.appointment_id:
        return False, 'no_booking'

    # get (multi-)booking
    from webapps.booking.models import Appointment
    from webapps.user.tools import get_system_user

    if deposit.appointment:
        deposit.appointment.update_appointment(
            updated_by=get_system_user(),
            status=Appointment.STATUS.CANCELED,
            who_makes_change=WhoMakesChange.STAFF,
            force_status_transition=True,
        )

    return True, 'success'


@post_transaction_task
def POSActivityChange(pos_id, active):  # pylint: disable=invalid-name, too-many-return-statements
    """Update business settings and handle deposits on POS (de-)activation.

    Activation needs to:
     - set default tax rate for all services

    Deactivation needs to:
     - remove default tax rate from all services
     - remove deposit settings from all service variants
     - release all authorized deposits

    """
    from webapps.pos.models import POS

    pos = POS.objects.filter(id=pos_id).first()
    if pos is None:
        return "no_pos", None
    if pos.active != active:
        return "pos.active changed already", None
    if pos.business is None:
        return "default pos", None
    if active:
        # update all services with default tax_rate
        tax_rate = (
            pos.tax_rates.filter(
                default_for_service=True,
            )
            .values_list('rate', flat=True)
            .first()
        )
        count = (
            pos.business.services.filter(
                active=True,
                deleted__isnull=True,
            )
            .exclude(
                tax_rate=tax_rate,
            )
            .update(
                tax_rate=tax_rate,
                updated=tznow(),
            )
        )
        return "activated", count

    # update all services to remove tax_rates
    service_count = (
        pos.business.services.filter(
            active=True,
            deleted__isnull=True,
        )
        .exclude(
            tax_rate=None,
        )
        .update(
            tax_rate=None,
            updated=tznow(),
        )
    )

    ReleaseAllDepositOnServicesAndBookings(pos.business.id)
    return "deactivated", service_count


@post_transaction_task
def CloseAllCashRegisters(pos_id, operator_id):  # pylint: disable=invalid-name
    """Close all business cash registers"""
    from webapps.pos.models import POS
    from webapps.register.models import Register
    from webapps.user.models import User

    pos = POS.objects.select_related('business').get(id=pos_id)
    user = User.objects.filter(id=operator_id).first()
    Register.objects.filter(pos_id=pos_id, is_open=True).update(
        is_open=False,
        closed_by=user,
        closed_at=pos.business.tznow,
    )


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def CancelAllTransactionsOlderThan30days():  # pylint: disable=invalid-name
    # Remove MemoryError after resolving https://booksy.atlassian.net/browse/PY-1458
    raise MemoryError('This task allocate too much memory')
    # pylint: disable=unreachable
    from webapps.pos.models import Transaction

    for transaction in Transaction.objects.filter(
        latest_receipt__status_code__in=receipt_status.NAV_UNFINISHED,
        latest_receipt__created__lte=tznow() - datetime.timedelta(days=30),
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    ):
        transaction.update_payment_rows(
            receipt_status.PAYMENT_CANCELED,
        )


@celery_task
def SendReceiptToCustomer(
    pos_id,
    transaction_id,
    language,
    email,
    sender=NotificationHistory.SENDER_BUSINESS,
):  # pylint: disable=invalid-name
    from webapps.pos.models import (
        POS,
        Transaction,
    )
    from webapps.pos.tools import get_receipt_data

    transaction = Transaction.objects.get(id=transaction_id)
    pos = POS.objects.get(id=pos_id)
    template_args, receipt_number = get_receipt_data(pos, transaction, sender, language)
    sjr = jinja_renderer.ScenariosJinjaRenderer()
    body = sjr.render(
        scenario_name='receipt',
        template_name='receipt',
        language=language,
        template_args=template_args,
        extension='html',
        default=(),
    )
    from_data = (
        pos.business.name,
        settings.NO_REPLY_EMAIL,
    )

    send_email(
        to_addr=email,
        body=body,
        history_data={
            'sender': sender,
            'business_id': pos.business.id,
            'customer_id': transaction.customer_id,
            'task_id': f'pos:receipt:transaction_id={transaction.id}',
            'meta_receipt_number': receipt_number,
        },
        from_data=from_data,
        to_name=(transaction.customer and transaction.customer.get_full_name()),
    )


def close_transactions(transactions: QuerySet['Transaction'], log_note: str) -> None:
    from webapps.pos.models import PaymentRowChange
    from webapps.pos.tools import (
        close_register,
        open_register_if_needed,
    )

    txn_grouped_by_pos = {}

    for txn in transactions:
        txn_grouped_by_pos.setdefault(txn.pos, []).append(txn)

    for pos, pos_txns in list(txn_grouped_by_pos.items()):
        register, newly_open = open_register_if_needed(pos)

        for txn in pos_txns:
            try:
                txn.register = register
                txn.operator = pos.business.owner
                txn.save(update_fields=['register', 'operator'])

                pr = txn.latest_receipt.payment_rows.get()
                pr.update_status(
                    status=receipt_status.PAYMENT_SUCCESS,
                    log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
                    log_note=log_note,
                )
            except DatabaseError as e:
                logger.warning(
                    "%s failed to update transaction: %s, exception: %s",
                    log_note,
                    txn.id,
                    e,
                )
                continue

        if newly_open:
            close_register(pos, register)


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def ClosePrepaymentTransactionAtTime():  # pylint: disable=invalid-name
    from lib.timezone_hours import get_timezones_with_hour
    from webapps.pos.tools import unfinished_prepayment_query

    closing_timezones = get_timezones_with_hour(23)
    txs = unfinished_prepayment_query(closing_timezones)
    close_transactions(txs, log_note='ClosePrepaymentTransactionAtTime')


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def CloseBooksyPayTransactionAtTime():  # pylint: disable=invalid-name
    from lib.timezone_hours import get_timezones_with_hour
    from webapps.pos.tools import unfinished_booksy_pay_query

    closing_timezones = get_timezones_with_hour(23)
    txs = unfinished_booksy_pay_query(closing_timezones)
    close_transactions(txs, log_note='CloseBooksyPayTransactionAtTime')


@celery_task
def ChargeCancellationFeeTransactions(pos_id):  # pylint: disable=invalid-name
    """
    Charge all pro
    :param pos_id: int. valid POS id
    :return: None
    """
    from webapps.pos import enums
    from webapps.pos.models import (
        POS,
        Transaction,
    )
    from webapps.pos.provider import get_payment_provider
    from webapps.pos.tools import (
        close_register,
        open_register_if_needed,
    )

    pos = POS.objects.select_related("business__owner").get(id=pos_id)
    operator = pos.business.owner
    midnight = {
        'hour': 0,
        'minute': 0,
        'second': 0,
        'microsecond': 0,
    }
    now = tznow()
    # IMPORTANT this midnight depend on the
    # webapps.pos.tasks.FetchAutoChargePOSes in settings/celery.py
    created_start = now.replace(**midnight)
    created_end = created_start + datetime.timedelta(hours=23, minutes=59)

    transactions = Transaction.objects.filter(
        pos_id=pos_id,
        transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        deleted__isnull=True,
        charge_date__gte=created_start - datetime.timedelta(days=1),
        charge_date__lt=created_end,
        latest_receipt__status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
    ).select_related("latest_receipt")
    action = enums.BUSINESS_ACTION__CHARGE_DEPOSIT

    transactions = [
        # check all bookings have status no_show or cancelled
        transaction
        for transaction in transactions
        if transaction.action_allowed(action=action)
    ]

    for transaction in transactions:
        register, newly_open = open_register_if_needed(pos)

        # if so try to charge deposit
        provider = get_payment_provider(
            codename=transaction.latest_receipt.payment_rows.get().provider,
            txn=transaction,
        )
        # charge and save transaction
        provider.charge_deposit(
            transaction,
            operator=operator,
            register=register,
        )

        if newly_open:
            close_register(pos, register)


@celery_task
def FetchAutoChargePOSes():  # pylint: disable=invalid-name
    from webapps.pos.models import POS

    pos_ids = POS.objects.filter(
        active=True, deleted__isnull=True, business__active=True, auto_charge_cancellation_fee=True
    ).values_list('id', flat=True)
    for pos_id in pos_ids.iterator():
        ChargeCancellationFeeTransactions.delay(pos_id)


@celery_task
def SwitchToFraud(pos_ids):  # pylint: disable=invalid-name
    """
    Switch all given pos_ids to fraud status.

    :param pos_ids: iterable, of valid POS ids
    :return: None
    """
    from webapps.pos.models import (
        POS,
        POSChangeLog,
    )
    from webapps.pos.serializers import POSChangeLogSerializer
    from webapps.user.tools import get_system_user

    pos_queryset = POS.objects.filter(id__in=pos_ids)

    pos_queryset.update(fraud_status=POS.FRAUD_STATUS_POSSIBLE_FRAUD)

    user = get_system_user()
    serializer = POSChangeLogSerializer(pos_queryset, many=True)
    POSChangeLog.create_bulk_history(serializer.data, user.id)


@retry_on_sync_error
@db_transaction.atomic
def disable_payments(business, service, service_variants):
    """
    Remove service variants payments
    :param business: Business object
    :param service: service object
    :param service_variants: ServiceVariants objects
    """
    from webapps.feeds.enums import EventType
    from webapps.feeds.utils import update_to_external_partners

    variant_ids = [variant.id for variant in service_variants]

    # bulk soft delete
    ServiceVariantPayment.objects.filter(
        service_variant_id__in=variant_ids,
        service_variant__service_id=service.id,
        service_variant__service__business_id=business.id,
        deleted__isnull=True,
    ).update(deleted=tznow())

    for variant in service_variants:
        ServiceVariantChangelog.create_entry(None, variant)

    update_to_external_partners(
        EventType.SERVICE,
        business=business,
        service=service,
        ids_to_create=[],
        ids_to_update=variant_ids,
        ids_to_delete=[],
    )


@post_transaction_task(
    default_retry_delay=BusinessActionLock.lock_expiration_time_ms // 1000,
    retry_kwargs={'max_retries': 3},
    autoretry_for=(PostTransactionTaskRetryException,),
    retry_backoff=True,
)
def ReleaseAllDepositOnServicesAndBookings(business_id):  # pylint: disable=invalid-name
    """
    Release all deposits from all business services
    Release all deposits from all transactions

    :param business_id: int, valid business_id
    :return: None
    """
    from webapps.business.models import Business
    from webapps.pos.models import (
        InvalidPaymentRowUpdateError,
        Transaction,
    )
    from webapps.pos.provider import get_payment_provider

    # Get lock so that only one task is run for one business at a time
    try:
        lock = BusinessActionLock.lock(business_id)
    except RedlockError as redlock_error:
        raise PostTransactionTaskRetryException from redlock_error

    if not lock:
        raise PostTransactionTaskRetryException

    # get business
    business = Business.objects.get(id=business_id)

    for service in business.services.all():
        # disable service variants with ServiceVariantPayment
        service_variants = list(
            service.service_variants.filter(
                active=True,
                payment__isnull=False,
                payment__deleted__isnull=True,
            )
        )
        disable_payments(business, service, service_variants)

    # free all deposits
    pos = business.pos
    deposits = pos.transactions.filter(
        transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        latest_receipt__status_code__in=[
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            receipt_status.DEPOSIT_CHARGE_FAILED,
        ],
    )

    should_retry = False
    for deposit in deposits:
        provider = get_payment_provider(
            codename=deposit.latest_receipt.payment_rows.get().provider,
            txn=deposit,
        )
        try:
            provider.cancel_deposit(deposit)
        except InvalidPaymentRowUpdateError:
            should_retry = True

    BusinessActionLock.unlock(lock)
    if should_retry:
        raise PostTransactionTaskRetryException


@post_transaction_task
def DisablePrepayments(business_id):  # pylint: disable=invalid-name
    """
    Disables all serviceVariantPayments type Prepayment.
    Used in POSAdmin, when switch off prepayments on business.

    :param business_id: int, valid business_id
    :return: None
    """
    from webapps.business.models import Business

    # get business
    business = Business.objects.get(id=business_id)

    for service in business.services.filter(active=True):
        service_variants = list(
            service.service_variants.filter(
                active=True,
                payment__isnull=False,
                payment__payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
                payment__deleted__isnull=True,
            )
        )

        disable_payments(business, service, service_variants)


@celery_task
def recalculate_pos_plans_for_payment_type(
    plan_type: POSPlanPaymentTypeEnum,
    pos_ids: List = None,
    batch_size: int = 1000,
):
    from webapps.market_pay.models import AccountHolder
    from webapps.pos.models import (
        POS,
        BankAccount,
        PaymentType,
        POSChangeLog,
        POSPlan,
        TaxRate,
        Tip,
    )
    from webapps.pos.serializers import POSChangeLogSerializer
    from webapps.pos.tools import annotate_correct_pos_plan_pk

    if not POSPlan.objects.filter(individual=False, plan_type=plan_type).exists():
        # this pos plan type is disabled
        return

    poses = POS.objects.all()
    if pos_ids:
        poses = poses.filter(pk__in=pos_ids)

    # excluded poses with given plan_type, but in individual mode - we don't want to override
    # individual pos plans
    poses = poses.annotate(
        has_individual_pos_plan=Exists(
            POS.pos_plans.through.objects.filter(
                pos_id=OuterRef("id"), posplan__plan_type=plan_type, posplan__individual=True
            )
        )
    ).exclude(has_individual_pos_plan=True)

    poses = annotate_correct_pos_plan_pk(poses, plan_type)

    # exclude poses that already have appropriate pos plan assigned
    poses_to_update = poses.exclude(pos_plans=F("correct_pos_plan_pk"))

    # remove current (incorrect) pos plans from poses
    POS.pos_plans.through.objects.filter(
        pos__in=poses_to_update, posplan__plan_type=plan_type
    ).delete()

    # assign new plans and create logs
    poses_to_update = poses_to_update.iterator(chunk_size=batch_size)
    while True:
        batch = list(islice(poses_to_update, batch_size))
        if not batch:
            break

        plans_to_create = (
            POS.pos_plans.through(pos_id=pos.id, posplan_id=pos.correct_pos_plan_pk)
            for pos in batch
        )

        # assign plans
        POS.pos_plans.through.objects.bulk_create(plans_to_create)

        # create logs
        prefetch_related_objects(
            batch,
            Prefetch('tips', queryset=Tip.objects.all().only('id', 'pos_id', 'rate', 'default')),
            Prefetch(
                'tax_rates',
                queryset=TaxRate.objects.all().only(
                    'id', 'pos_id', 'rate', 'default_for_service', 'default_for_product'
                ),
            ),
            Prefetch('pos_plans', queryset=POSPlan.objects.all().only('id')),
            Prefetch('account_holders', queryset=AccountHolder.objects.all().only('id', 'pos_id')),
            Prefetch(
                'payment_types',
                queryset=PaymentType.objects.all().only(
                    'id', 'pos_id', 'order', 'default', 'enabled', 'code'
                ),
            ),
            Prefetch(
                'bank_account',
                queryset=BankAccount.objects.all().only(
                    'id', 'pos_id', 'routing_number', 'account_number', 'type'
                ),
            ),
        )

        pos_change_logs_to_create = []
        # note: serializing list with many=True gives significant performance boost over for loop
        for log_data in POSChangeLogSerializer(batch, many=True).data:
            pos_change_logs_to_create.append(
                POSChangeLog(
                    pos_id=log_data['id'],
                    data=json.dumps(log_data),
                    operator_id=None,
                )
            )
        POSChangeLog.objects.bulk_create(pos_change_logs_to_create)


@celery_task
def recalculate_pos_plans(pos_ids: List = None):
    """
    Calculates POSPlans for all POSes, which are not locked
    :return: None
    """

    for plan_type in POSPlanPaymentTypeEnum:
        recalculate_pos_plans_for_payment_type.delay(
            plan_type=plan_type,
            pos_ids=pos_ids,
        )


@celery_task
def cancel_cfp_task():
    from webapps.pos.models import (
        PaymentRowChange,
        Transaction,
    )

    txns = Transaction.objects.filter(
        latest_receipt__status_code=receipt_status.CALL_FOR_PAYMENT,
        latest_receipt__created__lt=tznow() - relativedelta(days=3),
    ).prefetch_related('latest_receipt__payment_rows')

    for txn in txns:
        txn.update_payment_rows(
            receipt_status.PAYMENT_CANCELED,
            log_action=PaymentRowChange.MULTI_ROW_UPDATE,
            log_note='cancel_cfp_task celery task',
        )


@celery_task
def execute_pending_refunds_task(payment_row_id=None):
    from webapps.pos.refund import execute_pending_refunds

    return execute_pending_refunds(payment_row_id)


@post_transaction_task
def log_transaction_commission_changes_task(transaction_id: int):
    from webapps.pos.models import (
        Transaction,
        TransactionRow,
    )

    transaction = Transaction.objects.filter(id=transaction_id)
    if not transaction.exists():
        raise ValueError(f'Transaction {transaction_id} not found')

    transaction_rows = TransactionRow.objects.filter(
        transaction_id=transaction_id,
    ).select_related('commission')
    for row in transaction_rows.iterator():
        row.log_commission_changes()


@celery_task
def batch_change_default_payment_method_to_pba_task(business_ids):
    if not business_ids:
        return

    from webapps.pos.models import POS

    for pos in POS.objects.filter(business_id__in=business_ids):
        if pos.is_pay_by_app_active:
            pos.enable_pay_by_app_default()


@celery_task
def batch_change_pos_plans_task(update_log_id, file_path):  # pylint: disable=too-many-locals
    """
    Will parse raw xlsx file and change POS plans
    :param file_path: s3 path to xlsx file
    :param update_log_id: admin user id
    :return:
    """

    from webapps.pos.enums import POSPlanBatchUpdateLogStatus
    from webapps.pos.models import (
        POS,
        POSChangeLog,
        POSPlan,
        POSPlanBatchUpdateLog,
    )
    from webapps.pos.serializers import POSChangeLogSerializer
    from webapps.pos.tools import (
        check_duplicates_business_current_plan,
        create_stripped_data_for_batch_change_pos_plans,
        exclude_businesses_with_invalid_posplans,
        prepare_data_for_posplans_update,
        update_posplans,
    )

    update_log = POSPlanBatchUpdateLog.objects.filter(id=update_log_id).first()

    if not (
        stripped_data := create_stripped_data_for_batch_change_pos_plans(
            file_path=file_path, update_log=update_log, logger=logger
        )
    ):
        return

    businesses_to_update = [business_id for business_id, _, _ in stripped_data]
    poses = POS.objects.filter(business_id__in=businesses_to_update).prefetch_related('business')
    posplans = POSPlan.objects.all().only('id', 'plan_type')

    excluded_businesses = exclude_businesses_with_invalid_posplans(poses, posplans, stripped_data)
    if duplicated_business_plan := check_duplicates_business_current_plan(stripped_data):
        excluded_businesses.update(duplicated_business_plan)

    new_posplan_map, current_posplan_map = prepare_data_for_posplans_update(
        poses, excluded_businesses, stripped_data
    )
    created_plans, removed_plans = update_posplans(new_posplan_map, current_posplan_map)

    # create logs
    changed_poses = POS.objects.filter(id__in=new_posplan_map)
    prefetch_related_objects(
        changed_poses,
        Prefetch('pos_plans', queryset=POSPlan.objects.all().only('id')),
    )
    pos_change_logs_to_create = []
    for log_data in POSChangeLogSerializer(changed_poses, many=True).data:
        pos_change_logs_to_create.append(
            POSChangeLog(
                pos_id=log_data['id'],
                data=json.dumps(log_data),
                operator_id=None,
            )
        )
    POSChangeLog.objects.bulk_create(pos_change_logs_to_create)
    update_log.data = json.dumps(excluded_businesses, indent=1)
    if created_plans == removed_plans:
        update_log.data += f"\nupdated plans: {created_plans}"
    else:
        update_log.data += f"\ncreated plans: {created_plans}"
        update_log.data += f"\nremoved plans: {removed_plans}"
    update_log.status = POSPlanBatchUpdateLogStatus.FINISHED
    update_log.save()


@celery_task(ignore_result=False)
def initialize_basket_payments(
    txn_id: int,
    txn_status,
    payment_row_ids: list[int],
    device_data_dict: dict | None = None,
    payment_token: str | None = None,
):
    # pylint: disable=too-many-branches
    from webapps.point_of_sale.models import (
        Basket,
        BasketPayment,
    )
    from webapps.point_of_sale.services.basket import BasketService
    from webapps.pos.models import (
        PaymentRow,
        Transaction,
    )
    from webapps.pos.services import (
        PaymentRowService,
        TransactionService,
    )

    device_data = (
        from_dict(
            data_class=DeviceDataEntity,
            data=device_data_dict,
        )
        if device_data_dict
        else None
    )

    txn = Transaction.objects.filter(id=txn_id).last()
    payment_rows = PaymentRow.objects.filter(
        id__in=payment_row_ids,
        receipt__transaction_id=txn_id,
    )

    if not txn:
        raise ValueError(f'Transaction was not found. txn_id: {txn_id}')
    if len(payment_rows) != len(payment_row_ids):
        raise ValueError(f'Not all payment rows were found. payment_rows_ids: {payment_row_ids}')

    TransactionService.process_cf_auth_status(
        txn=txn,
        txn_status=txn_status,
    )

    basket = Basket.objects.filter(id=txn.basket_id).first()
    if not basket:
        return f'No basket. txn_id: {txn_id}'

    if txn_status == receipt_status.ARCHIVED:
        BasketService.archive_basket(basket)
        return f'Basket archived. txn_id: {txn_id}'

    basket_payments = BasketPayment.objects.filter(
        id__in=[pr.basket_payment_id for pr in payment_rows]
    )
    basket_payment_map = {bp.id: bp for bp in basket_payments}
    save_log = False

    for pr in payment_rows:
        basket_payment = basket_payment_map.get(pr.basket_payment_id)
        payment_initialization = pr.payment_type.code in PaymentTypeEnum.terminal_methods()

        # Chargeback are created during webhook flow, so we should do nothing with them here
        if pr.status in SPLITTING_STATUSES:
            continue

        if basket_payment:
            # We let editing basket_payment only in Square flow and when cancel is happening.
            if (
                pr.payment_type.code == PaymentTypeEnum.SQUARE
                or txn_status == receipt_status.PAYMENT_CANCELED
            ):
                # Failed -> Canceled is omitted.
                if (
                    pr.parent_payment_row
                    and pr.parent_payment_row.status == receipt_status.PAYMENT_FAILED
                ):
                    continue

                save_log = True
                basket_payment.status = PaymentRowService.map_receipt_status(pr.status)
                basket_payment.save(update_fields=['status'])
        else:
            save_log = True
            basket_payment = TransactionService.create_basket_payment(
                basket=basket,
                txn=txn,
                pr=pr,
                payment_initialization=payment_initialization,
                payment_token=payment_token,
            )

            BasketPaymentAnalyticsService.create_analytics_data(
                basket_payment=basket_payment,
                device_data=device_data,
                trigger=TransactionService.get_device_data_trigger(pr),
            )

        pr.basket_payment_id = basket_payment.id
        pr.save(update_fields=['basket_payment_id'])

        if payment_initialization:
            SynchronizeService.synchronize_stripe_payment_intent(basket_payment=basket_payment)

    if save_log:
        BasketService.save_history(basket)

    return f'Any changes done: txn_id: {txn_id} | save_log: {save_log}'


@celery_task(time_limit=16 * 30, soft_time_limit=15 * 30)
def remove_square_payment_method_for_selected_pos_ids(pos_ids, force=False):
    from lib.point_of_sale.enums import PaymentMethodType
    from webapps.point_of_sale.models import POS as NewPOS
    from webapps.point_of_sale.models import PaymentMethodVariant
    from webapps.pos.models import (
        POS,
        PaymentType,
        Receipt,
    )

    pos_ids_with_unused_square = set()
    affected_businesses_ids = set()

    for pos_id in pos_ids:
        square_used_last_60_days = (
            False
            if force
            else Receipt.objects.filter(
                payment_type__pos_id=pos_id,
                payment_type__code=PaymentTypeEnum.SQUARE,
                created__gte=tznow() - datetime.timedelta(days=60),
            ).exists()
        )
        if square_used_last_60_days:
            continue
        pos_ids_with_unused_square.add(pos_id)

        business_id = POS.objects.filter(id=pos_id).values_list('business_id', flat=True).first()
        if not business_id:
            continue
        affected_businesses_ids.add(business_id)

    PaymentType.objects.filter(
        pos_id__in=pos_ids_with_unused_square,
        code=PaymentTypeEnum.SQUARE,
    ).update(
        enabled=False,
        available=False,
    )
    PaymentMethodVariant.objects.filter(
        available=True,
        payment_method_type=PaymentMethodType.SQUARE,
        pos__in=NewPOS.objects.filter(
            business_id__in=affected_businesses_ids,
        ),
    ).update(
        available=False,
        enabled=False,
    )


@celery_task
def autopay_for_business_prepayment_task(
    appointment_id: int,
    device_data: DeviceDataDict,
):
    from service.pos.business_transactions import BaseAutoPayHandler
    from webapps.booking.models import Appointment

    appointment = Appointment.objects.filter(id=appointment_id).first()
    if appointment.status != AppointmentStatus.PENDING_PAYMENT:
        return
    transaction = appointment.transactions.filter(
        latest_receipt__status_code=receipt_status.CALL_FOR_PREPAYMENT
    ).first()
    if not transaction:
        return
    pba_row = transaction.latest_receipt.payment_rows.filter(
        status=receipt_status.CALL_FOR_PREPAYMENT,
        payment_type__code=PaymentTypeEnum.PREPAYMENT,
    ).first()

    if not (
        transaction.pos.payment_auto_accept
        and pba_row
        and transaction.customer
        and transaction.customer.payment_auto_accept
    ):
        return
    # Capture autopayment. No need to verify an outcome - payment link
    # to be sent with 2 minutes delay if appointment not accepted.
    BaseAutoPayHandler().auto_accept_business_prepayment(
        pos=appointment.business.pos,
        transaction=transaction,
        device_data=device_data,
    )


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def disable_square_integration_task(pos_ids: tuple[int]):
    from webapps.pos.models import PaymentType

    PaymentType.objects.filter(pos_id__in=pos_ids, code=PaymentTypeEnum.SQUARE).update(
        enabled=False, available=False
    )


@post_transaction_task
def reissue_gift_card_task(  # pylint: disable=invalid-name, too-many-return-statements, too-many-branches
    appointment_id: int,
    reason: ReissueGiftCardReasonEnum,
):
    from webapps.pos.models import Transaction

    txn = (
        Transaction.objects.by_appointment_id(appointment_id)
        .filter(transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT)
        .select_related(
            'pos',
            'latest_receipt',
        )
        .last()
    )

    if txn is None:
        return 'no_transaction', None

    payment_row = (
        txn.latest_receipt.payment_rows.filter(booksy_gift_cards__isnull=False).distinct().first()
    )

    if payment_row is None:
        return 'no_payment_row', None

    match reason:
        case ReissueGiftCardReasonEnum.CANCELLED_BY_CUSTOMER:
            reason_obj = ReissueReason.APPOINTMENT_CANCELLED_BY_CUSTOMER
        case ReissueGiftCardReasonEnum.CANCELLED_BY_BUSINESS:
            reason_obj = ReissueReason.APPOINTMENT_CANCELLED_BY_PROVIDER
        case _:
            return 'invalid_reason', None

    close_transaction(
        txn,
        log_note="reissue_gift_card_task",
        handle_gift_card_transaction_details=HandleGiftCardTransactionEntity(
            appointment_status=AppointmentStatus.CANCELED.value, charge_bgc=False
        ),
    )
    BooksyGiftCardsClient.reissue_booksy_gift_card(
        list(payment_row.booksy_gift_cards.values_list('external_id', flat=True)),
        reason_obj,
    )

    return 'ok', txn.id


@post_transaction_task
def handle_no_show_booksy_gift_card_task(  # pylint: disable=invalid-name, too-many-return-statements, too-many-branches
    appointment_id: int,
    business_id: int = None,
    charge_bgc: bool = True,
):
    from webapps.pos.models import Transaction

    try:
        lock_ = HandleNoShowBGCTransfer.lock(appointment_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        logger.warning(
            'Handle bgc transfer for no-show appointment_id=%s has been already started',
            appointment_id,
        )
        return

    txn = (
        Transaction.objects.by_appointment_id(appointment_id)
        .filter(transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT)
        .select_related(
            'pos',
            'latest_receipt',
        )
        .last()
    )

    if txn is None:
        return 'no_transaction', None

    payment_row = (
        txn.latest_receipt.payment_rows.filter(booksy_gift_cards__isnull=False).distinct().first()
    )

    if payment_row is None:
        return 'no_payment_row', None

    close_transaction(
        txn,
        log_note="handle_no_show_booksy_gift_card_task",
        handle_gift_card_transaction_details=HandleGiftCardTransactionEntity(
            appointment_status=AppointmentStatus.NOSHOW.value, charge_bgc=charge_bgc
        ),
    )
    BooksyGiftCardsClient.handle_no_show_gift_card(
        list(payment_row.booksy_gift_cards.values_list('external_id', flat=True)),
        business_id,
        charge_bgc,
        appointment_id,
    )

    return 'ok', txn.id


@post_transaction_task
def auto_refund_transaction(  # pylint: disable=too-many-return-statements, too-many-branches
    transaction_id=None, appointment_id=None, business_id=None, customer_user_id=None
):
    from webapps.pos.refund import do_refund
    from webapps.user.tools import get_system_user

    txn, error_msg = retrieve_transaction(
        transaction_id, appointment_id, business_id, customer_user_id
    )

    if error_msg:
        return error_msg

    if txn is None:
        return 'no_transaction', None

    if txn.latest_receipt.status_code not in (
        receipt_status.BOOKSY_PAY_SUCCESS,
        receipt_status.PAYMENT_SUCCESS,
    ):
        return 'bad_status', txn.id

    pr = txn.latest_receipt.payment_rows.get()
    do_refund(payment_row=pr, user=get_system_user())
    return 'ok', txn.id
