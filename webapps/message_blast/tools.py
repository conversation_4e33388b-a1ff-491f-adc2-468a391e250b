import datetime
import typing as t

from dateutil import tz

from django.conf import settings

from country_config import DEFAULT_TIME_ZONES
from country_config.enums import Country
from lib.booksy_sms import get_reasonable_send_datetime


from lib.feature_flag.feature.notification import RemoveFRSuffix<PERSON>romSMSBodyFlag
from lib.tools import tznow
from webapps.message_blast.consts import (
    BLAST_SMS_FR_SHORTCODE,
    TILL_NIGHT_SHIFT_MINUTES,
    ALWAYS_REQUIRE_GDPR_CONSENT_COUNTRIES,
)
from webapps.message_blast.content.content_br import BR_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_ca import CA_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_es import ES_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_fr import FR_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_gb import GB_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_ie import IE_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_mx import MX_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_pl import PL_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_us import US_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.message_blast.content.content_za import ZA_MESSAGE_BLAST_SPECIAL_OCCASIONS
from webapps.notification.base import (
    Channel,
)


def calculate_monday(month: int, ordinal_monday: int, year=None) -> t.Tuple[int, int]:
    """Calculates date of first/second monday in month.

    month: number representing month
    ordinal_monday: 1 means first monday, 2 means second monday of month
    Returns tuple(month, day)
    """

    if ordinal_monday > 4:
        raise ValueError

    lowest_day = 1 + (ordinal_monday - 1) * 7
    if year is None:
        year = tznow().year
    # Create lowest third/second day in month
    base_date = datetime.date(year, month, lowest_day)

    base_date_number = base_date.weekday()
    monday = base_date.replace(day=lowest_day + (7 - base_date_number))

    return month, monday.day


def get_country_base_templates(country):
    """Maps Country to SPECIAL_OCCASIONS templates"""
    return {
        Country.BR: BR_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.CA: CA_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.ES: ES_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.GB: GB_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.IE: IE_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.MX: MX_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.PL: PL_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.US: US_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.FR: FR_MESSAGE_BLAST_SPECIAL_OCCASIONS,
        Country.ZA: ZA_MESSAGE_BLAST_SPECIAL_OCCASIONS,
    }.get(country, [])


def enhance_blast_message_body(blast_body, business):
    base_business_subdomain = business.get_seo_url(protocol=False)
    blast_subdomain = business.get_seo_url(
        channel=Channel.Type.SMS,
        is_blast=True,
    )
    stop_code = get_message_blast_stop_code(settings.API_COUNTRY)
    if stop_code:
        blast_body = f'{blast_body} {stop_code}'

    if (
        blast_body
        and base_business_subdomain in blast_body
        and blast_subdomain not in blast_body
        and (blast_subdomain.replace(base_business_subdomain, '') == '/b/')
    ):
        blast_body = blast_body.replace(
            f'https://{base_business_subdomain}',
            blast_subdomain,
        )

    return blast_body


def get_message_blast_stop_code(country: Country) -> t.Optional[str]:
    """Returns stop code text. Used only in France for now."""
    return {
        Country.FR: BLAST_SMS_FR_SHORTCODE if not RemoveFRSuffixFromSMSBodyFlag() else None,
    }.get(country, None)


def is_gdpr_enabled():
    """
    Check if GDPR consent is required for the current country.

    Business rule: US and CA always require GDPR consent regardless of GDPR_COUNTRIES setting.
    For other countries, use the GDPR_COUNTRIES setting.
    """
    if settings.API_COUNTRY in ALWAYS_REQUIRE_GDPR_CONSENT_COUNTRIES:
        return True

    return settings.GDPR_COUNTRIES.get(
        settings.API_COUNTRY, settings.GDPR_COUNTRIES.get('default', False)
    )


def get_reasonable_datetime(time_delta_h=0, time_delta_m=0):
    if is_time_adjust():
        timezone = DEFAULT_TIME_ZONES.get(settings.API_COUNTRY)
        time_delta = datetime.timedelta(hours=time_delta_h, minutes=time_delta_m)
        send_at = tznow().astimezone(tz.gettz(timezone)) + time_delta
        till_night_shift = get_till_night_shift_minutes()
        return get_reasonable_send_datetime(
            timezone=timezone, send_at=send_at, till_night_shift=till_night_shift
        )


def get_till_night_shift_minutes() -> int:
    return TILL_NIGHT_SHIFT_MINUTES.get(settings.API_COUNTRY, 0)


def is_time_adjust() -> bool:
    return bool(TILL_NIGHT_SHIFT_MINUTES.get(settings.API_COUNTRY))
