import logging

from billiard.compat import mem_rss
from bo_obs.datadog.mixins import set_apm_tag_in_current_span, MANUAL_KEEP_KEY


logger = logging.getLogger('booksy.request_debug')


class HighMemoryAllocationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        allocated_memory_at_start = mem_rss()
        response = self.get_response(request)
        allocated_memory_at_end = mem_rss()
        memory_increased = allocated_memory_at_end - allocated_memory_at_start
        if memory_increased >= 300_000:
            logger.warning(
                "memory increased of %sKB to level %sKB for request %s",
                memory_increased,
                allocated_memory_at_end,
                request,
            )
            if allocated_memory_at_end > 1_000_000:
                set_apm_tag_in_current_span(MANUAL_KEEP_KEY)
                set_apm_tag_in_current_span('request_high_mem', 'True')

        return response
