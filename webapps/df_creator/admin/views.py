from django.contrib.admin import TabularInline
from django.utils.html import format_html

from lib.admin_helpers import BaseModelAdmin, ReadOnlyTabular, admin_link
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.df_creator.admin.filters import CategoryListFilter
from webapps.df_creator.admin.forms import (
    DigitalFlyerCategoryImageForm,
    DigitalFlyerBackgroundForm,
    DigitalFlyerGroupForm,
    DigitalFlyerHashtagForm,
)
from webapps.df_creator.models import (
    DigitalFlyerCategoryImage,
    DigitalFlyerGroup,
)
from webapps.user.groups import GroupNameV2
from webapps.warehouse.admin import AttributeAdminLinkMixin


class DigitalFlyerCategoryImageInline(TabularInline):
    model = DigitalFlyerCategoryImage
    form = DigitalFlyerCategoryImageForm
    ordering = ('business_category',)
    extra = 0


class DigitalFlyerGroupInline(ReadOnlyTabular, TabularInline):
    model = DigitalFlyerGroup

    show_change_link = True
    ordering = ('active_till',)
    readonly_fields = ('codename',)
    fields = (
        'codename',
        'display_name',
        'active_from',
        'active_till',
        'cyclic',
    )
    extra = 0


class DigitalFlyerCategoryAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    list_display = ('id', 'codename', 'order')
    list_filter = ('codename',)

    inlines = [DigitalFlyerCategoryImageInline, DigitalFlyerGroupInline]


class DigitalFlyerBackgroundAdmin(AttributeAdminLinkMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form = DigitalFlyerBackgroundForm

    list_display = ['id', 'image_link', 'codename', 'order']
    list_filter = ['groups']

    fieldsets = (
        (
            None,
            {
                'fields': (('image', 'image_upload'),),
            },
        ),
        (
            None,
            {
                'fields': tuple(
                    field
                    for field in DigitalFlyerBackgroundForm._meta.fields
                    if field not in ('image', 'image_upload')
                ),
            },
        ),
    )

    @staticmethod
    def image_link(obj):
        image = obj.image
        return format_html('<a href="{}">Image {}</a>', admin_link(image), image.id)


class DigitalFlyerTextAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    list_display = ['id', 'codename', 'text', 'text_size', 'order']
    list_filter = ['groups']


class DigitalFlyerGroupAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form = DigitalFlyerGroupForm
    list_display = [
        'id',
        'codename',
        'display_name',
        'category',
        'active_from',
        'active_till',
    ]
    list_filter = ['category']

    fieldsets = (
        (
            None,
            {
                'fields': (('image', 'image_upload'),),
            },
        ),
        (
            None,
            {
                'fields': tuple(
                    field
                    for field in DigitalFlyerGroupForm._meta.fields
                    if field not in ('image', 'image_upload')
                ),
            },
        ),
    )


class DigitalFlyerHashtagAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form = DigitalFlyerHashtagForm
    list_display = ['id', 'category', 'name']
    list_filter = [CategoryListFilter]
