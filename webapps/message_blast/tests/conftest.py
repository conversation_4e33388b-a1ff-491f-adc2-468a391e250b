# pylint: disable=redefined-outer-name
import pytest
from django.conf import settings
from model_bakery import baker

from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.category import BusinessCategory
from webapps.message_blast.helpers import update_message_blast_groups, turn_on_blasts_for_business
from webapps.message_blast.models import SMSBlastMarketingConsent
from webapps.message_blast.tests.test_helpers import create_templates
from webapps.notification.models import Reciever, UserNotification
from webapps.structure.models import Region
from webapps.subdomain_grpc.client import SubdomainGRPC
from webapps.user.models import User, UserProfile


@pytest.fixture()
def region():
    yield baker.make(
        Region,
        time_zone_name='Europe/Warsaw',
    )


@pytest.fixture()
def business(region):
    yield baker.make(
        Business,
        name='SMS Subdomain V2',
        active=True,
        status=Business.Status.PAID,
        region=region,
        primary_category=baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.BARBERS,
        ),
    )


@pytest.fixture()
def business_with_subdomain(business):
    SubdomainGRPC.claim(
        data=dict(
            business_id=business.id,
            country_code=settings.API_COUNTRY,
            subdomain='subdomain-v2',
        )
    )
    yield business


@pytest.fixture
def business_with_blasts(business):
    update_message_blast_groups()
    create_templates()
    turn_on_blasts_for_business(business)
    yield business


@pytest.fixture()
def bci_email(business):
    yield baker.make(
        BusinessCustomerInfo,
        email='<EMAIL>',
        business=business,
        web_communication_agreement=True,
    )


@pytest.fixture()
def bci_email_sms(business):
    bci = baker.make(
        BusinessCustomerInfo,
        email='<EMAIL>',
        cell_phone='+**************',
        business=business,
        web_communication_agreement=True,
    )

    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=True,
        bci=bci,
        cell_phone=bci.cell_phone,
    )

    yield bci


@pytest.fixture()
def bci_email_sms_with_user(business):
    bci = baker.make(
        BusinessCustomerInfo,
        user=baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+**************',
        ),
        business=business,
        web_communication_agreement=True,
    )

    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=True,
        bci=bci,
        cell_phone=bci.cell_phone,
    )

    yield bci


@pytest.fixture()
def bci_email_push(business):
    bci = baker.make(
        BusinessCustomerInfo,
        user=baker.make(
            User,
            email='<EMAIL>',
        ),
        business=business,
        web_communication_agreement=True,
    )

    baker.make(
        Reciever,
        customer_notifications=baker.make(
            UserNotification,
            type=UserNotification.PUSH_NOTIFICATION,
            profile=baker.make(
                UserProfile,
                profile_type=UserProfile.Type.CUSTOMER,
                user=bci.user,
            ),
        ),
    )
    yield bci


@pytest.fixture()
def bci_email_push_sms(business):
    bci = baker.make(
        BusinessCustomerInfo,
        business=business,
        cell_phone='+**************',
        user=baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+**************',
        ),
        web_communication_agreement=True,
    )

    baker.make(
        Reciever,
        customer_notifications=baker.make(
            UserNotification,
            type=UserNotification.PUSH_NOTIFICATION,
            profile=baker.make(
                UserProfile,
                profile_type=UserProfile.Type.CUSTOMER,
                user=bci.user,
            ),
        ),
    )

    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=True,
        bci=bci,
        cell_phone=bci.cell_phone,
    )

    yield bci


@pytest.fixture()
def bci_ids(bci_email, bci_email_sms, bci_email_push, bci_email_push_sms):
    bci_ids = (bci_email.id, bci_email_sms.id, bci_email_push.id, bci_email_push_sms.id)
    yield bci_ids


# fixtures for bci duplicates tests
COMMON_EMAIL = '<EMAIL>'
COMMON_PHONE_NUMBER = '+**************'


@pytest.fixture()
def bci_unique_email(business):
    yield baker.make(
        BusinessCustomerInfo,
        email='<EMAIL>',
        business=business,
        web_communication_agreement=True,
    )


@pytest.fixture()
def bci_common_email(business):
    yield baker.make(
        BusinessCustomerInfo,
        email=COMMON_EMAIL,
        business=business,
        web_communication_agreement=True,
    )


@pytest.fixture()
def bci_common_email_duplicate(business):
    yield baker.make(
        BusinessCustomerInfo,
        business=business,
        user=baker.make(
            User,
            email=COMMON_EMAIL,
        ),
        web_communication_agreement=True,
    )


@pytest.fixture()
def bci_common_phone(business):
    bci = baker.make(
        BusinessCustomerInfo,
        cell_phone=COMMON_PHONE_NUMBER,
        business=business,
        web_communication_agreement=True,
    )

    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=True,
        bci=bci,
        cell_phone=bci.cell_phone,
    )

    yield bci


@pytest.fixture()
def bci_common_phone_duplicate(business):
    bci = baker.make(
        BusinessCustomerInfo,
        business=business,
        user=baker.make(
            User,
            cell_phone=COMMON_PHONE_NUMBER,
        ),
        web_communication_agreement=True,
    )

    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=True,
        bci=bci,
        cell_phone=bci.cell_phone,
    )

    yield bci


@pytest.fixture()
def bci_unique_phone(business):
    bci = baker.make(
        BusinessCustomerInfo,
        cell_phone='+************** 893',
        business=business,
        web_communication_agreement=True,
    )

    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=True,
        bci=bci,
        cell_phone=bci.cell_phone,
    )

    yield bci


@pytest.fixture()
def bci_email_not_visible_with_user(business):
    yield baker.make(
        BusinessCustomerInfo,
        email='<EMAIL>',
        business=business,
        visible_in_business=False,
        user=baker.make(User),
    )
