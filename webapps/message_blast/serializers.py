import typing as t
from datetime import datetime, time, timedelta

from babel.dates import format_date
from django.conf import settings
from django.db.models import Q
from django.utils.translation import get_language
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from country_config import CountryConfig
from lib import safe_json
from lib.elasticsearch.consts import ESDocType


from lib.serializers import CommaSeparatedList<PERSON>ield, PaginatorSerializer
from lib.tools import get_locale_from_language
from lib.validators import ForbiddenWordsValidator
from service.business.serializers import ItemOrListSerializerField
from webapps.booking.enums import AppointmentStatusChoices
from webapps.business_customer_info.enums import BCIGroupName
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.searchables.business_customer import BusinessCustomerSearchable
from webapps.business.searchables.customer_groups import (
    BCIGroupCriteria,
    BCIGroupsAggregate,
    BCIGroupSearchCriteria,
)
from webapps.business.searchables.serializers import BusinessCustomerOnlyIdHitSerializer
from webapps.images.serializers import ImageSerializer
from webapps.message_blast.consts import BLAST_SHORT_BODY_LENGTH_LIMIT, BLAST_TITLE_LENGTH_LIMIT
from webapps.message_blast.enums import (
    MESSAGE_BLAST_REWARD_LIST,
    MessageBlastDateType,
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
    MessageBlastTimeType,
)
from webapps.message_blast.models import (
    CommonMessageBlastTemplate,
    MessageBlast,
    MessageBlastTemplate,
    SMSBlastMarketingConsent,
)
from webapps.message_blast.schedules import MessageBlastSendingSchedules
from webapps.message_blast.adapters import check_msg_for_spam_adapter
from webapps.message_blast.tools import is_gdpr_enabled


def get_default_image_for_blasts(
    template_internal_name: str,
    business: 'Business',
):
    """Returns default image for template internal_name"""
    from webapps.message_blast.models import MessageBlastImage

    # 78767 - OWN MESSAGES should not have default images.
    if template_internal_name == MessageBlastInternalNames.OWN_MESSAGE:
        return None

    images = MessageBlastImage.get_images_for_template(
        business=business,
        template_internal_names=[template_internal_name],
    )
    return images[0] if images else None


class MessageBlastShortInfoBase(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(read_only=True)
    description = serializers.CharField(read_only=True)
    recommended = serializers.BooleanField(read_only=True)
    automated_status = serializers.ChoiceField(
        choices=MessageBlastTemplateStatus.choices(), allow_null=True
    )
    status_label = serializers.SerializerMethodField()
    internal_name = serializers.CharField(read_only=True)

    @staticmethod
    def get_status_label(obj):
        return obj.get_automated_status_display()


class FilteredMBListingSerializer(serializers.ListSerializer):
    def to_representation(self, data):
        if not self.context.get('mb_reward_granted'):
            data = data.exclude(
                Q(internal_name=MessageBlastInternalNames.SPECIAL_MESSAGE_BLASTS_LINK)
                | Q(internal_name__in=MESSAGE_BLAST_REWARD_LIST),
            )
        return super().to_representation(data)


class MessageBlastTemplateListingSerializer(MessageBlastShortInfoBase):
    link_to_group = serializers.CharField(source='link_to_group.internal_name')
    new = serializers.SerializerMethodField()

    def get_new(self, obj) -> bool:
        return bool(
            obj.internal_name == MessageBlastInternalNames.SPECIAL_MESSAGE_BLASTS_LINK
            and self.context.get('new_special_message_blast')
        )

    class Meta:
        list_serializer_class = FilteredMBListingSerializer


class MessageBlastChannelsSerializer(serializers.Serializer):
    default = serializers.BooleanField(default=True, required=False)
    sms = serializers.BooleanField(default=False, required=False)
    prefer_push = serializers.BooleanField(default=False, required=False)
    push = serializers.BooleanField(default=False, required=False)
    email = serializers.BooleanField(default=False, required=False)

    def validate(self, attrs):
        if attrs:
            if not any(attrs.values()):
                raise serializers.ValidationError(
                    'At least one option in channels must activated.', code='wrong_channels'
                )
            if attrs.get('default') and any(
                [
                    attrs.get('prefer_push'),
                    attrs.get('push'),
                    attrs.get('sms'),
                    attrs.get('email'),
                ]
            ):
                raise serializers.ValidationError(
                    'Default and other option chosen at the same time.', code='wrong_channels'
                )
            if attrs.get('prefer_push') and not attrs.get('push'):
                raise serializers.ValidationError(
                    'Can not activate prefer_push without push.', code='wrong_channels'
                )
        return attrs


class SchedulesSerializer(serializers.Serializer):
    number = serializers.IntegerField(read_only=True)
    text = serializers.CharField(read_only=True)
    default = serializers.BooleanField(read_only=True)


class MessageBlastContentSerializer(serializers.Serializer):
    title = serializers.CharField(
        required=True,
        max_length=BLAST_TITLE_LENGTH_LIMIT,
        validators=[ForbiddenWordsValidator()],
        label=_('Title'),
    )
    body = serializers.CharField(
        required=True,
        validators=[ForbiddenWordsValidator()],
        label=_('Message body'),
    )
    body_short = serializers.CharField(
        required=True,
        max_length=BLAST_SHORT_BODY_LENGTH_LIMIT,
        validators=[ForbiddenWordsValidator()],
        label=_('Short message body'),
    )
    image = ImageSerializer(required=False, allow_null=True)

    def validate(self, attrs):
        check_msg_for_spam_adapter(attrs, self.context)

        return super().validate(attrs)


class MessageBlastTemplateSerializer(MessageBlastShortInfoBase, MessageBlastContentSerializer):
    schedules = SchedulesSerializer(
        many=True,
        required=False,
        allow_null=True,
    )
    date_schedule = serializers.IntegerField(
        required=False,
        allow_null=True,
    )
    date_month = serializers.IntegerField(
        required=False,
        allow_null=True,
        min_value=1,
        max_value=12,
    )
    date_day = serializers.IntegerField(
        required=False,
        allow_null=True,
        min_value=1,
        max_value=31,
    )
    date_hour = serializers.ChoiceField(
        required=False,
        allow_null=True,
        choices=MessageBlastTimeType.values(),
    )
    date_text = serializers.SerializerMethodField()
    date_text_recommended = serializers.SerializerMethodField()
    date_default_selected = serializers.SerializerMethodField()
    channel_priority = MessageBlastChannelsSerializer(
        required=False,
        allow_null=True,
    )

    def get_date_text(self, obj: t.Union[CommonMessageBlastTemplate, MessageBlastTemplate]):
        if date := obj.get_date(next_possible=True):
            return self._get_date_text(date)

    def get_date_default_selected(
        self, obj: t.Union[CommonMessageBlastTemplate, MessageBlastTemplate]
    ):
        if self.base_class(obj):
            obj = obj.messageblasttemplate
            return obj.common_template.get_date() == obj.get_date()
        return True

    def get_date_text_recommended(
        self, obj: t.Union[CommonMessageBlastTemplate, MessageBlastTemplate]
    ):
        if self.base_class(obj):
            obj = obj.messageblasttemplate.common_template
        if date := obj.get_date(next_possible=True):
            date_text = self._get_date_text(date)
            extra_text = obj.get_text_recommended()
            if extra_text:
                return _(f'{date_text} (recommended - {extra_text})')

            return _(f'{date_text} (recommended)')

    @staticmethod
    def _get_date_text(date):
        language = get_language() or CountryConfig(settings.API_COUNTRY).language_code
        date_text = format_date(
            date,
            locale=get_locale_from_language(language),
        )
        return date_text

    @staticmethod
    def base_class(instance):
        return hasattr(instance, 'messageblasttemplate')

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        if self.instance:
            self._check_if_one_time(validated_data)
            self._check_own_message()
            self._check_date_schedule(validated_data)
            self._check_month_day(validated_data)  # may change validate_data
        return validated_data

    def _check_month_day(self, validated_data):
        if ('date_month' in validated_data and validated_data['date_month'] is not None) or (
            'date_day' in validated_data and validated_data['date_day'] is not None
        ):
            # pylint: disable=no-else-raise
            if self.instance.internal_name in [
                MessageBlastGroupEnum.FIRST_IMPRESSION,
                MessageBlastGroupEnum.REACTIVATE,
                MessageBlastInternalNames.HAPPY_BIRTHDAY,
            ]:
                raise serializers.ValidationError(
                    'Month and Date are forbidden for this Message Blast',
                    code='wrong_date_option',
                )
            elif ('date_month' in validated_data and validated_data['date_month'] is not None) and (
                'date_day' in validated_data and validated_data['date_day'] is not None
            ):
                # change date type to strict if month day given and remove default Monday
                validated_data['date_type'] = MessageBlastDateType.STRICT
            else:
                raise serializers.ValidationError(
                    'Both month and date must be provided at the same time',
                    code='wrong_date_option',
                )
        elif ('date_month' in validated_data and validated_data['date_month'] is None) or (
            'date_day' in validated_data and validated_data['date_day'] is None
        ):
            template = self.instance

            if hasattr(template, 'messageblasttemplate'):
                template = template.messageblasttemplate

            if hasattr(template, 'common_template'):
                template = template.common_template

            # reset to default if template exists and no date given
            validated_data['date_type'] = template.date_type
            validated_data['date_month'] = template.date_month
            validated_data['date_day'] = template.date_day
            validated_data['date_monday'] = template.date_monday

    def _check_own_message(self):
        if self.instance.internal_name == MessageBlastInternalNames.OWN_MESSAGE:
            raise serializers.ValidationError(
                'Own message should not be overridden.', code='own_message_overridden'
            )

    def _check_date_schedule(self, validated_data):
        if date_schedule := validated_data.get('date_schedule'):
            if (
                self.instance.internal_name not in MessageBlastSendingSchedules
                or not MessageBlastSendingSchedules.get(self.instance.internal_name).exists(
                    number=date_schedule
                )
            ):
                raise serializers.ValidationError(
                    _('Time slot is no longer available.'), code='wrong_date_option'
                )

    def _check_if_one_time(self, validated_data):
        if self.instance.is_one_time:
            if (
                'automated_status' in validated_data
                and validated_data['automated_status'] == MessageBlastTemplateStatus.ACTIVE
            ):
                raise serializers.ValidationError(
                    'One time messages status should not be Active.', code='one_time_status'
                )

    def update(self, instance, validated_data):
        # instance is CommonMessageTemplate class instance
        # If there is MessageBlastTemplate instance we should update it
        # Unless we need to create new one
        user = self.context.get('user')
        metadata = self.context.get('metadata', {})
        if self.base_class(instance):
            template = instance.messageblasttemplate

            for field, value in list(validated_data.items()):
                setattr(template, field, value)

            metadata['method'] = 'Updated with Slider'
            template.save(
                _history={
                    'metadata': metadata,
                    'operator': user,
                },
            )
        else:
            template = MessageBlastTemplate(
                common_template=instance,
                order=instance.order,
                group=instance.group,
                name=instance.name,
                description=instance.description,
                recommended=instance.recommended,
                business=self.context['business'],
                internal_name=instance.internal_name,
                date_monday=validated_data.pop('date_monday', None) or instance.date_monday,
                date_month=validated_data.pop('date_month', None) or instance.date_month,
                date_day=validated_data.pop('date_day', None) or instance.date_day,
                date_type=validated_data.pop('date_type', None) or instance.date_type,
                **validated_data,
            )
            metadata['method'] = 'Created with Slider'
            template.save(
                _history={
                    'metadata': metadata,
                    'handler': {'handler': 'turn_on_blasts_for_business'},
                    'operator': user,
                },
            )

        return template

    def to_representation(self, instance):
        if not self.base_class(instance):
            # CommonMessageBlastTemplate when returned to front should be filled
            # with business name and subdomain.
            instance.body = instance.body.format(
                business_name=self.context['business'].name,
                subdomain=self.context['business'].get_seo_url(),
            )

            instance.body_short = instance.body_short.format(
                business_name=self.context['business'].name,
                subdomain=self.context['business'].get_seo_url(),
            )

            instance.image = get_default_image_for_blasts(
                instance.internal_name, self.context['business']
            )

        if instance.date_day is None and instance.date_month:
            # solution for Mondays, when date_type is not MessageBlastDateType.STRICT
            date = instance.get_date(next_possible=True)
            if date is not None:
                instance.date_day = date.day

        instance.schedules = MessageBlastSendingSchedules.get(instance.internal_name, [])
        if instance.date_schedule is None and instance.schedules:
            instance.date_schedule = instance.schedules.get_default_value()

        return super().to_representation(instance)


class MessageBlastScheduleSerializer(serializers.ModelSerializer):
    image = ImageSerializer(required=False, allow_null=True)
    schedule_date = serializers.SerializerMethodField()
    recipients_count = serializers.SerializerMethodField()
    estimated_sms_cost = serializers.FloatField()

    class Meta:
        model = CommonMessageBlastTemplate
        fields = (
            'body',
            'body_short',
            'channel_priority',
            'date_hour',
            'estimated_sms_cost',
            'id',
            'image',
            'name',
            'recipients_count',
            'schedule_date',
            'title',
        )

    @staticmethod
    def get_schedule_date(obj):
        language = get_language() or CountryConfig(settings.API_COUNTRY).language_code
        return format_date(
            obj.scheduled_date,
            locale=get_locale_from_language(language),
        )

    @staticmethod
    def get_recipients_count(obj):
        return len(obj.bcis)


class RecipientsSerializer(serializers.Serializer):
    """BCIs for blast can be searched in many ways:
    1. Just `ids`
    2. `group` and `excluded`
    3. `tags` and `excluded`
    4. Last ways is: get all clients with bookings. You need to set
       visit_frequency = True. If you want to filter results use
       ` bookings_start, bookings_end, service_id, staffer_id, booking_status`
       fields. `excluded` field can also be used here.

    Context business is required.
    """

    group = serializers.ChoiceField(
        required=False,
        choices=[f.value for f in BCIGroupName],
    )
    tags = ItemOrListSerializerField(child=serializers.CharField(), required=False)
    ids = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )
    excluded = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )

    visit_frequency = serializers.IntegerField(
        required=False,
        default=0,
    )
    # pylint: disable=duplicate-code
    bookings_start = serializers.DateField(required=False)
    bookings_end = serializers.DateField(required=False)
    service_id = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )
    staffer_id = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )
    booking_status = ItemOrListSerializerField(
        child=serializers.ChoiceField(choices=AppointmentStatusChoices),
        required=False,
    )
    with_agreements = serializers.BooleanField(default=True)

    def validate_ids(self, ids):
        bci_count = BusinessCustomerInfo.objects.filter(
            business_id=self.context['business'],
            id__in=ids,
        ).count()

        if len(ids) != bci_count:
            raise serializers.ValidationError(_('Not all clients exists'), code='doesnt_exist_ids')

        return ids

    def validate_excluded(self, ids):
        bci_count = BusinessCustomerInfo.objects.filter(
            business_id=self.context['business'],
            id__in=ids,
        ).count()

        if len(ids) != bci_count:
            raise serializers.ValidationError(
                _('Not all clients exists'), code='doesnt_exist_excluded'
            )

        ids.extend(
            MessageBlastTemplate.bcis_excluded_by_limits(
                business=self.context['business'],
                manual_blast=True,
            )
        )

        return ids

    def validate_tags(self, tags):
        business = self.context['business']
        criteria = BCIGroupCriteria(business)
        aggregates = BCIGroupsAggregate(criteria=criteria)
        available_tags = {x['name'] for x in aggregates.tags}

        for tag in tags:
            if tag not in available_tags:
                raise serializers.ValidationError(
                    _('Selected tag:{} is not supported').format(tag), code='tag_not_supported'
                )

        return tags

    @staticmethod
    def searchable(business, params):
        BusinessCustomerSearchable(
            ESDocType.BUSINESS_CUSTOMER,
            serializer=BusinessCustomerOnlyIdHitSerializer(),
        ).params(
            routing=business.id,
            **params,
        )

    @staticmethod
    def prepare_searchable(recipients, business, params):
        criteria = BCIGroupCriteria(business)
        search_criteria = BCIGroupSearchCriteria(criteria)

        group = recipients.get('group')
        if group:
            search_data = {**recipients, **search_criteria.get_group_params(group)}
        else:
            search_data = recipients.copy()

        with_agreements = search_data.pop('with_agreements', True)
        if with_agreements and is_gdpr_enabled():
            search_data.update({'web_communication_agreement': True})

        search_data.update({'blasts_unsubscribed': False})
        search_data.update({'business_id': business.id})

        return (
            BusinessCustomerSearchable(
                ESDocType.BUSINESS_CUSTOMER,
                serializer=BusinessCustomerOnlyIdHitSerializer(),
            )
            .params(routing=business.id, **params)
            .search(search_data)
        )

    @staticmethod
    def resolve_bcis(recipients, business) -> t.List[int]:
        res = RecipientsSerializer.prepare_searchable(recipients, business, {}).scan()

        return [x['id'] for x in res]

    @staticmethod
    def count_bcis(recipients, business) -> int:
        # DEPRECATED
        res = RecipientsSerializer.prepare_searchable(
            recipients, business, {'size': 0, '_source': False}
        ).execute()

        return res.hits.total['value']


class CostEstimationRecipientsSerializer(RecipientsSerializer):
    body_short = serializers.CharField(
        required=True,
        max_length=BLAST_SHORT_BODY_LENGTH_LIMIT,
    )
    channel_priority = MessageBlastChannelsSerializer(
        required=False,
        allow_null=True,
    )


class BaseMessageBlastSerializer(MessageBlastContentSerializer):
    date_hour = serializers.ChoiceField(
        required=False,
        allow_null=True,
        choices=MessageBlastTimeType.values(),
    )
    scheduled_date = serializers.DateField(required=False, allow_null=True)
    estimated_sms_cost = serializers.FloatField(required=False, allow_null=True)
    channel_priority = MessageBlastChannelsSerializer(
        required=False,
        allow_null=True,
    )

    @staticmethod
    def validate_scheduled_date(scheduled_date):
        if scheduled_date and scheduled_date <= datetime.today().date():
            raise serializers.ValidationError(
                'Scheduled date must be in future',
                code='wrong_scheduled_date',
            )
        return scheduled_date

    def save(self, **kwargs):
        validated_data = self.validated_data

        recipients = self.validated_data.pop('recipients', {})
        message_blast = MessageBlast.objects.create(
            **validated_data,
            internal_name=self.context['template'].internal_name,
            business=self.context['business'],
            with_agreements=recipients.get('with_agreements', True),
            recipients_filter=safe_json.dumps(recipients),
            sent_by=self.context['user'],
            blast_send_type=self.context.get('blast_send_type'),
        )

        return message_blast


class OneTimeMessageBlastSerializer(BaseMessageBlastSerializer):
    recipients = RecipientsSerializer()

    def _validate_send_possibility(self):
        business = self.context.get('business')
        if not business or business.sms_notification_status != Business.SMSStatus.ENABLED:
            raise serializers.ValidationError(
                _(
                    'Our custom message blast features are disabled by default to prevent spam. '
                    'Please contact our support.'
                )
            )

    def _adjust_marketing_time(self, validated_data):
        scheduled_date = validated_data.get('scheduled_date')
        date_hour = validated_data.get('date_hour')

        if scheduled_date and date_hour:
            return

        business = self.context.get('business')
        datetime_now = business.tznow
        night_adjust = settings.SMS_SETTINGS_PER_COUNTRY.get(settings.API_COUNTRY, {}).get(
            'marketing_night_adjust',
            settings.SMS_SETTINGS_DEFAULT['marketing_night_adjust'],
        )

        night_start_split = night_adjust['night_start'].split(':')
        night_end_split = night_adjust['night_end'].split(':')

        night_start = time(
            hour=int(night_start_split[0]),
            minute=int(night_start_split[1]),
        )
        night_end = time(
            hour=int(night_end_split[0]),
            minute=int(night_end_split[1]),
        )

        if datetime_now.time() > night_start:
            # before midnight, take tomorrow morning
            validated_data['scheduled_date'] = datetime_now.date() + timedelta(days=1)
            validated_data['date_hour'] = MessageBlastTimeType.MORNING
        elif night_end > datetime_now.time():
            # after midnight, take this morning
            validated_data['scheduled_date'] = datetime_now.date()
            validated_data['date_hour'] = MessageBlastTimeType.MORNING

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        self._validate_send_possibility()
        self._adjust_marketing_time(validated_data)

        if self.instance and not self.instance.is_one_time:
            raise serializers.ValidationError(
                _('This template is not one time message blast'),
                code='not_one_time',
            )

        if 'scheduled_date' in validated_data or 'date_hour' in validated_data:
            validated_data['sent'] = False
            if not all([validated_data.get('scheduled_date'), validated_data.get('date_hour')]):
                raise serializers.ValidationError(
                    'Scheduled date and date hour must be provided together',
                    code='wrong_scheduled_date',
                )
        if template := self.context['template']:
            if template.internal_name == MessageBlastInternalNames.OWN_MESSAGE:
                validated_data['name'] = validated_data['title']
            else:
                validated_data['name'] = template.name

        attrs['bcis'] = RecipientsSerializer.resolve_bcis(
            attrs['recipients'],
            self.context['business'],
        )

        return attrs


class ContentMessageBlastSerializer(BaseMessageBlastSerializer):
    stripped = serializers.BooleanField(
        required=False,
        allow_null=True,
        default=False,
    )


class MessageBlastGroupSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField()
    business_templates = MessageBlastTemplateListingSerializer(
        many=True,
        allow_null=True,
    )
    internal_name = serializers.CharField()
    sent_messages = serializers.IntegerField(allow_null=True)


class CreateMessageBlastTemplateFromCommonSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommonMessageBlastTemplate
        fields = (
            'name',
            'description',
            'title',
            'body',
            'body_short',
            'image_id',
            'internal_name',
            'order',
            'group_id',
            'link_to_group',
            'recommended',
            'automated_status',
            'date_type',
            'date_monday',
            'date_month',
            'date_day',
        )

    def to_representation(self, instance):
        instance.body = instance.body.format(
            business_name=self.context['business'].name,
            subdomain=self.context['business'].get_seo_url(),
        )

        instance.body_short = instance.body_short.format(
            business_name=self.context['business'].name,
            subdomain=self.context['business'].get_seo_url(),
        )

        instance.image = get_default_image_for_blasts(
            instance.internal_name, self.context['business']
        )

        return super().to_representation(instance)


class FlatRequestSerializer(serializers.Serializer):
    flat = serializers.BooleanField(
        required=False,
        default=False,
        allow_null=True,
    )


class ActivateMessageBlastSerializer(serializers.Serializer):
    common_message_blast_template_ids = CommaSeparatedListField(
        child=serializers.PrimaryKeyRelatedField(
            queryset=CommonMessageBlastTemplate.objects.filter_baseclass()
        ),
        source='common_templates',
        write_only=True,
    )

    def validate(self, attrs):
        business_events = self.context['business'].events

        if not business_events.ask_for_message_blast_activation:
            raise serializers.ValidationError(
                _('Business has already seen activation modal'), code='already_seen'
            )

        # Check if business already doesn't have selected templates activated.
        common_template_ids = [common_template.id for common_template in attrs['common_templates']]

        business_templates = MessageBlastTemplate.objects.filter(
            common_template_id__in=common_template_ids,
            business=self.context['business'],
        )

        if business_templates:
            raise serializers.ValidationError(
                _('Business has already activated some of these templates'),
                code='already_activated',
            )

        return attrs


class MessageBlastRequestImageSerializer(PaginatorSerializer):
    business_category = serializers.BooleanField(
        default=True,
        required=False,
    )
    template_internal_name = serializers.ChoiceField(
        choices=MessageBlastInternalNames.choices(),
        required=False,
    )
    group_internal_name = serializers.ChoiceField(
        choices=MessageBlastGroupEnum.choices(),
        required=False,
    )

    def validate(self, attrs):
        template_internal_name = attrs.get('template_internal_name')
        group_internal_name = attrs.get('group_internal_name')

        if not template_internal_name and not group_internal_name:
            raise serializers.ValidationError('At least one name required.')

        return attrs


class SMSMarketingCommunicationConsentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SMSBlastMarketingConsent
        fields = ('confirmation_key',)

    confirmation_key = serializers.UUIDField(required=True)
