from bo_obs.datadog.enums import BooksyTeams
from django.db.models import Count, Prefetch, QuerySet
from django.utils.translation import gettext
from rest_framework import status

from lib.serializers import PaginatorSerializer
from service.images.helpers import get_file_from_request_files, PaginatorPage
from service.images.v2.serializers import (
    ImageCommentSerializer,
    ImageCommentUpdateSerializer,
    ImageV2CreateSerializer,
    ImageListRequestSerializer,
    ImageV2Serializer,
    ImageV2UpdateSerializer,
    PaginatorPageSerializer,
)
from service.tools import json_request, RequestHandler, session
from webapps.images.enums import ImageTypeEnum

from webapps.images.models import Image, ImageComment
from webapps.onboarding_space.public import (
    onboarding_space_service,
    ProfileSetupStepsEnum,
    OnboardingSpaceMetricsEnum,
)
from webapps.user.models import UserProfile


# pylint: disable=duplicate-code
class ImageListHandler(RequestHandler):
    """
    List handler is responsible for handling:
    - listing images via "GET .../images/v2/"
    - uploading new image via "POST .../images/v2/"
    """

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @staticmethod
    def get_queryset(business_id: int) -> QuerySet:
        return (
            Image.objects.filter(
                business_id=business_id,
            )
            .annotate(
                likes_count=Count('likes'),
            )
            .order_by('order')
        )

    @staticmethod
    def filter_queryset(qs: QuerySet, filter_kwargs: dict) -> QuerySet:
        return qs.filter(**filter_kwargs)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: List business images
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: page
                  type: integer
                  paramType: query
                  required: false
                - name: per_page
                  type: integer
                  paramType: query
                  required: false
                - name: category
                  type: string
                  paramType: query
                  required: true
                  enum_from_const: webapps.images.enums.NOT_DEPRECATED_IMAGE_CATEGORIES
            type: ImageV2ListResponse
        :swagger
        swaggerModels:
            ImageV2ListResponse:
                id: ImageV2ListResponse
                properties:
                    count:
                        type: integer
                    per_page:
                        type: integer
                    data:
                        type: array
                        items:
                            type: ImageV2
            ImageV2:
                id: ImageV2
                properties:
                    id:
                        type: integer
                    tags:
                        type: array
                        items:
                            type: string
                    description:
                        type: string
                    category:
                        type: string
                    is_cover_photo:
                        type: boolean
                    inspiration_categories:
                        type: array
                        items:
                            type: integer
                    order:
                        type: integer
                    url:
                        type: string
                    width:
                        type: integer
                    height:
                        type: integer
                    active:
                        type: boolean
                    visible:
                        type: boolean
                    likes_count:
                        type: integer
                    instagram_id:
                        type: integer
        :swaggerModels
        """
        self.business_with_staffer(business_id)

        query_params = self._prepare_get_arguments()
        paginator_serializer = PaginatorSerializer(data=query_params)
        pagination_data = self.validate_serializer(paginator_serializer)

        request_serializer = ImageListRequestSerializer(data=query_params)
        filter_kwargs = self.validate_serializer(request_serializer)

        qs = self.get_queryset(business_id)
        qs = self.filter_queryset(qs, filter_kwargs)

        paginator_page = PaginatorPage(qs, pagination_data['per_page'], pagination_data['page'])
        serializer = PaginatorPageSerializer(
            item_serializer_cls=ImageV2Serializer,
            instance=paginator_page,
        )
        self.finish_with_json(status.HTTP_200_OK, serializer.data)

    def _check_if_only_one_image_is_uploaded(self):
        self.quick_assert(
            len(self.request.files.values()) == 1,
            ('invalid', 'validation', 'image'),
            gettext('Only one image can be uploaded.'),
        )

    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Add new business image
            consumes: multipart/form-data
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: media
                  type: File
                  paramType: form
                  required: true
                - name: description
                  type: string
                  paramType: form
                - name: category
                  type: string
                  paramType: form
                  description: Defaults to biz_photo
                  enum_from_const: webapps.images.enums.NOT_DEPRECATED_IMAGE_CATEGORIES
                  required: true
                - name: inspiration_categories
                  description: >
                    Business category associated with inspiration.
                    Required only if category is inspiration.
                  type: array
                  items:
                    type: integer
                  paramType: form
                - name: tags
                  type: array
                  items:
                    type: string
                  paramType: form
            type: ImageV2SingleResponse
        :swagger
        swaggerModels:
            ImageV2SingleResponse:
                id: ImageV2SingleResponse
                properties:
                    data:
                        type: ImageV2
        :swaggerModels
        """
        business = self.business_with_advanced_staffer(business_id)
        self._check_if_only_one_image_is_uploaded()

        data = self._prepare_get_arguments()
        data['uploaded_image'] = get_file_from_request_files(self.request)

        serializer = ImageV2CreateSerializer(
            data=data,
            context={
                'business': business,
                'booking_source': self.booking_source,
            },
        )
        self.validate_serializer(serializer)
        image = serializer.save()

        if data['category'] == ImageTypeEnum.BIZ_PHOTO:
            onboarding_space_service.mark_profile_setup_step_completed(
                business_id=business.id, step=ProfileSetupStepsEnum.ADD_COVER_PHOTO
            )

        if data['category'] == ImageTypeEnum.INSPIRATION:
            onboarding_space_service.increment_metric(
                business_id=business.id, metric=OnboardingSpaceMetricsEnum.PORTFOLIOS
            )

        response_serializer = ImageV2Serializer(instance=image)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field
        self.finish_with_json(status.HTTP_201_CREATED, {'data': response_serializer.data})


class ImageDetailsHandler(RequestHandler):
    """
    Details handler is responsible for handling:
    - retrieving image via "GET .../images/v2/(id)/"
    - deleting image via "DELETE .../images/v2/(id)/"
    - updating image details via "PATCH .../images/v2/(id)/"
    """

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @session(login_required=True)
    def get(self, business_id, image_id):
        """
        swagger:
            summary: Get image
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
            type: ImageV2SingleResponse
        :swagger
        """
        self.business_with_advanced_staffer(business_id)
        image = self.get_object_or_404(Image, id=image_id, business_id=business_id)
        serializer = ImageV2Serializer(instance=image)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field
        self.finish_with_json(status.HTTP_200_OK, {'data': serializer.data})

    @session(login_required=True)
    def delete(self, business_id, image_id):
        """
        swagger:
            summary: Delete image
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
        :swagger
        """
        business = self.business_with_advanced_staffer(business_id)
        image = self.get_object_or_404(Image, id=image_id, business=business_id)
        image.soft_delete()

        if image.category == ImageTypeEnum.BIZ_PHOTO:
            onboarding_space_service.mark_profile_setup_step_uncompleted(
                business_id=business.id, step=ProfileSetupStepsEnum.ADD_COVER_PHOTO
            )

        if image.category == ImageTypeEnum.INSPIRATION:
            onboarding_space_service.decrement_metric(
                business_id=business.id, metric=OnboardingSpaceMetricsEnum.PORTFOLIOS
            )
        self.finish_with_json(status.HTTP_200_OK, {})

    @json_request
    @session(login_required=True)
    def patch(self, business_id, image_id):
        """
        swagger:
            summary: Update image details
            note: >
                This endpoint updates only particular fields (it does not override
                whole object).
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  paramType: body
                  type: UpdateImageData
                  required: true
            type: ImageV2SingleResponse
        :swagger
        swaggerModels:
            UpdateImageData:
                id: UpdateImageData
                properties:
                    description:
                        type: string
                    inspiration_categories:
                        type: array
                        desscription: >
                            Empty list clears inspiration categories, null does not change current
                            state.
                        items:
                            type: integer
        :swaggerModels
        """
        business = self.business_with_advanced_staffer(business_id)
        image = self.get_object_or_404(Image, id=image_id, business_id=business_id)
        serializer = ImageV2UpdateSerializer(
            instance=image,
            data=self.data,
            partial=True,
            context={'business': business},
        )
        self.validate_serializer(serializer)
        image = serializer.save()
        response_serializer = ImageV2Serializer(instance=image)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field
        self.finish_with_json(status.HTTP_200_OK, {'data': response_serializer.data})


class ImageCommentsListHandler(RequestHandler):
    """
    Details handler is responsible for handling:
    - listing image comments via "GET .../images/v2/(id)/comments/"
    - adding new comment for image via "POST .../images/v2/(id)/comments/"
    """

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @staticmethod
    def get_queryset(business_id: int, image_id: int) -> QuerySet:
        return (
            ImageComment.objects.filter(
                image__business_id=business_id,
                image_id=image_id,
            )
            .prefetch_related(
                # We prefetch data for ImageComment.get_user_avatar()
                Prefetch(
                    'image__business__images',
                    queryset=Image.objects.filter(category=ImageTypeEnum.LOGO),
                    to_attr='logo_images',
                ),
                Prefetch(
                    'user__profiles',
                    queryset=UserProfile.objects.all().select_related('photo'),
                ),
            )
            .order_by('-created')
        )

    @session(login_required=True)
    def get(self, business_id, image_id):
        """
        swagger:
            summary: List image comments
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
                - name: page
                  type: integer
                  paramType: query
                  required: false
                - name: per_page
                  type: integer
                  paramType: query
                  required: false
            type: ImageCommentListResponse
        :swagger
        swaggerModels:
            ImageCommentResponse:
                id: ImageCommentResponse
                properties:
                    data:
                        type: ImageV2Comment
            ImageCommentListResponse:
                id: ImageCommentListResponse
                properties:
                    count:
                        type: integer
                    per_page:
                        type: integer
                    data:
                        type: array
                        items:
                            type: ImageV2Comment
            ImageV2Comment:
                id: ImageV2Comment
                properties:
                    id:
                        type: integer
                    created:
                        type: string
                        format: date-time
                    updated:
                        type: string
                        format: date-time
                    content:
                        type: string
                    profile_type:
                        type: string
                        enum: [C, B]
                    user_id:
                        type: integer
                    user_name:
                        type: string
                    user_avatar:
                        type: string
        :swaggerModels
        """
        self.business_with_staffer(business_id, __only='id')

        query_params = self._prepare_get_arguments()
        paginator_serializer = PaginatorSerializer(data=query_params)
        pagination_data = self.validate_serializer(paginator_serializer)

        qs = self.get_queryset(business_id, image_id)

        paginator_page = PaginatorPage(qs, pagination_data['per_page'], pagination_data['page'])
        serializer = PaginatorPageSerializer(
            item_serializer_cls=ImageCommentSerializer,
            instance=paginator_page,
        )
        self.finish_with_json(status.HTTP_200_OK, serializer.data)

    @json_request
    @session(login_required=True)
    def post(self, business_id, image_id):
        """
        swagger:
            summary: Add new comment to image
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  type: ImageCommentRequestBody
                  paramType: body
                  required: true
            type: ImageCommentResponse
        :swagger
        swaggerModels:
            ImageCommentRequestBody:
                id: ImageCommentRequestBody
                properties:
                    content:
                        type: string
        :swaggerModels
        """
        self.business_with_staffer(business_id, __only='id')
        image = self.get_object_or_404(Image, id=image_id, business=business_id, __only='id')
        self.data['image'] = image.id
        self.data['user'] = self.user.id
        self.data['profile_type'] = self.user.profile.profile_type
        serializer = ImageCommentSerializer(data=self.data)
        self.validate_serializer(serializer)
        image_comment = serializer.save()
        response_serializer = ImageCommentSerializer(instance=image_comment)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field
        self.finish_with_json(status.HTTP_201_CREATED, {'data': response_serializer.data})


class ImageCommentsDetailsHandler(RequestHandler):
    """
    Details handler is responsible for handling:
    - updating image comment via "PATCH .../images/v2/(id)/comments/(id)/"
    - deleting comment via "DELETE .../images/v2/(id)/comments/(id)/"
    """

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @json_request
    @session(login_required=True)
    def patch(self, business_id, image_id, comment_id):
        """
        swagger:
            summary: Update comment
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
                - name: comment_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  paramType: body
                  type: UpdateImageCommentData
                  required: true
            type: ImageCommentResponse
        :swagger
        swaggerModels:
            UpdateImageCommentData:
                id: UpdateImageCommentData
                properties:
                    content:
                        type: string
        :swaggerModels
        """
        self.business_with_advanced_staffer(business_id, __only='id')
        comment: ImageComment = self.get_object_or_404(
            ImageComment,
            id=comment_id,
            image_id=image_id,
            image__business=business_id,
            user_id=self.user.id,  # we only allow edition of comments made by user
        )
        serializer = ImageCommentUpdateSerializer(instance=comment, data=self.data)
        self.validate_serializer(serializer)
        comment = serializer.save()
        response_serializer = ImageCommentSerializer(instance=comment)
        self.finish_with_json(status.HTTP_200_OK, {'data': response_serializer.data})

    @session(login_required=True)
    def delete(self, business_id, image_id, comment_id):
        """
        swagger:
            summary: Deletes image comment
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
                - name: comment_id
                  type: integer
                  paramType: path
                  required: true
        :swagger
        """
        self.business_with_advanced_staffer(business_id)
        comment: ImageComment = self.get_object_or_404(
            ImageComment,
            id=comment_id,
            image_id=image_id,
            image__business=business_id,
            user_id=self.user.id,  # we allow deletion only of comments made by user
        )
        comment.soft_delete()
        self.finish_with_json(status.HTTP_200_OK, {})


class ImageCoverHandler(RequestHandler):
    """
    This handler is responsible for handling:
    - retrieving biz cover image via "GET .../images/v2/cover/"
    - uploading new biz cover image via "POST .../images/v2/cover/"
    - clearing biz cover image via "DELETE ../images/v2/cover/"
    """

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Retrieve business cover image
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
            type: ImageV2SingleResponse
        :swagger
        """
        self.business_with_staffer(business_id, __only='id')
        # We intentionally do not return 404 when there is no cover image
        cover_image: Image = Image.objects.filter(
            business_id=business_id,
            category=ImageTypeEnum.BIZ_PHOTO,
            is_cover_photo=True,
        ).first()
        data = None
        if cover_image:
            data = ImageV2Serializer(instance=cover_image).data
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field
        self.finish_with_json(status.HTTP_200_OK, {'data': data})

    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Upload new cover image for business
            consumes: multipart/form-data
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: media
                  type: File
                  paramType: form
                  required: true
            type: ImageV2SingleResponse
        :swagger
        """
        business = self.business_with_reception(business_id)
        uploaded_file = get_file_from_request_files(self.request)
        serializer = ImageV2CreateSerializer(
            data={
                'category': ImageTypeEnum.BIZ_PHOTO,
                'is_cover_photo': True,
                'uploaded_image': uploaded_file,
            },
            context={
                'business': business,
                'booking_source': self.booking_source,
            },
        )
        self.validate_serializer(serializer)
        cover_image = serializer.save()
        onboarding_space_service.mark_profile_setup_step_completed(
            business_id=business.id, step=ProfileSetupStepsEnum.ADD_COVER_PHOTO
        )

        response_serializer = ImageV2Serializer(instance=cover_image)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field
        self.finish_with_json(status.HTTP_200_OK, {'data': response_serializer.data})

    @session(login_required=True)
    def delete(self, business_id):
        """
        swagger:
            summary: Clears business cover image (does not delete the image)
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
        :swagger
        """
        business = self.business_with_advanced_staffer(business_id, __only='id')
        cover_image: Image = Image.objects.filter(
            business_id=business_id,
            category=ImageTypeEnum.BIZ_PHOTO,
            is_cover_photo=True,
        ).first()
        if cover_image:
            cover_image.is_cover_photo = False
            cover_image.save(update_fields=['is_cover_photo'])
            onboarding_space_service.mark_profile_setup_step_uncompleted(
                business_id=business.id, step=ProfileSetupStepsEnum.ADD_COVER_PHOTO
            )
        self.finish_with_json(status.HTTP_200_OK, {})


class BusinessSetCoverImageHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @session(login_required=True)
    def post(self, business_id, image_id):
        """
        swagger:
            summary: Set business photo as cover image
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: image_id
                  type: integer
                  paramType: path
                  required: true
            type: ImageV2SingleResponse
        :swagger
        """
        self.business_with_advanced_staffer(business_id, __only='id')
        image = self.get_object_or_404(Image, id=image_id, business_id=business_id)

        serializer = ImageV2UpdateSerializer(
            instance=image, data={'is_cover_photo': True}, partial=True
        )
        self.validate_serializer(serializer)
        image = serializer.save()

        response_serializer = ImageV2Serializer(instance=image)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field
        self.finish_with_json(status.HTTP_200_OK, {'data': response_serializer.data})
