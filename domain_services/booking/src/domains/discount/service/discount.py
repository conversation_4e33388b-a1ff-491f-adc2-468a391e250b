from decimal import Decimal
import itertools
from typing import Protocol

from ..aggregate import DiscountAggregate
from ..repository import (
    DiscountDefinitionRepository,
)
from ..dto import (
    PointInTime,
    TimeSlotDiscount,
)


class DiscountService(Protocol):
    def calculate_best_discount(
        self,
        business_id: int,
        service_variants_price_map: dict[int, Decimal],
        time_slots_starts: dict[int, list[PointInTime]],
    ) -> dict[int, TimeSlotDiscount]: ...


class DiscountServiceImpl(DiscountService):
    def __init__(
        self,
        discount_aggregate: DiscountAggregate,
        discount_definition_repository: DiscountDefinitionRepository,
    ) -> None:
        self._discount_aggregate = discount_aggregate
        self._discount_definition_repository = discount_definition_repository

    # pylint: disable=unused-argument
    @staticmethod
    def _extract_range(time_slots_starts: list[PointInTime]) -> tuple[PointInTime, PointInTime]:
        # basing on the earliest and latest timeslots calculate what range of discounts should be
        # fetched from respository
        ...

    def calculate_best_discount(
        self,
        business_id: int,
        service_variants_price_map: dict[
            int, Decimal
        ],  #  tuple of service variant id and service variant price
        time_slots_starts: dict[
            int, list[PointInTime]
        ],  #  dict mapping service variant id to list of its timeslots
    ) -> dict[int, TimeSlotDiscount]:

        time_slots_discounts: dict[int, TimeSlotDiscount] = {}

        # pylint: disable=unpacking-non-sequence
        start, end = self._extract_range(itertools.chain.from_iterable(time_slots_starts.values()))

        discount_definitions = self._discount_definition_repository.get_discounts_for_range(
            business_id=business_id,
            service_variant_ids=service_variants_price_map.keys(),
            range_start=start,
            range_end=end,
        )

        # pylint: disable=too-many-nested-blocks
        for variant_id, time_slots in service_variants_price_map.items():
            service_variant_price = service_variants_price_map.get(variant_id)
            service_variant_discounts = {}
            for time_slot in time_slots:
                best_discount = None
                for discount_definition in discount_definitions:
                    discount = self._discount_aggregate.apply_discount(
                        service_variant_price=service_variant_price,
                        discount_definition=discount_definition,
                        time_slots_start=time_slot,
                    )
                    if discount:
                        if best_discount:
                            if discount[1].discounted_price < best_discount.discounted_price:
                                best_discount = discount
                        else:
                            best_discount = discount
                if best_discount:
                    service_variant_discounts[time_slot] = best_discount
                time_slots_discounts[variant_id] = service_variant_discounts

            time_slots_discounts[variant_id] = time_slots_discounts

        return time_slots_discounts
