from typing import Iterable, Protocol

from .dto import Staffer
from .repository import StafferRepository


class StafferService(Protocol):

    def get_staffer(self, staffer_id: int) -> Staffer | None: ...

    def get_staffers(self, staffer_ids: Iterable[int]) -> list[Staffer]: ...


class StafferServiceImpl(StafferService):
    def __init__(
        self,
        staffer_repository: StafferRepository,
    ) -> None:
        self._staffer_repository = staffer_repository

    def get_staffer(self, staffer_id: int) -> Staffer | None:
        return self._staffer_repository.get_staffer(staffer_id=staffer_id)

    def get_staffers(self, staffer_ids: Iterable[int]) -> list[Staffer]:
        return self._staffer_repository.get_staffers(staffer_ids)
