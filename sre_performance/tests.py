from datetime import timedelta
import pytest

from django.shortcuts import reverse
from django.conf import settings
from django.test import override_settings
from mock import patch
from model_bakery import baker
from rest_framework import status
from rest_framework.test import APITestCase
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST

from lib.baker_utils import get_or_create_booking_source
from lib.tools import tznow
from sre_performance.serializers import DateRangeSerializer
from webapps.booking.baker_recipes import booking_recipe
from webapps.booking.models import Appointment, BookingChange, BookingSources
from webapps.consts import PERFORMANCE_TEST, WEB


@pytest.mark.django_db
class ArchivedBookingChangesViewTestCase(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.url = reverse('archived_booking_changes')
        cls.headers = {
            'HTTP_Content_Type': 'application/json; charset=UTF-8',
            'HTTP_X_API_Country': settings.API_COUNTRY,
            'HTTP_X_API_KEY': 'performance_test_key',
        }
        cls.created = tznow() - timedelta(days=123)
        cls.created_out_of_range = tznow() - timedelta(days=124)
        booking_source = baker.make(
            'booking.BookingSources', name=WEB, app_type=BookingSources.CUSTOMER_APP
        )
        appointment = baker.make(Appointment, source=booking_source)
        appointment_out_of_range = baker.make(Appointment, source=booking_source)
        subbooking = booking_recipe.prepare(appointment=appointment)
        subbooking_out_of_range = booking_recipe.prepare(appointment=appointment_out_of_range)
        subbooking.save(override=True)
        subbooking_out_of_range.save(override=True)
        baker.make(
            BookingChange,
            appointment_id=appointment.id,
            subbooking_id=subbooking.id,
            created=cls.created,
        )
        baker.make(
            BookingChange,
            appointment_id=appointment_out_of_range.id,
            subbooking_id=subbooking_out_of_range.id,
            created=cls.created_out_of_range,
        )

    def test_date_range_serializer(self):
        date_from = self.created.strftime('%Y-%m-%d')
        date_to = self.created_out_of_range.strftime('%Y-%m-%d')
        serializer = DateRangeSerializer(data={'date_from': date_from, 'date_to': date_to})
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['date_from'].date(), self.created.date())
        self.assertEqual(
            serializer.validated_data['date_to'].date(), self.created_out_of_range.date()
        )

    def test_date_range_serializer_incorrect(self):
        serializer = DateRangeSerializer(data={'date_from': 'abc', 'date_to': 'cba'})
        self.assertFalse(serializer.is_valid())

    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_correct_response(self, get_cached_mock):
        self._prepare_performance_test_mock(get_cached_mock)
        response = self.client.get(
            f'{self.url}?date_from={self.created.strftime("%Y-%m-%d")}'
            f'&date_to={self.created.strftime("%Y-%m-%d")}',
            **self.headers,
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)

    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_incorrect_response(self, get_cached_mock):
        self._prepare_performance_test_mock(get_cached_mock)
        response = self.client.get(
            f'{self.url}?date_from=some_invalid_data&date_to=some_invalid_data', **self.headers
        )
        self.assertEqual(response.status_code, HTTP_400_BAD_REQUEST)

    def _prepare_performance_test_mock(self, get_cached_mock):
        get_cached_mock.return_value = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key=self.headers['HTTP_X_API_KEY'],
            name=PERFORMANCE_TEST,
        )


@pytest.mark.django_db
class BranchInfoViewTestCase(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.url = reverse('branch_info')
        cls.test_data = {
            'branch': 'test_branch',
            'commit': 'test_hash_#############',
        }
        cls.headers = {
            'HTTP_Content_Type': 'application/json; charset=UTF-8',
            'HTTP_X_API_Country': settings.API_COUNTRY,
            'HTTP_X_API_KEY': 'performance_test_key',
        }

    @override_settings(LIVE_DEPLOYMENT=False)
    @patch('sre_performance.views.BranchInfoView._get_branch_info')
    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_200_get_git_info_mock(self, get_cached_mock, get_git_info_mock):
        self._prepare_performance_test_mock(get_cached_mock)
        self._prepare_git_info_test_mock(get_git_info_mock)

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        for field, value in self.test_data.items():
            self.assertContains(response, field)
            self.assertEqual(response.data[field], value)

    @override_settings(LIVE_DEPLOYMENT=True)
    @patch('sre_performance.views.BranchInfoView._get_branch_info')
    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_401_deployment_live(self, get_cached_mock, get_git_info_mock):
        self._prepare_performance_test_mock(get_cached_mock)
        self._prepare_git_info_test_mock(get_git_info_mock)

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @override_settings(LIVE_DEPLOYMENT=False)
    @patch('sre_performance.views.BranchInfoView._get_branch_info')
    def test_403_api_key(self, get_git_info_mock):
        self._prepare_git_info_test_mock(get_git_info_mock)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def _prepare_performance_test_mock(self, get_cached_mock):
        get_cached_mock.return_value = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key=self.headers['HTTP_X_API_KEY'],
            name=PERFORMANCE_TEST,
        )

    def _prepare_git_info_test_mock(self, get_git_info_mock):
        get_git_info_mock.return_value = self.test_data
