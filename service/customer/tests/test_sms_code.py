import time

import mock
import pytest
from django.conf import settings
from django.core.cache import cache
from django.test import override_settings
from mock import patch
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from lib.booksy_sms import parse_phone_number
from lib.feature_flag.feature.notification import (
    OTPPhoneThrottleRateFlag,
    OTPSimpleThrottleRateFlag,
)
from lib.feature_flag.feature.security import ExcludeFromOTPSMSFlag
from lib.rate_limiter import RedisRateLimiter
from lib.tests.utils import override_eppo_feature_flag
from service.customer.customers import (
    CustomerSMSCodeHandler,
    OTPPhoneRateThrottle,
    OTPSimpleRateThrottle,
)
from service.customer.recaptcha import RecaptchaRequestValidator
from service.tests import BaseAsyncHTTPTest
from webapps.user.enums import AuthOriginEnum

_MAX_NUMBER_REQUEST = 2


@pytest.mark.django_db
class CustomerSMSCodeHandlerTestCase(BaseAsyncHTTPTest):

    url = '/customer_api/account/sms_code'

    def setUp(self):
        super().setUp()
        self.user = baker.make_recipe('webapps.user.customer_user', email='<EMAIL>')
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def _init_data(self):
        cell_phone = '******-555-0117'
        parsed_phone = parse_phone_number(cell_phone)
        # clear cache
        # same as in service/customer/serializers.py:18
        limiter = RedisRateLimiter(
            limit=settings.SMS_REGISTRATION_LIMIT,
            expire=settings.SMS_REGISTRATION_ABUSE_PERIOD,
            prefix='sms_registration_limiter',
        )
        limiter.clear_key(parsed_phone.db_format)
        return dict(cell_phone=cell_phone)

    @override_settings(
        API_COUNTRY='us',
        SMS_REGISTRATION_ABUSE_PERIOD=60 * 10,  # ten minutes
        SMS_REGISTRATION_LIMIT=6,
    )
    @patch('service.customer.customers.send_sms_registration_code_task.delay')
    @patch.object(RecaptchaRequestValidator, 'validate')
    def test_sms_code_rate_not_exceeded(self, _, patched_task):
        data = self._init_data()
        call_counter = 0
        for _ in range(4):
            response = self.fetch(self.url, method='POST', body=data)
            assert response.code == status.HTTP_200_OK
            call_counter += 1
        assert len(patched_task.call_args_list) == call_counter
        response = self.fetch(self.url, method='POST', body=data)
        call_counter += 1
        assert response.code == status.HTTP_200_OK
        assert len(patched_task.call_args_list) == call_counter

    @override_settings(
        API_COUNTRY='us',
        SMS_REGISTRATION_ABUSE_PERIOD=60 * 10,  # ten minutes
        SMS_REGISTRATION_LIMIT=_MAX_NUMBER_REQUEST,  # two requests
    )
    @patch('service.customer.customers.send_sms_registration_code_task.delay')
    @patch.object(RecaptchaRequestValidator, 'validate')
    def test_sms_code_rate_exceeded(self, _, patched_task):
        data = self._init_data()
        num_code_sent = _MAX_NUMBER_REQUEST + 1
        for _ in range(num_code_sent):
            response = self.fetch(self.url, method='POST', body=data)
            assert response.code == status.HTTP_200_OK
        # check number of times task was spawned
        assert len(patched_task.call_args_list) == num_code_sent
        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_400_BAD_REQUEST
        # assert task called twice
        assert len(patched_task.call_args_list) == num_code_sent

    @override_settings(
        API_COUNTRY='us',
        SMS_REGISTRATION_ABUSE_PERIOD=2,  # two seconds
        SMS_REGISTRATION_LIMIT=_MAX_NUMBER_REQUEST,  # two requests
    )
    @patch('service.customer.customers.send_sms_registration_code_task.delay')
    @patch.object(RecaptchaRequestValidator, 'validate')
    def test_sms_code_rate_expire(self, _, patched_task):
        data = self._init_data()
        number_calls = _MAX_NUMBER_REQUEST + 1
        for _ in range(number_calls):
            response = self.fetch(self.url, method='POST', body=data)
            assert response.code == status.HTTP_200_OK
        task_called = number_calls
        assert len(patched_task.call_args_list) == task_called
        time.sleep(4)
        for _ in range(number_calls):
            response = self.fetch(self.url, method='POST', body=data)
            assert response.code == status.HTTP_200_OK
        task_called = number_calls * 2
        assert len(patched_task.call_args_list) == task_called

    @override_eppo_feature_flag({ExcludeFromOTPSMSFlag.flag_name: True})
    @override_settings(
        API_COUNTRY='us',
    )
    @patch('service.customer.customers.send_sms_registration_code_task.delay')
    @patch.object(RecaptchaRequestValidator, 'validate')
    def test_sms_code_blockade__gb_number(self, _, patched_task):
        data = self._init_data()
        data['cell_phone'] = '+44 7924 666531'
        response = self.fetch(self.url, method='POST', body=data)
        assert len(patched_task.call_args_list) == 0
        assert response.code == status.HTTP_200_OK

    @override_eppo_feature_flag({ExcludeFromOTPSMSFlag.flag_name: True})
    @override_settings(
        API_COUNTRY='us',
    )
    @patch('service.customer.customers.send_sms_registration_code_task.delay')
    @patch.object(RecaptchaRequestValidator, 'validate')
    def test_sms_code_blockade__local_number(self, _, patched_task):
        data = self._init_data()
        data['cell_phone'] = '******-456-7890'
        response = self.fetch(self.url, method='POST', body=data)
        assert len(patched_task.call_args_list) == 1
        assert response.code == status.HTTP_200_OK


@pytest.mark.django_db
class CustomerSMSCodeRateLimitingTestCase(BaseAsyncHTTPTest):
    url = '/customer_api/account/sms_code'

    def setUp(self):
        super().setUp()
        cache.clear()
        self.user = baker.make_recipe('webapps.user.customer_user', email='<EMAIL>')
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def make_request(self, cell_phone=None):
        data = {'cell_phone': cell_phone or '****** 555 0117'}
        kwargs = {'method': 'POST', 'headers': self.get_headers(self.url), 'body': data}
        return self.fetch(self.url, **kwargs)

    @parameterized.expand([OTPSimpleRateThrottle, OTPPhoneRateThrottle])
    @override_settings(API_COUNTRY='pl')
    @mock.patch(
        'service.customer.serializers.'
        'BooksyCustomerRegistrationPhoneSerializer.validate_cell_phone',
        return_value=parse_phone_number('****** 555 0117'),
    )
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    def test_otp_rate_limiter(self, throttle_class, *args):
        with mock.patch.object(throttle_class, 'rate', new='1/hour'):
            with mock.patch.object(
                CustomerSMSCodeHandler, 'throttle_classes', new=[throttle_class]
            ):
                assert self.make_request().code == status.HTTP_200_OK
                assert self.make_request().code == status.HTTP_429_TOO_MANY_REQUESTS
                cell_phone_local = '+48 787 787 787'
                assert self.make_request(cell_phone_local).code == status.HTTP_200_OK
                assert self.make_request(cell_phone_local).code == status.HTTP_200_OK

    @override_settings(API_COUNTRY='pl')
    @patch.dict('service.mixins.throttling.THROTTLE_RATES', {'customer_sms_code': '1/minute'})
    @mock.patch(
        'service.customer.serializers.'
        'BooksyCustomerRegistrationPhoneSerializer.validate_cell_phone',
        return_value=parse_phone_number('****** 555 0117'),
    )
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    def test_otp_rate_limiter__all(self, *args):
        assert self.make_request().code == status.HTTP_200_OK
        assert self.make_request().code == status.HTTP_429_TOO_MANY_REQUESTS

        cell_phone_local = '+48 787 787 787'
        assert self.make_request(cell_phone_local).code == status.HTTP_429_TOO_MANY_REQUESTS

    @parameterized.expand([OTPSimpleRateThrottle, OTPPhoneRateThrottle])
    def test_throttling_rates(self, throttle_class):
        instance = throttle_class()
        assert instance.rate == instance.get_rate()

    @parameterized.expand(
        [
            (OTPSimpleRateThrottle, OTPSimpleThrottleRateFlag),
            (OTPPhoneRateThrottle, OTPPhoneThrottleRateFlag),
        ]
    )
    def test_throttling_rates__with_flags(self, throttle_class, throttle_flag):
        with override_eppo_feature_flag({throttle_flag.flag_name: '1'}):
            assert throttle_class().get_rate() == '1/hour'

    def test_empty_payload(self):
        kwargs = {'method': 'POST', 'headers': self.get_headers(self.url), 'body': {}}
        resp = self.fetch(self.url, **kwargs)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        kwargs['body']['cell_phone'] = ''
        resp = self.fetch(self.url, **kwargs)
        assert resp.code == status.HTTP_400_BAD_REQUEST
