from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, NoDelMixin, ReadOnlyTabular
from lib.tools import tznow
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin, use_new_permissions
from webapps.billing.admin.base import BillingAdminHistoryMixin
from webapps.billing.enums import ProductType
from webapps.billing.forms import (
    BillingBusinessOfferForm,
    BillingLongSubMapperForm,
    ProductOfferForm,
)
from webapps.billing.models import (
    BillingBusinessOffer,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingProductOfferItemHistory,
)
from webapps.billing.models.offer import BillingLongSubMapper
from webapps.billing.permissions import (
    BillingAdvancedAdminPermissionsNoDelMixin,
    BillingUserPermission,
)
from webapps.business.history_retriever import HistoryRetriever
from webapps.user.groups import GroupNameV2


class BillingProductOfferItemInline(
    BillingAdminHistoryMixin,
    ReadOnlyTabular,
    admin.StackedInline,
):
    model = BillingProductOfferItem
    extra = 0
    fields = (
        'product',
        'active',
        'discount_type',
        'discount_amount',
        'discount_frac',
        'discount_duration',
        'created',
        'updated',
        *BillingAdminHistoryMixin._history_fields,
    )
    readonly_fields = fields
    can_delete = False


class ActiveOffersFilter(admin.SimpleListFilter):
    title = 'Active'
    parameter_name = 'active'

    def lookups(self, request, model_admin):
        return (
            ('no', 'No'),
            ('all', 'All'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'no':
            queryset = queryset.filter(active=False)
        elif self.value() is None:
            queryset = queryset.filter(active=True)
        return queryset


class ProductOfferAdmin(
    BillingAdvancedAdminPermissionsNoDelMixin,
    BillingAdminHistoryMixin,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (
        GroupNameV2.BILLING_MANAGER,
        GroupNameV2.BILLING_AND_SUBSCRIPTION_ADVANCED,
    )
    read_only_access_groups = (GroupNameV2.BILLING_AND_SUBSCRIPTION_BASIC,)

    model = BillingProductOffer
    form = ProductOfferForm
    list_display = (
        'id',
        'name',
        'active',
        'default',
        'active_for_b2b_referral',
        'active_for_cs',
        'active_for_advanced_user',
        'created',
    )
    readonly_fields = (
        'created',
        'updated',
        'deleted',
        *BillingAdminHistoryMixin._history_fields,
    )
    search_fields = ('=id',)
    list_filter = ('default', ActiveOffersFilter)
    inlines = [
        BillingProductOfferItemInline,
    ]

    def has_add_permission(self, request):
        if use_new_permissions(request.user):
            user_groups = set(request.user.groups.values_list('name', flat=True))
            user_in_groups_with_add_permissions = any(
                group.value in user_groups
                for group in self.full_access_groups
                if group not in (GroupNameV2.BILLING_AND_SUBSCRIPTION_ADVANCED,)
            )
            if not user_in_groups_with_add_permissions:
                return False

        return super().has_add_permission(request)

    @staticmethod
    def _get_offer_item_history(obj, product_type):
        offer_item = obj.offer_items.filter(active=True, product__product_type=product_type).first()
        return HistoryRetriever(BillingProductOfferItemHistory).get_changes(offer_item)

    @staticmethod
    def saas_item_history(obj):
        return ProductOfferAdmin._get_offer_item_history(obj, ProductType.SAAS)

    saas_item_history.short_description = 'SaaS offer item changes'

    @staticmethod
    def staffer_saas_item_history(obj):
        return ProductOfferAdmin._get_offer_item_history(obj, ProductType.STAFFER_SAAS)

    staffer_saas_item_history.short_description = 'Staffer SaaS offer item changes'

    @staticmethod
    def postpaid_sms_item_history(obj):
        return ProductOfferAdmin._get_offer_item_history(obj, ProductType.POSTPAID_SMS)

    postpaid_sms_item_history.short_description = 'Postpaid SMS offer item changes'


class BillingBusinessOfferFilter(admin.SimpleListFilter):
    title = 'Billing offer'
    parameter_name = 'billing_offer'

    def lookups(self, request, model_admin):
        return ((offer.id, offer.name) for offer in BillingProductOffer.objects.all())

    def queryset(self, request, queryset):
        value = self.value()
        if value:
            queryset = queryset.filter(billing_subscriptions__offer_id=int(value)).distinct()
        return queryset


class BillingBusinessOfferAdmin(
    NoDelMixin, BillingAdminHistoryMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (
        GroupNameV2.BILLING_MANAGER,
        GroupNameV2.BILLING_AND_SUBSCRIPTION_ADVANCED,
    )
    read_only_access_groups = (GroupNameV2.BILLING_AND_SUBSCRIPTION_BASIC,)

    model = BillingBusinessOffer
    form = BillingBusinessOfferForm
    list_display = (
        'id',
        'offer',
        'business',
        'visible',
        'active',
        'valid_from',
        'valid_to',
    )
    search_fields = (
        'id',
        'business__name',
        'business__owner__email',
        'offer__name',
    )
    query_fields = (
        'id',
        'business_id',
        'offer_id',
    )
    list_filter = ('active',)
    readonly_fields = ('visible',)
    change_readonly_fields = (
        'offer',
        'business',
        'visible',
        *BillingAdminHistoryMixin._history_fields,
    )
    raw_id_fields = ('business',)
    list_select_related = True

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)

        if 'offer' in form.base_fields:
            qs = form.base_fields['offer'].queryset.filter(active=True).order_by('-id')
            if BillingUserPermission(request).is_billing_admin:
                form.base_fields['offer'].queryset = qs
            elif BillingUserPermission(request).is_billing_advanced_user:
                qs = qs.filter(active_for_advanced_user=True)
            else:
                qs = qs.filter(active_for_cs=True)
            form.base_fields['offer'].queryset = qs
        return form

    def get_readonly_fields(self, request, obj=None):
        if not obj:
            return super().get_readonly_fields(request, obj=None)
        return self.change_readonly_fields

    @staticmethod
    def visible(obj=None) -> bool:
        if not obj:
            return False
        _now = tznow()
        return (
            obj.active
            and (obj.valid_from is None or obj.valid_from <= _now)
            and (obj.valid_to is None or obj.valid_to > _now)
        )

    visible.boolean = True


class BillingLongSubMapperAdmin(
    BillingAdvancedAdminPermissionsNoDelMixin,
    BillingAdminHistoryMixin,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (
        GroupNameV2.BILLING_MANAGER,
        GroupNameV2.BILLING_AND_SUBSCRIPTION_ADVANCED,
    )
    read_only_access_groups = (GroupNameV2.BILLING_AND_SUBSCRIPTION_BASIC,)
    model = BillingLongSubMapper
    form = BillingLongSubMapperForm
    list_display = [
        'id',
        'name',
        'offer',
        'subscription_duration',
        'free_staff_qty',
    ]
    readonly_fields = [
        'created',
        'updated',
        'deleted',
        *BillingAdminHistoryMixin._history_fields,
    ]
    search_fields = ['=id']
