from datetime import datetime
from decimal import Decimal
from unittest.mock import patch

import pytest
from pytz import UTC
from django.shortcuts import reverse
from django.conf import settings
from django.test import override_settings
from model_bakery import baker
from rest_framework import status

from drf_api.service.tests.base import AuthenticatedBusinessAPITestCase
from webapps.billing.enums import SubscriptionStatus
from webapps.billing.models import BillingSubscription
from webapps.boost.apis.overdue_transactions import BoostOverdueInfo
from webapps.business.models import BusinessPromotion, Resource
from webapps.invoicing.baker_recipes import user_recipe
from webapps.session.utils import get_user


class TestTotalOverdueInfo(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.business.time_zone_name = 'Asia/Seoul'  # UTC +09:00 offset
        self.business.has_new_billing = True
        self.business.save()

        self.subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            next_billing_date=datetime(2021, 3, 20, microsecond=1, tzinfo=UTC),
            paid_through_date=datetime(2021, 2, 20, tzinfo=UTC),
            currency='USD',
            balance=Decimal('33'),
            status=SubscriptionStatus.BLOCKED,
        )

        self.url = reverse('total_overdue_info', args=(self.business.id,))

    @patch('webapps.billing.interfaces.boost.total_overdue_info')
    def test_boost_and_subscription_overdue_amounts(self, mocked_total_overdue):
        mocked_total_overdue.return_value = BoostOverdueInfo(
            business_id=self.business.id,
            gross_amount=Decimal('30.12'),
        )
        baker.make(BusinessPromotion, business=self.business)

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['boost_overdue_amount'], '30.12')
        self.assertEqual(response.data['currency'], settings.CURRENCY_CODE)
        self.assertEqual(response.data['subscription_overdue_amount'], '33.00')
        self.assertEqual(response.data['total_overdue_amount'], '63.12')

    def test_subscription_overdue_amount(self):
        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['boost_overdue_amount'], '0.00')
        self.assertEqual(response.data['currency'], settings.CURRENCY_CODE)
        self.assertEqual(response.data['subscription_overdue_amount'], '33.00')
        self.assertEqual(response.data['total_overdue_amount'], '33.00')

    @override_settings(STATUS_FLOW__BLOCKED_OVERDUE_AFTER_DAYS=14)
    @pytest.mark.freeze_time(datetime(2021, 2, 24, 12, tzinfo=UTC))
    def test_subscription_overdue_details(self):
        response = self.client.get(self.url, **self.headers)

        overdue_details = response.data['subscription_overdue_details']
        self.assertEqual(overdue_details['subscription_id'], self.subscription.id)
        self.assertEqual(overdue_details['due_date'], '2021-02-20T09:00:00')
        self.assertEqual(overdue_details['account_lock_in_days'], 9)

    @patch('webapps.billing.interfaces.boost.total_overdue_info')
    def test_boost_overdue_amount(self, mocked_total_overdue):
        mocked_total_overdue.return_value = BoostOverdueInfo(
            business_id=self.business.id,
            gross_amount=Decimal('30.12'),
        )
        baker.make(BusinessPromotion, business=self.business)
        self.subscription.delete()

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['boost_overdue_amount'], '30.12')
        self.assertEqual(response.data['currency'], settings.CURRENCY_CODE)
        self.assertEqual(response.data['subscription_overdue_amount'], '0.00')
        self.assertEqual(response.data['total_overdue_amount'], '30.12')

    def test_no_overdue(self):
        self.subscription.balance = 0
        self.subscription.save()

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['boost_overdue_amount'], '0.00')
        self.assertEqual(response.data['currency'], settings.CURRENCY_CODE)
        self.assertEqual(response.data['subscription_overdue_amount'], '0.00')
        self.assertEqual(response.data['total_overdue_amount'], '0.00')
        self.assertIsNone(response.data['subscription_overdue_details'])

    def test_has_new_billing_is_false(self):
        self.business.has_new_billing = False
        self.business.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_not_authenticated(self):
        self.session.delete()
        get_user.clear_from_cache(user_id=self.user.id)

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_not_authorized(self):
        self.business.owner = user_recipe.make()
        self.business.save()
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        self.resource.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
