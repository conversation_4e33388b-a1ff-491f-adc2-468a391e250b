from typing import Any, Protocol

from ..dto.appointment import Appointment, AppointmentFeature
from ..dto.draft import Draft


class AppointmentAggregate(Protocol):

    def schedule(
        self,
        draft: Draft,
        **_: Any,
    ) -> Appointment: ...

    def list_features(self, appointment_id: int) -> list[AppointmentFeature]: ...

    def get_business_id(self, appointment_id: int) -> int | None: ...
