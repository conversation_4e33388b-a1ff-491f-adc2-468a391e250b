from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, DictFlag


class BoostBanBackendFlag(BooleanFlag):
    flag_name = 'Feature_BoostBanBackend'
    adapter = FeatureFlagAdapter.EPPO


class BoostBanIsTheOnlyBanningFlowFlag(BooleanFlag):
    flag_name = 'Feature_BoostBanIsTheOnlyBanningFlow'
    adapter = FeatureFlagAdapter.EPPO


class BoostBanSMSChannelFlag(BooleanFlag):
    flag_name = 'Feature_BoostBanSMSChannel'
    adapter = FeatureFlagAdapter.EPPO


class BoostDontDisableServicePromotionsWhenEndingBoost(BooleanFlag):
    flag_name = 'Fix_BoostDontDisableServicePromotions'
    adapter = FeatureFlagAdapter.EPPO


class BoostHideVatInGbUnderTheKillSwitchFlag(BooleanFlag):
    flag_name = 'Feature_BoostHideVatInGbUnderTheKillSwitch'
    adapter = FeatureFlagAdapter.EPPO


class BoostOverduesBasedOnCutoffCustomTestDateFlag(DictFlag):
    flag_name = 'Feature_BoostOverduesBasedOnCutoffCustomTestDate'
    adapter = FeatureFlagAdapter.LD


class BoostOverduesBasedOnCutoffDateFlag(BooleanFlag):
    flag_name = 'Feature_BoostOverduesBasedOnCutoffDateFlag'
    adapter = FeatureFlagAdapter.LD


class BoostPreSuspensionWarningBackendFlag(BooleanFlag):
    flag_name = 'Feature_BoostPreSuspensionWarningBackend'
    adapter = FeatureFlagAdapter.EPPO


class RefactorGetBoostLandingPageUrlFlag(BooleanFlag):
    flag_name = 'Refactor_GetBoostLandingPageUrl'
    adapter = FeatureFlagAdapter.EPPO


class RefactorSetChargeableTaskFlag(BooleanFlag):
    flag_name = 'Refactor_SetChargeableTask'
    adapter = FeatureFlagAdapter.EPPO
