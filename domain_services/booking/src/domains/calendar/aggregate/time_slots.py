from collections import defaultdict
from dataclasses import dataclass
from datetime import date
from itertools import chain
from typing import Callable, Generator, Iterable

from ....commons.types import DayMinutes, ResourceId
from ...business.dto import PaddingType, PriceList, Service
from ..dto import (
    ComboBooking,
    DailyResourceBookedOccupancyMap,
    Occupancy,
    PossibleSlotsMap,
    ResourceBookedOccupancy,
    ResourceBookedOccupancyMap,
    SlotDetails,
)
from .commons import BinMixin
from .types import Bin, BinSlot, MatchingBinsMap


@dataclass(frozen=True)
class TimeSlotsCalculator(BinMixin):
    def find_time_slots(  # pylint: disable=too-many-arguments
        self,
        bookings: list[ComboBooking],
        price_list: PriceList,
        daily_resources_booked_occupancy_map: DailyResourceBookedOccupancyMap,
        minutes_offsets_map: dict[date, DayMinutes] | None = None,
        include_empty_days: bool = False,
        find_only_first: bool = False,
    ) -> PossibleSlotsMap:
        minutes_offsets_map = minutes_offsets_map or {}

        daily_slots_map = {}
        for date_, resources_booked_occupancy_map in daily_resources_booked_occupancy_map.items():
            if resources_booked_occupancy_map is None:
                if include_empty_days:
                    daily_slots_map[date_] = {}
                continue
            slots = self._find_day_bin_slots(
                bookings=bookings,
                price_list=price_list,
                resources_booked_occupancy_map=resources_booked_occupancy_map,
                minutes_offset=minutes_offsets_map.get(date_) or 0,
            )
            if not slots:
                if include_empty_days:
                    daily_slots_map[date_] = {}
                continue
            slots_iterator = slots.items()
            if find_only_first:
                slots_iterator = [next(iter(slots_iterator))]
            daily_slots_map[date_] = {
                self.bin_length * slot_start_bin: SlotDetails(resources=resource_ids, discount=None)
                for slot_start_bin, resource_ids in slots_iterator
            }
            if find_only_first:
                break

        return daily_slots_map

    # region: match slots
    def _find_day_bin_slots(
        self,
        bookings: list[ComboBooking],
        price_list: PriceList,
        resources_booked_occupancy_map: ResourceBookedOccupancyMap,
        minutes_offset: DayMinutes = 0,
    ) -> MatchingBinsMap:
        # TODO: Add multi-booking appointment here
        return self._match_resources_occupancy(
            appointment_item=bookings[0],
            price_list=price_list,
            resources_booked_occupancy_map=resources_booked_occupancy_map,
            add_padding_before=True,
            add_padding_after=True,
            offset=self._to_bin(minutes_offset),
        )

    def _match_resources_occupancy(  # pylint: disable=too-many-arguments
        self,
        appointment_item: ComboBooking,
        price_list: PriceList,
        resources_booked_occupancy_map: ResourceBookedOccupancyMap,
        add_padding_before: bool = False,
        add_padding_after: bool = False,
        offset: int = 0,
    ) -> MatchingBinsMap:
        # TODO: Add parallel combo here
        service = price_list.services[appointment_item.service_variant_id]
        staffer_ids: list[ResourceId] = (
            [appointment_item.staffer_id] if appointment_item.staffer_id else service.staffer_ids
        ) or []
        appliance_ids = service.appliance_ids or []
        service_slots = self._build_slots(service)

        padding_before, padding_after = 0, 0
        padding = self._to_bin(service.padding_duration or 0)
        if add_padding_before and service.padding_type in {
            PaddingType.BEFORE,
            PaddingType.BEFORE_AND_AFTER,
        }:
            padding_before = padding
        if add_padding_after and service.padding_type in {
            PaddingType.AFTER,
            PaddingType.BEFORE_AND_AFTER,
        }:
            padding_after = padding

        # Slots are described by begginig of slot
        slots_map: MatchingBinsMap = defaultdict(set)
        for resource_id in chain(staffer_ids, appliance_ids):
            if booked_occpancy := resources_booked_occupancy_map.get(resource_id):
                for slot_start in self._match_slots(
                    service_slots=service_slots,
                    booked_occupancy=booked_occpancy,
                    offset=offset,
                    padding_before=padding_before,
                    padding_after=padding_after,
                ):
                    slots_map[slot_start].add(resource_id)

        # If service require appliance any appliance must be present
        if appliance_ids:
            condition = self._get_appliance_condition(appliance_ids, staffer_ids)
            slots_map = {
                start_bin: resource_ids
                for start_bin, resource_ids in slots_map.items()
                if condition(resource_ids)
            }

        return self._filter_slots(
            slots_map=slots_map,
            response_bin_resolution=self._to_bin(service.time_slot_interval),
        )

    @staticmethod
    def _match_slots(  # pylint: disable=too-many-arguments
        service_slots: list[BinSlot],
        booked_occupancy: ResourceBookedOccupancy,
        offset: Bin = 0,
        padding_before: Bin = 0,
        padding_after: Bin = 0,
    ) -> Generator[int, None, None]:
        if not service_slots:
            return
        occupancy = booked_occupancy.booking[offset:]
        padding_only_occupancy = booked_occupancy.padding[offset:]
        occupancy_length = len(occupancy)
        service_length = service_slots[-1][1]
        for ss in occupancy.search(Occupancy('0' * service_slots[0][1])):
            if (
                padding_before
                and padding_only_occupancy[ss - padding_before : ss]
                != Occupancy('0' * padding_before)
            ) or ((occupancy_length - ss) < service_length):
                continue
            match = True
            for beg, end in service_slots[1:]:
                length = end - beg
                if occupancy[ss:][beg:end] != Occupancy('0' * length):
                    match = False
                    break
            se = ss + service_slots[-1][1]
            if padding_after and (
                padding_only_occupancy[se : se + padding_after] != Occupancy('0' * padding_after)
            ):
                match = False
            if match:
                yield ss + offset

    @classmethod
    def _filter_slots(
        cls,
        slots_map: MatchingBinsMap,
        response_bin_resolution: Bin = 1,
    ) -> MatchingBinsMap:
        return dict(cls._filter_slots_generator(slots_map, response_bin_resolution))

    @staticmethod
    def _get_appliance_condition(
        appliance_ids: Iterable[ResourceId],
        staffer_ids: Iterable[ResourceId],
    ) -> Callable[[set[ResourceId]], bool]:
        """
        Returns conditions for services with applianace.

        Check for staffer ids is added to handle the situation when service require any staffer
        and method _match_slots returns only appliance.
        """
        if staffer_ids:
            return lambda resource_ids_: bool(
                resource_ids_.intersection(appliance_ids)
                and resource_ids_.intersection(staffer_ids)
            )
        return lambda resource_ids_: bool(resource_ids_.intersection(appliance_ids))

    @staticmethod
    def _filter_slots_generator(
        slots_map: MatchingBinsMap,
        response_bin_resolution: Bin,
    ) -> Generator[tuple[Bin, set[ResourceId]], None, None]:
        latest_yielded = float("-inf")
        for ss, resource_ids in sorted(slots_map.items()):
            if (ss - latest_yielded) >= response_bin_resolution:
                yield ss, resource_ids
                latest_yielded = ss

    # endregion: match slots

    # region: build slots
    def _build_slots(
        self,
        service: Service,
    ) -> list[BinSlot]:
        # TODO: Add sequence combo here
        return self._build_simple_service_slots(
            service=service,
        )

    def _build_simple_service_slots(
        self,
        service: Service,
    ) -> list[BinSlot]:
        beg, end = 0, self._to_bin(service.duration)

        if gap_start := service.gap_start_after:
            gap_start = beg + self._to_bin(gap_start)
            gap_end = gap_start + self._to_bin(service.gap_duration)
            result = [(0, gap_start), (gap_end, end)]
        else:
            result = [(0, end)]

        return result

    # endregion: build slots
