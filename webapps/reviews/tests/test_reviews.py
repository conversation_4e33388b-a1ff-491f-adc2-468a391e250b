import json
import secrets
from datetime import datetime, timedelta

import pytest
from django.test import TestCase
from mock import patch
from model_bakery import baker
from pytz import UTC
from rest_framework import status

import lib
from lib.test_utils import create_subbooking
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment
from webapps.experiment_v3.exp import DfCommunication
from webapps.user.models import User
from webapps.user.tools import truncate_last_name


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2021, 3, 22, 8, tzinfo=UTC))
class TestAddCustomerReview(BaseAsyncHTTPTest, TestCase):
    def test_add_review_without_appointment(self):
        url = f'/customer_api/businesses/{self.business.id}/reviews/'
        body = {
            'rank': 5,
            'review': 'awesome!!!11111!!!',
        }
        response = self.fetch(url, method='POST', body=body)
        expected_errors = {
            'code': 'invalid',
            'type': 'validation',
            'field': 'user',
            'description': 'Make an appointment and post your review' ' once you\'re finished.',
        }
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.code)
        self.assertDictEqual(expected_errors, response.json['errors'][0])


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2021, 3, 22, 8, tzinfo=UTC))
class TestEditCustomerReview(BaseAsyncHTTPTest, TestCase):
    def test_edit_review_without_review(self):
        review_id = secrets.randbelow(999_999_999)
        url = f'/customer_api/businesses/{self.business.id}/reviews/{review_id}'
        body = {
            'rank': 2,
            'review': 'Edited: not awesome',
        }
        response_edit = self.fetch(url, method='PUT', body=body)
        self.assertEqual(status.HTTP_404_NOT_FOUND, response_edit.code)


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2021, 3, 22, 8, tzinfo=UTC))
class TestAddAndEditCustomerReview(BaseAsyncHTTPTest, TestCase):
    def setUp(self):
        super().setUp()

        with patch.dict(DfCommunication.config, {'active': False}):
            DfCommunication.initialize()

    def test_add_and_edit_review(self):
        self._create_finished_appointment()
        url_post = f'/customer_api/businesses/{self.business.id}/reviews/'
        body = {
            'rank': 5,
            'review': 'awesome!!!11111!!!',
        }
        response_add = self.fetch(url_post, method='POST', body=body)
        self.assertEqual(status.HTTP_200_OK, response_add.code)

        # check whole response_add
        review_id = response_add.json['id']
        expected = json.dumps(self._get_expected_response_add(review_id, body))
        self.assertJSONEqual(expected, response_add.json)

        # edit
        url_put = f'/customer_api/businesses/{self.business.id}/reviews/{review_id}'
        hacker_user = baker.make(
            User,
            first_name='Hacker',
            last_name='Man',
        )
        body = {
            'rank': 2,
            'review': 'Edited: not awesome',
            'user': hacker_user.id,
        }
        response_edit = self.fetch(url_put, method='PUT', body=body)
        self.assertEqual(status.HTTP_200_OK, response_edit.code)

        # check whole response_edit
        expected = self._get_expected_response_add(review_id, body)
        expected['business']['reviews_count'] = 1
        expected['business']['reviews_stars'] = 2
        expected = json.dumps(expected)
        self.assertJSONEqual(expected, response_edit.json)

    def _create_finished_appointment(self):
        bci = baker.make(
            'business.BusinessCustomerInfo',
            business=self.business,
            user=self.user,
        )
        self.now = lib.tools.tznow()
        self.booked_from = self.now - timedelta(days=7, minutes=60)
        self.booked_till = self.now - timedelta(days=7)

        create_subbooking(
            business=self.business,
            booking_kws=dict(
                status=Appointment.STATUS.FINISHED,
                booked_for=bci,
                booked_from=self.booked_from,
                booked_till=self.booked_till,
                autoassign=True,
                updated_by=self.user,
            ),
        )

    def _get_expected_response_add(self, review_id, body):
        return {
            'id': review_id,
            'rank': body['rank'],
            'title': '',
            'review': body['review'],
            'user': {
                'id': self.user.id,
                'first_name': self.user.first_name,
                'last_name': truncate_last_name(self.user.last_name),
            },
            'business': {
                'id': self.business.id,
                'name': self.business.name,
                'reviews_count': None,
                'reviews_stars': 0,
                'url': self.business.get_marketplace_sitemap_url(),
                'full_address': 'asdf, A Warszawa najlepsze miasto świata',
            },
            'reply_content': None,
            'reply_updated': None,
            'created': self.now.isoformat(timespec='minutes').split('+')[0],
            'updated': self.now.isoformat(timespec='minutes').split('+')[0],
            'services': [{'id': None, 'name': None, 'treatment_id': None}],
            'staff': [],
            'source': None,
            'verified': True,
            'appointment_date': self.booked_from.isoformat(
                timespec='minutes',
            ).split(
                '+'
            )[0],
            'photos': [],
            'anonymized': False,
        }
