import logging
from collections import defaultdict

from webapps.suit.templatetags.suit_menu import get_admin_site, Menu

from django.urls import reverse
from django.http import HttpRequest
from django.template.defaulttags import register
from django.conf import settings

_logger = logging.getLogger('booksy.full')


class BooksyAdminMenu(Menu):
    """Allows to add links to non-model admin views in main admin menu."""

    def __init__(self, context, request, app_list, custom_views):
        super(BooksyAdminMenu, self).__init__(context, request, app_list)
        self.custom_views = custom_views

    def process_app(self, app):
        app = super(BooksyAdminMenu, self).process_app(app)
        app['custom_views'] = self.custom_views.get(app['name'])
        return app


@register.simple_tag(takes_context=True)
def get_menu(context, request):
    """
    :type request: HttpRequest
    """
    if not isinstance(request, HttpRequest) or settings.PYTEST:
        return None

    admin_site = get_admin_site(context.request.resolver_match.namespace)
    template_response = admin_site.index(request)

    # Group custom views by main_menu_app param
    custom_views = defaultdict(list)
    for custom_view in admin_site.custom_views:
        if custom_view.main_menu_app is not None:
            custom_view_url = reverse('admin:{}'.format(custom_view.urlname))
            # Needed in menu template
            custom_view_data = dict(
                name=custom_view.name,
                url=custom_view_url,
                is_active=(custom_view_url == request.path),
            )
            custom_views[custom_view.main_menu_app].append(custom_view_data)

    try:
        app_list = template_response.context_data['app_list']
    except Exception:
        _logger.exception('[ADMIN]')
        return

    return BooksyAdminMenu(
        context,
        request,
        app_list,
        custom_views,
    ).get_app_list()
