# pylint: disable=line-too-long
from django.apps import AppConfig

from v2.domain.interfaces.settings import SettingsProvider
from v2.infrastructure.settings import SettingsProviderDjango
from v2.infrastructure.translation import TranslatorDjango, Translator
from webapps.google_places.application.ports.address_validation import (
    AbstractAddressValidationService,
)
from webapps.google_places.domain.ports.address_validation_client import (
    GoogleAddressValidationAbstractClient,
)
from webapps.google_places.domain.ports.address_validation_gateway import (
    GoogleAddressValidationGateway,
)
from webapps.google_places.domain.ports.address_validation_service import (
    AbstractAddressValidationDomainService,
)
from webapps.google_places.infrastructure.apis.address_validation_api import (
    GoogleAddressValidationClient,
)
from webapps.google_places.infrastructure.gateways.address_validation_gateway import (
    GoogleAddressValidationApiGateway,
)


class GooglePlacesConfig(AppConfig):
    name = 'webapps.google_places.infrastructure'
    label = 'google_places'
    verbose_name = 'Google Places'

    def ready(self):
        from webapps.google_places.application.services.details import (
            AbstractDetailsService,
            AbstractDetailsServiceV2,
            DetailsService,
            DetailsServiceV2,
        )
        from webapps.google_places.application.services.autocomplete import (
            AbstractAutocompleteService,
            AutocompleteService,
        )
        from webapps.google_places.application.services.address_validation import (
            AddressValidationService,
        )
        from webapps.google_places.domain.services.pin_location_validation_service import (
            PinLocationValidationService,
        )
        from webapps.google_places.domain.services.address_validation_service import (
            AddressValidationDomainService,
        )
        from webapps.google_places.infrastructure.converters.validate_address_response_converter import (
            ValidateAddressResponseConverter,
        )
        from webapps.google_places.domain.ports.venue_port import VenuePort
        from webapps.google_places.infrastructure.adapters.venue_adapter import VenueAdapter
        from webapps.google_places.containers import container
        from service.search.v2.application.venue_search_service import VenueSearchService
        from service.search.v2.domain.ports import VenueFinderPort, VenueRepository
        from service.search.v2.infrastructure.repository import ElasticsearchVenueRepository
        from webapps.google_places.domain.ports.google_places_gateway import (
            GooglePlacesGateway,
        )
        from webapps.google_places.infrastructure.gateways.google_places_gateway import (
            GooglePlacesAPIGateway,
        )

        from webapps.google_places.infrastructure.apis.google_places_api import GooglePlacesClient
        from webapps.google_places.domain.ports.google_places_client import (
            GooglePlacesAbstractClient,
        )
        from webapps.google_places.domain.ports.address_components_converter import (
            AbstractAddressComponentsConverter,
        )
        from webapps.google_places.infrastructure.converters.address_components_converter import (
            AddressComponentsConverter,
            ValidateAddressComponentsConverter,
        )

        # Register Address Validation Domain Service
        container[AbstractAddressValidationDomainService] = (
            lambda c: AddressValidationDomainService()
        )

        # Register services
        container[VenueRepository] = lambda c: ElasticsearchVenueRepository()
        container[VenueFinderPort] = lambda c: VenueSearchService(repository=c[VenueRepository])
        container[VenuePort] = lambda c: VenueAdapter(c[VenueFinderPort])
        container[GooglePlacesGateway] = GooglePlacesAPIGateway
        container[GooglePlacesAbstractClient] = GooglePlacesClient
        container[AbstractAddressComponentsConverter] = AddressComponentsConverter
        container[AbstractDetailsService] = DetailsService
        container[AbstractDetailsServiceV2] = lambda c: DetailsServiceV2(
            gateway=c[GooglePlacesGateway],
            settings_provider=c[SettingsProvider],
            address_validation_service=c[AddressValidationDomainService],
        )
        container[AbstractAutocompleteService] = AutocompleteService

        # Register SettingProvider
        container[SettingsProvider] = SettingsProviderDjango()

        # Register API Clients
        container[GoogleAddressValidationAbstractClient] = lambda c: GoogleAddressValidationClient(
            settings_provider=c[SettingsProvider]
        )

        # Register Address Components Converter
        container[AddressComponentsConverter] = lambda c: AddressComponentsConverter()

        # Register ValidateAddressComponentsConverter
        container[ValidateAddressComponentsConverter] = (
            lambda c: ValidateAddressComponentsConverter()
        )

        # Register ValidateAddressResponseConverter with ValidateAddressComponentsConverter
        container[ValidateAddressResponseConverter] = lambda c: ValidateAddressResponseConverter(
            address_components_converter=c[ValidateAddressComponentsConverter]
        )

        # Register Address Validation API Gateway with api client and converter
        container[GoogleAddressValidationGateway] = lambda c: GoogleAddressValidationApiGateway(
            api_client=c[GoogleAddressValidationAbstractClient],
            response_converter=c[ValidateAddressResponseConverter],
        )

        # Register PinLocationValidationService
        container[PinLocationValidationService] = lambda c: PinLocationValidationService()

        # Register Translator
        container[Translator] = TranslatorDjango

        # Register address Validation service with api gateway
        container[AbstractAddressValidationService] = lambda c: AddressValidationService(
            google_gateway=c[GoogleAddressValidationGateway],
            settings_provider=c[SettingsProvider],
            pin_location_validation_service=c[PinLocationValidationService],
            address_validation_service=c[AddressValidationDomainService],
            translator=c[TranslatorDjango],
        )
