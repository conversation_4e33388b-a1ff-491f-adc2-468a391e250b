#!/usr/bin/env python
from decimal import Decimal
from django.utils.translation import gettext_lazy as _

# https://en.wikipedia.org/wiki/Payment_card_number
from lib.enums import BaseEnum, StrChoicesEnum, StrEnum

CARD_TYPE__AMERICAN_EXPRESS = 'amex'
CARD_TYPE__CHINA_UNIONPAY = 'unionpay'
CARD_TYPE__DINERS = 'diners'
CARD_TYPE__DISCOVER = 'discover'
CARD_TYPE__INTERPAYMENT = 'interpayment'
CARD_TYPE__JCB = 'jcb'
CARD_TYPE__MAESTRO = 'maestro'
CARD_TYPE__DANKORT = 'dankort'
CARD_TYPE__NSPK_MIR = 'nspk_mir'
CARD_TYPE__MASTERCARD = 'mastercard'
CARD_TYPE__VISA = 'visa'
CARD_TYPE__UATP = 'uatp'
CARD_TYPE__VERVE = 'verve'
CARD_TYPE__GOOGLE_PAY = 'google_pay'
CARD_TYPE__APPLE_PAY = 'apple_pay'
CARD_TYPE__BOOKSY_GIFT_CARD = 'gift_card'
CARD_TYPE__BLIK = 'blik'
CARD_TYPE__OTHER = 'other'
CARD_TYPE__KEYED_IN_PAYMENT = 'kip'
CARD_TYPE__KLARNA = 'klarna'
CARD_TYPES = [
    (CARD_TYPE__AMERICAN_EXPRESS, 'American Express'),
    (CARD_TYPE__CHINA_UNIONPAY, 'China UnionPay'),
    (CARD_TYPE__DINERS, 'Diners Club'),
    (CARD_TYPE__DISCOVER, 'Discover Card'),
    (CARD_TYPE__INTERPAYMENT, 'InterPayment'),
    (CARD_TYPE__JCB, 'JCB'),
    (CARD_TYPE__MAESTRO, 'Maestro'),
    (CARD_TYPE__DANKORT, 'Dankort'),
    (CARD_TYPE__NSPK_MIR, 'NSPK MIR'),
    (CARD_TYPE__MASTERCARD, 'MasterCard'),
    (CARD_TYPE__VISA, 'Visa'),
    (CARD_TYPE__UATP, 'UATP'),
    (CARD_TYPE__VERVE, 'Verve'),
    (CARD_TYPE__GOOGLE_PAY, 'GooglePay'),
    (CARD_TYPE__APPLE_PAY, 'ApplePay'),
    (CARD_TYPE__BOOKSY_GIFT_CARD, 'BooksyGiftCard'),
    (CARD_TYPE__BLIK, 'Blik'),
    (CARD_TYPE__OTHER, ''),
    (CARD_TYPE__KEYED_IN_PAYMENT, 'Keyed In Payment'),
    (CARD_TYPE__KLARNA, 'Klarna'),
]

# actions
BUSINESS_ACTION__SET_PAYMENT_TYPE = 'set_payment_type'
BUSINESS_ACTION__CANCEL_PAYMENT = 'cancel_payment'
BUSINESS_ACTION__CHARGE_DEPOSIT = 'charge_deposit'
BUSINESS_ACTION__CANCEL_DEPOSIT = 'cancel_deposit'
BUSINESS_ACTION__SET_PAYMENT_STATUS = 'set_payment_status'
BUSINESS_ACTION__PARK_TRANSACTION = 'park_transaction'
BUSINESS_ACTION__RETRY_KIP = 'retry_kip'
BUSINESS_ACTIONS = (
    BUSINESS_ACTION__SET_PAYMENT_TYPE,
    BUSINESS_ACTION__CANCEL_PAYMENT,
    BUSINESS_ACTION__CHARGE_DEPOSIT,
    BUSINESS_ACTION__CANCEL_DEPOSIT,
    BUSINESS_ACTION__SET_PAYMENT_STATUS,
    BUSINESS_ACTION__PARK_TRANSACTION,
    BUSINESS_ACTION__RETRY_KIP,
)
CUSTOMER_ACTION__SET_TIP_RATE = 'set_tip_rate'
CUSTOMER_ACTION__MAKE_PAYMENT = 'make_payment'
CUSTOMER_ACTION__CANCEL_PAYMENT = 'cancel_payment'
CUSTOMER_ACTION__RETRY_PAYMENT = 'retry_payment'
CUSTOMER_ACTIONS = (
    CUSTOMER_ACTION__SET_TIP_RATE,
    CUSTOMER_ACTION__MAKE_PAYMENT,
    CUSTOMER_ACTION__CANCEL_PAYMENT,
    CUSTOMER_ACTION__RETRY_PAYMENT,
)

# PaymentMethod
PAYMENT_METHOD_TYPE__CREDIT_CARD = 'credit_card'
PAYMENT_METHOD_TYPES = [
    (PAYMENT_METHOD_TYPE__CREDIT_CARD, _('Credit / Debit Card')),
]

# PaymentType special types used in business logic
PAY_BY_APP = 'pay_by_app'  # special type for PBA
CASH = 'cash'  # special type for Cash Registers

# pay_by_app status
PAY_BY_APP_DISABLED = 'D'
PAY_BY_APP_PENDING = 'P'
PAY_BY_APP_ENABLED = 'E'
PAY_BY_APP_PENDING_ADYEN_APPROVAL = 'F'
PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS = 'G'
PAY_BY_APP_RESTRICTED_ACCESS = 'H'
PAY_BY_APP_ACTIVE_STATUSES = (
    PAY_BY_APP_ENABLED,
    PAY_BY_APP_PENDING,
    PAY_BY_APP_PENDING_ADYEN_APPROVAL,
)
# fake status for PBA
# exists only if pay_by_app enabled
# and do not have Bank Account
NO_BANK_ACCOUNT_STATUS = 'N'

# Consts for commissions
COMMISSION_TYPE__PRODUCT = 'product'
COMMISSION_TYPE__SERVICE = 'service'
COMMISSION_TYPE__EGIFT_CARD = 'egift'
COMMISSION_TYPE__MEMBERSHIP = 'membership'
COMMISSION_TYPE__PACKAGE = 'package'


class CommissionObjectType(BaseEnum):
    PRODUCTS = 'products'
    SERVICE_VARIANTS = 'service_variants'
    EGIFTS = 'egifts'
    PACKAGES = 'packages'
    MEMBERSHIPS = 'memberships'


class CommissionType(StrChoicesEnum):
    AMOUNT = 'A', _('amount')
    # xgettext:no-python-format
    PERCENT = '%', _('% of sale')


class CommissionForType(StrChoicesEnum):
    CASHIER = 'C', _('Checkout Cashier')
    RESOURCE = 'R', _('Service Provider')


class CommissionAction(BaseEnum):
    APPLY = 'apply'
    APPLY_PRODUCTS = 'apply_products'
    APPLY_SERVICES = 'apply_services'
    CLEAR = 'clear'
    STAFF_APPLY = 'staff_apply'
    STAFF_APPLY_PRODUCTS = 'staff_apply_products'
    STAFF_APPLY_SERVICES = 'staff_apply_services'
    STAFF_CLEAR = 'staff_clear'


class CommissionRateAction(BaseEnum):
    APPLY = 'apply'


class StaffCommissionRateAction(BaseEnum):
    DEFAULT = 'default'
    CLEAR = 'clear'


class PaymentTypeEnum(StrChoicesEnum):
    PAY_BY_APP = PAY_BY_APP, _('Mobile Payment')  # special type for PBA
    PAY_BY_APP_DONATIONS = 'pba_donations', _('Donations')  # deprecated
    CASH = CASH, _('Cash')  # special type for Cash Registers
    CHECK = 'check', _('Check')
    CHEQUE = 'cheque', _('Cheque')
    CREDIT_CARD = 'credit_card', _('Credit Card')
    SUBSCRIPTION = 'subscription', _('Subscription Card')
    STORE_CREDIT = 'store_credit', _('Store Credit')
    BANK_TRANSFER = 'bank_transfer', _('Bank Transfer')
    AMERICAN_EXPRESS = 'american_express', 'American Express'
    PAYPAL = 'paypal', 'PayPal'
    SQUARE = 'square', 'Square'
    SPLIT = 'split', _('Split payment')
    PREPAYMENT = 'prepayment', _('Deposit')
    EGIFT_CARD = 'egift_card', _('Gift Card')
    MEMBERSHIP = 'membership', _('Membership')
    PACKAGE = 'package', _('Package')
    DIRECT_PAYMENT = 'direct_payment', _('Direct Payment')
    STRIPE_TERMINAL = 'stripe_terminal', _('Booksy Card Reader')
    TAP_TO_PAY = 'tap_to_pay', _('Tap to Pay')
    BOOKSY_GIFT_CARD = 'booksy_gift_card', _('Booksy Gift Card')
    BOOKSY_PAY = 'booksy_pay', _('Booksy Pay')
    BLIK = 'blik', _('Blik')
    KEYED_IN_PAYMENT = 'keyed_in_payment', _('Keyed In Payment')
    KLARNA = 'klarna', 'Klarna'

    # These two paymentTypes are deprecated. Still here, because of displaying
    # receips. >>
    GIFT_CARD = 'giftcard', _('Own GiftCard')
    VOUCHER = 'voucher', _('Voucher')
    # <<

    @classmethod
    def online_methods(cls):
        return [
            cls.PREPAYMENT,
            cls.PAY_BY_APP,
            cls.PAY_BY_APP_DONATIONS,
            cls.STRIPE_TERMINAL,
            cls.TAP_TO_PAY,
            cls.BOOKSY_PAY,
            cls.BLIK,
            cls.KEYED_IN_PAYMENT,
        ]

    @classmethod
    def terminal_methods(cls) -> list:
        return [cls.STRIPE_TERMINAL, cls.TAP_TO_PAY, cls.KEYED_IN_PAYMENT]

    @classmethod
    def stripe_only_methods(cls) -> list:
        return [cls.BOOKSY_PAY]


class POSPlanPaymentTypeEnum(StrChoicesEnum):
    ADYEN_MOBILE_PAYMENT = 'adyen_mobile_payment', _('Adyen Mobile Payment')
    ADYEN_DONATIONS = 'adyen_donations', _('Adyen Donations')  # deprecated
    STRIPE_MOBILE_PAYMENT = 'stripe_mobile_payment', _('Stripe Mobile Payment')
    STRIPE_TERMINAL = 'stripe_terminal', _('Stripe Terminal')
    FAST_PAYOUT = 'fast_payout', _('Fast Payout')
    TAP_TO_PAY = 'tap_to_pay', _('Tap To Pay')
    BOOKSY_PAY = 'booksy_pay', _('Booksy Pay')


POS_PLAN_FEE_DESCRIPTION_MAP = {
    POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT: _("Fee per mobile payment and refund transaction"),
    POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT: _(
        "Fee per mobile payment and refund transaction"
    ),
    POSPlanPaymentTypeEnum.STRIPE_TERMINAL: _(
        "Fee per Booksy Card Reader payment and refund transaction"
    ),
    POSPlanPaymentTypeEnum.FAST_PAYOUT: _("Fee per Fast Payout"),
    POSPlanPaymentTypeEnum.TAP_TO_PAY: _("Fee per Tap to Pay and refund transaction"),
    POSPlanPaymentTypeEnum.BOOKSY_PAY: _("Fee per Booksy Pay transaction"),
}


POS_PLAN_FEE_DESCRIPTION_EXCLUDING_TAX_MAP = {
    POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT: _(
        "Fee per mobile payment and refund transaction (excluding tax)"
    ),
    POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT: _(
        "Fee per mobile payment and refund transaction (excluding tax)"
    ),
    POSPlanPaymentTypeEnum.STRIPE_TERMINAL: _(
        "Fee per Booksy Card Reader payment and refund transaction (excluding tax)"
    ),
    POSPlanPaymentTypeEnum.FAST_PAYOUT: _("Fee per Fast Payout (excluding tax)"),
    POSPlanPaymentTypeEnum.TAP_TO_PAY: _(
        "Fee per Tap to Pay and refund transaction (excluding tax)"
    ),
    POSPlanPaymentTypeEnum.BOOKSY_PAY: _("Fee per Booksy Pay transaction (excluding tax)"),
}


PAYMENT_TYPE_LABEL = dict(PaymentTypeEnum.choices())


class PaymentProviderEnum(StrChoicesEnum):
    ADYEN_PROVIDER = 'adyen_ee', _('Adyen EE')
    FAKE_PROVIDER = 'fake', _('Fake')
    STRIPE_PROVIDER = 'stripe', _('Stripe')
    ADYEN_PROXY_PROVIDER = 'adyen_proxy', _('Adyen Proxy')
    STRIPE_PROXY_PROVIDER = 'stripe_proxy', _('Stripe Proxy')
    BOOKSY_GIFT_CARDS = 'booksy_gc', _('Booksy Gift Cards')


class PaymentRowsSummaryScopes(StrEnum):
    DAY = 'day'
    MONTH = 'month'
    CUSTOM = 'custom'


class POSPlanBatchUpdateLogStatus(StrChoicesEnum):
    FAILED = 'failed', _('failed')
    FINISHED = 'finished', _('finished')
    IN_PROGRESS = 'in_progress', _('in progress')


class POSMigrationGroup(StrChoicesEnum):
    ZERO_TRANSACTION = 'zero_transaction', 'Zero Transaction'
    OFFLINE_TRANSACTION = 'offline_transaction', 'Offline Transaction'
    ONLINE_TRANSACTION = 'online_transaction', 'Online Transaction'
    ONLINE_DATA_TRANSACTION = 'online_transaction_wth_data', 'Online Transaction With Data'
    NEW_BUSINESS = 'new_business', 'New Business'
    DIRECT_ACTIVATION = 'direct_activation', 'Direct Activation'


class ReissueGiftCardReasonEnum(StrEnum):
    CANCELLED_BY_CUSTOMER = 'appointment_cancelled_by_customer'
    CANCELLED_BY_BUSINESS = 'appointment_cancelled_by_business'


class PaymentTypeStatus(StrEnum):
    NOT_VERIFIED = 'NOT_VERIFIED'
    VERIFICATION_PENDING = 'VERIFICATION_PENDING'
    TURNED_OFF = 'TURNED_OFF'
    FEES_NOT_ACCEPTED = 'FEES_NOT_ACCEPTED'
    VERIFIED = 'VERIFIED'


class TapToPayStatus(StrEnum):
    DISABLED = 'DISABLED'
    ENABLED = 'ENABLED'
