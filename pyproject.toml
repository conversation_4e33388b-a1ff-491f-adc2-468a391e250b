[tool.black]
line-length = 100
skip-string-normalization = true
target-version = ['py311']
force-exclude = ".*/.venv/.*"

[tool.isort]

[tool.pytest.ini_options]
minversion = "6.0"
env = [
    "DJANGO_SETTINGS_MODULE=settings",
    "ASYNC_TEST_TIMEOUT=20",
    ]
addopts = [
    "--ignore=domain_services/booking/tests/test_scripts/test_compare_requirements.py",
    "--strict-markers",
    "--nomigrations",
    "-m not (online or production or random_failure or public_api or grpc_api)",
    "--create-db",
    "--log-level=ERROR",
    "--tb=short",
    ]
norecursedirs = [
    ".git",
    ".docker",
    ".docker_volume",
    "bin",
    "config",
    "data",
    "deploy",
    "docs/api",
    "locale",
    "statics",
    "templates",
    "migrations",
    "webapps/public_partners",
    ]
markers = [
    "random_failure: Mark test with this marker and create ticket to fix it",
    "grpc_api: gRPC based tests. Use MODE_API=grpc_api to run.",
    "public_api: Test suite of Booksy Public API. Use MODE_API=public to run.",
    "production: Ask Rafał Mirończyk(voy) for more info about this label - test_full_flow_ee_production",
    "online: Ask Rafał Mirończyk (voy) for more info about this label (adyem and dwolla tests)",
    "freeze_time: This label is used to freeze time (Thank you captain obviousness!)",
    "patch_segment_api_calls: takeover all get_sement_api calls",
    "patch_iterable_api_calls: takeover all iterable client _request_api calls",
    "pubsub_emulator: requires pubsub_emulator",
    "patch_booksy_auth_sync: takeover all sync task to booksy auth",
    "patch_booksy_auth_client_make_request: takeover all requests to booksy auth",
    "asyncio: mark a test as being asyncio-driven",
    ]

[tool.coverage.run]
branch = true
omit = [
    "*/migrations/*",
    "*/baker_recipes/*",
    ]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.",
    ]

[tool.coverage.html]
directory = "cov_html"

[tool.coverage.xml]
output = "coverage.xml"
