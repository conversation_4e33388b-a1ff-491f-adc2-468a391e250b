import pytest
from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.business_customer_info.enums import BCIGroupName
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.message_blast.models import SMSBlastMarketingConsent
from webapps.message_blast.enums import MessageBlastGroupEnum
from webapps.message_blast.models import CommonMessageBlastTemplate
from webapps.message_blast.tests.test_helpers import (
    create_templates,
    update_message_blast_groups,
)
from webapps.profile_completeness.models import CompletedStep
from webapps.profile_completeness.steps import SendMessageBlastStep


@pytest.mark.django_db
class SendMessageBlastStepTestCase(BaseAsyncHTTPTest):
    def setUp(
        self,
    ):
        super().setUp()
        self.business.sms_notification_status = Business.SMSStatus.ENABLED
        self.business.save()

        update_message_blast_groups()
        create_templates()

    @staticmethod
    def get_data():
        return {
            'title': 'bb',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': None,
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }
