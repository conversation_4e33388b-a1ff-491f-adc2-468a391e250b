from django import forms
from django.contrib import admin, messages
from django.shortcuts import redirect
from django.urls import re_path as url, reverse
from django.utils.html import format_html

from lib.admin_helpers import BaseModelAdmin
from lib.tools import tznow
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin, use_new_permissions
from webapps.business.models import BusinessCategory
from webapps.ecommerce.models.notification import NotificationCampaign
from webapps.ecommerce.models.permissions import EcommercePermission
from webapps.ecommerce.tasks import send_notification_campaign
from webapps.user.groups import GroupNameV2


class EcommerceNotificationCampaignAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = NotificationCampaign
    filter_horizontal = ('business_categories',)

    def get_field_queryset(self, db, db_field, request):
        if db_field.name == 'business_categories':
            return BusinessCategory.objects.filter(
                type=BusinessCategory.CATEGORY,
            )
        super().get_field_queryset(db, db_field, request)

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ('send_notifications',)
        return ()

    def send_notifications(self, obj):
        return format_html(
            '<a href="{}" class="btn">SEND NOTIFICATIONS ! (use wisely)</a>',
            reverse("admin:ecommerce_send_notifications_view", args=(obj.id,)),
        )

    def get_form(self, request, obj=None, **kwargs):  # pylint: disable=arguments-differ
        kwargs['widgets'] = {'business_ids': forms.Textarea}
        return super().get_form(request, obj, **kwargs)

    @staticmethod
    def send_notifications_view(request, notification_campaign_id):
        notification_campaign = NotificationCampaign.objects.get(id=notification_campaign_id)
        date_now = tznow().date()
        last_triggered = notification_campaign.last_triggered
        if last_triggered and last_triggered.date() >= date_now:
            messages.warning(request, 'Notifications has been already sent today!')
        else:
            send_notification_campaign.delay(notification_campaign.id)
            notification_campaign.last_triggered = tznow()
            notification_campaign.save(update_fields=['last_triggered'])
            messages.success(request, 'Notifications has been sent')
        return redirect(
            reverse(
                'admin:ecommerce_notificationcampaign_change',
                args=(notification_campaign_id,),
            )
        )

    def get_urls(self):
        urls = super().get_urls()
        urls.insert(
            0,
            url(
                r'^(?P<notification_campaign_id>\d+)/send_notifications/$',
                self.admin_site.admin_view(self.send_notifications_view),
                name='ecommerce_send_notifications_view',
            ),
        )
        return urls


admin.site.register(NotificationCampaign, EcommerceNotificationCampaignAdmin)


class EcommercePermissionAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = EcommercePermission
    list_display = ('resource', 'permission')
    list_filter = ('resource__id', 'resource__business__id', 'resource__staff_email', 'permission')

    def has_change_permission(self, request, obj=None):
        if use_new_permissions(request.user):
            return super().has_change_permission(request, obj)
        # Deny permission to modify object.
        if obj is not None:
            return False
        # Allow to create new objects.
        return True


admin.site.register(EcommercePermission, EcommercePermissionAdmin)
