name: serializers_logic
description: This rule prevents the inclusion of business logic within serializer classes or methods, enforcing the separation of concerns.
message: Business logic detected inside a serializer. Business logic in serializers violates separation of concerns and makes code harder to test and maintain. Refactor this logic into a dedicated service class, model/domain method, or helper function. Call it from the view/service layer, not the serializer. For details and rationale, see ADR-012 “Disallow Business Logic in Serializers” (aka ADR-09/2025 “Prohibition of Business Logic in Serializers”) on Confluence (BFS space).
severity: error
language: python
files:
  - "**/*serializer*.py"
  - "**/serializers.py"
  - "**/serializers/**/*.py"

Instructions:

Analyze Python files that define classes subclassing `rest_framework.serializers.Serializer` or `rest_framework.serializers.ModelSerializer`. Within these classes, focus on methods that typically harbor logic: `validate`, `validate_<field>`, `create`, `update`, `save`, `to_representation`, `run_validation`, `run_validators`.

Allowed in serializers (do NOT flag):
- Simple data-type, length, and format validation (e.g., `EmailField`, regex check for phone format, max_length, min_value/max_value)
- Trivial value normalization (e.g., `.strip()`, `.lower()`, `.title()`, parsing booleans/ints)
- Declarative constraint wiring and error shaping
- Presentation-only formatting in `to_representation` (e.g., date/enum formatting, key renaming) without domain decisions

Flag as business logic if ANY of the following patterns appear inside the methods above:

1) Database access or ORM queries
- Any use of a Django queryset or manager:
  - Regex: `\b[A-Za-z_][A-Za-z0-9_]*\s*\.objects\.(get|filter|exclude|create|update|bulk_create|bulk_update|aggregate|annotate|exists|count|raw)\b`
  - Regex: `\b(select_related|prefetch_related|only|defer|values|values_list)\s*\(`
  - Regex: `\bfrom\s+django\.db\.(models|transaction)\b` or calls to `transaction\.atomic\(`
  - Regex: `\b(Q|F)\s*\(` when imported from `django.db.models`

2) External or cross-boundary calls (I/O, side effects)
- Outbound HTTP or SDKs: `requests\.(get|post|put|delete|patch|head)\(`, `httpx\.(get|post|put|delete|patch|head)\(`
- Third-party service clients: `boto3\.client\(`, `stripe\.`, `twilio\.`, `slack_sdk\.`
- Messaging/async tasks: `\.delay\(`, `apply_async\(`, `producer\.send\(`, `publish\(`, `kafka`, `sns`, `sqs`
- Email or notifications: `send_mail\(`, `EmailMessage\(`, `sentry_sdk\.`
- RPC/DB drivers: `grpc\.`, `redis\.`, raw SQL via `connection\.cursor\(`

3) Role/permission or request-context business rules
- Checking or branching on user roles/permissions/groups inside a serializer:
  - Regex: `self\.context\s*\[\s*["']request["']\s*\]\.(user|auth)\.(is_superuser|is_staff|has_perm|has_perms|has_module_perms|groups|is_authenticated)`
  - Any direct reference to `request.user` or `permissions` within serializer methods

4) Complex conditional/process flow beyond structural validation
- Two or more `if`/`elif`/`else` blocks, nested conditionals, or loops (`for`/`while`) that drive domain outcomes
- Heuristic: method body > ~20 lines AND contains multiple branches → flag

5) Business calculations or pricing/finance logic
- Decimal-heavy math or totals: imports/usage of `Decimal` plus `+ - * /` to compute tax, tip, discount, fees, totals, payouts
- Domain keywords within serializer logic: `tax`, `tip`, `total`, `fee`, `discount`, `price`, `payout`, `gift_card`, `prepayment`, `invoice`

6) Feature flags, settings, or environment-driven business choices
- Regex: `\bsettings\.` (from `django.conf`), `waffle\.` or LaunchDarkly/Unleash clients used to branch behavior

7) Transactions, locks, or advanced ORM semantics
- Regex: `transaction\.atomic\(`, `select_for_update\(`, advisory lock helpers

8) Caching or shared state coordination
- Regex: `cache\.(get|set|add|delete)\(`, `lru_cache\(` used to implement behavior

9) Direct calls into domain/services/managers/repositories from a serializer
- Import or usage of modules/objects with names or paths indicating domain/service layers:
  - Regex: `\b(from|import)\s+.*\.(services|domain|use_cases|usecases|managers|repositories)\b`
  - Calling objects named `.*Service`, `.*Manager`, `.*UseCase`, `.*Repository`

10) Cross-field domain validation
- Implementing policy like “Only superusers can create admins”, “Payment cannot be processed”, etc., inside `validate`/`validate_<field>` rather than a service/domain method
- Heuristic cue words inside serializer errors: `only`, `cannot`, `requires`, `not allowed`, `insufficient`, `permission`, `policy`

False-positive guards (do NOT flag):
- Pure structural validation (type/format/range) and trivial normalization
- Simple mapping/formatting in `to_representation` without decisions


