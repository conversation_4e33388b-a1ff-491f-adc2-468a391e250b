"""Domain service for address validation business rules."""

from webapps.google_places.application.dtos.address_validation import PinLocation
from webapps.google_places.domain.dtos.address_validation import GoogleAddressValidationResponse
from webapps.google_places.domain.dtos.shared import AddressComponents
from webapps.google_places.domain.enums import (
    PinLocationStatus,
    PossibleNextAction,
    Country,
    PRECEDING_STREET_NUMBER_COUNTRIES,
)
from webapps.google_places.domain.ports.address_validation_service import (
    AbstractAddressValidationDomainService,
)


class AddressValidationDomainService(AbstractAddressValidationDomainService):
    """
    Domain service for address validation business rules.

    Determines if an address is valid based on business criteria.
    """

    @staticmethod
    def is_address_valid(
        validate_address_response: GoogleAddressValidationResponse,
        pin_location: PinLocation,
    ) -> bool:
        """
        Determine if an address is valid based on business rules.

        Business rule: An address is considered valid if:
        1. Google's possible next action is ACCEPT or CONFIRM or CONFIRM_ADD_SUBPREMISES
        2. Pin location validation status is OK or WARN

        Args:
            validate_address_response: Google's address validation response
            pin_location: Pin location validation result

        Returns:
            True if address meets all validation criteria, False otherwise
        """
        return validate_address_response.possible_next_action in [
            PossibleNextAction.ACCEPT,
            PossibleNextAction.CONFIRM,
            PossibleNextAction.CONFIRM_ADD_SUBPREMISES,
        ] and pin_location.status in [PinLocationStatus.OK, PinLocationStatus.WARN]

    @staticmethod
    def format_address_lines(
        address_lines: list[str], address_components: AddressComponents, country: Country
    ) -> list[str]:
        """
        Ensures the street number is part of the first address line
        and subpremise is in the second line.

        This method prepends or appends the street number based on country-specific
        conventions if it's not already present. It also adds the subpremise (apartment/unit/suite)
        to the second address line if present. It returns a new list of
        address lines to maintain immutability.

        If address_lines is empty, it will be populated with data from address_components.

        Business rule: Address formatting follows country-specific conventions:
        - Countries in PRECEDING_STREET_NUMBER_COUNTRIES (US, CA, GB, IE, AU, FR, ZA)
          put street number before street name: "123 Main Street"
        - Other countries (PL, ES, etc.) put street number after street name: "Main Street 123"
        - Subpremise (apartment/unit/suite) is always added to the second address line

        Args:
            address_lines: List of address lines to format
            address_components: Address components containing street_number and subpremise
            country: Country enum value to determine formatting convention

        Returns:
            New list of address lines with street number and subpremise properly formatted
        """
        street_number = address_components.street_number
        subpremise = address_components.subpremise
        street = address_components.street

        if address_lines:
            formatted_lines = address_lines.copy()
        else:
            formatted_lines = []
            if street:
                formatted_lines.append(street)
            if subpremise:
                formatted_lines.append(subpremise)

        if street_number and formatted_lines and street_number not in formatted_lines[0]:
            if country in PRECEDING_STREET_NUMBER_COUNTRIES:
                formatted_lines[0] = f"{street_number} {formatted_lines[0]}"
            else:
                formatted_lines[0] = f"{formatted_lines[0]} {street_number}"

        if subpremise and not any(subpremise in line for line in formatted_lines):
            if len(formatted_lines) >= 2:
                formatted_lines[1] = f"{formatted_lines[1]} {subpremise}".strip()
            else:
                formatted_lines.append(subpremise)

        return formatted_lines

    @staticmethod
    def should_skip_pin_location_validation(
        address_lines: list[str], address_components: AddressComponents
    ) -> bool:
        """
        Determine if pin location validation should be skipped based on business rules.

        Business rule: Skip pin location validation if street number is present in
        address_components but missing from address_lines, as the address can be
        completed with the missing street number. This is the case when user
        fills missing street number in the address.

        Args:
            address_lines: List of address lines from the request
            address_components: Address components containing street_number and other data

        Returns:
            True if pin location validation should be skipped, False otherwise
        """
        street_number = address_components.street_number

        if not street_number:
            return False

        if not address_lines:
            return False

        return not any(street_number in line for line in address_lines)
