# Booking service

This directory is prepared to separate code that will be moved to booking service.

## Usefull commands

Run them in `<monoloith_root_dir>/domain_services/booking`.

To run them from `<monoloith_root_dir>` use

```bash
(cd domain_services/booking ; <command>)
```

e.g.

```bash
(cd domain_services/booking ; poetry run pytest)
```

### Tests

- Run tests

```bash
poetry run pytest
```

### Static code analysis

- Run pre-commit manually:

```bash
poetry run pre-commit run --all-files
```

### Package maintenance

- Enter venv shell (will create it and install required packages if missing)

```bash
poetry shell
```

- Add new dependency

```bash
poetry add <package_name>  # to update package type <package_name>@latest
```

- Build and export new requirements to monolith

```bash
./commands/build_requirements.sh
```
