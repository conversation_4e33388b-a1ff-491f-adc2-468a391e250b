import logging

from django.conf import settings
from django.core.validators import MaxValueValidator
from django.db import models
from django.db.models import Q
from django.utils.functional import cached_property

from lib.models import (
    ArchiveModel,
    AutoAddHistoryModel,
)
from lib.tools import tznow
from webapps.billing.enums import BillingLongSubscriptionDuration, ProductType
from webapps.billing.models.base import (
    BillingDiscountBase,
    BillingHistoryModel,
)
from webapps.billing.models.managers import (
    AutoUpdateAndAutoAddHistoryManager,
    ArchiveAndAutoAddHistoryManager,
)
from webapps.navision.ports.tax_rates import TaxMatrix

_logger = logging.getLogger('booksy.billing')


class BillingProductOfferManager(AutoUpdateAndAutoAddHistoryManager):
    def get_default(self):
        return self.filter(default=True).order_by('-id').first()

    @staticmethod
    def get_for_business(business_id: int) -> 'BillingProductOffer':
        _now = tznow()
        business_offer = (
            BillingBusinessOffer.objects.filter(
                business_id=business_id,
                active=True,
            )
            .filter(models.Q(valid_from__isnull=True) | models.Q(valid_from__lte=_now))
            .filter(models.Q(valid_to__isnull=True) | models.Q(valid_to__gt=_now))
            .select_related(
                'offer',
            )
            .order_by(
                '-id',
            )
            .first()
        )
        return business_offer.offer if business_offer else None

    def get_for_business_or_default(
        self,
        business_id: int,
    ) -> 'BillingProductOffer':
        """
        In the first place searches special offer assigned to the business.
        If not found returns the default one.
        """

        return self.get_for_business(business_id=business_id) or self.get_default()


class BillingProductOffer(AutoAddHistoryModel, ArchiveModel):
    """
    Set of products available for purchase.
    Always has to contain SaaS.
    """

    class Meta:
        verbose_name = 'Product offer'
        verbose_name_plural = 'Product offers'

    name = models.CharField(max_length=255)
    default = models.BooleanField(default=False)
    active_for_b2b_referral = models.BooleanField(null=True, blank=True)
    active_for_cs = models.BooleanField(default=False)
    active_for_advanced_user = models.BooleanField(default=True)
    active = models.BooleanField(default=True)
    subscription_duration = models.IntegerField(
        verbose_name='Subscription duration (# of months)',
        help_text='Notice! Change duration only if subscription longer than month needed.',
        choices=BillingLongSubscriptionDuration.choices(),
        null=True,
        blank=True,
        default=None,
    )

    objects = BillingProductOfferManager()

    def __str__(self):
        return f'{self.name} id={self.id}. Default={self.default}'

    def get_standalone_product_ids(self):
        return list(
            self.offer_items.filter(
                active=True, product__product_type__in=list(ProductType.standalone())
            ).values_list('product_id', flat=True)
        )


class BillingProductOfferHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingProductOffer,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BillingProductOfferItem(AutoAddHistoryModel, ArchiveModel, BillingDiscountBase):
    """
    Product sold within BillingProductOffer.
    Each item can have separate discount percent and amount to make it possible
    ex. to give SaaS for free in case of Busy purchase.
    """

    offer = models.ForeignKey(
        'billing.BillingProductOffer',
        on_delete=models.CASCADE,
        related_name='offer_items',
    )
    product = models.ForeignKey(
        'billing.BillingProduct',
        on_delete=models.CASCADE,
    )
    active = models.BooleanField(default=False)

    objects = ArchiveAndAutoAddHistoryManager()

    def __str__(self):
        return f' Package item id={self.id}'

    @cached_property
    def staff_offer_item(self):
        if self.product.staff_add_on_id is not None:
            return BillingProductOfferItem.objects.filter(
                offer_id=self.offer_id,
                product_id=self.product.staff_add_on_id,
            ).first()

    @cached_property
    def sms_offer_item(self):
        if self.product.sms_add_on_id is not None:
            return BillingProductOfferItem.objects.filter(
                offer_id=self.offer_id,
                product_id=self.product.sms_add_on_id,
            ).first()

    def get_gross_summary_with_add_ons(
        self,
        staff_count: int,
        tax_matrix: TaxMatrix,
    ) -> dict:
        # Discounted price for main product
        discounted_price = self.get_product_discounted_price_gross(
            product=self.product,
            tax_matrix=tax_matrix,
        )
        # Initial values
        price_with_add_ons = self.product.get_gross_unit_price(tax_matrix)
        final_price_with_add_ons = discounted_price
        staff_reduced_price = None

        # Staff charges can be applied
        if self.staff_offer_item:
            staff_add_on = self.staff_offer_item.product
            payable_staff_count = staff_add_on.get_payable_staff_count(staff_count=staff_count)

            staff_price = staff_add_on.get_gross_unit_price(tax_matrix) * payable_staff_count
            price_with_add_ons += staff_price

            staff_reduced_price = self.staff_offer_item.get_product_discounted_price_gross(
                product=self.staff_offer_item.product,
                tax_matrix=tax_matrix,
            )
            final_staff_price = staff_reduced_price * payable_staff_count
            final_price_with_add_ons += final_staff_price

        discount_with_add_ons = price_with_add_ons - final_price_with_add_ons

        return {
            'price_with_add_ons': price_with_add_ons,
            'discount_with_add_ons': discount_with_add_ons,
            'final_price_with_add_ons': final_price_with_add_ons,
            'main_discounted_price': discounted_price,
            'staff_discounted_price': staff_reduced_price,
        }

    def get_summary_with_add_ons(
        self,
        staff_count: int,
    ) -> dict:
        """
        Calculates prices & sms allowance for offered product, based on provided
        staff count and required add-ons, as if those add-ons were included in
        main product.
        Assumes that product quantity will equal 1.
        Sms add-on is postpaid that's why only staff add-on is here.
        This method is used ONLY TO DISPLAY prices. We can't use this approach
        during charge, as we want to have main product charges & staff charges
        separated on invoice.
        """
        # Discounted price for main product
        discounted_price = self.get_product_discounted_price(
            product=self.product,
        )
        # Initial values
        price_with_add_ons = self.product.unit_price
        final_price_with_add_ons = discounted_price
        sms_with_add_ons = self.product.sms_amount
        staff_reduced_price = None

        # Staff charges can be applied
        if self.staff_offer_item:
            staff_add_on = self.staff_offer_item.product
            payable_staff_count = staff_add_on.get_payable_staff_count(staff_count=staff_count)

            sms_with_add_ons += payable_staff_count * staff_add_on.sms_amount

            staff_price = staff_add_on.unit_price * payable_staff_count
            price_with_add_ons += staff_price

            staff_reduced_price = self.staff_offer_item.get_product_discounted_price(
                product=self.staff_offer_item.product,
            )
            final_staff_price = staff_reduced_price * payable_staff_count
            final_price_with_add_ons += final_staff_price

        discount_with_add_ons = price_with_add_ons - final_price_with_add_ons

        return {
            'price_with_add_ons': price_with_add_ons,
            'discount_with_add_ons': discount_with_add_ons,
            'final_price_with_add_ons': final_price_with_add_ons,
            'sms_with_add_ons': sms_with_add_ons,
            'main_discounted_price': discounted_price,
            'staff_discounted_price': staff_reduced_price,
        }


class BillingProductOfferItemHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingProductOfferItem,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BillingBusinessOfferManager(AutoUpdateAndAutoAddHistoryManager):
    def active(self):
        now_ = tznow()
        return (
            self.filter(active=True, deleted__isnull=True)
            .filter(Q(valid_from__isnull=True) | Q(valid_from__lte=now_))
            .filter(Q(valid_to__isnull=True) | Q(valid_to__gt=now_))
        )

    def get_for_business(self, business_id: int) -> 'BillingBusinessOffer':
        _now = tznow()
        return (
            self.filter(
                business_id=business_id,
                active=True,
            )
            .filter(models.Q(valid_from__isnull=True) | models.Q(valid_from__lte=_now))
            .filter(models.Q(valid_to__isnull=True) | models.Q(valid_to__gt=_now))
            .order_by(
                '-id',
            )
            .first()
        )


class BillingBusinessOffer(AutoAddHistoryModel, ArchiveModel):
    class Meta:
        verbose_name = 'Product offer for Business'
        verbose_name_plural = 'Product offers for Business'

    offer = models.ForeignKey(
        BillingProductOffer,
        on_delete=models.CASCADE,
        related_name='business_offers',
    )
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.PROTECT,
    )
    active = models.BooleanField(default=True)
    valid_from = models.DateTimeField(null=True, blank=True)
    valid_to = models.DateTimeField(null=True, blank=True)

    objects = BillingBusinessOfferManager()

    def __str__(self):
        return f'Product offer {self.offer.name} for business id={self.business_id}'


class BillingBusinessOfferHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingBusinessOffer,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BillingLongSubMapper(AutoAddHistoryModel, ArchiveModel):
    class Meta:
        verbose_name = 'Long subscription offer mapping'
        verbose_name_plural = 'Long subscription offers mapping'
        unique_together = ('subscription_duration', 'free_staff_qty')

    name = models.CharField(max_length=50, unique=True)
    offer = models.ForeignKey(
        BillingProductOffer,
        on_delete=models.PROTECT,
        related_name='offers',
    )
    subscription_duration = models.IntegerField(
        verbose_name='Subscription duration (# of periods)',
        choices=BillingLongSubscriptionDuration.choices(),
    )
    free_staff_qty = models.PositiveIntegerField(
        validators=[MaxValueValidator(settings.BILLING_STAFF_UNLIMITED_QTY)],
    )

    objects = AutoUpdateAndAutoAddHistoryManager()

    def __str__(self):
        return (
            f'Long subscription offer: {self.name}, '
            f'id={self.id}, '
            f'subscription_duration={self.subscription_duration}, '
            f'staffers_qty={self.free_staff_qty}.'
        )


class BillingLongSubMapperHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingLongSubMapper,
        on_delete=models.CASCADE,
        related_name='history',
    )
