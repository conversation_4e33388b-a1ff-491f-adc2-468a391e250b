#!/usr/bin/env python
import base64
import datetime
import functools
import ipaddress
import json
import logging
import os
import re
import time
import typing as t
import uuid
from calendar import timegm
from enum import Enum
from urllib.parse import urlparse

import jwt
import tornado.web
from billiard.compat import mem_rss
from bo_obs.datadog import (
    set_apm_tag_in_current_span,
    set_apm_tag_in_root_span,
    set_pod_name_in_root_span,
)
from bo_obs.datadog.mixins import DatadogAPMTagTornadoMixin, MANUAL_KEEP_KEY
from django.conf import settings
from django.contrib.sessions.backends.base import UpdateError
from django.db import (
    DatabaseError,
    InterfaceError,
    connection,
)
from django.db.models import Q

# noinspection PyProtectedMember
# pylint: disable=protected-access
from django.http.request import split_domain_port, validate_host
from django.utils.cache import _if_modified_since_passes
from django.utils.http import http_date, parse_http_date_safe
from django.utils.translation import activate, gettext as _
from rest_framework import status

import lib.tools
from lib import jinja_renderer
from lib.cache import lru_booksy_cache
from lib.datadog.mixins import BooksyTeamInRootSpanTornadoMixin
from lib.db import READ_ONLY_DB, close_old_connections, database_cleanup, using_db_for_reads
from lib.dispatch_context import dispatch_context
from lib.elasticsearch.tools import ESJSONEncoder
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature import (
    ServiceTypeAlertGenerationNumberFlag,
    ServiceTypeCampaignFlag,
    ServiceTypeServicesAndCombosBanner,
)
from lib.feature_flag.feature.business import BusinessDetailsReplica
from lib.feature_flag.feature.customer import BooksyAuthTempExtraMetadataFlag
from lib.locks import AbstractLock
from lib.segment_analytics.enums import BranchioTokens, EventType, PseudoIdSourceName
from lib.tools import l_b
from service.base import AbstractBooksyHandler
from service.exceptions import ServiceError
from service.mixins.validation import ValidationMixin
from webapps.booking.adapters import get_internal_booking_source, validate_api_key
from webapps.booking.models import BookingSources
from webapps.business.cache import (
    get_services_count_data,
    has_unassigned_services,
)
from webapps.business.models import Business, Resource
from webapps.business.models.external import AppsFlyer
from webapps.consts import (
    ANDROID,
    FRONTDESK_SOURCES,
    MOBILE_SOURCES,
    WEB,
)
from webapps.kill_switch.models import KillSwitch
from webapps.segment.enums import DeviceTypeName
from webapps.segment.tasks import (
    analytics_view_item_list_gtm_task,
)
from webapps.segment.utils import get_device_type_name_from_api_key
from webapps.session.booksy_auth import BooksyAuthServiceException
from webapps.session.ports import SessionMediator
from webapps.user.const import Gender
from webapps.user.models import (
    User,
    UserProfile,
)
from webapps.user.tasks.sync import sync_user_booksy_auth

saiyan_log = logging.getLogger('booksy.saiyans_tracking')
_logger = logging.getLogger('booksy.request_time')
user_log = logging.getLogger('booksy.user_log')
_req_debug = logging.getLogger('booksy.request_debug')
_sql_debug = logging.getLogger('booksy.sql_debug')
throttle_logger = logging.getLogger('booksy.throttle')
request_log = logging.getLogger('request')
logger_session = logging.getLogger('booksy.session')


# <editor-fold desc="json_request">
def _load_json(data):
    try:
        return json.loads(l_b(data) or '{}')
    except (ValueError, TypeError) as e:
        raise ServiceError(  # pylint: disable=raise-missing-from
            400, [{'code': 'json_malformed', 'description': str(e)}]
        )


def json_request(method):
    """
    Decorate methods which get in request data json objects.
    They would be load as self.data, or return error.
    """

    @functools.wraps(method)
    def wrapper(self, *args, **kwargs):
        self.data = _load_json(self.request.body)
        return method(self, *args, **kwargs)

    wrapper._swagger_json_request = True  # pylint: disable=protected-access
    return wrapper


# </editor-fold>


def session(
    login_required=False,
    optional_login=False,
    api_key_required=True,
    source_name=None,
    strict_optional_login=True,
):
    """
    Decorator for RequestHandler adding various other functionalities.

    :param login_required: Whether user is required to be logged in
        (i.e. pass a valid X-Access-Token in headers).
    :param optional_login: Login is not required, but if access token
        is passed, a session will be loaded
    :param api_key_required: Whether X-Api-Key is required in headers.
    :param source_name: Require specific X-Api-Key (BookingSources) name.
    :param strict_optional_login: Return 403 if access token is invalid
    :return: method wrapper
    """
    # TODO delete it as soon as it possible
    assert not (login_required and optional_login)

    def decor(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            self.user_agent = self.request.headers.get('User-Agent')
            self.fingerprint = self.request.headers.get('X-FINGERPRINT')
            self.user_pseudo_id = self.request.headers.get('X-User-Pseudo-ID')
            self.api_key = self.request.headers.get('X-API-KEY')

            # <editor-fold desc="api-key validation">
            invalid_api_key, self.booking_source = validate_api_key(
                self.api_key,
                self.request.path,
                api_key_required,
                source_name,
            )

            lib.tools.sasrt(
                not invalid_api_key,
                403,
                [
                    {
                        'code': 'invalid',
                        'field': 'api_key',
                        'description': _('Invalid api key.'),
                    }
                ],
            )
            self.internal_booking_source = self.booking_source == get_internal_booking_source()
            # </editor-fold>

            if login_required or optional_login:
                self.access_token = self.request.headers.get('X-ACCESS-TOKEN')
                # for more info see RequestHandler
                self.check_access_token()

                if self.user:
                    self.inject_user_profile()

                error = {
                    'code': 'unauthorized',
                    'field': 'access_token',
                    'description': _('Unauthorized access attempt.'),
                }
                if not self.access_token:
                    error = {
                        'code': 'required',
                        'field': 'auth_header_token',
                        'description': _('Authorization token is required for this endpoint.'),
                    }
                    if not optional_login:
                        log_request_without_access_token(
                            request_headers=dict(self.request.headers),
                            request_url=self.request.path,
                            request_method=self.request.method,
                        )
                lib.tools.sasrt(
                    # ok if we successfully got user from session
                    self.user or
                    # ok if login was optional and no token was sent
                    # if token was sent and was invalid, we return 403 error
                    # if strict is True, else we ignore id and return 200 as usual
                    (optional_login and (not self.access_token or strict_optional_login)),
                    403,
                    [error],
                )
            else:
                # deprecated - those attrs should be removed in the future
                self.session = None
                self.access_level = None
                self.user = None
            self.request.user = self.user
            self.request.user_ip = self.get_user_request_ip()
            self.request.booking_source = self.booking_source
            self.check_throttles(self.request)
            if self.user and self.session and self.session.get('superuser'):
                saiyan_log.info(
                    '%r as %r -> %s %r',
                    self.session.get('superuser'),
                    self.user.email,
                    self.request.method,
                    self.request.full_url(),
                )

            self.log_metadata(user_id=(self.user.id if getattr(self, 'user', None) else None))

            # execute the wrapped handler method
            ret = method(self, *args, **kwargs)

            # post request session save
            # do not bump session with from new login system
            if (
                self.user
                and not getattr(self.session, 'BUMP_DISABLED', False)
                and self.request.method not in getattr(self, 'SESSION_BUMP_DISABLED_FOR', [])
            ):
                if (
                    self.session.modified
                    or settings.SESSION_SAVE_EVERY_REQUEST
                    or self.session.bump_session()
                ):
                    if not self.session.exists(
                        self.session._session_key  # pylint: disable=protected-access
                    ):
                        return ret

                    self.session['session_expire_date'] = self.session.get_expiry_date().strftime(
                        settings.ES_DATETIME_FORMAT
                    )
                    try:
                        self.session.save()
                    except UpdateError:
                        request_log.warning("Session wrapper raised an exception", exc_info=True)
            return ret

        # pylint: disable=protected-access
        wrapper._swagger_login_required = login_required
        wrapper._swagger_optional_login = optional_login
        wrapper._swagger_api_key_required = api_key_required
        # pylint: enable=protected-access
        return wrapper

    return decor


def get_language_from_header(langs, supported_langs=None, default_lang=None):
    if supported_langs is None:
        supported_langs = dict(settings.LANGUAGES)

    if default_lang is None:
        if settings.LANGUAGE_CODE in supported_langs:
            default_lang = settings.LANGUAGE_CODE
        else:
            default_lang = settings.LANGUAGE_CODE[:2]

    languages = re.findall(r'(\w{2,}[-_]?\w*[-_]?\w*[-_]?\w*)', langs)

    for language in languages:
        language = re.sub(r"\_|\W+", "-", language).lower()
        if language.startswith('es') or language in supported_langs:
            # es will be treated as the language for other Latin American countries es-mx
            return 'es-mx' if language == 'es' else language
        language_entity = language.split("-")
        if len(language_entity) > 1:
            if language_entity[0] == 'en' and language_entity[1] == 'ie':
                candidate = 'en-gb'
            else:
                candidate = f'{language_entity[0]}-{language_entity[1]}'
            if candidate in supported_langs:
                return candidate
        if language_entity[0] in supported_langs:
            return language_entity[0]
    return default_lang


def get_basic_netlock(netlock):
    return netlock.replace(f'{settings.API_COUNTRY}.', '', 1)


def require_basic_auth(credentials=None):
    # http://kevinsayscode.tumblr.com/post/7362319243
    # /easy-basic-http-authentication-with-tornado

    def decor(handler_class):
        def wrap_execute(handler_execute):
            def require_basic_auth(handler, kwargs):  # pylint: disable=redefined-outer-name
                auth_header = handler.request.headers.get('Authorization')
                if auth_header and auth_header.startswith('Basic '):
                    auth_decoded = base64.b64decode(auth_header[6:]).decode()
                    username, password = auth_decoded.split(':', 2)
                    # kwargs['basicauth_user'], kwargs['basicauth_pass'] = \
                    #     username, password
                    #  TODO: remove, nobody is using this and extras passed to POST cause exception

                    if credentials is None or (username, password) == (
                        credentials[0],
                        credentials[1],
                    ):
                        return True

                handler.set_status(status.HTTP_401_UNAUTHORIZED)
                handler.set_header('WWW-Authenticate', 'Basic realm=Restricted')
                handler._transforms = []  # pylint: disable=protected-access
                handler.finish()
                return False

            def _execute(self, transforms, *args, **kwargs):
                if not require_basic_auth(self, kwargs):
                    return False
                return handler_execute(self, transforms, *args, **kwargs)

            return _execute

        # pylint: disable=protected-access
        handler_class._execute = wrap_execute(handler_class._execute)
        return handler_class

    return decor


def require_token_auth(token=None):
    def decor(handler_class):
        def wrap_execute(handler_execute):
            def require_token_auth(handler, **kwargs):  # pylint: disable=redefined-outer-name
                auth_header = handler.request.headers.get('Authorization')
                if auth_header and auth_header.startswith('Token ') and auth_header[6:] == token:
                    return True

                handler.set_status(status.HTTP_401_UNAUTHORIZED)
                handler.set_header('WWW-Authenticate', 'Token realm=Restricted')
                handler._transforms = []  # pylint: disable=protected-access
                handler.finish()
                return False

            def _execute(self, transforms, *args, **kwargs):
                if not require_token_auth(self, **kwargs):
                    return False
                return handler_execute(self, transforms, *args, **kwargs)

            return _execute

        # pylint: disable=protected-access
        handler_class._execute = wrap_execute(handler_class._execute)
        return handler_class

    return decor


class HTTPErrorWithCode(tornado.web.HTTPError):
    """An HTTPError that allows us to store our internal error codes."""

    def __init__(self, *args, **kwargs):
        if 'reason' in kwargs and isinstance(kwargs['reason'], dict):
            # extract code and leave textual reason
            # tornado disallows duck typing
            self.code = kwargs['reason']['code']
            kwargs['reason'] = kwargs['reason']['description']
        # old style class -- cannot use super() :(
        tornado.web.HTTPError.__init__(self, *args, **kwargs)


class BaseBooksyRequestHandler(
    DatadogAPMTagTornadoMixin, BooksyTeamInRootSpanTornadoMixin, AbstractBooksyHandler
):
    """Base Booksy request handler for all requests to API"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._request_time = None
        self._request_uuid = None

    def initialize(self, **kwargs):
        """
        Hook for subclass initialization. Called for each request.
        Handle some global stuff used in every request.
        """
        super().initialize()
        close_old_connections()

    @property
    def forwarded_ip(self):
        forwarded = (
            self.request.headers.get('X-Original-Forwarded-For', '')  # GCP Cloud Load Balancer
            .split(',')[0]
            .strip()
        )
        if not forwarded:
            forwarded = (
                self.request.headers.get('X-Forwarded-For', '')  # Deployer nginx
                .split(',')[0]
                .strip()
            )
        return forwarded or self.request.remote_ip

    def prepare(self):
        """Prepare request time and drop prefix from request path arguments."""

        database_cleanup()

        if settings.DEBUG_SQL:
            _sql_debug.debug('Request START: [%s] %s', self.request.method, self.request.uri)

        # drop matched prefix so it won't be propagated to handlers
        if self.path_args:
            self.path_args = self.path_args[1:]
        else:
            self.path_kwargs.pop('prefix')

        # super(BaseBooksyRequestHandler, self).prepare()
        # Overwrite abstract method do not call super()
        self._request_time = time.time()
        self._request_uuid = uuid.uuid4().hex

        # sanity check for ALLOWED_HOSTS
        domain, _port = split_domain_port(self.request.host)
        lib.tools.sasrt(
            validate_host(domain, settings.ALLOWED_HOSTS),
            status.HTTP_400_BAD_REQUEST,
            {
                'code': 'invalid_host',
                'description': 'Invalid HTTP_HOST header',
            },
        )

    # pylint: disable=duplicate-code
    def set_default_headers(self):
        super().set_default_headers()
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # CORS
        api_url = urlparse(settings.API_URL)
        api_netloc = get_basic_netlock(api_url.netloc)
        origin = f'{api_url.scheme}://{api_netloc}'
        request_origin = self.request.headers.get('Origin', origin)
        allowed_origin = request_origin in settings.CORS_ALLOWED_ORIGINS or any(
            re.match(pattern, request_origin) for pattern in settings.CORS_ALLOWED_ORIGIN_REGEXES
        )
        if settings.CORS__ALLOW_ALL_ORIGINS or allowed_origin:
            origin = request_origin
        self.set_header('Access-Control-Allow-Origin', origin)
        self.set_header(
            'Access-Control-Allow-Headers',
            ', '.join(settings.CORS_ALLOW_HEADERS),
        )
        # Access-Control-Expose-Headers
        # See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Expose
        # -Headers
        self.set_header(
            'Access-Control-Expose-Headers',
            ', '.join(settings.CORS_EXPOSE_HEADERS),
        )
        self.set_header(
            'Access-Control-Allow-Methods',
            ', '.join(settings.CORS_ALLOW_METHODS),
        )
        self.set_header('Access-Control-Max-Age', '86400')
        self.set_header('Vary', 'Origin')

    def options(self, *args, **kwargs):
        self.set_status(status.HTTP_204_NO_CONTENT)
        self.finish()

    def on_finish(self):
        super().on_finish()
        if settings.DEBUG_SQL:
            request_time = None
            if getattr(self, '_request_time', None):
                request_time = int(1000.0 * (time.time() - self._request_time))
            _sql_debug.debug(
                '(%s) Request FINISH: [%s] %s',
                request_time,
                self.request.method,
                self.request.uri,
            )
        database_cleanup()

    def return_error(self, errors, code=status.HTTP_400_BAD_REQUEST):
        """flush list of dict errors"""
        self.clear()
        self.set_status(code)
        self.set_header("Content-Type", "application/json")
        self.finish(json.dumps({'errors': errors}))

    @property
    def client_error_codes(self) -> dict:
        """Return supported error client error codes by Booksy"""
        return {
            status.HTTP_400_BAD_REQUEST: (_('Bad Request'), 'bad_request'),
            status.HTTP_401_UNAUTHORIZED: (_('Unauthorized'), 'unathorized'),
            status.HTTP_402_PAYMENT_REQUIRED: (
                _('Payment Required'),
                'payment_required',
            ),
            status.HTTP_403_FORBIDDEN: (
                _('Access Denied'),
                'access_denied',
            ),
            status.HTTP_404_NOT_FOUND: (
                _('Requested object not found'),
                'not_found',
            ),
            status.HTTP_405_METHOD_NOT_ALLOWED: (
                _('Method not allowed'),
                'not_allowed',
            ),
            status.HTTP_406_NOT_ACCEPTABLE: (
                _('Method no acceptable'),
                'not_acceptable',
            ),
            status.HTTP_407_PROXY_AUTHENTICATION_REQUIRED: (
                _('Proxy Authentication Required'),
                'proxy_authentication_required',
            ),
            status.HTTP_408_REQUEST_TIMEOUT: (
                _('Request Timeout'),
                'request_timeout',
            ),
            status.HTTP_409_CONFLICT: (
                _('Conflict'),
                'conflict',
            ),
            status.HTTP_410_GONE: (
                _('Business is not publicly available.'),
                'not_public',
            ),
            status.HTTP_418_IM_A_TEAPOT: (
                _('I am a teapot'),
                'i_am_teapot',
            ),
            status.HTTP_426_UPGRADE_REQUIRED: (
                _('Upgrade Required'),
                'upgrade_required',
            ),
            status.HTTP_422_UNPROCESSABLE_ENTITY: (
                _('Unprocessable entity'),
                'unprocessable_entity',
            ),
            status.HTTP_429_TOO_MANY_REQUESTS: (
                _('Too many requests'),
                'too_many_requests',
            ),
        }

    # pylint: disable=too-many-return-statements
    def write_error(self, status_code, **kwargs):
        err = kwargs.get('exc_info')
        if err:
            if issubclass(err[0], ServiceError):
                self.set_status(err[1].code)
                if self.no_body_for_status(err[1].code):
                    self.finish({})
                    return
                self.return_error(err[1].errors, code=err[1].code)
                return
            if issubclass(err[0], DatabaseError):
                # rollback any hanging transactions
                try:
                    connection._rollback()  # pylint: disable=protected-access
                except InterfaceError:
                    pass

        # return error message to the user
        if status_code == status.HTTP_304_NOT_MODIFIED:
            self.set_status(status_code)
            self.finish()
        elif status_code in self.client_error_codes:
            reason = None
            code = None
            if err:
                error = err[1]
                reason = getattr(error, 'reason', None)
                code = getattr(error, 'code', None)
            # if empty fallback to default values
            reason = reason or self.client_error_codes[status_code][0]
            code = code or self.client_error_codes[status_code][1]
            self.return_error(
                [
                    {
                        'type': 'invalid',
                        'code': code,
                        'description': reason,
                    }
                ],
                code=status_code,
            )
            return
        elif status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
            self.return_error(
                [
                    {
                        'type': 'server',
                        'code': 'server',
                        'description': _('Server error'),
                    }
                ],
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            return

        # unknown error! return 500 to let the world know we have a bug
        if not self._finished:
            self.return_error(
                [
                    {
                        'type': 'server',
                        'code': 'server',
                        'description': _('Server error'),
                    }
                ],
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            return

    @staticmethod
    def no_body_for_status(code):
        # Consult tornado/simple_httpclient.py and RequestHandler.finish()
        return status.HTTP_100_CONTINUE <= code < status.HTTP_200_OK or code in [
            status.HTTP_204_NO_CONTENT,
            status.HTTP_304_NOT_MODIFIED,
        ]

    def quick_assert(self, condition, code_type_field, description, extra=None):
        """
        lib.tools.sasrt(
            resource.staff_user is not None,
            400,
            [{
                'code': 'required',
                'type': 'validation',
                'field': 'resource.staff_user',
                'description': _('Staff already has an account'),
            }]
        )
        ==>
        self.quick_assert(
            resource.staff_user is not None,
            ('required', 'validation', 'resource.staff_user'),
            _('Staff already has an account'),
        )
        """

        lib.tools.quick_assert(condition, code_type_field, description, extra)

    def quick_error(self, code_type_field, description, extra=None):
        lib.tools.quick_error(code_type_field, description, extra)

    @staticmethod
    def quick_assert_404(condition, reason=None):
        if not condition:
            raise HTTPErrorWithCode(status.HTTP_404_NOT_FOUND, reason=reason)

    def _debug(self, message, *args, **kwargs):
        """Log debug message with unique request identifier."""
        request_time = None
        if getattr(self, '_request_time', None):
            request_time = int(1000.0 * (time.time() - self._request_time))
        message = (
            f'[{type(self).__name__} {self.request.method} {self._request_uuid}] '
            f'({request_time:.3f}) {message}'
        )
        _logger = kwargs.pop('_logger', _req_debug)
        _exception = kwargs.pop('_exception', None)
        if _exception:
            _logger.exception(message, *args, **kwargs)
        else:
            _logger.debug(message, *args, **kwargs)

    def deprecation_error(self):
        self.return_error(
            [
                {
                    'type': 'deprecation',
                    'code': 'deprecation',
                    'description': _('Please update your application to continue.'),
                },
            ],
            code=status.HTTP_426_UPGRADE_REQUIRED,
        )

    def set_auth_origin_header(self):
        """Add info about authentication source."""
        auth_origin = ''
        if session_ := getattr(self, 'session', None):
            auth_origin = session_.get('origin') or ''
        self.set_header('X-SessionAuthOrigin', str(auth_origin))


class AnalyticsTokensMixin:
    AUTH_DICT_TOKENS = {
        'firebase_auth': PseudoIdSourceName.values(),
        'branchio_auth': BranchioTokens.values(),
    }
    _analytics_tokens: dict = None

    def _parse_analytics_legacy_header(self):
        if pseudo_id := self.request.headers.get('X-User-Pseudo-ID'):
            token_name = PseudoIdSourceName.APP_INSTANCE_ID
            if '.' in pseudo_id:
                token_name = PseudoIdSourceName.CLIENT_ID
            self._analytics_tokens[token_name] = pseudo_id

    def _parse_analytics_header(self):
        if self._analytics_tokens is not None:
            return

        self._analytics_tokens = {}

        if user_agent := self.request.headers.get('User-Agent', ''):
            self._analytics_tokens[BranchioTokens.USER_AGENT] = user_agent
        if header := self.request.headers.get('X-Analytics-Tokens', ''):
            analytics_token_pattern = r'([^;]+);([^;]+)'
            matches = re.findall(analytics_token_pattern, header)
            for name, value in matches:
                self._analytics_tokens[name] = value
        else:
            self._parse_analytics_legacy_header()

    @functools.cached_property
    def analytics_auth_dict(self):
        auth_dict = {}
        self._parse_analytics_header()

        for auth_dict_name, auth_dict_tokens in self.AUTH_DICT_TOKENS.items():
            tokens = {}
            for token_name in auth_dict_tokens:
                if token_name in self._analytics_tokens:
                    tokens[token_name] = self._analytics_tokens[token_name]

            if tokens:
                auth_dict[auth_dict_name] = tokens

                if auth_dict_name == 'firebase_auth':
                    auth_dict[auth_dict_name]['booking_source_id'] = self.booking_source.id
        return auth_dict

    @property
    def firebase_auth_dict(self):
        return self.analytics_auth_dict.get('firebase_auth', None)

    def extend_data_with_firebase_auth(self, data):
        if not data:
            data = {}
        if self.firebase_auth_dict:
            for token_name in self.AUTH_DICT_TOKENS['firebase_auth']:
                if token_value := self.firebase_auth_dict.get(token_name):
                    data[token_name] = token_value
        return data


class AppsFlyerMixin:
    def update_appsflyer(self, business_id):
        af_user_id = self.request.headers.get('X-Appsflyer-User-ID')
        if not af_user_id:
            return

        device_type = get_device_type_name_from_api_key(self.booking_source.api_key)
        if device_type == DeviceTypeName.DESKTOP:
            if appsflyer := AppsFlyer.get_for_business(business_id, self.booking_source.id):
                if appsflyer.appsflyer_user_id == af_user_id:
                    return

            AppsFlyer.objects.update_or_create(
                business_id=business_id,
                booking_source_id=self.booking_source.id,
                defaults={
                    'appsflyer_user_id': af_user_id,
                },
            )


class ServiceTypeMarketingMixin:
    def _is_user_manager_or_owner(self):
        return self.access_level in (
            Resource.STAFF_ACCESS_LEVEL_OWNER,
            Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )


class ServiceTypeMarketingMyBusinessHandlerMixin(ServiceTypeMarketingMixin):
    def get_service_type_marketing_data(self, business_id):
        common_conditions_value = (
            ServiceTypeCampaignFlag()
            and self._is_user_manager_or_owner()
            and has_unassigned_services(business_id)
        )

        return {
            'show_alert': common_conditions_value and self._show_alert(business_id),
            'show_banner': common_conditions_value,
            'show_intro': common_conditions_value and self._show_intro(),
        }

    def _show_alert(self, business_id):
        business_user_internal_data = self.user.business_user_internal_data.filter(
            business=business_id,
        ).first()

        last_seen_service_type_alert_generation = (
            business_user_internal_data.last_seen_service_type_alert_generation
            if business_user_internal_data
            else 0
        )

        return ServiceTypeAlertGenerationNumberFlag() > last_seen_service_type_alert_generation

    def _show_intro(self):
        if (
            self.user
            and hasattr(self.user, 'internal_data')
            and self.user.internal_data.show_service_type_intro_screen is not None
        ):
            return self.user.internal_data.show_service_type_intro_screen
        return True


class ServiceTypeMarketingServiceCategoriesHandlerMixin(ServiceTypeMarketingMixin):
    def get_service_type_marketing_data(self, business_id):
        assigned_services_count, total_services_count = (
            get_services_count_data(business_id)
            if ServiceTypeServicesAndCombosBanner()
            else (None, None)
        )

        return {
            'show_banner': (
                ServiceTypeServicesAndCombosBanner()
                and self._is_user_manager_or_owner()
                and has_unassigned_services(business_id)
            ),
            'assigned_services_count': assigned_services_count,
            'total_services_count': total_services_count,
        }


class UserRequestIPMixin:
    def get_user_request_ip(self):
        headers = self.request.headers
        request_ip = (
            headers.get('X-Real-IP') or headers.get('X-Forwarded-For') or self.request.remote_ip
        )
        if settings.THROTTLING_DEBUG_MODE:
            throttle_logger.warning(
                'get_user_request_ip',
                extra={
                    'real': headers.get('X-Real-IP'),
                    'Forwarded': headers.get('X-Forwarded-For'),
                    'remote_ip': self.request.remote_ip,
                },
            )
        return ipaddress.ip_address(self.parse_ip_address(request_ip))

    @staticmethod
    def parse_ip_address(ip_address):
        return ip_address.split(',')[0]


class InsertUserProfile:
    @staticmethod
    @lru_booksy_cache(
        timeout=3 * 60 * 60,
        skip_in_pytest=True,
    )
    def get_user_profile(user_id, profile_type):
        profile = UserProfile.objects.get(
            user_id=user_id,
            profile_type=profile_type,
        )
        return profile

    def inject_user_profile(self):
        try:
            self.user.profile = self.get_user_profile(
                self.user.id,
                self.profile_type,
            )
        except UserProfile.DoesNotExist:
            self.user.profile = self.user.profiles.create(
                profile_type=self.profile_type,
                language=self.language,
                source=self.booking_source,
            )


class AuthenticateUsingAccessTokenMixin(InsertUserProfile):
    @property
    def new_login_turn_on(self):
        return KillSwitch.exist_and_alive(KillSwitch.System.BOOKSY_AUTH)

    def _prepare_get_arguments(self, list_values=None):
        """
        Return a dictionary of GET arguments but coerced to a single
        value instead of a list.

        It is more handy to use it this way since most arguments
        are not multi-valued.
        :return:
        """

        ret = {}
        if list_values is None:
            list_values = []
        for key, value in list(self.request.arguments.items()):
            # force unicode
            value = [str(e, 'utf-8', 'ignore') for e in value]

            # coerce value to None, first element or list
            if len(value) == 0:
                ret[key] = None
            elif len(value) > 1 or key in list_values:
                ret[key] = value
            else:
                ret[key] = value[0]

        return ret

    def _get_user_from_session(self):
        """Common method to get user from BooksyAuthSessionStore or SessionStore"""
        if self.access_token and self.session.exists(self.access_token):
            self.user = self.session.get_user()
        else:
            self.user = None

    def check_access_token(self):
        """Check if session with given access_token exists;
        If exists fill assign corresponding values to
            self.user - User object,
            self.session - AttrDict in new login session_objects;
                instance of settings.SESSION_ENGINE in old system

        :return: None
        """
        sync_session_with_auth = False
        if self.new_login_turn_on:
            self.session = SessionMediator(
                session_key=self.access_token,
                new_login_turned_on=True,
            ).get_session()

            if BooksyAuthTempExtraMetadataFlag():
                set_apm_tag_in_current_span('debug_request_url', self.request.full_url())
            try:
                self._get_user_from_session()
            except BooksyAuthServiceException:
                pass
                # probably Booksy Auth is not working
                # fallback to old method
            else:
                if self.user:
                    return
                sync_session_with_auth = True  # Booksy Auth is working but session is not sync yet

        self.session = SessionMediator(
            session_key=self.access_token,
            new_login_turned_on=False,
        ).get_session()
        self._get_user_from_session()
        if sync_session_with_auth and self.user:
            sync_user_booksy_auth([self.user.id], use_master=True)


# noinspection PyUnresolvedReferences
class ConditionalRequestMixin:
    """Used to perform conditional HTTP requests as described here:
    *) https://docs.djangoproject.com/en/3.2/topics/conditional-view-processing/
    *) https://developer.mozilla.org/en-US/docs/Web/HTTP/Conditional_requests
    Currently supports only "GET If-Modified-Since" scenario.
    """

    @property
    def if_modified_since(self) -> t.Optional[int]:
        """See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Modified-Since
        :return:  Returns Unix timestamp from GMT
        """
        if_modified_since = self.request.headers.get('If-Modified-Since')
        if_modified_since = if_modified_since and parse_http_date_safe(if_modified_since)
        return if_modified_since

    def should_return_304_not_modified(self, last_modified: datetime.datetime) -> bool:
        last_modified = last_modified and timegm(last_modified.utctimetuple())
        setattr(self, 'last_modified', last_modified)
        return (
            self.request.method in ('GET', 'HEAD')
            and self.if_modified_since
            and not _if_modified_since_passes(last_modified, self.if_modified_since)
        )

    def check_if_not_modified(self, last_modified: datetime.datetime) -> None:
        if self.should_return_304_not_modified(last_modified):
            raise tornado.web.HTTPError(status.HTTP_304_NOT_MODIFIED)

    def finish(self, chunk=None):
        self.set_last_modified_header()
        return super().finish(chunk)

    # noinspection PyMethodMayBeStatic
    def compute_etag(self):
        """We disable Tornado's built-in Etag support when our custom Last-Modified flow is
        enabled."""
        return None

    def set_last_modified_header(self) -> None:
        if (
            self.request.method in ('GET', 'HEAD')
            and hasattr(self, 'last_modified')
            and self.last_modified
        ):
            self.set_header('Last-Modified', http_date(self.last_modified))
            self.set_header('Cache-Control', 'private, must-revalidate')


class DatadogSpanMixin:
    """
    business_id_in_root_span: Used when business_id is needed in the root span of a Datadog trace
    user_id_in_root_span: Used when user_id is needed in the root span of a Datadog trace
    """

    business_id_in_root_span = True
    user_id_in_root_span = True

    def _add_user_id_to_root_span(self):
        user_id = None
        try:
            user_id = self.user.id
        except AttributeError:
            pass
        if user_id:
            set_apm_tag_in_root_span('user_id', user_id)

    def _set_business_id_in_root_span_tornado(self):
        business_id = None
        try:
            if hasattr(self, 'business_id'):
                business_id = self.business_id
            elif self.business:
                business_id = self.business.id
        except AttributeError:
            pass

        if not business_id:
            if self.path_kwargs:
                business_id = self.path_kwargs.get('business_id', None)
            elif self.path_args:
                pattern = r'(businesses?/)(\d+)'
                if result := re.search(pattern, self.request.path):
                    business_id = result.group(2)

        if business_id:
            set_apm_tag_in_root_span('business_id', str(business_id))

    def on_finish(self):
        set_pod_name_in_root_span()
        if self.business_id_in_root_span:
            self._set_business_id_in_root_span_tornado()
        if self.user_id_in_root_span:
            self._add_user_id_to_root_span()
        if mem_rss() > 1_000_000:
            set_apm_tag_in_current_span(MANUAL_KEEP_KEY)
            set_apm_tag_in_current_span('request_high_mem', 'True')
        super().on_finish()


class RequestHandler(
    DatadogSpanMixin,
    UserRequestIPMixin,
    ValidationMixin,
    AuthenticateUsingAccessTokenMixin,
    BaseBooksyRequestHandler,
):
    """Base handler class for almost all cases.

    It checks country_code header and fills language and profile info.

    """

    def initialize(self, **kwargs):
        super().initialize(**kwargs)
        # It is a good practice to add here all the custom variables
        # NOTE: some additional attributes of this class are injected
        #       with the session() decorator

        # Language of current request.
        self.language = None

        # This variable tells session() object what API keys it should
        # validate against -- customer or business. Basically it
        # specifies what type of view (business, customer or internal)
        # the current handler is.
        self.profile_type = None

        # Booking source is connected with self.profile_type -- it is
        # the object with UUID same as in requests' X-Api-Key header.
        # self.profile_type is validated against the type of
        # self.booking_source so that Business requests don't get
        # mixed up in the client with Customer handlers, for example.
        self.booking_source = None

        # Jinja rendering engine.
        self.jinja_renderer = None

        # autofill profile_type based on automagic kwargs in run.py
        # pylint: disable=redefined-outer-name, reimported
        from webapps.user.models import (
            UserProfile,
        )

        self.profile_type = kwargs.get(
            "profile_type",
            UserProfile.Type.CUSTOMER,
        )

    def prepare(self):
        super().prepare()
        self.prepare_language()

        self.request._request_uuid = self._request_uuid  # pylint: disable=protected-access
        dispatch_context.activate_in_tornado(handler=self)

    def _execute(self, transforms, *args, **kwargs):
        allocated_memory_at_start = mem_rss()
        try:
            return super()._execute(transforms, *args, **kwargs)
        finally:
            allocated_memory_at_end = mem_rss()
            memory_increased = allocated_memory_at_end - allocated_memory_at_start
            if memory_increased >= 300_000:
                _req_debug.warning(
                    "memory increased of %sKB to level %sKB for %s %s %s",
                    memory_increased,
                    allocated_memory_at_end,
                    self,
                    self.request.method,
                    self.request.uri,
                )
            dispatch_context.deactivate()

    def prepare_language(self):
        # 'pl,en-US;q=0.8,en;q=0.6' --> ['pl', 'en-US;q=0.8', 'en;q=0.6']
        lang = self.request.headers.get('Accept-Language', settings.LANGUAGE_CODE)
        self.language = get_language_from_header(lang)
        activate(self.language)

    def get_template_namespace(self, *args, **kwargs):
        """
        HACK: tornado inserts its own translation system, injecting
        _ into Jinja's render function. We remove this stuff.

        See

        http://tornado.readthedocs.org/en/latest/locale.html?highlight=locale#module-tornado.locale

        for more info.

        :return:
        """

        namespace = super().get_template_namespace(*args, **kwargs)

        if '_' in namespace:
            del namespace['_']

        return namespace

    def render_string(self, *args, **kwargs):
        # HACK: turn off tornado's loaders caching
        tornado.web.RequestHandler._template_loaders = {}  # pylint: disable=protected-access

        return super().render_string(*args, **kwargs)

    def template_paths(self):
        """Get list of template directories, based on current
        booking_source."""
        return self.jinja_renderer.get_template_paths()

    def create_template_loader(self, *args):
        self.jinja_renderer = jinja_renderer.JinjaRenderer(
            booking_source=self.booking_source, language=self.language
        )
        return self.jinja_renderer.renderer

    def finish_with_json(self, status_code, ret_dict, status_message=None, tag_span=False):
        self.set_auth_origin_header()
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        self.set_status(status_code, status_message)
        if tag_span:
            self.add_span_tag('reason', ret_dict)
        self.finish(json.dumps(ret_dict, cls=ESJSONEncoder))

    def get_access_level(self, user):
        """Return staff_access_level of last created resource

        :param user: User instance
        :return: enum from Resource.STAFF_ACCESS_LEVELS_ALL can be None
        """
        # TODO cache
        # pylint: disable=redefined-outer-name, reimported
        from webapps.business.models import Resource, Business
        from webapps.user.models import UserProfile

        # pylint: disable=consider-using-f-string
        if self.profile_type == UserProfile.Type.CUSTOMER:
            # error someone call access level for customer app
            user_log.error(
                '[ACCESS_LEVEL] \'get_access_level\' '
                'call for not business endpoint'
                '{}'.format(self.__class__)
            )
            return None
        if user is None:
            user_log.error(
                '[ACCESS_LEVEL] \'get_access_level\' '
                'call for with None user'
                '{}'.format(self.__class__)
            )
            return None
        # pylint: enable=consider-using-f-string
        owned_business = user.businesses.filter(
            active=True,
            deleted__isnull=True,
        ).exclude(
            status__in=[
                Business.Status.VENUE,
                Business.Status.B_LISTING,
            ],
        )
        if owned_business.exists():
            return Resource.STAFF_ACCESS_LEVEL_OWNER

        access_level = (
            user.staffers.filter(active=True, deleted__isnull=True)
            .order_by(
                # set defined order
                '-created'
            )
            .values_list(
                'staff_access_level',
                flat=True,
            )
            .first()
        )

        # this may be counterintuitive why
        # in the end we return Resource.STAFF_ACCESS_LEVEL_OWNER;
        # but until onboarding is not changed
        # you can't create get access_level of resource
        # because resource doesn't exists
        # since business doesn't exists
        return access_level or Resource.STAFF_ACCESS_LEVEL_OWNER

    def get_arguments(self, name, strip=True):
        value = super().get_arguments(name, strip=strip)
        return value

    def _check_flag(self, name, default=False):
        """Helper for deprecated behaviour transition."""
        if name not in self.request.arguments:
            return default
        value = self.request.arguments[name][0]
        value = str(value, 'utf-8', 'ignore').lower()
        return value in ['1', 'yes', 'true']

    def get_object_or_404(self, model, **kwargs):
        """
        Try to retrieve a single object with .get() method.
        In **kwargs are all filters.
        If record DoesNotExist then HTTP 404 error is raised.
        If model is list or tuple then apps.get_model() function is called
        with this parameter in order to get model.
        """

        return lib.tools.get_object_or_404(model=model, **kwargs)

    def _get_business_and_user(self, business, user, business_kwargs):
        if isinstance(business, (int, bytes, str)):
            try:
                business_id = int(business)
            except ValueError:
                raise tornado.web.HTTPError(404)  # pylint: disable=raise-missing-from
            if self.request.method in ('GET', 'HEAD', 'OPTIONS') and BusinessDetailsReplica(
                UserData(subject_key=self.__class__.__name__)
            ):
                with using_db_for_reads(READ_ONLY_DB):
                    business = self.get_object_or_404(
                        ('business', 'Business'), id=business_id, **business_kwargs
                    )
            else:
                business = self.get_object_or_404(
                    ('business', 'Business'), id=business_id, **business_kwargs
                )
            # nobody can log on venue
            if business.is_venue():
                raise tornado.web.HTTPError(404)

        if user is None:
            user = self.user

        return business, user

    def _business_with_worker_with_access_levels(
        self, business, user, business_kwargs, access_levels
    ):
        # pylint: disable=redefined-outer-name, reimported
        from webapps.business.models import Resource

        check_region = business_kwargs.pop('__check_region', True)
        business, user = self._get_business_and_user(business, user, business_kwargs)

        self.handle_whitelisting_ip_access_with_business_id(business.id)
        self.log_metadata(business_id=business.id)
        self.check_business_activeness(business)

        if check_region:
            self.quick_assert(
                business.region_id,
                ('invalid', 'validation', 'business'),
                _('Business has no region assigned.'),
            )
        # read business.owner here, so it will be fetched from main db, not read-only
        business_owner = business.owner
        with using_db_for_reads(READ_ONLY_DB):
            if user.id == business_owner.id:
                # fetch owner staffer - can be None
                worker = business.resources.filter(
                    Q(
                        staff_user_id=user.id,
                    )
                    | Q(
                        staff_email=business_owner.email,  # legacy businesses
                    ),
                    deleted__isnull=True,
                    active=True,
                ).first()
                staff_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
                if worker:
                    # legacy fix - overwrite Resource access level with owner
                    worker.staff_access_level = staff_access_level
            else:
                worker = self.get_object_or_404(
                    Resource,
                    active=True,
                    deleted__isnull=True,
                    staff_user_id=user.id,
                    business_id=business.id,
                )
                staff_access_level = worker.staff_access_level
                if worker.staff_access_level not in access_levels:
                    raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND)

        self.user_staffer = worker
        self.user_staffer_id = worker.id if worker else None
        self.access_level = staff_access_level
        return business

    def check_business_activeness(self, business):
        active = business.status != Business.Status.BLOCKED
        self._handle_business_active(active)

    def handle_whitelisting_ip_access_for_staffer(
        self,
        user_obj=None,
        user_email=None,
    ):
        if user_obj is not None:
            user = user_obj
        elif user_email is not None:
            user = User.objects.filter(email=user_email).first()
        else:
            user = getattr(self, 'user', None)

        if user:
            business_ids = self._get_user_business_ids(user)
            allowed = is_allowed_request_for_business_ids(
                business_ids,
                self.request.user_ip,
            )
            self._handle_allowed(allowed)

    def handle_whitelisting_ip_access_with_business_id(self, business_id):
        allowed = is_allowed_request_for_business_ids(
            [business_id],
            self.request.user_ip,
        )
        self._handle_allowed(allowed)

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def _get_user_business_ids(user):
        qs = Business.objects.filter(
            active=True,
            deleted__isnull=True,
        ).values_list('id', flat=True)
        business_ids_through_owner = qs.filter(owner=user)
        business_ids_through_staffers = qs.filter(
            resources__in=user.staffers.values_list('id', flat=True),
        )
        return [*business_ids_through_owner.union(business_ids_through_staffers)]

    @staticmethod
    def _handle_allowed(allowed):
        if not allowed:
            raise ServiceError(
                status.HTTP_403_FORBIDDEN,
                [
                    {
                        'code': 'unauthorized',
                        'description': _('Access Denied'),
                    }
                ],
            )

    @staticmethod
    def _handle_business_active(active):
        if not active:
            raise ServiceError(
                status.HTTP_401_UNAUTHORIZED,
                [
                    {
                        'code': 'blocked',
                        'description': _('Access Denied'),
                    }
                ],
            )

    def business_with_owner(self, business, user=None, **kwargs):
        # pylint: disable=redefined-outer-name, reimported
        from webapps.business.models import Resource

        return self._business_with_worker_with_access_levels(
            business,
            user,
            kwargs,
            [
                Resource.STAFF_ACCESS_LEVEL_OWNER,
            ],
        )

    def business_with_manager(self, business, user=None, **kwargs):
        # pylint: disable=redefined-outer-name, reimported
        from webapps.business.models import Resource

        return self._business_with_worker_with_access_levels(
            business,
            user,
            kwargs,
            [
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
            ],
        )

    def business_with_reception(self, business, user=None, **kwargs):
        # pylint: disable=redefined-outer-name, reimported
        from webapps.business.models import Resource

        return self._business_with_worker_with_access_levels(
            business,
            user,
            kwargs,
            [
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
                Resource.STAFF_ACCESS_LEVEL_RECEPTION,
            ],
        )

    def business_with_advanced_staffer(self, business, user=None, **kwargs):
        # pylint: disable=redefined-outer-name, reimported
        from webapps.business.models import Resource

        return self._business_with_worker_with_access_levels(
            business,
            user,
            kwargs,
            [
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
                Resource.STAFF_ACCESS_LEVEL_RECEPTION,
                Resource.STAFF_ACCESS_LEVEL_ADVANCED,
            ],
        )

    def business_with_staffer(self, business, user=None, **kwargs):
        # pylint: disable=redefined-outer-name, reimported
        from webapps.business.models import Resource

        return self._business_with_worker_with_access_levels(
            business,
            user,
            kwargs,
            [
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
                Resource.STAFF_ACCESS_LEVEL_RECEPTION,
                Resource.STAFF_ACCESS_LEVEL_ADVANCED,
                Resource.STAFF_ACCESS_LEVEL_STAFF,
            ],
        )

    def payment_extra_data(self):
        return {
            'cardholder_ip': self.forwarded_ip,
            'browser_language': self.language,
            'user': self.user,
        }

    @property
    def no_thumbs(self):
        val = self.get_argument('no_thumbs', default=False)
        return val == 'true'

    @property
    def is_frontdesk(self):
        return self.booking_source.name in FRONTDESK_SOURCES

    @property
    def is_mobile(self):
        return self.booking_source.name in MOBILE_SOURCES

    @property
    def is_customer_web(self):
        return (
            self.booking_source.app_type == BookingSources.CUSTOMER_APP
            and self.booking_source.name == WEB
        )

    @property
    def is_customer_android(self):
        return (
            self.booking_source.app_type == BookingSources.CUSTOMER_APP
            and self.booking_source.name == ANDROID
        )

    @staticmethod
    def validate_business_subscription(business: Business):
        if not business.has_subscription:
            raise ServiceError(
                status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'invalid',
                        'description': _("Invalid subscription status"),
                    },
                ],
            )


class DryRunMixin:
    dry_run = None
    force_dry_run_from_path = False

    def initialize(self, dry_run=False, **kwargs):
        super().initialize(**kwargs)
        self.dry_run = dry_run

    def set_dry_run_from_params(self):
        if self.force_dry_run_from_path or self.dry_run is True:
            self.data['dry_run'] = self.dry_run
        else:
            # this condition is for backward compatibility
            self.data.setdefault('dry_run', self.dry_run)


class LogFileMixin:
    @staticmethod
    def _log_file(business_id, success, data, filename):
        directory = os.path.join(settings.LOG_DIRECTORY, 'commodities_import')
        if not os.path.exists(directory):
            os.makedirs(directory)

        # pylint: disable=consider-using-f-string
        name = '%s__%s__%.6d.%s' % (
            datetime.datetime.utcnow().strftime('%F_%T.%f'),
            'success' if success else 'failure',
            int(business_id),
            filename.rsplit('.', 1)[-1].lower(),
        )
        # pylint: enable=consider-using-f-string

        with open(os.path.join(directory, name), 'wb') as fout:
            fout.write(data)


class ViewListItemEventMixin:
    booking_source: BookingSources
    firebase_auth_dict: dict
    user: User
    fingerprint: str

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    def _trigger_view_item_list_event(
        self, item_list_id, item_list_name, items, extra_parameters=None, allow_empty=False
    ):
        if not allow_empty and not items or not self.firebase_auth_dict:
            return

        for item in items:
            item['item_id'] = lib.tools.id_to_external_api(item['item_id'])

        context = {
            'event_type': EventType.USER,
            'source_id': self.booking_source.id,
        }
        event_parameters = {
            'item_list_id': item_list_id,
            'items': items,
            'fingerprint': self.fingerprint,
        }
        if item_list_name:
            event_parameters['item_list_name'] = item_list_name
        gender = Gender.Both.value
        if self.user and self.user.is_authenticated:
            context['session_user_id'] = self.user.id
            context['fingerprint'] = self.fingerprint
            gender = self.user.gender or gender
            if self.user.email:
                event_parameters['email'] = self.user.email

        if extra_parameters:
            event_parameters.update(extra_parameters)

        analytics_view_item_list_gtm_task.delay(
            event_parameters=event_parameters,
            metadata={
                'gender_code': gender,
            },
            auth_data=self.firebase_auth_dict,
            context=context,
        )


@lru_booksy_cache(timeout=60 * 60, skip_in_pytest=True)
def is_allowed_request_for_business_ids(
    business_ids: t.List[int],
    request_ip,
) -> bool:
    if not business_ids:
        return True
    return any(filter_allowed_business_ids(business_ids, request_ip))


def _check_if_client_has_allowed_ip(request_ip, network_ip):
    try:
        network_ip = ipaddress.ip_network(network_ip)
    except ValueError:
        # no request_ip but configuration exists
        allowed = False
    else:
        allowed = request_ip in network_ip

    return allowed


def filter_allowed_business_ids(business_ids: t.List[int], request_ip: str) -> t.List[int]:
    businesses_query = Business.objects.filter(
        id__in=business_ids, security_settings__deleted__isnull=True
    )
    allowed_ips_by_business_ids = dict(
        businesses_query.values_list('id', 'security_settings__allowed_ips')
    )

    def is_business_allowed(business_id):
        allowed_ips = allowed_ips_by_business_ids.get(business_id)
        if allowed_ips is None:
            return True

        return any(
            _check_if_client_has_allowed_ip(request_ip, network_ip) for network_ip in allowed_ips
        )

    return list(filter(is_business_allowed, business_ids))


def load_jwt_key(key_name):
    local_dev = settings.LOCAL_DEPLOYMENT
    return settings.USER_JWT_DEV_KEY if local_dev else getattr(settings, key_name, None)


def jwt_private_key():
    return load_jwt_key('USER_RESET_JWT_PRIVATE_KEY')


def jwt_public_key():
    return load_jwt_key('USER_RESET_JWT_PUBLIC_KEY')


class InvalidJWTError(Exception):
    def __init__(
        self,
        message: str,
    ):
        self.message = message


class JWTTokenType(Enum):
    EMAIL = 'email'
    PASSWORD = 'password'


def validate_user_jwt_token(token, token_type):
    try:
        decoded_token = jwt.decode(
            token,
            jwt_public_key(),
            algorithms=[settings.USER_JWT_ALG],
            audience=f'{settings.API_COUNTRY}.booksy.com',
        )

        try:
            user = User.objects.get(email=decoded_token['iss'])
        except User.DoesNotExist:
            # pylint: disable=raise-missing-from
            raise InvalidJWTError(message='User does not exist')

        if token_type == JWTTokenType.PASSWORD.value:
            nonce = user.password_reset_nonce()
        else:
            nonce = user.email_change_nonce()

        if decoded_token['nonce'] != nonce:
            raise InvalidJWTError(message='Invalid nonce')  # # pylint: disable=raise-missing-from
    except jwt.DecodeError:
        raise InvalidJWTError(message='JWT decode error')  # # pylint: disable=raise-missing-from
    except jwt.ExpiredSignatureError:
        raise InvalidJWTError(message='JWT Signature expired')  # pylint: disable=raise-missing-from

    return decoded_token


class RequestLockMixin:
    def _acquire_lock(
        self,
        lock_id: int,
        lock_class: t.Type[AbstractLock],
        error_description: str,
    ):
        """Locks request to prevent race conditions.

        Setup a time expiring lock on the selected resource. If acquiring the
        lock fails that means that another request from the same resource is
        already being processed, so we drop the request that could not acquire
        the lock.
        """
        lock_acquired = lock_class.try_to_lock(lock_id)
        if not lock_acquired:
            raise tornado.web.HTTPError(
                status_code=status.HTTP_409_CONFLICT,
                reason='lock_error',
                log_message=error_description,
            )
        return lock_acquired


def log_request_without_access_token(request_headers: dict, request_url: str, request_method: str):
    allowed_headers = {
        'User-Agent',
        'X-Api-Key',
        'X-Fingerprint',
        'X-Forwarded-For',
        'X-Real-Ip',
        'X-Real-Url',
        'X-User-Pseudo-Id',
        'X-Version',
    }
    log_params = {
        'httpRequest': {
            'requestUrl': request_url,
            'requestMethod': request_method,
        },
        'requestHeaders': {k: v for k, v in request_headers.items() if k in allowed_headers},
    }
    logger_session.warning('No access token provided.', extra=log_params)
