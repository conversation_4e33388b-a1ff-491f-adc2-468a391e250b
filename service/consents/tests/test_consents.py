# pylint: skip-file

import datetime
import typing as t
import uuid
from decimal import Decimal

import pytest
from django.core.files.base import ContentFile
from django.core.files.storage import FileSystemStorage, Storage
from django.utils.translation import get_language
from freezegun import freeze_time
from mock import patch
from model_bakery import baker
from pytz import UTC
from rest_framework import status

from lib.tools import tznow
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import (
    Business,
    Resource,
    Service,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business_related.enums import (
    ClaimLogReasons,
    ClaimLogStatuses,
    ClaimLogType,
)
from webapps.business_related.models import ClaimLog
from webapps.consents.fields import CheckboxField, InputField, TextField
from webapps.consents.models import Consent, ConsentForm
from webapps.consents.storages import consents_storage
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import UserProfile, User

TEST_DATETIME = datetime.datetime(2018, 1, 1, tzinfo=UTC)

PIXEL_PNG = (
    b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08'
    b'\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\x0cIDAT\x08\xd7c\xf8\xff\xff?\x00'
    b'\x05\xfe\x02\xfe\xdc\xccY\xe7\x00\x00\x00\x00IEND\xaeB`\x82'
)

PDF_CONTENT = b'%PDF-'


def mock_storage_save(*args, **kwargs):
    return 'mock-image.png'


def mock_storage_open(*args, **kwargs):
    return ContentFile(PIXEL_PNG, name='mock-image.png')


def mock_storage_get_modified_time(*args, **kwargs):
    return TEST_DATETIME + datetime.timedelta(seconds=30)


original_storage_save = Storage.save


class BaseConsentAsyncHTTPTest(BaseAsyncHTTPTest):

    def tearDown(self):
        super().tearDown()

        Consent.all_objects.all().delete()
        ConsentForm.all_objects.all().delete()


class SessionMixin:

    @staticmethod
    def get_basic_staffer_session(business: Business) -> t.Any:
        basic_staffer: Resource = baker.make_recipe(
            'webapps.business.basic_staffer_recipe',
            business=business,
        )
        return basic_staffer.staff_user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')


@pytest.mark.django_db
class TestBusinessConsentsHandler(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.url = f'/business_api/me/businesses/{self.business.id}/consents/'

        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                InputField('Contact number ICE'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        self.customer = BusinessCustomerInfo.objects.create(
            business=self.business, first_name='John', last_name='Doe'
        )

    @pytest.mark.freeze_time(TEST_DATETIME)
    @patch.object(Storage, 'save', original_storage_save)
    @patch.object(consents_storage, '_save', mock_storage_save)
    @patch.object(consents_storage, '_open', mock_storage_open)
    def test_post_201(self, *mocks):
        expected_datetime = TEST_DATETIME.astimezone(self.business.get_timezone())

        response = self.fetch(
            self.url,
            method='POST',
            body={
                'form': self.consent_form.id,
                'customer': self.customer.id,
            },
        )
        self.assertEqual(response.code, 201)

        self.assertIsInstance(response.json['consent']['uuid'], str)
        self.assertEqual(response.json['consent']['form'], self.consent_form.id)
        self.assertEqual(response.json['consent']['form_title'], 'Take-My-Kidneys Consent')
        self.assertEqual(
            response.json['consent']['form_fields'],
            [
                {
                    'type': 'text',
                    'label': ('In case of death, I agree for the ' 'business to take my kidneys.'),
                },
                {
                    'label': 'Contact number ICE',
                    'type': 'input',
                },
                {
                    'type': 'checkbox',
                    'label': 'I\'m aware of the risks',
                },
            ],
        )
        self.assertEqual(response.json['consent']['customer'], self.customer.id)
        self.assertEqual(response.json['consent']['customer_full_name'], self.customer.full_name)
        self.assertEqual(response.json['consent']['created'], expected_datetime.isoformat())
        self.assertEqual(response.json['consent']['updated'], expected_datetime.isoformat())
        self.assertIsNone(response.json['consent']['signed'])
        self.assertIsNone(response.json['consent']['signature'])
        self.assertIsNone(response.json['consent']['photo'])

        response_get = self.fetch(self.url, method='GET')
        self.assertEqual(response_get.code, 200)
        self.assertEqual(response_get.json['count'], 1)
        self.assertEqual(response_get.json['consents'][0]['uuid'], response.json['consent']['uuid'])

    def test_get(self):
        response = self.fetch(self.url + '?page=1&per_page=100', method='GET')
        self.assertEqual(response.code, 200)
        self.assertDictEqual(
            response.json, {'page': 1, 'per_page': 100, 'count': 0, 'consents': []}
        )


@pytest.mark.django_db
class TestBusinessConsentHandler(BaseConsentAsyncHTTPTest, SessionMixin):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)

        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                InputField('Contact number ICE'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        with patch.object(Consent, 'render_pdf'):
            self.consent = self.consent_form.create_consent(self.customer)

        self.url = (
            f'/business_api/me/businesses/{self.business.id}' f'/consents/{self.consent.uuid}/'
        )

    def test_get(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['uuid'], str(self.consent.uuid))

    def test_get_for_basic_staffer(self):
        self.session = self.get_basic_staffer_session(self.business)

        response = self.fetch(self.url)
        assert response.code == status.HTTP_200_OK

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put(self, *mocks):
        self.session = self.get_basic_staffer_session(self.business)
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {'type': 'input', 'label': 'ICE number', 'value_string': '************'},
                    {'type': 'checkbox', 'label': 'I\'m aware of the risks', 'value_boolean': True},
                ],
            },
        )
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['customer'], self.customer.id)
        self.assertEqual(response.json['consent']['form'], self.consent_form.id)
        self.assertNotEqual(
            response.json['consent']['created'], response.json['consent']['updated']
        )
        self.assertListEqual(
            response.json['consent']['form_fields'],
            [
                {
                    'type': 'text',
                    'label': (
                        'In case of death, I agree for the ' 'business to take my kidneys. #2'
                    ),
                },
                {
                    'type': 'input',
                    'label': 'ICE number',
                    'value': '************',
                    'value_string': '************',
                },
                {
                    'type': 'checkbox',
                    'label': 'I\'m aware of the risks',
                    'value': True,
                    'value_boolean': True,
                },
            ],
        )

    def test_delete(self):
        response = self.fetch(self.url, method='DELETE')
        self.assertEqual(response.code, 200)
        self.assertTrue(
            Consent.all_objects.filter(
                uuid=self.consent.uuid,
                deleted__isnull=False,
            ).exists()
        )

        for method in ('GET', 'DELETE'):
            response = self.fetch(self.url, method=method)
            self.assertEqual(response.code, 404)


@pytest.mark.django_db
class TestBusinessConsentSignHandler(BaseConsentAsyncHTTPTest, SessionMixin):

    content_type = 'image/png'

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)

        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                InputField('Contact number ICE'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        with patch.object(Consent, 'render_pdf'):
            self.consent = self.consent_form.create_consent(self.customer)

        self.url = (
            f'/business_api/me/businesses/{self.business.id}' f'/consents/{self.consent.uuid}/sign/'
        )

    @patch.object(Storage, 'save', original_storage_save)
    @patch.object(consents_storage, '_save', mock_storage_save)
    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch(
        'webapps.consents.models.Consent.signature.field.storage',
        FileSystemStorage(),
    )
    def test_post_signature(self, *mocks):
        response = self.fetch(self.url, method='POST', body=b'')
        self.assertEqual(response.code, 400)

        response = self.fetch(
            self.url,
            method='POST',
            body=PIXEL_PNG,
            headers={
                'Content-Type': self.content_type,
                'Content-Length': len(PIXEL_PNG),
                'Content-Disposition': 'inline; filename="pixel.png"',
                **self.get_headers(self.url),
            },
        )
        self.assertEqual(response.code, 200)
        self.assertIsNotNone(response.json['consent']['signature'])
        self.assertIsNotNone(response.json['consent']['signed'])

    @patch.object(Storage, 'save', original_storage_save)
    @patch.object(consents_storage, '_save', mock_storage_save)
    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch(
        'webapps.consents.models.Consent.photo.field.storage',
        FileSystemStorage(),
    )
    def test_post_photo(self, *mocks):
        response = self.fetch(self.url + '?field=photo', method='POST', body=b'')
        self.assertEqual(response.code, 400)

        response = self.fetch(
            self.url + '?field=photo',
            method='POST',
            body=PIXEL_PNG,
            headers={
                'Content-Type': self.content_type,
                'Content-Length': len(PIXEL_PNG),
                'Content-Disposition': 'inline; filename="pixel.png"',
                **self.get_headers(self.url),
            },
        )
        self.assertEqual(response.code, 200)
        self.assertIsNotNone(response.json['consent']['photo'])
        self.assertIsNotNone(response.json['consent']['signed'])

    @patch.object(Storage, 'save', original_storage_save)
    @patch.object(consents_storage, '_save', mock_storage_save)
    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch(
        'webapps.consents.models.Consent.signature.field.storage',
        FileSystemStorage(),
    )
    def test_post_signature_as_basic_staffer(self, *_):
        self.session = self.get_basic_staffer_session(self.business)

        response = self.fetch(self.url, method='POST', body=b'')
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)

        response = self.fetch(
            self.url,
            method='POST',
            body=PIXEL_PNG,
            headers={
                'Content-Type': self.content_type,
                'Content-Length': len(PIXEL_PNG),
                'Content-Disposition': 'inline; filename="pixel.png"',
                **self.get_headers(self.url),
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIsNotNone(response.json['consent']['signature'])
        self.assertIsNotNone(response.json['consent']['signed'])


@pytest.mark.django_db
class TestBusinessConsentSignatureHandler(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)

        with freeze_time(TEST_DATETIME):
            self.consent_form = ConsentForm.objects.create(
                business=self.business,
                title='Take-My-Kidneys Consent',
                fields=[
                    TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                    InputField('Contact number ICE'),
                    CheckboxField('I\'m aware of the risks'),
                ],
            )
            self.consent_form.services.add(
                Service.objects.create(business=self.business),
                Service.objects.create(business=self.business),
            )

            with patch.object(Consent, 'render_pdf'):
                self.consent = self.consent_form.create_consent(self.customer)

                Consent.objects.filter(uuid=self.consent.uuid).update(
                    signature='mock-image.png',
                )

        self.url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/consents/{self.consent.uuid}/signature/'
        )

    @patch.object(consents_storage, '_open', mock_storage_open)
    def test_get(self, *mocks):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertEqual(response.headers['Content-Type'], 'image/png')

        # expect contents to be the same as from storage
        self.assertEqual(response.body, PIXEL_PNG)


@pytest.mark.django_db
class TestBusinessConsentPdfHandler(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)

        with freeze_time(TEST_DATETIME):
            self.consent_form = ConsentForm.objects.create(
                business=self.business,
                title='Take-My-Kidneys Consent',
                fields=[
                    TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                    InputField('Contact number ICE'),
                    CheckboxField('I\'m aware of the risks'),
                ],
            )
            self.consent_form.services.add(
                Service.objects.create(business=self.business),
                Service.objects.create(business=self.business),
            )

            with patch.object(Consent, 'render_pdf'):
                self.consent = self.consent_form.create_consent(self.customer)

        self.url = (
            f'/business_api/me/businesses/{self.business.id}' f'/consents/{self.consent.uuid}/pdf/'
        )

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_get(self, *mocks):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertEqual(response.headers['Content-Type'], 'application/pdf')
        self.assertIn('.pdf', response.headers['Content-Disposition'])

        # expect contents to be the same as from storage
        self.assertEqual(response.body, PIXEL_PNG)


@pytest.mark.django_db
class TestBusinessConsentNotifyHandler(BaseConsentAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        baker.make(
            UserProfile,
            user=self.user,
            source=None,
            profile_type=UserProfile.Type.CUSTOMER,
            language=get_language(),
        )

        self.customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                InputField('Contact number ICE'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        with patch.object(Consent, 'render_pdf'):
            self.consent = self.consent_form.create_consent(self.customer)

        self.url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/consents/{self.consent.uuid}/notify/'
        )

    @patch('webapps.notification.tasks.push.fcm_android_push_task')
    @patch('webapps.notification.tasks.push.apns_push_task')
    def test_post_with_user(self, fcm_android_push_task_mock, apns_push_task_mock):
        self.customer.user = self.user
        self.customer.save(update_fields=['user'])

        self.assertFalse(self.consent.notifications.exists())

        response = self.fetch(self.url, method='POST', body='')
        self.assertEqual(response.code, 200)

        notification = self.consent.notifications.get()
        self.assertFalse(notification.used)

    def test_post_without_user(self):
        self.assertFalse(self.consent.notifications.exists())

        response = self.fetch(self.url, method='POST', body='')
        self.assertEqual(response.code, 400)

        self.assertFalse(self.consent.notifications.exists())


@pytest.mark.django_db
class TestBusinessConsentSendEmailHandler(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=None,
        )
        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        with patch.object(Consent, 'render_pdf'):
            self.consent = self.consent_form.create_consent(
                self.customer,
                notify=False,
            )

        self.url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/consents/{self.consent.uuid}/send_email/'
        )

    @patch('lib.email.send_email')
    @patch.object(Consent, 'render_pdf', return_value=PDF_CONTENT)
    def test_post(self, render_pdf_mock, send_email_mock):

        response = self.fetch(self.url, method='POST', body={})

        self.assertEqual(response.code, 200)
        self.assertEqual(send_email_mock.call_count, 1)

        call_args = send_email_mock.call_args_list[0][0]
        call_kwargs = send_email_mock.call_args_list[0][1]

        self.assertEqual(call_args[0], self.user.email)
        self.assertTrue(call_args[1].startswith('<!DOCTYPE html'))
        self.assertListEqual(
            call_kwargs['attachments'], [('form.pdf', PDF_CONTENT, 'application/pdf')]
        )

        # check with empty email
        response = self.fetch(self.url, method='POST', body={'email': None})
        self.assertEqual(response.code, 400)


@pytest.mark.django_db
class TestCustomerConsentsHandler(BaseConsentAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)
        self.consent_form = self._create_consent_form(self.business)
        self.url = f'/customer_api/me/consents/'

    def _create_consent_form(self, business):
        consent_form = ConsentForm.objects.create(
            business=business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                InputField('Contact number ICE'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        consent_form.services.add(
            Service.objects.create(business=business),
            Service.objects.create(business=business),
        )
        return consent_form

    @patch.object(Consent, 'render_pdf', return_value=PDF_CONTENT)
    def test_get(self, *mocks):
        other_customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        consent_other_customer = self.consent_form.create_consent(other_customer)
        consent_other_customer.secret = None
        consent_other_customer.save()

        response_0 = self.fetch(self.url + '?page=1&per_page=100', method='GET')
        self.assertEqual(response_0.code, 200)
        self.assertDictEqual(
            response_0.json,
            {
                'page': 1,
                'per_page': 100,
                'count': 0,
                'consents': [],
            },
        )

        consent_0 = self.consent_form.create_consent(self.customer)
        response_1 = self.fetch(self.url + '?page=1&per_page=100', method='GET')
        self.assertEqual(response_1.code, 200)
        self.assertEqual(
            response_1.json['consents'][0]['uuid'],
            str(consent_0.uuid),
        )
        assert response_1.json['count'] == 1

        other_business = baker.make(Business)
        other_bci = baker.make(
            BusinessCustomerInfo,
            business=other_business,
            user=self.user,
        )
        other_consent_form = self._create_consent_form(other_business)
        consent_1 = other_consent_form.create_consent(other_bci)
        consent_1.secret = None
        consent_1.save()
        response_2 = self.fetch(self.url + '?page=1&per_page=100', method='GET')
        self.assertEqual(response_2.code, 200)
        assert response_2.json['count'] == 2

        consent_3_data = other_consent_form._build_consent_defaults(customer=None)
        consent_3_data['secret'] = uuid.uuid4()
        consent_3 = Consent(form=other_consent_form, **consent_3_data)
        consent_3.save()
        response_3 = self.fetch(
            self.url + f'?page=1&per_page=100&secret={consent_3_data["secret"]}', method='GET'
        )
        self.assertEqual(response_3.code, 200)
        assert response_3.json['count'] == 3  # 2 with BCI + 1 from secret uuid


@pytest.mark.django_db
class BaseTestCustomerConsentHandlerMixin(BaseConsentAsyncHTTPTest):

    def setUp(self):
        super().setUp()
        self._create_additional_stuff()
        wrapped_appointment = getattr(self, 'wrapped_appointment', None)

        if not getattr(self, 'customer', None):
            self.customer = baker.make(
                BusinessCustomerInfo,
                business=self.business,
                user=self.user,
                cell_phone='***********',
            )
        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                InputField('Contact number ICE'),
                CheckboxField('I\'m aware of the risks'),
            ],
            required_per_appointment=bool(wrapped_appointment),
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        with patch.object(Consent, 'render_pdf'):
            appointment_id = wrapped_appointment.appointment_id if wrapped_appointment else None
            self.consent = self.consent_form.create_consent(
                self.customer,
                appointment_id=appointment_id,
            )

        self.url = f'/customer_api/me/consents/{self.consent.uuid}/'

    def _create_additional_stuff(self):
        pass


class TestCustomerConsentHandler(BaseTestCustomerConsentHandlerMixin):

    def test_get(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['uuid'], str(self.consent.uuid))

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put(self):
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {'type': 'input', 'label': 'ICE number', 'value_string': '************'},
                    {'type': 'checkbox', 'label': 'I\'m aware of the risks', 'value_boolean': True},
                ],
            },
        )
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['customer'], self.customer.id)
        self.assertEqual(response.json['consent']['form'], self.consent_form.id)
        self.assertNotEqual(
            response.json['consent']['created'], response.json['consent']['updated']
        )
        self.assertListEqual(
            response.json['consent']['form_fields'],
            [
                {
                    'type': 'text',
                    'label': (
                        'In case of death, I agree for the ' 'business to take my kidneys. #2'
                    ),
                },
                {
                    'type': 'input',
                    'label': 'ICE number',
                    'value': '************',
                    'value_string': '************',
                },
                {
                    'type': 'checkbox',
                    'label': 'I\'m aware of the risks',
                    'value': True,
                    'value_boolean': True,
                },
            ],
        )

        consent = Consent.objects.get(uuid=self.consent.uuid)
        self.assertEqual(consent.form_fields[1].value, '************')
        self.assertTrue(consent.form_fields[2].value)

    def test_delete(self):
        response = self.fetch(self.url, method='DELETE')
        self.assertEqual(response.code, 405)


class TestCustomerConsentWithAppointmentHandler(BaseTestCustomerConsentHandlerMixin):

    def _create_additional_stuff(self):
        service = baker.make_recipe(
            'webapps.business.service_recipe',
            business=self.business,
        )
        service_variant = baker.make_recipe(
            'webapps.business.service_variant_recipe',
            service=service,
            price=Decimal('100.'),
        )

        booked_from = tznow()
        booked_till = booked_from + service_variant.duration
        self.appointment = create_appointment(
            [
                dict(
                    booked_from=booked_from,
                    booked_till=booked_till,
                    service_variant=service_variant,
                )
            ],
            business=self.business,
            source=self.biz_booking_src,
            updated_by=self.user,
        )
        self.wrapped_appointment = AppointmentWrapper(
            list(self.appointment.subbookings),
        )

    def test_get(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['uuid'], str(self.consent.uuid))

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put_appointment_with_secret_without_booked_for(self):
        self.customer.delete()
        common_cell_phone = '***********'
        logged_in_user = User.objects.filter(
            id=self.logged_in_user_id,
        ).first()
        logged_in_user.cell_phone = common_cell_phone
        logged_in_user.save()
        other_bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone=common_cell_phone,
        )
        self.consent.customer = other_bci
        self.consent.save()
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {'type': 'input', 'label': 'ICE number', 'value_string': '************'},
                    {'type': 'checkbox', 'label': 'I\'m aware of the risks', 'value_boolean': True},
                ],
                'secret': str(self.consent.secret),
            },
        )
        self.assertEqual(response.code, 200)
        self.assertEqual(
            response.json['consent']['customer'],
            other_bci.id,
        )
        self.assertEqual(
            response.json['consent']['form'],
            self.consent_form.id,
        )
        self.assertNotEqual(
            response.json['consent']['created'], response.json['consent']['updated']
        )
        self.consent.refresh_from_db()
        assert self.consent.customer == other_bci
        assert ClaimLog.objects.filter(
            target_bci=other_bci,
            new_user=self.user,
            type=ClaimLogType.CUSTOM_FORM,
        ).exists()
        assert ClaimLog.objects.all().count() == 1
        other_bci.refresh_from_db()
        assert other_bci.user == self.user

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put_consent_with_secret_without_appointment(self):
        other_bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
        )
        self.consent.customer = other_bci
        self.consent.appointment = None
        self.consent.save()
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {
                        'type': 'input',
                        'label': 'ICE number',
                        'value_string': '************',
                    },
                    {
                        'type': 'checkbox',
                        'label': 'I\'m aware of the risks',
                        'value_boolean': True,
                    },
                ],
                'secret': str(self.consent.secret),
            },
        )
        self.assertEqual(response.code, 200)
        self.assertEqual(
            response.json['consent']['customer'],
            self.customer.id,
        )
        self.assertEqual(
            response.json['consent']['form'],
            self.consent_form.id,
        )
        self.assertNotEqual(
            response.json['consent']['created'],
            response.json['consent']['updated'],
        )
        self.consent.refresh_from_db()
        assert self.consent.customer == self.customer
        assert ClaimLog.objects.filter(
            target_bci=self.customer,
            source_bci=other_bci,
            type=ClaimLogType.BUSINESS_CUSTOMER_INFO,
            status=ClaimLogStatuses.MERGED,
        ).exists()
        assert ClaimLog.objects.all().count() == 1

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put_consent_with_secret_without_appointment_different_phone_number(self):
        other_bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        self.consent.customer = other_bci
        self.consent.appointment = None
        self.consent.save()
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {
                        'type': 'input',
                        'label': 'ICE number',
                        'value_string': '************',
                    },
                    {
                        'type': 'checkbox',
                        'label': 'I\'m aware of the risks',
                        'value_boolean': True,
                    },
                ],
                'secret': str(self.consent.secret),
            },
        )
        self.assertEqual(response.code, 200)
        # self.customer and other_bci not merged
        self.assertEqual(
            response.json['consent']['customer'],
            other_bci.id,
        )
        self.assertEqual(
            response.json['consent']['form'],
            self.consent_form.id,
        )
        self.assertNotEqual(
            response.json['consent']['created'],
            response.json['consent']['updated'],
        )
        self.consent.refresh_from_db()
        assert self.consent.customer == other_bci
        assert ClaimLog.objects.filter(
            target_bci=self.customer,
            source_bci=other_bci,
            type=ClaimLogType.BUSINESS_CUSTOMER_INFO,
            status=ClaimLogStatuses.PROPOSED,
        ).exists()
        assert ClaimLog.objects.all().count() == 1

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put_appointment_consent_with_secret_with_different_customer(self):
        other_bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
        )
        self.appointment.booked_for = other_bci
        self.appointment.save()
        self.consent.customer = other_bci
        self.consent.save()
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {'type': 'input', 'label': 'ICE number', 'value_string': '************'},
                    {'type': 'checkbox', 'label': 'I\'m aware of the risks', 'value_boolean': True},
                ],
                'secret': str(self.consent.secret),
            },
        )
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['customer'], self.customer.id)
        self.assertEqual(response.json['consent']['form'], self.consent_form.id)
        self.assertNotEqual(
            response.json['consent']['created'], response.json['consent']['updated']
        )
        self.appointment.refresh_from_db()
        assert self.appointment.booked_for == self.customer
        self.consent.refresh_from_db()
        assert self.consent.customer == self.appointment.booked_for
        assert self.consent.customer != other_bci
        assert ClaimLog.objects.filter(
            target_bci=self.customer,
            source_bci=other_bci,
            source_model_related_objects__isnull=False,
            merge_reason=ClaimLogReasons.CUSTOM_SECRET,
            type=ClaimLogType.BUSINESS_CUSTOMER_INFO,
            status=ClaimLogStatuses.MERGED,
        )
        assert ClaimLog.objects.all().count() == 1


class TestCustomerConsentWithAppointmentHandlerUnrelatedBCI(BaseTestCustomerConsentHandlerMixin):

    def _create_additional_stuff(self):
        self.customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            # no user, so no relation beetween loggin in user and consent
        )
        service = baker.make_recipe(
            'webapps.business.service_recipe',
            business=self.business,
        )
        service_variant = baker.make_recipe(
            'webapps.business.service_variant_recipe',
            service=service,
            price=Decimal('100.'),
        )

        booked_from = tznow()
        booked_till = booked_from + service_variant.duration
        self.appointment = create_appointment(
            [
                dict(
                    booked_from=booked_from,
                    booked_till=booked_till,
                    service_variant=service_variant,
                )
            ],
            business=self.business,
            source=self.biz_booking_src,
            updated_by=self.user,
            booked_for=self.customer,
        )
        self.wrapped_appointment = AppointmentWrapper(
            list(self.appointment.subbookings),
        )

    def test_get_without_secret(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 404)
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'type': 'invalid',
                        'code': 'not_found',
                        'description': 'Requested object not found',
                    }
                ]
            },
        )

    def test_get_with_secret(self):
        response = self.fetch(
            self.url + f'?secret={self.consent.secret}',
            method='GET',
        )
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['uuid'], str(self.consent.uuid))

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put_with_secret(self):
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {'type': 'input', 'label': 'ICE number', 'value_string': '************'},
                    {'type': 'checkbox', 'label': 'I\'m aware of the risks', 'value_boolean': True},
                ],
                'secret': str(self.consent.secret),
            },
        )
        self.assertEqual(response.code, 200)
        self.assertEqual(response.json['consent']['customer'], self.customer.id)
        self.assertEqual(response.json['consent']['form'], self.consent_form.id)
        self.assertNotEqual(
            response.json['consent']['created'], response.json['consent']['updated']
        )
        self.assertListEqual(
            response.json['consent']['form_fields'],
            [
                {
                    'type': 'text',
                    'label': (
                        'In case of death, I agree for the ' 'business to take my kidneys. #2'
                    ),
                },
                {
                    'type': 'input',
                    'label': 'ICE number',
                    'value': '************',
                    'value_string': '************',
                },
                {
                    'type': 'checkbox',
                    'label': 'I\'m aware of the risks',
                    'value': True,
                    'value_boolean': True,
                },
            ],
        )

        consent = Consent.objects.get(uuid=self.consent.uuid)
        self.assertEqual(consent.form_fields[1].value, '************')
        self.assertTrue(consent.form_fields[2].value)

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_put_without_secret(self):
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'customer': 0,  # expect this to be ignored
                'form': 0,  # expect this to be ignored
                'form_fields': [
                    {
                        'type': 'text',
                        'label': (
                            'In case of death, I agree for the ' 'business to take my kidneys. #2'
                        ),
                    },
                    {'type': 'input', 'label': 'ICE number', 'value_string': '************'},
                    {'type': 'checkbox', 'label': 'I\'m aware of the risks', 'value_boolean': True},
                ],
            },
        )
        self.assertEqual(response.code, 404)


@pytest.mark.django_db
class TestCustomerConsentSignHandler(BaseConsentAsyncHTTPTest):

    content_type = 'image/png'

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)
        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                InputField('Contact number ICE'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        with patch.object(Consent, 'render_pdf'):
            self.consent = self.consent_form.create_consent(self.customer)

        self.url = f'/customer_api/me/consents/{self.consent.uuid}/sign/'

    @patch.object(Storage, 'save', original_storage_save)
    @patch.object(consents_storage, '_save', mock_storage_save)
    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch(
        'webapps.consents.models.Consent.signature.field.storage',
        FileSystemStorage(),
    )
    def test_post_signature(self):
        response = self.fetch(self.url, method='POST', body=b'')
        self.assertEqual(response.code, 400)

        response = self.fetch(
            self.url,
            method='POST',
            body=PIXEL_PNG,
            headers={
                'Content-Type': self.content_type,
                'Content-Length': len(PIXEL_PNG),
                'Content-Disposition': 'inline; filename="pixel.png"',
                **self.get_headers(self.url),
            },
        )
        self.assertEqual(response.code, 200)
        self.assertIsNotNone(response.json['consent']['signature'])
        self.assertIsNotNone(response.json['consent']['signed'])


@pytest.mark.django_db
class TestCustomerConsentSignatureHandler(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)

        with freeze_time(TEST_DATETIME):
            self.consent_form = ConsentForm.objects.create(
                business=self.business,
                title='Take-My-Kidneys Consent',
                fields=[
                    TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                    InputField('Contact number ICE'),
                    CheckboxField('I\'m aware of the risks'),
                ],
            )
            self.consent_form.services.add(
                Service.objects.create(business=self.business),
                Service.objects.create(business=self.business),
            )

            with patch.object(Consent, 'render_pdf'):
                self.consent = self.consent_form.create_consent(self.customer)

                Consent.objects.filter(uuid=self.consent.uuid).update(
                    signature='mock-image.png',
                )

        self.url = f'/customer_api/me/consents/{self.consent.uuid}/signature/'

    @patch.object(consents_storage, '_open', mock_storage_open)
    def test_get(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertEqual(response.headers['Content-Type'], 'image/png')

        # expect contents to be the same as from storage
        self.assertEqual(response.body, PIXEL_PNG)


@pytest.mark.django_db
class TestCustomerConsentPdfHandler(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)

        with freeze_time(TEST_DATETIME):
            self.consent_form = ConsentForm.objects.create(
                business=self.business,
                title='Take-My-Kidneys Consent',
                fields=[
                    TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                    InputField('Contact number ICE'),
                    CheckboxField('I\'m aware of the risks'),
                ],
            )
            self.consent_form.services.add(
                Service.objects.create(business=self.business),
                Service.objects.create(business=self.business),
            )

            with patch.object(Consent, 'render_pdf'):
                self.consent = self.consent_form.create_consent(self.customer)

        self.url = f'/customer_api/me/consents/{self.consent.uuid}/pdf/'

    @patch.object(consents_storage, '_open', mock_storage_open)
    @patch.object(consents_storage, 'get_modified_time', mock_storage_get_modified_time)
    def test_get(self, *mocks):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertEqual(response.headers['Content-Type'], 'application/pdf')
        self.assertIn('.pdf', response.headers['Content-Disposition'])

        # expect contents to be the same as from storage
        self.assertEqual(response.body, PIXEL_PNG)


@pytest.mark.django_db
class TestCustomerConsentSendEmailHandler(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()

        self.customer = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )
        self.consent_form = ConsentForm.objects.create(
            business=self.business,
            title='Take-My-Kidneys Consent',
            fields=[
                TextField('In case of death, I agree for the ' 'business to take my kidneys.'),
                CheckboxField('I\'m aware of the risks'),
            ],
        )
        self.consent_form.services.add(
            Service.objects.create(business=self.business),
            Service.objects.create(business=self.business),
        )

        with patch.object(Consent, 'render_pdf'):
            self.consent = self.consent_form.create_consent(
                self.customer,
                notify=False,
            )

        self.url = f'/customer_api/me/consents/{self.consent.uuid}/send_email/'

    @patch('lib.email.send_email')
    @patch.object(Consent, 'render_pdf', return_value=PDF_CONTENT)
    def test_post(self, render_pdf_mock, send_email_mock):

        response = self.fetch(self.url, method='POST', body={})

        self.assertEqual(response.code, 200)
        self.assertEqual(send_email_mock.call_count, 1)

        call_args = send_email_mock.call_args_list[0][0]
        call_kwargs = send_email_mock.call_args_list[0][1]

        self.assertEqual(call_args[0], self.user.email)
        self.assertTrue(call_args[1].startswith('<!DOCTYPE html'))
        self.assertListEqual(
            call_kwargs['attachments'], [('form.pdf', PDF_CONTENT, 'application/pdf')]
        )

        # check with empty email
        response = self.fetch(self.url, method='POST', body={'email': None})
        self.assertEqual(response.code, 400)
