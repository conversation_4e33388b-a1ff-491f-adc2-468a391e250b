from datetime import timed<PERSON><PERSON>
from unittest import mock

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from model_bakery import baker

from lib.tools import tznow
from lib.x_version_compatibility.typing import RequestType
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.factory.subbookings import customer_subbookings_make
from webapps.booking.models import Appointment, BookingSources, SubBooking
from webapps.booking.serializers.appointment import (
    CustomerAppointmentSerializer,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import (
    bci_recipe,
    business_recipe,
    service_recipe,
    service_variant_recipe,
)
from webapps.business.enums import ComboType
from webapps.business.models import Business
from webapps.consts import ANDROID, IPHONE
from webapps.family_and_friends.baker_recipes import (
    inactive_bci_relation_recipe,
    member_appointment_recipe,
)
from webapps.pos.baker_recipes import default_pos_recipe
from webapps.reviews.models import Review
from webapps.user.baker_recipes import user_recipe


class TestAppointmentCanAddReviewField(TestCase):
    """
    Set of tests checking behaviour of the `can_add_review` appointment boolean
    field.

    This field should be set to True only if:
    - the appointment has been already successfully finished
    - the appointment is not older than 30 days
     for this client and business
    - if it's family and friends appointment and it was booked for customer with user account, then
      then the user in context must match customer user
    - if it's family and friends appointment and it was booked for inactive BCI
    """

    def setUp(self):
        super().setUp()

        default_pos_recipe.make()

    @staticmethod
    def _get_context(appointment: Appointment):
        return {
            'user': appointment.booked_for.user,
            'business': appointment.business,
        }

    @staticmethod
    def _get_request(app_type: str, app_name: str, x_version: str) -> RequestType:
        booking_source = mock.MagicMock(app_type=app_type)
        booking_source.name = app_name
        return mock.MagicMock(
            spec=RequestType,
            headers={'X-Version': x_version},
            booking_source=booking_source,
        )

    @staticmethod
    def _check_appointment_can_add_review_value(
        booking: SubBooking, expected_can_add_review: bool, context: dict = None
    ):

        appointment = AppointmentWrapper.get_by_appointment_id(
            appointment_id=booking.appointment_id,
            business_id=booking.appointment.business_id,
        )
        assert appointment is not None
        if context is None:
            context = TestAppointmentCanAddReviewField._get_context(appointment)
        serializer = CustomerAppointmentSerializer(instance=appointment, context=context)
        assert serializer is not None
        assert serializer.data['can_add_review'] is expected_can_add_review

    @staticmethod
    def _create_booking() -> SubBooking:
        business = business_recipe.make(status=Business.Status.PAID)
        customer = bci_recipe.make(
            user=user_recipe.make(gender='M'),
            business=business,
        )
        return create_appointment(
            [
                dict(
                    booked_from=tznow() - timedelta(hours=1),
                    booked_till=tznow() - timedelta(minutes=30),
                )
            ],
            business=business,
            booked_for=customer,
            status=Appointment.STATUS.FINISHED,
            updated_by=business.owner,
        ).subbookings[0]

    @staticmethod
    def _create_old_booking() -> SubBooking:
        business = business_recipe.make(status=Business.Status.PAID)
        customer = bci_recipe.make(
            user=user_recipe.make(gender='M'),
            business=business,
        )
        return create_appointment(
            [
                dict(
                    booked_from=tznow() - timedelta(days=35, hours=1),
                    booked_till=tznow() - timedelta(days=35, minutes=30),
                )
            ],
            business=business,
            booked_for=customer,
            status=Appointment.STATUS.FINISHED,
            updated_by=business.owner,
        ).subbookings[0]

    @staticmethod
    def _create_multibooking() -> Appointment:
        """
        Creates 3 bookings treated as one multibooking
        """
        business = business_recipe.make(status=Business.Status.PAID)
        customer = bci_recipe.make(
            user=user_recipe.make(gender='M'),
            business=business,
        )
        return create_appointment(
            [
                dict(
                    booked_from=tznow() - timedelta(hours=1),
                    booked_till=tznow() - timedelta(minutes=30 - i),
                )
                for i in range(3)
            ],
            business=business,
            type=Appointment.TYPE.BUSINESS,
            booked_for=customer,
            status=Appointment.STATUS.FINISHED,
        )

    @staticmethod
    def _create_family_and_friends_booking(active: bool = True) -> Appointment:
        business = business_recipe.make(status=Business.Status.PAID)
        if active:
            customer = bci_recipe.make(
                user=user_recipe.make(gender='M'),
                business=business,
            )
        else:
            customer = bci_recipe.make(
                user=None,
                business=business,
            )
            customer.use_parent_data = True
            customer.save()
        parent = bci_recipe.make(
            user=user_recipe.make(gender='K'),
            business=business,
        )
        inactive_bci_relation_recipe.make(parent_bci=parent, member_bci=customer)
        appointment = create_appointment(
            [
                dict(
                    booked_from=tznow() - timedelta(hours=1),
                    booked_till=tznow() - timedelta(minutes=30 - i),
                )
                for i in range(3)
            ],
            business=business,
            type=Appointment.TYPE.BUSINESS,
            booked_for=customer,
            status=Appointment.STATUS.FINISHED,
        )
        member_appointment_recipe.make(
            appointment=appointment, booked_by=parent, booked_for=customer
        )
        return appointment

    @staticmethod
    def _create_combo_booking():
        business = business_recipe.make(status=Business.Status.PAID)
        child2 = service_variant_recipe.make(
            service=service_recipe.make(business=business),
            duration=relativedelta(minutes=20),
        )
        child1 = service_variant_recipe.make(
            service=service_recipe.make(business=business),
            duration=relativedelta(minutes=10),
        )
        combo = service_variant_recipe.make(
            service=service_recipe.make(combo_type=ComboType.SEQUENCE, business=business),
        )
        baker.make(
            'business.ComboMembership', combo=combo, child=child1, order=1, gap_time=relativedelta()
        )
        baker.make(
            'business.ComboMembership', combo=combo, child=child2, order=2, gap_time=relativedelta()
        )

        subbookings = customer_subbookings_make(
            [  # noqa
                dict(
                    service_variant=combo,
                    booked_from=tznow() - timedelta(hours=3),
                    combo_children=[
                        dict(
                            service_variant=child1,
                            booked_from=tznow() - timedelta(hours=2),
                        ),
                        dict(
                            service_variant=child2,
                            booked_from=tznow() - timedelta(hours=1),
                        ),
                    ],
                )
            ],
            business=business,
        )
        customer = bci_recipe.make(
            user=user_recipe.make(gender='M'),
            business=business,
        )
        booking = create_appointment(
            subbookings,
            business=business,
            booked_for=customer,
            status=Appointment.STATUS.FINISHED,
        ).bookings.first()

        return booking

    def test_review_not_present(self):
        """
        Checks if regular appointment without review can be reviewed.
        """
        booking = self._create_booking()
        self._check_appointment_can_add_review_value(booking, expected_can_add_review=True)

    def test_review_present(self):
        """
        Checks if adding a review properly marks appointment as not reviewable.
        """
        booking = self._create_booking()
        Review(
            user_id=booking.appointment.booked_for.user_id,
            subbooking=booking,
            business_id=booking.appointment.business_id,
            rank=5,
        ).save()
        self._check_appointment_can_add_review_value(booking, expected_can_add_review=False)

    def test_not_youngest_booking(self):
        """
        Checks whether previous appointments (in terms of finish date) is reviewable.
        """
        booking = self._create_booking()
        new_booking = create_appointment(
            [
                dict(
                    booked_from=booking.booked_from + timedelta(minutes=30),
                    booked_till=booking.booked_till + timedelta(minutes=30),
                )
            ],
            business=booking.appointment.business,
            status=Appointment.STATUS.FINISHED,
            booked_for_id=booking.appointment.booked_for_id,
            updated_by=booking.appointment.business.owner,
        ).subbookings[0]

        self._check_appointment_can_add_review_value(booking, expected_can_add_review=True)
        self._check_appointment_can_add_review_value(new_booking, expected_can_add_review=True)

    def test_not_youngest_booking_for_given_app(self):
        """
        Checks whether new reviews behaviour is applied for given app configuration.
        """
        booking = self._create_booking()
        _new_booking = create_appointment(
            [
                dict(
                    booked_from=booking.booked_from + timedelta(minutes=30),
                    booked_till=booking.booked_till + timedelta(minutes=30),
                )
            ],
            business=booking.appointment.business,
            status=Appointment.STATUS.FINISHED,
            booked_for_id=booking.appointment.booked_for_id,
            updated_by=booking.appointment.business.owner,
        ).subbookings[0]
        context = self._get_context(booking.appointment)

        # Assert iOS
        self._check_appointment_can_add_review_value(
            booking,
            expected_can_add_review=False,
            context=context
            | {
                'request': self._get_request(BookingSources.CUSTOMER_APP, IPHONE, '2.40.0'),
            },
        )
        self._check_appointment_can_add_review_value(
            booking,
            expected_can_add_review=True,
            context=context
            | {
                'request': self._get_request(BookingSources.CUSTOMER_APP, IPHONE, '2.40.1'),
            },
        )

        # Assert Android
        self._check_appointment_can_add_review_value(
            booking,
            expected_can_add_review=False,
            context=context
            | {
                'request': self._get_request(BookingSources.CUSTOMER_APP, ANDROID, '2.37.0_487'),
            },
        )
        self._check_appointment_can_add_review_value(
            booking,
            expected_can_add_review=True,
            context=context
            | {
                'request': self._get_request(BookingSources.CUSTOMER_APP, ANDROID, '2.37.5_493'),
            },
        )

    def test_multibooking(self):
        """
        Checks if finished multibooking properly marks the appointment as
        ready for review.
        """
        multibooking = self._create_multibooking()
        for booking in multibooking.bookings.all():
            self._check_appointment_can_add_review_value(
                booking,
                expected_can_add_review=True,
            )

    def test_review_family_and_friends_triggered_from_booked_for(self):
        appointment = self._create_family_and_friends_booking()
        self._check_appointment_can_add_review_value(
            appointment.bookings.first(),
            expected_can_add_review=True,
        )

    def test_review_family_and_friends_triggered_from_booked_by(self):
        appointment = self._create_family_and_friends_booking()
        context = self._get_context(appointment)
        context['user'] = appointment.booked_by.user
        self._check_appointment_can_add_review_value(
            appointment.bookings.first(), expected_can_add_review=False, context=context
        )

    def test_review_family_and_friends_triggered_inactive_from_booked_by(self):
        appointment = self._create_family_and_friends_booking(active=False)
        context = self._get_context(appointment)
        context['user'] = appointment.booked_by.user
        self._check_appointment_can_add_review_value(
            appointment.bookings.first(), expected_can_add_review=True, context=context
        )

    def test_old_booking(self):
        """
        Checks if appointment with booking older than 30 days can be reviewed.
        """
        booking = self._create_old_booking()
        self._check_appointment_can_add_review_value(booking, expected_can_add_review=False)

    def test_combo_booking(self):
        booking = self._create_combo_booking()
        self._check_appointment_can_add_review_value(booking, expected_can_add_review=True)
