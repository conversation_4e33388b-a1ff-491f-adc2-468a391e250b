from datetime import date
from enum import StrEnum

from ....commons.types import DayMinutes, ResourceId
from ..dto import Occupancy


class AppointmentType(StrEnum):
    BUSINESS = 'B'
    CUSTOMER = 'C'
    RESERVATION = 'R'
    TIMEOFF__DEPRECATED = 'T'


TimeSlot = tuple[DayMinutes, DayMinutes]
AvailabilitySlotsMap = OccupancySlotsMap = dict[ResourceId, list[TimeSlot]]
DailyAvailabilitySlotsMap = DailyOccupancySlotsMap = dict[date, OccupancySlotsMap]
BookedSlotsMap = dict[ResourceId, dict[AppointmentType, list[TimeSlot]]]
DailyBookedSlotsMap = dict[date, BookedSlotsMap]

ResourcesOccupancyMap = dict[ResourceId, Occupancy | None]

Bin = int
BinSlot = tuple[Bin, Bin]
MatchingBinsMap = dict[Bin, set[ResourceId]]
