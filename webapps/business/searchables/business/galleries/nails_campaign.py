import elasticsearch_dsl as dsl
from elasticsearch_dsl.query import FunctionScore

from lib.searchables.searchables import (
    V,
    Searchable,
    VGeoPoint,
)
from webapps.business.searchables.business.search_engine import VisibleBusinessSearchable
from webapps.business.searchables.business.sub_searchables import BusinessInsertSingleImages


class NailsCampaignFallbackSearchable(Searchable):
    # Fallback for when the NailsCampaignSearchable won't return any results.
    # That means that there is not enough MRC Nails businesses in range, so the
    # search is repeated with non-MRC nails businesses (active_from older than 14 days)

    # 18 km
    DISTANCE = '18000'

    visible = VisibleBusinessSearchable()
    category = dsl.query.Terms(business_primary_category__id=V('category_id'))
    location = dsl.query.GeoDistance(
        distance=DISTANCE,
        business_location__coordinate=V('location_geo'),
    )
    insert_images = BusinessInsertSingleImages()

    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
        ],
        score_mode='multiply',
    )

    _script_fields = {
        'distance': {
            'id': 'distance_from_business',
            'params': {
                'loc': VGeoPoint('location_geo', default=None),
            },
        }
    }


class NailsCampaignSearchable(NailsCampaignFallbackSearchable):
    # The same criteria as in fallback case, but only for MRC businesses

    # Experiment is done only on MRC business, meaning those that have
    # active_from date 14 days from now
    # To guarantee the full 14 days of data, check for 15 days
    new_business = dsl.query.Range(active_from={'gte': 'now-15d'})
