from lib.point_of_sale.entities import PlatformFeeSettingsEntity
from lib.point_of_sale.enums import PlatformFeeMode
from webapps.point_of_sale.models import PlatformFeeSettings, PlatformFeePricingPlan


class PlatformFeeService:

    @staticmethod
    def set_platform_fee_mode(business_id: int, mode: PlatformFeeMode):
        settings, _ = PlatformFeeSettings.objects.filter(business_id=business_id).get_or_create()
        settings.fee_mode = mode
        settings.save(update_fields=['fee_mode'])

    @staticmethod
    def get_platform_fee_settings(business_id: int) -> PlatformFeeSettingsEntity:
        """
        Resolve the platform fee mode for the business (falls back to default mode if doesnt exist),
        then pick the appropriate pricing plan and return its fees.
        Raises RuntimeError only when a required GLOBAL default plan is missing.

        Selection rules:
        - mode = row in PlatformFeeSettings for business_id, else default (PX_FULL_COST).
        - if mode in {SPLIT, CX_FULL_COST}:
            1) try business-specific plan,
            2) else fall back to GLOBAL default plan (default=True, business_id IS NULL),
            3) if no global default exists -> RuntimeError (env misconfiguration).
        - if mode == PX_FULL_COST: plan/fees are None (fees come from POS plans).
        """
        # 1) Determine mode for business (or fall back to the model's default)
        settings = PlatformFeeSettings.objects.filter(business_id=business_id).first()
        mode = settings.fee_mode if settings else PlatformFeeMode.PX_FULL_COST

        plan = None

        if mode in (PlatformFeeMode.SPLIT, PlatformFeeMode.CX_FULL_COST):
            # 2) Prefer business-specific plan
            plan = PlatformFeePricingPlan.objects.filter(
                business_id=business_id,
                fee_mode=mode,
            ).first()

            # 3) Otherwise use global default plan
            if plan is None:
                plan = PlatformFeePricingPlan.objects.filter(
                    default=True,
                    business_id__isnull=True,
                    fee_mode=mode,
                ).first()

                if plan is None:
                    return
                    # Only error case: required global default plan is missing
                    # raise RuntimeError(  # pylint: disable=broad-exception-raised
                    #     "Environment misconfiguration, default PlatformFeePricingPlan "
                    #     f"for fee mode {mode} doesn't exist! Add it in admin to fix this error."
                    # )

        # For PX_FULL_COST plan/fees are None (POS plans carry fees)
        return PlatformFeeSettingsEntity(
            mode=mode,
            payment_provision_percentage_merchant=(
                plan.payment_provision_percentage_merchant if plan else None
            ),
            payment_provision_fee_merchant=(
                plan.payment_provision_fee_merchant if plan else None
            ),
            payment_provision_percentage_customer=(
                plan.payment_provision_percentage_customer if plan else None
            ),
            payment_provision_fee_customer=(
                plan.payment_provision_fee_customer if plan else None
            ),
        )

    @staticmethod
    def get_all_platform_fee_pricing() -> dict[PlatformFeeMode, PlatformFeeSettingsEntity]:
        r = {}
        for mode in PlatformFeeMode:
            if mode == PlatformFeeMode.PX_FULL_COST:
                plan = None
            else:
                plan = PlatformFeePricingPlan.objects.filter(
                    default=True,
                    business_id__isnull=True,
                    fee_mode=mode,
                ).first()
                if plan is None:
                    return
                    # raise RuntimeError(  # pylint: disable=broad-exception-raised
                    #     "Environment misconfiguration, default PlatformFeePricingPlan "
                    #     f"for fee mode {mode} doesn't exist! Add it in admin to fix this error."
                    # )
            r[mode] = PlatformFeeSettingsEntity(
                mode=mode,
                payment_provision_percentage_merchant=(
                    plan.payment_provision_percentage_merchant if plan else None
                ),
                payment_provision_fee_merchant=(
                    plan.payment_provision_fee_merchant if plan else None
                ),
                payment_provision_percentage_customer=(
                    plan.payment_provision_percentage_customer if plan else None
                ),
                payment_provision_fee_customer=(
                    plan.payment_provision_fee_customer if plan else None
                ),
            )
        return r
