import uuid
from typing import Protocol

from .dto.draft import Draft


class DraftRepository(Protocol):
    def get(self, draft_id: uuid.UUID) -> Draft | None: ...

    def insert(self, draft: Draft) -> None: ...

    def update(self, draft: Draft) -> None: ...

    def remove(self, draft_id: uuid.UUID) -> None: ...

    # older_than - in minutes
    def get_oldest_draft_ids(self, older_than: int, limit: int) -> list[uuid.UUID]: ...


class AppointmentInfoRepository(Protocol):
    def insert(self, appointment_id: int) -> None: ...
