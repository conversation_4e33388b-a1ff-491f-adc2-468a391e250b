import pytest

from ...dto import Occupancy, ResourceBookedOccupancy
from ..time_slots import TimeSlotsCalculator
from ..types import Bin, BinSlot
from .commons import EXAMPLE_BOOKED_OCCUPANCY

EMPTY_OCCUPANCY = Occupancy("0" * 24)
EMPTY_BOOKED_OCCUPANCY = ResourceBookedOccupancy(
    booking=EMPTY_OCCUPANCY,
    padding=EMPTY_OCCUPANCY,
)


@pytest.mark.parametrize(
    [
        "service_slots",
        "booked_occupancy",
        "offset",
        "expected",
    ],
    [
        pytest.param(
            [],
            EMPTY_BOOKED_OCCUPANCY,
            0,
            [],
            id="no service slots",
        ),
        pytest.param(
            [(0, 19)],
            EMPTY_BOOKED_OCCUPANCY,
            0,
            [0, 1, 2, 3, 4, 5],
            id="single slot",
        ),
        pytest.param(
            [(0, 7), (14, 19)],
            EMPTY_BOOKED_OCCUPANCY,
            0,
            [0, 1, 2, 3, 4, 5],
            id="multiple slot",
        ),
        pytest.param(
            [(0, 19)],
            ResourceBookedOccupancy(
                booking=Occupancy("11111 00000 00000 00000 0000"),
                padding=EMPTY_OCCUPANCY,
            ),
            0,
            [5],
            id="single slot, only one match",
        ),
        pytest.param(
            [(0, 7), (14, 19)],
            ResourceBookedOccupancy(
                booking=Occupancy("11111 00000 00111 00000 0000"),
                padding=EMPTY_OCCUPANCY,
            ),
            0,
            [5],
            id="multiple slots, only one, exact match",
        ),
        pytest.param(
            [(0, 7), (14, 19)],
            ResourceBookedOccupancy(
                booking=Occupancy("11111 00000 00111 10000 0001"),
                padding=EMPTY_OCCUPANCY,
            ),
            0,
            [],
            id="multiple slots, no match",
        ),
        pytest.param(
            [(0, 3), (5, 8)],
            EXAMPLE_BOOKED_OCCUPANCY,
            0,
            [6, 11, 12],
            id="multiple slots, multiple matches",
        ),
        pytest.param(
            [(0, 3), (5, 8)],
            EXAMPLE_BOOKED_OCCUPANCY,
            7,
            [11, 12],
            id="multiple slots, multiple matches, with offset",
        ),
    ],
)
def test__match_slots(
    service_slots: list[BinSlot],
    booked_occupancy: ResourceBookedOccupancy,
    offset: Bin,
    expected: list[Bin],
) -> None:
    actual = list(
        TimeSlotsCalculator._match_slots(  # pylint: disable=protected-access
            service_slots=service_slots,
            booked_occupancy=booked_occupancy,
            offset=offset,
        )
    )
    assert actual == expected


@pytest.mark.parametrize(
    [
        "padding_before",
        "padding_after",
        "expected",
    ],
    [
        pytest.param(
            0,
            0,
            [6, 11, 12, 16, 17],
            id="no padding",
        ),
        pytest.param(
            1,
            0,
            [6, 12, 16, 17],
            id="padding before",
        ),
        pytest.param(
            0,
            1,
            [11, 12, 16],
            id="padding after",
        ),
        pytest.param(
            1,
            1,
            [12, 16],
            id="padding before and after",
        ),
    ],
)
def test__match_slots__paddings__single_booking(
    padding_before: Bin,
    padding_after: Bin,
    expected: list[Bin],
) -> None:
    actual = list(
        TimeSlotsCalculator._match_slots(  # pylint: disable=protected-access
            service_slots=[(0, 3)],
            booked_occupancy=EXAMPLE_BOOKED_OCCUPANCY,
            padding_before=padding_before,
            padding_after=padding_after,
        )
    )
    assert actual == expected


@pytest.mark.parametrize(
    [
        "padding_before",
        "padding_after",
        "expected",
    ],
    [
        pytest.param(
            0,
            0,
            [6, 11, 12],
            id="no padding",
        ),
        pytest.param(
            1,
            0,
            [6, 12],
            id="padding before",
        ),
        pytest.param(
            0,
            1,
            [6, 11],
            id="padding after",
        ),
        pytest.param(
            1,
            1,
            [6],
            id="padding before and after",
        ),
    ],
)
def test__match_slots__paddings__single_booking_with_gap(
    padding_before: Bin,
    padding_after: Bin,
    expected: list[Bin],
) -> None:
    actual = list(
        TimeSlotsCalculator._match_slots(  # pylint: disable=protected-access
            service_slots=[(0, 3), (5, 8)],
            booked_occupancy=EXAMPLE_BOOKED_OCCUPANCY,
            padding_before=padding_before,
            padding_after=padding_after,
        )
    )
    assert actual == expected
