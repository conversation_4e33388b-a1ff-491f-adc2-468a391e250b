import uuid
from dataclasses import dataclass
from datetime import datetime


@dataclass
class ProductAnalyticsParams:  # pylint: disable=too-many-instance-attributes
    draft_id: uuid.UUID | None
    business_id: int
    user_id: int | None
    fingerprint: str
    country_code: str
    booking_source: str | None
    traffic_source: str | None
    traffic_channel: str | None
    traffic_initial_referrer: str | None
    traffic_referrer: str | None
    task_id: str | None
    platform: str | None
    business_primary_category: str | None
    app_version: str | None
    device_type: str | None
    appointment_id: int | None = None
    created: datetime | None = None
    sent_from_script: bool | None = None


@dataclass
class MarketingAnalyticsParams:
    properties: dict | None = None
    context: dict | None = None
    integrations: dict | None = None
    traits: dict | None = None
