import pytest
from django.test import override_settings

from country_config import Country


from webapps.notification.base import Recipient
from webapps.notification.channels import SMSChannelMarketing
from webapps.user.models import UserProfile


class TestSMSChannelMarketing:
    @pytest.fixture
    def channel(self) -> SMSChannelMarketing:
        return SMSChannelMarketing(None)

    @pytest.fixture
    def recipient(self) -> Recipient:
        return Recipient(
            name='Recipient', email='', profile_type=UserProfile.Type.CUSTOMER, phone='123456789'
        )

    @override_settings(API_COUNTRY=Country.US)
    def test_channel_sms_marketing_valid_without_consent(self, recipient, channel):
        assert not recipient.sms_marketing_consent
        assert not channel.is_valid_recipient(recipient)

    @override_settings(API_COUNTRY=Country.CA)
    def test_channel_sms_marketing_valid_without_consent_country_ca(self, recipient, channel):
        assert not recipient.sms_marketing_consent
        assert not channel.is_valid_recipient(recipient)

    @override_settings(API_COUNTRY=Country.PL)
    def test_channel_sms_marketing_valid_without_consent_country_pl(self, recipient, channel):
        assert not recipient.sms_marketing_consent
        assert channel.is_valid_recipient(recipient)

    @override_settings(API_COUNTRY=Country.US)
    def test_channel_sms_marketing_valid_with_consent(self, recipient, channel):
        recipient.sms_marketing_consent = True
        assert channel.is_valid_recipient(recipient)

    @override_settings(API_COUNTRY=Country.PL)
    def test_should_be_invalid_if_blocked_phone_number(self, recipient, channel):
        assert recipient.has_blocked_phone_number is False
        assert channel.is_valid_recipient(recipient)
        recipient.has_blocked_phone_number = True
        assert not channel.is_valid_recipient(recipient)
