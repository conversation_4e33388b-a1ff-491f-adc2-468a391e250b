from random import choice
from string import ascii_lowercase

import rest_framework.status
from mock import patch
from parameterized import parameterized
from rest_framework.test import APITestCase
from segment.analytics import Client

from lib.baker_utils import get_or_create_booking_source

from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import bci_recipe
from webapps.consts import WEB
from webapps.marketing.models import Unsubscribed
from webapps.marketing.utils import unsubscribe_token_encode
from webapps.user.baker_recipes import user_recipe
from webapps.user.models import EmailToken, UnsubscribedEmail


class TestUnsubscribe(APITestCase):
    URL = '/api/us/2/customer_api/unsubscribe/'

    @classmethod
    def setUpTestData(cls):
        cls.user = user_recipe.make(email='<EMAIL>')
        token = EmailToken.create_token(cls.user.email)
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=WEB,
        )
        cls.data = {'token': token}
        cls.post_kwargs = {'format': 'json', 'HTTP_X_API_KEY': cls.booking_source.api_key}

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_unsubscribe(self, analytics_track_mock, analytics_identify_mock):
        assert UnsubscribedEmail.can_send_email(self.user.email)

        response = self.client.post(self.URL, data=self.data, **self.post_kwargs)

        assert response.status_code == rest_framework.status.HTTP_201_CREATED
        assert UnsubscribedEmail.objects.count() == 1
        assert not UnsubscribedEmail.can_send_email(self.user.email)
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Contact_Preferencess_Updated',
                'properties': {
                    'email': self.user.email,
                    'user_unsubscribedemail': False,
                    'marketing_agreement': False,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'email': self.user.email,
                    'user_role': 'Customer',
                    'user_unsubscribedemail': False,
                    'marketing_agreement': False,
                },
            },
        )

    def check_asserts(self, response, mock, call_count):
        assert response.status_code == rest_framework.status.HTTP_201_CREATED
        mock.assert_called_with(
            user_id=self.user.id,
            context={'session_user_id': self.user.id, 'source_id': self.booking_source.id},
        )
        assert mock.call_count == call_count
        assert UnsubscribedEmail.objects.count() == 1
        assert not UnsubscribedEmail.can_send_email(self.user.email)

    @patch('webapps.segment.tasks.analytics_contact_preferences_updated_task.delay')
    def test_unsubscribe_twice(self, celery_task_delay_mock):
        assert UnsubscribedEmail.can_send_email(self.user.email)

        response = self.client.post(self.URL, data=self.data, **self.post_kwargs)
        self.check_asserts(response, celery_task_delay_mock, call_count=1)
        response = self.client.post(self.URL, data=self.data, **self.post_kwargs)
        self.check_asserts(response, celery_task_delay_mock, call_count=2)

    def test_token_too_long(self):
        assert UnsubscribedEmail.can_send_email(self.user.email)
        token = ''.join(choice(ascii_lowercase) for i in range(46))

        response = self.client.post(self.URL, data={'token': token}, **self.post_kwargs)

        assert UnsubscribedEmail.can_send_email(self.user.email)
        assert response.status_code == rest_framework.status.HTTP_400_BAD_REQUEST
        assert response.data == {
            "errors": [
                {
                    "code": "required",
                    "type": "validation",
                    "field": "token",
                    "description": "Token is invalid",
                }
            ]
        }

    def test_no_token(self):
        assert UnsubscribedEmail.can_send_email(self.user.email)

        response = self.client.post(self.URL, **self.post_kwargs)

        assert UnsubscribedEmail.can_send_email(self.user.email)
        assert response.status_code == rest_framework.status.HTTP_400_BAD_REQUEST
        assert response.data == {
            "errors": [
                {
                    "code": "required",
                    "type": "validation",
                    "field": "token",
                    "description": "Token is invalid",
                }
            ]
        }


class TestUnsubscribeBlasts(APITestCase):
    URL = '/api/us/2/customer_api/blasts/unsubscribe/'

    @classmethod
    def setUpTestData(cls):
        cls.bci = bci_recipe.make()
        cls.bci.reindex()
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=WEB,
        )
        cls.post_kwargs = {'format': 'json', 'HTTP_X_API_KEY': cls.booking_source.api_key}
        cls.token = unsubscribe_token_encode(cls.bci)

    def check_asserts(self, response, bci):
        assert response.status_code == rest_framework.status.HTTP_201_CREATED
        assert response.data == {'success': 'true'}
        unsub = Unsubscribed.objects.all()
        assert len(unsub) == 1
        assert unsub[0].bci_id == bci.id

    def test_unsubscribe_blasts_with_post_data(self):
        assert Unsubscribed.objects.count() == 0

        response = self.client.post(self.URL, data={'token': self.token}, **self.post_kwargs)

        self.check_asserts(response, self.bci)

    def test_unsubscribe_blasts_with_query_data(self):
        assert Unsubscribed.objects.count() == 0

        response = self.client.post(f'{self.URL}?token={self.token}', data={})

        self.check_asserts(response, self.bci)

    def test_bci_reindex_after_unsubscribe(self):
        assert Unsubscribed.objects.count() == 0
        assert not self.bci.get_document().blasts_unsubscribed

        response = self.client.post(self.URL, data={'token': self.token}, **self.post_kwargs)

        assert self.bci.get_document().blasts_unsubscribed
        self.check_asserts(response, self.bci)

    def test_unsubscribe_blasts_no_token(self):
        assert Unsubscribed.objects.count() == 0

        response = self.client.post(self.URL, **self.post_kwargs)

        assert response.status_code == rest_framework.status.HTTP_400_BAD_REQUEST
        assert response.data == {
            "errors": [
                {
                    "code": "required",
                    "type": "validation",
                    "field": "token",
                    "description": "Token not provided",
                },
            ],
        }
        assert Unsubscribed.objects.count() == 0

    @parameterized.expand([(None,), ('',)])
    def test_unsubscribe_blasts_empty_token(self, token):
        assert Unsubscribed.objects.count() == 0

        response = self.client.post(self.URL, data={'token': token}, **self.post_kwargs)

        assert response.status_code == rest_framework.status.HTTP_400_BAD_REQUEST
        assert response.data == {
            "errors": [
                {
                    "code": "required",
                    "type": "validation",
                    "field": "token",
                    "description": "Token not provided",
                },
            ],
        }
        assert Unsubscribed.objects.count() == 0

    def test_unsubscribe_blasts_invalid_token(self):
        assert Unsubscribed.objects.count() == 0

        response = self.client.post(self.URL, data={'token': 'invalid token'}, **self.post_kwargs)

        assert response.status_code == rest_framework.status.HTTP_400_BAD_REQUEST
        assert response.data == {"errors": ["Invalid token"]}
        assert Unsubscribed.objects.count() == 0

    def test_unsubscribe_blasts_twice(self):
        assert Unsubscribed.objects.count() == 0

        response = self.client.post(self.URL, data={'token': self.token}, **self.post_kwargs)
        self.check_asserts(response, self.bci)
        response = self.client.post(self.URL, data={'token': self.token}, **self.post_kwargs)
        self.check_asserts(response, self.bci)
