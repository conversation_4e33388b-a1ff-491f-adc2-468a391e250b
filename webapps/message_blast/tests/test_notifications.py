# pylint: disable=protected-access

from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from django.test import override_settings
from model_bakery import baker

from country_config import Country
from lib.feature_flag.feature.notification import RemoveFRSuffixFromSMSBodyFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.business_customer_info.enums import BCIGroupName
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.message_blast.channels import MessageBlastChannelType
from webapps.message_blast.consts import BLAST_SMS_FR_SHORTCODE
from webapps.message_blast.models import Blocked<PERSON><PERSON><PERSON><PERSON>ber, MessageBlast, SMSBlastMarketingConsent
from webapps.message_blast.notifications import (
    BlastTestStaffer,
    MessageBlastNotification,
    MessageBlastReportNotification,
    RecipientsResolver,
    RecipientsSelectionData,
    _get_unsubscribe_api_url,
)
from webapps.notification.base import Channel
from webapps.notification.channels import SMSChannelMarketing
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.enums import NotificationSendStatus
from webapps.notification.models import UserNotification
from webapps.user.models import User


@pytest.mark.django_db
def test_resolve_clients(business, bci_email, bci_email_sms, bci_email_push):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email.id, bci_email_sms.id, bci_email_push.id],
        business=business,
    )

    notification = MessageBlastNotification(message_blast)
    channel_selector = notification.channel_selector(notification)
    recipients_by_channel = channel_selector.get_recipients_by_channel(
        [channel(notification) for channel in notification.channels],
        notification.resolved_recipients,
    )
    assert len(recipients_by_channel[Channel.Type.PUSH]) == 1

    sms_recipients = recipients_by_channel[Channel.Type.SMS]
    assert len(sms_recipients) == 1
    assert sms_recipients[0].phone == bci_email_sms.cell_phone

    email_recipients = recipients_by_channel[Channel.Type.EMAIL]
    assert len(email_recipients) == 3
    assert {x.email for x in email_recipients} == {
        bci_email.email,
        bci_email_sms.email,
        bci_email_push.user.email,
    }


@pytest.mark.parametrize(
    'channel_priority, results',
    (
        pytest.param(
            {'default': True, 'sms': False, 'prefer_push': False, 'push': False, 'email': False},
            {'sms': 1, 'push': 2, 'email': 4},
            id='default, max effective',
        ),
        pytest.param(
            {'default': False, 'sms': True, 'prefer_push': False, 'push': False, 'email': False},
            {'sms': 2, 'push': 0, 'email': 0},
            id='only sms',
        ),
        pytest.param(
            {'default': False, 'sms': False, 'prefer_push': False, 'push': True, 'email': False},
            {'sms': 0, 'push': 2, 'email': 0},
            id='only push',
        ),
        pytest.param(
            {'default': False, 'sms': False, 'prefer_push': False, 'push': False, 'email': True},
            {'sms': 0, 'push': 0, 'email': 4},
            id='only email',
        ),
        pytest.param(
            {'default': False, 'sms': True, 'prefer_push': True, 'push': True, 'email': False},
            {'sms': 1, 'push': 2, 'email': 0},
            id='sms, push (prefer push)',
        ),
        pytest.param(
            {'default': False, 'sms': True, 'prefer_push': False, 'push': True, 'email': False},
            {'sms': 2, 'push': 2, 'email': 0},
            id='sms, push (not prefer push, double channel)',
        ),
    ),
)
@pytest.mark.django_db
def test_resolve_clients_and_channels(channel_priority, results, business, bci_ids):
    message_blast = baker.make(
        MessageBlast, bcis=bci_ids, business=business, channel_priority=channel_priority
    )

    notification = MessageBlastNotification(message_blast)
    channel_selector = notification.channel_selector(notification)
    recipients_by_channel = channel_selector.get_recipients_by_channel(
        [channel(notification) for channel in notification.channels],
        notification.resolved_recipients,
    )
    assert len(recipients_by_channel[Channel.Type.SMS]) == results['sms']
    assert len(recipients_by_channel[Channel.Type.PUSH]) == results['push']
    assert len(recipients_by_channel[Channel.Type.EMAIL]) == results['email']


@pytest.mark.django_db
@patch('webapps.notification.models.NotificationSMSStatistics.sms_limits', return_value=0)
def test_resolve_clients_out_of_sms(_mock, business, bci_email, bci_email_sms, bci_email_push):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email.id, bci_email_sms.id, bci_email_push.id],
        business=business,
    )

    notification = MessageBlastNotification(message_blast)
    channel_selector = notification.channel_selector(notification)
    recipients_by_channel = channel_selector.get_recipients_by_channel(
        [channel(notification) for channel in notification.channels],
        notification.resolved_recipients,
    )
    assert len(recipients_by_channel[Channel.Type.PUSH]) == 1

    sms_recipients = recipients_by_channel[Channel.Type.SMS]
    assert len(sms_recipients) == 0
    assert channel_selector.out_of_sms is True

    email_recipients = recipients_by_channel[Channel.Type.EMAIL]
    assert len(email_recipients) == 3
    assert {x.email for x in email_recipients} == {
        bci_email.email,
        bci_email_sms.email,
        bci_email_push.user.email,
    }


@pytest.mark.django_db
def test_remove_duplicated_recipients_for_email_channel(
    business, bci_unique_email, bci_common_email, bci_common_email_duplicate
):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_common_email.id, bci_common_email_duplicate.id, bci_unique_email.id],
        business=business,
    )
    notification = MessageBlastNotification(message_blast)
    channel_selector = notification.channel_selector(notification)
    recipients_by_channel = channel_selector.get_recipients_by_channel(
        [channel(notification) for channel in notification.channels],
        notification.resolved_recipients,
    )
    email_recipients = recipients_by_channel[Channel.Type.EMAIL]
    bcis = {recipient.customer_id for recipient in email_recipients}
    assert len(email_recipients) == 2
    assert bcis == {bci_common_email.id, bci_unique_email.id}


@pytest.mark.django_db
def test_remove_duplicated_recipients_for_sms_channel(
    business, bci_unique_phone, bci_common_phone, bci_common_phone_duplicate
):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_common_phone.id, bci_common_phone_duplicate.id, bci_unique_phone.id],
        business=business,
    )
    notification = MessageBlastNotification(message_blast)
    channel_selector = notification.channel_selector(notification)
    recipients_by_channel = channel_selector.get_recipients_by_channel(
        [channel(notification) for channel in notification.channels],
        notification.resolved_recipients,
    )
    sms_recipients = recipients_by_channel[Channel.Type.SMS]
    bcis = {recipient.customer_id for recipient in sms_recipients}
    assert len(sms_recipients) == 2
    assert bcis == {bci_common_phone.id, bci_unique_phone.id}


@pytest.mark.django_db
def test_get_sms_content_with_subdomain(business_with_subdomain, bci_email_sms):
    message_blast = baker.make(
        MessageBlast,
        bcis=[
            bci_email_sms.id,
        ],
        business=business_with_subdomain,
        body_short=(
            f'Discount 10% if you book now! Best deeplinker! Book now!'
            f' {business_with_subdomain.get_seo_url()}'
        ),
    )
    notification = MessageBlastNotification(message_blast)
    sms_content = notification.get_sms_content()
    assert sms_content == message_blast.body_short.replace(
        business_with_subdomain.get_seo_url(),
        business_with_subdomain.get_seo_url(
            channel=Channel.Type.SMS,
            is_blast=True,
        ),
    )


@pytest.mark.django_db
def test_get_sms_content_with_subdomain_remove_protocol(business_with_subdomain, bci_email_sms):
    subdomain = business_with_subdomain.get_seo_url()
    message_blast = baker.make(
        MessageBlast,
        bcis=[
            bci_email_sms.id,
        ],
        business=business_with_subdomain,
        body_short=(f'Discount 10% if you book now! Best deeplinker! Book now! {subdomain}'),
    )
    notification = MessageBlastNotification(message_blast)
    sms_content = notification.get_sms_content()
    assert sms_content == message_blast.body_short.replace(
        subdomain,
        f'{subdomain.removeprefix("https://")}/b/',
    )


@pytest.mark.django_db
def test_get_sms_content_without_subdomain(business_with_subdomain, bci_email_sms):
    message_blast = baker.make(
        MessageBlast,
        bcis=[
            bci_email_sms.id,
        ],
        business=business_with_subdomain,
        body_short=('Discount 10% if you book now! Best deeplinker! Book now!'),
    )
    notification = MessageBlastNotification(message_blast)
    sms_content = notification.get_sms_content()
    assert sms_content == message_blast.body_short


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.FR)
def test_get_sms_content_shortcode_fr(business_with_subdomain, bci_email_sms):
    message_blast = baker.make(
        MessageBlast,
        bcis=[
            bci_email_sms.id,
        ],
        business=business_with_subdomain,
        body_short=('Discount 10% if you book now! Best deeplinker! Book now!'),
    )
    notification = MessageBlastNotification(message_blast)
    sms_content = notification.get_sms_content()
    assert sms_content.endswith(BLAST_SMS_FR_SHORTCODE)


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.FR)
@override_eppo_feature_flag({RemoveFRSuffixFromSMSBodyFlag.flag_name: True})
def test_get_sms_content_shortcode_removed_in_fr(business_with_subdomain, bci_email_sms):
    message_blast = baker.make(
        MessageBlast,
        bcis=[
            bci_email_sms.id,
        ],
        business=business_with_subdomain,
        body_short=('Discount 10% if you book now! Best deeplinker! Book now!'),
    )
    notification = MessageBlastNotification(message_blast)
    sms_content = notification.get_sms_content()
    assert not sms_content.endswith(BLAST_SMS_FR_SHORTCODE)


@pytest.mark.django_db
@patch('webapps.notification.models.NotificationSMSStatistics.sms_limits', return_value=100)
def test_resolve_clients_via_resolver(
    _sms_limits_mock, business, bci_email, bci_email_sms, bci_email_push
):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email.id, bci_email_sms.id, bci_email_push.id],
        business=business,
    )

    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=message_blast.bcis,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)

    assert len(resolver.push_recipients) == 1

    assert len(resolver.sms_recipients) == 1
    assert resolver.sms_recipients[0].phone == bci_email_sms.cell_phone

    assert len(resolver.email_recipients) == 3
    assert {x.email for x in resolver.email_recipients} == {
        bci_email.email,
        bci_email_sms.email,
        bci_email_push.user.email,
    }


@pytest.mark.django_db
@patch('webapps.notification.models.NotificationSMSStatistics.sms_limits', return_value=0)
def test_resolve_clients_via_resolver_out_of_sms(_sms_limits_mock, business, bci_ids):
    message_blast = baker.make(
        MessageBlast,
        bcis=bci_ids,
        business=business,
    )
    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=message_blast.bcis,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)

    assert len(resolver.push_recipients) == 2
    assert resolver.out_of_sms is True
    assert len(resolver.sms_recipients) == 0
    assert len(resolver.email_recipients) == 4


@pytest.mark.django_db
@pytest.mark.parametrize('with_user', (False, True))
def test_resolve_clients_blocked_phone_number_should_affect_estimated_sms(
    business, bci_email_sms, bci_email_sms_with_user, with_user
):
    if with_user:
        bci = bci_email_sms_with_user
        cell_phone = bci.user.cell_phone
        assert bci.user is not None
    else:
        bci = bci_email_sms
        cell_phone = bci.cell_phone
        assert bci.user is None

    bci.reindex(refresh_index=True)
    assert not BlockedPhoneNumber.objects.filter(cell_phone=cell_phone).exists()

    message_blast = baker.make(
        MessageBlast,
        bcis=[bci.id],
        business=business,
    )
    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=message_blast.bcis,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)

    assert len(resolver.sms_recipients) == 1
    assert len(resolver.email_recipients) == 1

    baker.make(BlockedPhoneNumber, cell_phone=cell_phone)

    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)
    assert len(resolver.sms_recipients) == 0
    assert len(resolver.email_recipients) == 1


@pytest.mark.django_db
def test_message_blast_report_notification_receivers(bci_email_push_sms, bci_email_sms):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email_push_sms.id, bci_email_sms.id],
        title='title',
        body='body',
        body_short='body short',
        business=bci_email_push_sms.business,
        channel_priority={  # to send all channels
            MessageBlastChannelType.DEFAULT.value: False,
            MessageBlastChannelType.SMS.value: True,
            MessageBlastChannelType.PREFER_PUSH.value: False,
            MessageBlastChannelType.PUSH.value: True,
            MessageBlastChannelType.EMAIL.value: True,
        },
    )
    NotificationHistoryDocument(
        **dict(
            task_id=f'MessageBlastNotification, {message_blast.id}',
            customer_card_id=bci_email_push_sms.id,
            type=UserNotification.EMAIL_NOTIFICATION,
            status=NotificationSendStatus.GATEWAY_SUCCESS,
        )
    ).save(refresh=True)

    NotificationHistoryDocument(
        **dict(
            task_id=f'MessageBlastNotification, {message_blast.id}',
            customer_card_id=bci_email_push_sms.id,
            type=UserNotification.SMS_NOTIFICATION,
            status=NotificationSendStatus.WEBHOOK_SUCCESS,
        )
    ).save(refresh=True)

    NotificationHistoryDocument(
        **dict(
            task_id=f'MessageBlastNotification, {message_blast.id}',
            customer_card_id=bci_email_sms.id,
            type=UserNotification.SMS_NOTIFICATION,
            status=NotificationSendStatus.GATEWAY_SUCCESS,
        )
    ).save(refresh=True)

    NotificationHistoryDocument(
        **dict(
            task_id=f'MessageBlastNotification, {message_blast.id}',
            status=None,
            type=UserNotification.PUSH_NOTIFICATION,
            metadata={'customers': [[bci_email_push_sms.id, 0]]},
        )
    ).save(refresh=True)

    notification = MessageBlastReportNotification(message_blast)
    recipients_report = notification.recipients_report

    assert len(recipients_report[MessageBlastChannelType.SMS]) == 2
    assert len(recipients_report[MessageBlastChannelType.EMAIL]) == 1
    assert len(recipients_report[MessageBlastChannelType.PUSH]) == 1


@pytest.mark.django_db
def test_should_skip_notification_if_business_is_not_active(business, bci_email):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email.id],
        business=business,
    )

    assert business.active
    assert not MessageBlastNotification(message_blast).should_skip_with_plea()[0]

    business.active = False
    business.status = Business.Status.CHURNED
    business.save()

    assert not business.active
    assert MessageBlastNotification(message_blast).should_skip_with_plea()[0]


@pytest.mark.django_db
def test_filter_out_recipients_no_visible_in_business(business, bci_email_not_visible_with_user):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email_not_visible_with_user.id],
        business=business,
    )

    notification = MessageBlastNotification(message_blast)
    assert len(notification.resolved_recipients) == 0


@pytest.mark.django_db
def test_testmessage_get_recipients():
    user = baker.make(User)
    blast = BlastTestStaffer(notification=MagicMock())
    blast.message_blast.sent_by = user
    recipients = blast.get_recipients()
    assert recipients[0].sms_marketing_consent is True


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.US)
def test_filter_out_sms_recipients_no_double_opt_in_consent(business):
    # Create a BCI without SMS marketing consent for this test
    bci_no_consent = baker.make(
        BusinessCustomerInfo,
        email='<EMAIL>',
        cell_phone='+**************',
        business=business,
        web_communication_agreement=True,  # Required for GDPR compliance
    )

    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_no_consent.id],
        business=business,
    )
    notification = MessageBlastNotification(message_blast)
    channel_selector = notification.channel_selector(notification)
    recipients = channel_selector.get_recipients_by_channel(
        [SMSChannelMarketing(notification)],
        notification.resolved_recipients,
    )

    assert len(recipients) == 0


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.US)
def test_filter_out_sms_recipients_double_opt_in_consented(
    business,
    bci_email_sms,
    bci_unique_phone,
):
    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=True,
        bci=bci_email_sms,
        cell_phone=bci_email_sms.cell_phone,
    )
    baker.make(
        SMSBlastMarketingConsent,
        business=business,
        consented=False,
        bci=bci_unique_phone,
        cell_phone=bci_unique_phone.cell_phone,
    )
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email_sms.id, bci_unique_phone.id],
        business=business,
    )
    notification = MessageBlastNotification(message_blast)
    channel_selector = notification.channel_selector(notification)
    recipients = channel_selector.get_recipients_by_channel(
        [SMSChannelMarketing(notification)],
        notification.resolved_recipients,
    )

    assert len(recipients) == 1


@pytest.mark.django_db
@patch.object(Business, 'get_mp_deeplink', Mock())
def test_should_attach_business_link(business):
    message_blast = baker.make(
        MessageBlast,
        business=business,
        body='',
        title='',
    )
    notification = MessageBlastNotification(message_blast)

    assert 'business_link' in notification.get_context()


@pytest.mark.django_db
@patch('webapps.message_blast.notifications.unsubscribe_token_encode')
def test_email_easy_unsubscribe_url(mock_token, business, bci_email):
    message_blast = baker.make(
        MessageBlast,
        bcis=[bci_email.id],
        business=business,
        body='',
        title='',
    )
    mock_token.return_value = '36-MTcxNzU4NjE1OS02MC05MTI1NDI2NDQ='

    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=message_blast.bcis,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)
    recipient = resolver.email_recipients[0]

    notification = MessageBlastNotification(message_blast)
    assert notification.email_easy_unsubscribe_url is None

    with notification.set_recipient(recipient):
        email_content = notification.get_email_content()
        unsubscribe_url = notification.get_email_unsubscribe_url()
        email_easy_unsubscribe_url = notification.email_easy_unsubscribe_url
        assert email_easy_unsubscribe_url
        assert unsubscribe_url
        assert unsubscribe_url in email_content


@pytest.mark.django_db
def test_get_recipients(business):
    bci_1 = baker.make(
        BusinessCustomerInfo,
        business=business,
        web_communication_agreement=True,  # Required for GDPR compliance
    )
    bci_2 = baker.make(
        BusinessCustomerInfo,
        business=business,
        web_communication_agreement=True,  # Required for GDPR compliance
    )

    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=[],
        ids=[bci_1.id],
        excluded_ids=[bci_1.id, bci_2.id],
        with_agreements=True,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)
    result = resolver._get_recipients()
    assert len(result) == 0

    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=[],
        ids=[bci_1.id],
        excluded_ids=[bci_2.id],
        with_agreements=True,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)
    result = resolver._get_recipients()
    assert len(result) == 1
    assert result[0].customer_id == bci_1.id

    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=[],
        ids=[bci_1.id],
        excluded_ids=[],
        with_agreements=True,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)
    result = resolver._get_recipients()
    assert len(result) == 1
    assert result[0].customer_id == bci_1.id

    recipients_data = RecipientsSelectionData(
        group_name=BCIGroupName.ALL_CUSTOMERS,
        elastic_bci_ids=[],
        ids=[],
        excluded_ids=[bci_2.id],
        with_agreements=True,
    )
    resolver = RecipientsResolver(business=business, recipients_data=recipients_data)
    result = resolver._get_recipients()
    assert len(result) == 1
    assert result[0].customer_id == bci_1.id


@pytest.mark.parametrize(
    'token, query',
    [
        pytest.param('', '', id='no token'),
        pytest.param('abcd-123=', '?token=abcd-123%3D', id='with token'),
        pytest.param('abcd-1234', '?token=abcd-1234', id='with token'),
    ],
)
def test_get_unsubscribe_api_url(token: str, query: str):
    url = f'http://localhost:8888/api/us/2/customer_api/blasts/unsubscribe/{query}'
    assert _get_unsubscribe_api_url(token) == url
