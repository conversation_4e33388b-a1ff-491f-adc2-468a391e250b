import traceback
import uuid
from typing import Optional

from django.db import transaction
from django.db.models import QuerySet

from lib.point_of_sale.enums import BasketType
from lib.tools import tznow
from webapps.point_of_sale.models import (
    Basket,
    BasketHistory,
)
from webapps.point_of_sale.serializers import BasketHistorySerializer


class BasketService:
    @staticmethod
    @transaction.atomic
    def create_basket(
        business_id: str,
        customer_card_id: int | None,
        basket_type: BasketType = BasketType.PAYMENT,
        metadata: dict = None,
    ) -> Basket:
        """
        Creates empty basket related to provided business.
        Save to history action included.

        :param business_id: id of Business related to Basket.
        :param customer_card_id: id of BusinessCustomerInfo
        :param basket_type: BasketType enum
        :param metadata: dict describing additional parameters of basket
        """
        basket = Basket.objects.create(
            business_id=business_id,
            customer_card_id=customer_card_id,
            type=basket_type,
            metadata=metadata if metadata else {},
        )
        BasketService.save_history(basket)
        return basket

    @staticmethod
    def get_basket(
        basket_id: uuid.UUID = None,
        business_id: int = None,
    ) -> Optional[Basket]:
        """
        Gets basket.

        :param basket_id: Id of Basket
        :param business_id: Id of Business
        """
        if not (basket_id or business_id):
            return None

        filters = {}
        if basket_id:
            filters['id'] = basket_id
        if business_id:
            filters['business_id'] = business_id

        return Basket.objects.filter(**filters).first()

    @staticmethod
    def get_customer_baskets(customer_card_id: int) -> QuerySet[Basket]:
        """
        Gets list of baskets related to Customer
        :param customer_card_id: id of BusinessCustomerInfo
        :return: list of Basket
        """
        return Basket.objects.filter(
            customer_card_id=customer_card_id,
        )

    @staticmethod
    def save_history(basket: Basket):
        """
        Serializes basket and all related to it objects and saves it as snapshot of history.

        :param basket: Basket which will be serialized and saved to BasketHistory object.
        """
        basket = (
            Basket.objects.filter(
                id=basket.id,
            )
            .prefetch_related(
                'tips',
                'payments',
                'items',
                'items__related_items',
            )
            .first()
        )
        serializer = BasketHistorySerializer(instance=basket)
        BasketHistory.objects.create(
            data=serializer.data,
            basket_id=basket.id,
            traceback=traceback.format_stack(),
        )

    @staticmethod
    @transaction.atomic
    def archive_basket(basket: Basket):
        """
        Basket should not be removed. We should archive it instead.
        Save to history action included.

        :param basket: Basket which will be archived.
        """
        if basket.archived:
            raise ValueError('Basket cannot be archived twice')

        basket.archived = tznow()
        basket.save(update_fields=['archived'])
        BasketService.save_history(basket)
