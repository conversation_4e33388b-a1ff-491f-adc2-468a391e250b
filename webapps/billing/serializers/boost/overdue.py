from decimal import Decimal

from django.utils.translation import gettext as _
from rest_framework import serializers

from drf_api.base_serializers import CustomValidationErrorSerializer
from webapps.billing.interfaces import boost
from webapps.billing.models.boost import (
    BillingBoostPaymentRequest,
    BillingBoostTransaction,
)


class BusinessBoostInfoSerializer(serializers.Serializer):
    promoted_before = serializers.BooleanField()
    status = serializers.CharField()
    boost_blocked = serializers.BooleanField()


class BoostOverdueTotalInfoSerializer(serializers.Serializer):
    total_amount = serializers.DecimalField(decimal_places=2, max_digits=10, allow_null=True)
    currency = serializers.CharField(max_length=10)
    business_boost_info = BusinessBoostInfoSerializer(many=False)


class BoostOverdueAppointmentSerializer(serializers.Serializer):
    amount = serializers.DecimalField(decimal_places=2, max_digits=10)
    client_name = serializers.Char<PERSON><PERSON>(max_length=255, allow_null=True, required=False)
    date = serializers.DateField()


class BoostOverdueDetailsSerializer(serializers.Serializer):
    total_amount = serializers.DecimalField(decimal_places=2, max_digits=10)
    currency = serializers.CharField(max_length=10)
    appointments = BoostOverdueAppointmentSerializer(many=True)


class BoostOverdueRetryChargeSerializer(CustomValidationErrorSerializer):
    selected_boost_ids = serializers.ListField(
        child=serializers.CharField(min_length=1, max_length=100),
        required=False,
        allow_null=True,
    )

    def __init__(self, *args, **kwargs):
        self.business_id = kwargs.pop('business_id')
        self.boost_transactions = []
        self.total_amount = Decimal(0)
        self.currency = None
        super().__init__(*args, **kwargs)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        selected_boost_ids = attrs.get('selected_boost_ids')
        if selected_boost_ids and len(selected_boost_ids) != len(set(selected_boost_ids)):
            raise serializers.ValidationError(
                {'selected_boost_ids': _('Duplicated transactions.')},
            )

        boost_transactions = boost.overdue_transactions(business_id=self.business_id)

        if selected_boost_ids:
            selected_boost_transactions = []
            boost_transactions_by_id = {tnx.public_id: tnx for tnx in boost_transactions}
            for selected_boost_id in selected_boost_ids:
                if selected_boost_id not in boost_transactions_by_id:
                    raise serializers.ValidationError(_('At least one transaction is missing.'))
                selected_boost_transactions.append(boost_transactions_by_id[selected_boost_id])
            self.boost_transactions = selected_boost_transactions
        else:
            self.boost_transactions = boost_transactions

        boost_ids = []
        for transaction in self.boost_transactions:
            self.total_amount += transaction.gross_amount
            boost_ids.append(transaction.public_id)

        if not self.total_amount:
            raise serializers.ValidationError(_('You currently have no overdue payments.'))

        if BillingBoostPaymentRequest.objects.currently_processing(business_id=self.business_id):
            raise serializers.ValidationError(
                _(
                    'The payment process has already started. Wait for it to complete or contact '
                    'customer service if the error persists.'
                )
            )

        if BillingBoostTransaction.objects.currently_processing_or_charged(boost_ids=boost_ids):
            raise serializers.ValidationError(
                _(
                    'Some transactions have already been paid. If the error still occurs please '
                    'contact customer service.'
                )
            )

        self.currency = self.boost_transactions[0].currency

        return attrs
