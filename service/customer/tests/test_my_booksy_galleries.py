from datetime import timed<PERSON><PERSON>

import pytest
from django.conf import settings
from django.test import override_settings
from mock import patch
from model_bakery import baker
from rest_framework import status

from conftest_helpers import clean_elastisearch_index_helper
from lib.baker_utils import get_or_create_booking_source
from lib.elasticsearch.consts import ESIndex
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.booking import NailsCampaignOnMyBooksy
from lib.feature_flag.experiment.customer import (
    S4UGalleriesOrderExperiment,
    GalleryNameS4UExperiment,
)
from lib.feature_flag.feature.customer import (
    CustomerMyBooksySugestedForYouGalleryFlag,
    GalleryNameS4UExperimentOnFlag,
)
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import id_to_external_api, tznow
from service.customer.my_booksy import GalleriesType
from service.tests import BaseAsyncHTTPTest, ElasticBaseTestCase, dict_assert
from service.tests.view_item_list_event_base import ViewItemListEventTestCaseMixin
from webapps.booking.models import BookingSources
from webapps.boost.consts import PROMOTED_LABELS, PROMOTED_LABELS_DSA
from webapps.business.baker_recipes import (
    business_recipe,
    category_recipe,
    service_category_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    treatment_recipe,
)
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import BusinessCategory
from webapps.consts import IPHONE, WEB
from webapps.elasticsearch.elastic import ELASTIC
from webapps.experiment_v3.exp import (
    NewOnBooksyGalleryExperiment,
    UTT2InSearch,
)
from webapps.experiment_v3.models import ExperimentSlot
from webapps.images.models import Image
from webapps.segment.enums import BooksyAppVersions, DeviceTypeName
from webapps.segment.utils import get_user_role_by_id
from webapps.user.const import Gender
from webapps.user.models import CustomerFavoriteCategory


@pytest.mark.django_db
class BusinessesGalleryHandlerTestCase(BaseAsyncHTTPTest):
    url = '/customer_api/my_booksy/galleries/?'

    def setUp(self):
        super().setUp()
        NewOnBooksyGalleryExperiment.initialize()

    def tearDown(self):
        super().tearDown()
        # without this line test will be unstable
        ExperimentSlot.objects.all().delete()

    def test_get(self):
        self.user.gender = Gender.Female.value
        self.user.save()

        response = self.fetch(self.url)

        assert response.code == 200

    @patch('lib.tagmanager.client.GTMClient._request_api')
    def test_trigger_customer_app_opened(self, analytics_gtm_mock):
        latitude = 41.82
        longitude = -87.74

        self.customer_booking_src.name = WEB
        self.customer_booking_src.save()

        extra_headers = {
            'X-User-Pseudo-ID': 'TEST.1234',
            'X-API-KEY': self.customer_booking_src.api_key,
        }
        self.fetch(
            self.url + f'geo_location={latitude}%2C{longitude}',
            extra_headers=extra_headers,
        )

        dict_assert(
            analytics_gtm_mock.call_args_list[0][1],
            {
                'endpoint': '/p/collect',
                'method': 'post',
                'payload': {
                    'firebase_auth': {
                        'client_id': 'TEST.1234',
                    },
                    'events': [
                        {
                            'name': 'Customer_App_Opened',
                            'params': {
                                'email': self.user.email,
                                'last_known_latitude': latitude,
                                'last_known_longitude': longitude,
                            },
                        }
                    ],
                    'user_id': id_to_external_api(self.user.id),
                    'user_properties': {
                        'app_version': {'value': BooksyAppVersions.B30},
                        'control_group': {'value': not bool(self.user.id % 100)},
                        'country': {'value': settings.API_COUNTRY},
                        'device_type': {'value': DeviceTypeName.DESKTOP},
                        'logged_in_user_id': {'value': id_to_external_api(self.user.id)},
                        'phone': {'value': self.user.cell_phone or ''},
                        'user_role': {'value': get_user_role_by_id(self.user.id)},
                    },
                },
            },
        )


@pytest.mark.django_db
class BusinessesGalleryHandlerLoggedOutTestCase(BaseAsyncHTTPTest):
    url = '/customer_api/my_booksy/galleries/?'

    def get_headers(self, path):
        headers = super().get_headers(path)
        headers.pop('X-ACCESS-TOKEN', None)
        return headers

    def test_get_without_user(self):
        NewOnBooksyGalleryExperiment.initialize()

        self.user = None
        response = self.fetch(self.url)

        assert response.code == 200


@pytest.mark.django_db
class PromotedLabelsTestCase(ElasticBaseTestCase):
    url = '/customer_api/my_booksy/galleries/?'

    def setUp(self, **kwargs):
        super().setUp(**kwargs)

        # prevent status 500 from unrelated experiments
        NewOnBooksyGalleryExperiment.initialize()
        UTT2InSearch.initialize()

        for id_ in range(2):
            BusinessDocument(
                id=id_,
                visible=True,
                join='business',
                promoted=True,
                is_b_listing=False,
            ).save()

        self.index = ELASTIC.indices[ESIndex.BUSINESS]
        self.index.refresh()

    def tearDown(self):
        ExperimentSlot.objects.all().delete()
        self.prepare_es_index(ESIndex.BUSINESS)
        super().tearDown()

    def test_promoted_labels(self):
        response = self.fetch(self.url)

        assert response.code == status.HTTP_200_OK
        assert 'promoted_labels' in response.json['2']
        assert response.json['2']['businesses'][0]['promoted'] is True
        assert response.json['2']['businesses'][0]['is_recommended'] is False
        assert response.json['2']['promoted_labels'] == PROMOTED_LABELS

    def test_promoted_labels_pl(self):
        with override_settings(API_COUNTRY='pl'):
            response = self.fetch(self.url)

        assert response.code == status.HTTP_200_OK
        assert 'promoted_labels' in response.json['2']
        assert response.json['2']['promoted_labels'] == PROMOTED_LABELS_DSA

    def test_promoted_labels_us(self):
        with override_settings(API_COUNTRY='us'):
            response = self.fetch(self.url)

        assert response.code == status.HTTP_200_OK
        assert 'promoted_labels' in response.json['2']
        assert response.json['2']['promoted_labels'] == PROMOTED_LABELS


class ViewItemListEventTestCase(ViewItemListEventTestCaseMixin, ElasticBaseTestCase):
    url = '/customer_api/my_booksy/galleries/'
    item_list_id = 'recommended'
    item_list_name = 'Recommended'
    request_args = {
        'geo_location': '1.0,-70.0',
        'galleries': 2,
    }

    def results_are_not_empty(self, response):
        galleries = list(response.json.values())
        return len(galleries) == 1 and galleries[0]['type'] == 'recommended'

    def test_empty(self):
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.get()
        assert response.code == status.HTTP_200_OK
        assert not self.results_are_not_empty(response)
        request_api_mock.assert_not_called()


def _create_business(
    active_x_days,
    has_cover_photo,
    categories,
    services_count,
    location_geo,
    primary_category=None,
    active_x_hours=0,
):
    business = business_recipe.make(
        active_from=tznow() - timedelta(days=active_x_days, hours=active_x_hours),
        latitude=location_geo[0],
        longitude=location_geo[1],
        primary_category=primary_category,
    )

    baker.make(
        Image,
        image_url='http://image.url/image.jpg',
        business=business,
        is_cover_photo=has_cover_photo,
    )
    business.categories.add(*categories)
    business.save()
    service_category = service_category_recipe.make(business=business)
    staffer = staffer_recipe.make(business=business)
    for i in range(services_count):
        category = categories[i % len(categories)]
        treatment = treatment_recipe.make(parent=category, name=f'T-{category.internal_name}')
        service = service_recipe.make(
            business=business,
            name=f'Service-{category.internal_name}',
            note=f'test note {i}',
            description=f'test description {i}',
            service_category=service_category,
            treatment=treatment,
        )
        service_variant_recipe.make(service=service)
        service.add_staffers([staffer])
    business_document = business.get_document()
    business_document.save()
    return business


@pytest.fixture(scope='function')
def disable_annoying_task_or_signals_():
    """overwrite fixture which disabled assign_treatments_task"""


@pytest.mark.django_db
class MyBooksyGalleriesSelectedForYouTestCase(BaseAsyncHTTPTest):
    url = (
        '/customer_api/my_booksy/galleries/?min_gallery_businesses=2'
        '&geo_location=52.237489,20.990810'
    )

    def setUp(self, **kwargs):
        super().setUp(**kwargs)
        self.index = ELASTIC.indices[ESIndex.BUSINESS]
        # prevent status 500 from unrelated experiments
        NewOnBooksyGalleryExperiment.initialize()

    @staticmethod
    def _remove_authorization_token(headers):
        headers.pop('X-ACCESS-TOKEN')
        return headers

    def _prepare_data(self):
        categories_types = [
            BusinessCategoryEnum.BARBERS,
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
        ]
        categories = {
            c: category_recipe.make(full_name=c.value, internal_name=c.value)
            for c in categories_types
        }
        business_0 = _create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories=[
                categories[BusinessCategoryEnum.BARBERS],
                categories[BusinessCategoryEnum.HAIR_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        business_1 = _create_business(
            active_x_days=10,
            has_cover_photo=True,
            categories=[categories[BusinessCategoryEnum.HAIR_SALONS]],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        _business_2 = _create_business(
            active_x_days=10,
            has_cover_photo=False,
            categories=[
                categories[BusinessCategoryEnum.BARBERS],
                categories[BusinessCategoryEnum.HAIR_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        _business_3 = _create_business(
            active_x_days=10,
            has_cover_photo=False,
            categories=[categories[BusinessCategoryEnum.PIERCING_CATEGORY]],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        self.index.refresh()

        CustomerFavoriteCategory.objects.create(
            user=self.user,
            category=categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        return business_0, business_1

    @override_eppo_feature_flag(
        {CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True}}
    )
    def test_get_200_with_ff_with_user(self):
        businesses = self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert list(response.json.keys()) == ['4', '5']
        assert response.json['5']['label'] == 'For You'
        assert response.json['5']['type'] == GalleriesType.SELECTED_FOR_YOU_GALLERY.value
        assert {b.id for b in businesses} == {b['id'] for b in response.json['5']['businesses']}
        assert response.json['5']['businesses'][0]['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True}}
    )
    def test_get_200_with_ff_with_user_with_empty_categories(self):
        self._prepare_data()

        response = self.fetch(self.url + '&categories_ids=')

        assert response.code == 200
        assert response.json != {}
        assert list(response.json.keys()) == ['4', '5']

    @override_eppo_feature_flag(
        {
            CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {
                'is_required_user': True,
                'max_km_distance': 0.1,
            }
        }
    )
    def test_get_200_with_ff_limit_distance(self):
        self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert list(response.json.keys()) == ['4']
        assert response.json['4']['type'] != GalleriesType.SELECTED_FOR_YOU_GALLERY.value

    @override_eppo_feature_flag(
        {
            CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {
                'is_required_user': True,
                'max_active_days': 0,
                'max_km_distance': 100,
            }
        }
    )
    def test_get_200_with_ff_limit_max_active_days(self):
        self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert list(response.json.keys()) == ['4']
        assert response.json['4']['type'] != GalleriesType.SELECTED_FOR_YOU_GALLERY.value

    @override_eppo_feature_flag(
        {CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True}}
    )
    def test_get_200_with_ff_with_user_no_location(self):
        self._prepare_data()
        url = self.url.split('&geo_', maxsplit=1)[0]

        response = self.fetch(url)

        assert response.code == 200
        assert response.json == {}

    @override_eppo_feature_flag({CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {}})
    def test_get_200_without_ff_with_user(self):
        self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert list(response.json.keys()) == ['4']

    @override_eppo_feature_flag(
        {CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True}}
    )
    def test_get_200_with_ff_without_user_is_required_user(self):
        self._prepare_data()
        headers = self.get_headers(self.url)

        response = self.fetch(self.url, headers=self._remove_authorization_token(headers))

        assert response.code == 200
        assert list(response.json.keys()) == ['4']

    @override_eppo_feature_flag(
        {CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': False}}
    )
    def test_get_200_with_ff_without_user_is_not_required_user(self):
        self._prepare_data()
        headers = self.get_headers(self.url)
        headers = self._remove_authorization_token(headers)
        category = BusinessCategoryEnum.HAIR_SALONS.value
        category_id = BusinessCategory.objects.filter(internal_name=category).values_list(
            'id', flat=True
        )[0]

        response = self.fetch(self.url, headers=headers)

        assert response.code == 200
        assert list(response.json.keys()) == ['4']

        response = self.fetch(self.url + f'&categories_ids={category_id}', headers=headers)

        assert response.code == 200
        assert list(response.json.keys()) == ['4', '5']

        response = self.fetch(self.url + f'&categories_ids={category_id+100}', headers=headers)

        assert response.code == 200
        assert list(response.json.keys()) == ['4']

    @override_eppo_feature_flag({CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {}})
    def test_get_200_without_ff_without_user(self):
        self._prepare_data()
        headers = self.get_headers(self.url)

        response = self.fetch(self.url, headers=self._remove_authorization_token(headers))

        assert response.code == 200
        assert list(response.json.keys()) == ['4']

    @override_eppo_feature_flag(
        {
            CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True},
            S4UGalleriesOrderExperiment.flag_name: ExperimentVariants.VARIANT_A,
        }
    )
    def test_get_200_with_ff_order_experiment(self):
        businesses = self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert list(response.json.keys()) == ['1', '5']
        assert response.json['1']['label'] == 'For You'
        assert response.json['1']['type'] == GalleriesType.SELECTED_FOR_YOU_GALLERY.value
        assert {b.id for b in businesses} == {b['id'] for b in response.json['1']['businesses']}
        assert response.json['1']['businesses'][0]['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True},
            S4UGalleriesOrderExperiment.flag_name: ExperimentVariants.VARIANT_A,
        }
    )
    def test_get_200_with_ff_order_experiment_without_categories(self):
        self._prepare_data()
        self.user.customerfavoritecategory_set.all().delete()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert list(response.json.keys()) == ['4']

    @override_eppo_feature_flag(
        {
            CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True},
            GalleryNameS4UExperimentOnFlag.flag_name: True,
            GalleryNameS4UExperiment.flag_name: ExperimentVariants.VARIANT_A,
        }
    )
    def test_gallery_name_experiment_variant_a(self):
        businesses = self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert response.json['5']['label'] == 'New on Booksy'
        assert response.json['5']['type'] == GalleriesType.SELECTED_FOR_YOU_GALLERY.value
        assert {b.id for b in businesses} == {b['id'] for b in response.json['5']['businesses']}
        assert response.json['5']['businesses'][0]['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True},
            GalleryNameS4UExperimentOnFlag.flag_name: True,
            GalleryNameS4UExperiment.flag_name: ExperimentVariants.VARIANT_B,
        }
    )
    def test_gallery_name_experiment_variant_b(self):
        businesses = self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert response.json['5']['label'] == 'Find your new favorite'
        assert response.json['5']['type'] == GalleriesType.SELECTED_FOR_YOU_GALLERY.value
        assert {b.id for b in businesses} == {b['id'] for b in response.json['5']['businesses']}
        assert response.json['5']['businesses'][0]['is_selected_for_you'] is True

    @override_eppo_feature_flag(
        {
            CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': True},
            GalleryNameS4UExperimentOnFlag.flag_name: True,
            GalleryNameS4UExperiment.flag_name: ExperimentVariants.CONTROL,
        }
    )
    def test_gallery_name_experiment_variant_control(self):
        businesses = self._prepare_data()

        response = self.fetch(self.url)

        assert response.code == 200
        assert response.json != {}
        assert response.json['5']['label'] == 'For You'
        assert response.json['5']['type'] == GalleriesType.SELECTED_FOR_YOU_GALLERY.value
        assert {b.id for b in businesses} == {b['id'] for b in response.json['5']['businesses']}
        assert response.json['5']['businesses'][0]['is_selected_for_you'] is True


@pytest.mark.django_db
class NailsCampaignOnMyBooksyTestcase(BaseAsyncHTTPTest):
    url = '/customer_api/my_booksy/galleries/?min_gallery_businesses=2'

    def setUp(self, **kwargs):
        super().setUp(**kwargs)
        self.index = clean_elastisearch_index_helper(ESIndex.BUSINESS)
        # prevent status 500 from unrelated experiments
        NewOnBooksyGalleryExperiment.initialize()

    def _get_extra_headers(self):
        return {
            'X-Fingerprint': "123abc",
            'X-User-Pseudo-ID': 'TEST.1234',
            'X-API-KEY': self.customer_booking_src.api_key,
        }

    def _prepare_businesses(self, categories):
        nails_business_1 = _create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories=[
                categories[BusinessCategoryEnum.NAIL_SALONS],
                categories[BusinessCategoryEnum.HAIR_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
        )
        _not_nails_business_1 = _create_business(
            active_x_days=10,
            has_cover_photo=True,
            categories=[categories[BusinessCategoryEnum.HAIR_SALONS]],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        nails_business_2 = _create_business(
            active_x_days=14,
            has_cover_photo=False,
            categories=[
                categories[BusinessCategoryEnum.NAIL_SALONS],
                categories[BusinessCategoryEnum.HAIR_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
            # business has techincally more than 14 days, but in experiment it
            # should be 14 full days, so this one will still be selected
            active_x_hours=3,
        )
        _non_nails__business_2 = _create_business(
            active_x_days=10,
            has_cover_photo=False,
            categories=[
                categories[BusinessCategoryEnum.PIERCING_CATEGORY],
                categories[BusinessCategoryEnum.NAIL_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.PIERCING_CATEGORY],
        )
        # business older than 14 days, shouldn't be shown in gallery
        _old_nails_business = _create_business(
            active_x_days=16,
            has_cover_photo=False,
            categories=[categories[BusinessCategoryEnum.NAIL_SALONS]],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
        )
        _nails_business_outside_distance = _create_business(
            active_x_days=10,
            has_cover_photo=False,
            categories=[categories[BusinessCategoryEnum.NAIL_SALONS]],
            services_count=3,
            # a little more than 18 km from 52.237489,20.990810"
            location_geo=[52.112001, 21.187554],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
        )
        self.index.refresh()
        return nails_business_1, nails_business_2

    def _prepare_businesses_fallback(self, categories):
        # Create non MRC businesses, so older than 14 days
        old_nails_business_1 = _create_business(
            active_x_days=16,
            has_cover_photo=True,
            categories=[
                categories[BusinessCategoryEnum.NAIL_SALONS],
                categories[BusinessCategoryEnum.HAIR_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
        )
        old_nails_business_2 = _create_business(
            active_x_days=16,
            has_cover_photo=True,
            categories=[
                categories[BusinessCategoryEnum.NAIL_SALONS],
                categories[BusinessCategoryEnum.HAIR_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
        )
        _non_nails_business = _create_business(
            active_x_days=16,
            has_cover_photo=True,
            categories=[
                categories[BusinessCategoryEnum.HAIR_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        _nails_business_outside_distance = _create_business(
            active_x_days=16,
            has_cover_photo=False,
            categories=[categories[BusinessCategoryEnum.NAIL_SALONS]],
            services_count=3,
            # a little more than 18 km from 52.237489,20.990810"
            location_geo=[52.112001, 21.187554],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
        )
        self.index.refresh()
        return old_nails_business_1, old_nails_business_2

    def _prepare_data(self):
        self.iphone_booking_source = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=IPHONE,
        )
        self.web_booking_source = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=WEB,
        )
        categories_types = [
            BusinessCategoryEnum.NAIL_SALONS,
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
        ]
        categories = {
            c: category_recipe.make(full_name=c.value, internal_name=c.value)
            for c in categories_types
        }

        return categories

    def _was_nails_event_sent_once(self, request_api_mock) -> bool:
        if not request_api_mock.call_args_list:
            return False

        events_counter = 0
        for call in request_api_mock.call_args_list:
            try:
                payload = call[1]['payload']
                if 'events' not in payload:
                    continue
                for event in payload['events']:
                    if 'params' not in event:
                        continue
                    if 'item_list_id' in event['params']:
                        if event['params']['item_list_id'] == 'nails_campaign':
                            events_counter += 1
            except (KeyError, TypeError):
                continue

        return events_counter == 1

    @pytest.mark.django_db
    def test_nails_gallery_shown_for_iphone_user(self):
        categories = self._prepare_data()
        businesses_to_show = self._prepare_businesses(categories)
        self.customer_booking_src = self.iphone_booking_source
        url = f"{self.url}&geo_location=52.237489%2C20.990810"
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.fetch(url, extra_headers=self._get_extra_headers())

        assert response.code == 200
        assert response.json != {}
        assert len(response.json.keys()) == 2
        assert '1' in list(response.json.keys())
        assert response.json['1']['label'] == 'Nails campaign'
        assert response.json['1']['businesses'][0]['images']['cover']
        assert response.json['1']['businesses'][0]['distance']
        assert response.json['1']['businesses'][0]['location']['address'] == "Test City, 19999"

        assert response.json['1']['type'] == GalleriesType.NAILS_CAMPAIGN.value

        # Only business with primary_category nails, with active_from date
        # 14 days from now, and in range of 18 km shown
        assert {b.id for b in businesses_to_show} == {
            b['id'] for b in response.json['1']['businesses']
        }

        assert self._was_nails_event_sent_once(request_api_mock)

    @pytest.mark.django_db
    def test_fallback_nails_gallery_shown_when_no_mrc_nails_businesses(self):
        categories = self._prepare_data()
        businesses_to_show = self._prepare_businesses_fallback(categories)
        self.customer_booking_src = self.iphone_booking_source
        url = f"{self.url}&geo_location=52.237489%2C20.990810"
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.fetch(url, extra_headers=self._get_extra_headers())

        assert response.code == 200
        assert response.json != {}
        assert len(response.json.keys()) == 2
        assert list(response.json.keys()) == ['1', '5']
        assert response.json['1']['label'] == 'Nails campaign'
        assert response.json['1']['businesses'][0]['images']['cover']
        assert response.json['1']['businesses'][0]['distance']
        assert response.json['1']['businesses'][0]['location']['address'] == "Test City, 19999"

        assert response.json['1']['type'] == GalleriesType.NAILS_CAMPAIGN.value

        # when there is not enough MRC nails businesses show all Nails businesses
        assert {b.id for b in businesses_to_show} == {
            b['id'] for b in response.json['1']['businesses']
        }

        # fallback businesses are sent in item_list_view event
        assert self._was_nails_event_sent_once(request_api_mock)

    @pytest.mark.django_db
    def test_fallback_for_only_one_mrc_business_found(self):
        categories = self._prepare_data()
        _single_mrc_nails_business = _create_business(
            active_x_days=10,
            has_cover_photo=True,
            categories=[
                categories[BusinessCategoryEnum.NAIL_SALONS],
            ],
            services_count=3,
            location_geo=[52.232989, 20.990810],
            primary_category=categories[BusinessCategoryEnum.NAIL_SALONS],
        )

        businesses_to_show = self._prepare_businesses_fallback(categories)
        self.customer_booking_src = self.iphone_booking_source
        url = f"{self.url}&geo_location=52.237489%2C20.990810"
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.fetch(url, extra_headers=self._get_extra_headers())

        assert response.code == 200
        assert response.json != {}
        assert len(response.json.keys()) == 2
        assert list(response.json.keys()) == ['1', '5']
        assert response.json['1']['label'] == 'Nails campaign'
        assert response.json['1']['businesses'][0]['images']['cover']
        assert response.json['1']['businesses'][0]['distance']
        assert response.json['1']['businesses'][0]['location']['address'] == "Test City, 19999"

        assert response.json['1']['type'] == GalleriesType.NAILS_CAMPAIGN.value

        # when there is not enough MRC nails businesses show all Nails businesses
        assert {b.id for b in [_single_mrc_nails_business] + list(businesses_to_show)} == {
            b['id'] for b in response.json['1']['businesses']
        }

        # fallback businesses are sent in item_list_view event
        assert self._was_nails_event_sent_once(request_api_mock)

    def test_nails_gallery_not_shown_for_web_user(self):
        categories = self._prepare_data()
        self._prepare_businesses(categories)
        self.customer_booking_src = self.web_booking_source
        url = f"{self.url}&geo_location=52.237489%2C20.990810"
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.fetch(url, extra_headers={'x-fingerprint': "123abc"})

        assert response.code == 200
        assert response.json != {}
        assert len(response.json.keys()) == 1
        # no nails gallery
        assert '1' not in list(response.json.keys())
        # event for nails_gallery not sent
        assert not self._was_nails_event_sent_once(request_api_mock)

    @override_eppo_feature_flag({NailsCampaignOnMyBooksy.flag_name: False})
    def test_nails_gallery_shown_with_false_experiment_flag(self):
        categories = self._prepare_data()
        self._prepare_businesses(categories)
        self.customer_booking_src = self.iphone_booking_source
        url = f"{self.url}&geo_location=52.237489%2C20.990810"
        with patch('lib.tagmanager.client.GTMClient._request_api') as request_api_mock:
            response = self.fetch(url, extra_headers={'x-fingerprint': "123abc"})

        assert response.code == 200
        assert response.json != {}
        assert len(response.json.keys()) == 1
        # no nails gallery
        assert '1' not in list(response.json.keys())

        # event for nails_gallery not sent
        assert not self._was_nails_event_sent_once(request_api_mock)
