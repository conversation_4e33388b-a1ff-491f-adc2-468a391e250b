#!/usr/bin/env python3
import importlib
import os
import sys
from datetime import timezone, datetime
from typing import Optional
import vertexai  # type: ignore
from vertexai.generative_models import GenerativeModel, GenerationConfig  # type: ignore


"""
This is a POC script for running an AI code review.

We start off with a check against agreements base on the ADR-09/2025 Prohibition of Business Logic in Serializers
https://booksy.atlassian.net/wiki/spaces/BFS/pages/3471343619/ADR-09+2025+Prohibition+of+Business+Logic+in+Serializers


Further development will include more rules which will be distributed as described in the RFC - Standardising cursorrules for Backend 
https://booksy.atlassian.net/wiki/spaces/BFS/pages/3492413470/RFC+-+Standardising+cursorrules+for+Backend
"""


PROJECT_ID = "bks-genai-dev"
LOCATION = "europe-west1"
MODEL_NAME = "gemini-2.5-flash"
SHEET_ID = '1uS_LPmuscQziSjkfkoXyfLkWre3TWI1QiASIqQ8wV5g'
FEEDBACK_SHEET_URL = 'https://docs.google.com/spreadsheets/d/1uS_LPmuscQziSjkfkoXyfLkWre3TWI1QiASIqQ8wV5g'

RULE_FILE_PATH = os.path.join(os.path.dirname(__file__), "serializer_executable_rule.md")


def _get_sheets_client():
    import google.auth  # type: ignore
    from googleapiclient.discovery import build  # type: ignore
    credentials, _ = google.auth.default(
        scopes=["https://www.googleapis.com/auth/spreadsheets"]
    )
    return build("sheets", "v4", credentials=credentials, cache_discovery=False)


def append_review_row(sheet_id: str, tab_name: str, fields: dict) -> None:
    if not sheet_id or not tab_name:
        return
    
    service = _get_sheets_client()
    values = [[
        fields.get("timestamp", datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")),
        fields.get("merge_request_url", ""),
        fields.get("job_url", ""),
        fields.get("branch_name", ""),
        fields.get("was_job_successful", ""),
        fields.get("tokens_usage", ""),
    ]]
    body = {"values": values}
    service.spreadsheets().values().append(
        spreadsheetId=sheet_id,
        range=f"{tab_name}!A:F",
        valueInputOption="USER_ENTERED",
        insertDataOption="INSERT_ROWS",
        body=body,
    ).execute()

def read_rule_text(rule_path: str) -> str:
    with open(rule_path, "r", encoding="utf-8") as fh:
        return fh.read()

def construct_prompt(rule_text: str, mr_diff: str) -> str:
    return (
        "You are an expert Python code reviewer for Booksy. Analyze the following code diff"
        " against a single rule: business logic is forbidden inside Django Rest Framework"
        " serializers. Be precise and concise.\n\n"
        "--- RULE DEFINITION ---\n" + rule_text + "\n--- RULE DEFINITION END ---\n\n"
        "--- CODE DIFF TO ANALYZE ---\n" + mr_diff + "\n--- CODE DIFF TO ANALYZE END ---\n\n"
        "Output formatting requirements:\n"
        "- If NO violations are found, respond with exactly: OK (uppercase, no extra text).\n"
        "- If violations ARE found, respond in Markdown using this template:\n\n"
        "--- OUTPUT TEMPLATE START ---\n"
        "### AI Code Review - Serializer Rule\n\n"
        "Summary:\n"
        "- status: failed\n"
        "- violations: <number>\n\n"
        "Violations:\n"
        "- file: <path/to/file.py>\n"
        "  method: <serializer_method_name>\n"
        "  finding: <short description of the issue>\n"
        "  evidence: <one-line snippet or line number>\n"
        "  fix: <specific fix suggestion based on the rule>\n"
        "--- OUTPUT TEMPLATE END ---\n"
    )



def write_stdout(text: str) -> None:
    sys.stdout.write(text)


def _read_diff_from_stdin_or_argv() -> Optional[str]:
    """Return diff content from stdin if piped, otherwise from argv[1].

    We avoid blocking if stdin is a TTY. We catch only I/O related errors.
    """

    if not sys.stdin.isatty():
        data = sys.stdin.read()
        if data:
            return data

    return sys.argv[1] if len(sys.argv) >= 2 else None


def main() -> None:
    mr_diff = _read_diff_from_stdin_or_argv()
    if not mr_diff:
        sys.stderr.write("Missing MR diff input on stdin or argv[1]\n")
        sys.stdout.write("❌ AI code review failed: Missing MR diff input.\n")
        sys.exit(1)

    rule_text = read_rule_text(RULE_FILE_PATH)
    prompt = construct_prompt(rule_text, mr_diff)


    vertexai.init(project=PROJECT_ID, location=LOCATION)
    model = GenerativeModel(MODEL_NAME)
    try:
        response = model.generate_content(
            prompt,
            generation_config=GenerationConfig(temperature=0.7),
        )
        feedback = (response.text or "").strip()
        # Best-effort token usage extraction -> single total number or None
        tokens_total = None
        try:
            usage = getattr(response, "usage_metadata", None)
            if usage:
                total_tokens = getattr(usage, "total_token_count", None)
                if isinstance(total_tokens, int):
                    tokens_total = total_tokens
        except Exception:
            pass
    except Exception as error:
        sys.stdout.write(
            "❌ AI code review failed during Vertex AI call.\n\n"
            f"Error type: {type(error).__name__}\n"
            f"Error: {error}\n"
        )
        sys.exit(1)

    if feedback == "OK":
        tokens_value = str(tokens_total) if tokens_total is not None else "None"
        sys.stdout.write("✅ AI code review passed. No violations found.\n")
        append_review_row(
            sheet_id=SHEET_ID,
            tab_name="AI_Reviews",
            fields={
                "merge_request_url": f"{os.getenv('CI_PROJECT_URL', '')}/-/merge_requests/{os.getenv('CI_MERGE_REQUEST_IID', '')}",
                "job_url": os.getenv("CI_JOB_URL", ""),
                "branch_name": os.getenv("CI_COMMIT_REF_NAME", ""),
                "was_job_successful": "true",
                "tokens_usage": tokens_value,
            },
        )
        sys.stdout.write(
            f"Help us improve this POC: open the sheet and add your notes in the 'Developer Feedback' column after your row is created: {FEEDBACK_SHEET_URL}\n"
        )
        sys.exit(0)
    else:
        tokens_value = str(tokens_total) if tokens_total is not None else "None"
        sys.stdout.write(f"❌ AI code review found issues:\n\n{feedback}\n")
        append_review_row(
            sheet_id=SHEET_ID,
            tab_name="AI_Reviews",
            fields={
                "merge_request_url": f"{os.getenv('CI_PROJECT_URL', '')}/-/merge_requests/{os.getenv('CI_MERGE_REQUEST_IID', '')}",
                "job_url": os.getenv("CI_JOB_URL", ""),
                "branch_name": os.getenv("CI_COMMIT_REF_NAME", ""),
                "was_job_successful": "false",
                "tokens_usage": tokens_value,
            },
        )
        sys.stdout.write(
            f"Help us improve this POC: open the sheet and add your notes in the 'Developer Feedback' column after your row is created: {FEEDBACK_SHEET_URL}\n"
        )
        sys.exit(1)


if __name__ == "__main__":
    main()

