from bo_obs.datadog.enums import BooksyTeams
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED

from lib.db import using_db_for_reads, READ_ONLY_DB
from lib.elasticsearch.consts import ESDocType
from lib.serializers import OrderingSerializer
from service.images.helpers import BasePhotoU<PERSON><PERSON><PERSON><PERSON><PERSON>, PaginatorPage
from service.images.v2.serializers import PaginatorPageSerializer
from service.tools import RequestHand<PERSON>, json_request, session
from webapps.business.models import (
    ComboMembership,
    Service,
    ServiceAddOn,
)
from webapps.business.serializers.addon import (
    GetRequestSerializer,
    ServiceAddOnRepresentationSerializer,
    ServiceAddOnSerializer,
)
from webapps.images.enums import ImageTypeEnum
from webapps.pos.elasticsearch.addons import ServiceAddOnDocument, ServiceAddOnDocumentHitSerializer
from webapps.pos.searchables.addon_searchables import ServiceAddOnSearchable


def get_addons_list(*, business_id=None, validated_data=None):
    qs = (
        ServiceAddOn.objects.filter(
            business_id=business_id,
        )
        .select_related('photo')
        .order_by('order', 'created')
    )
    if validated_data and validated_data.get('query'):
        qs = qs.filter(name__istartswith=validated_data['query'])
    return qs


class ServicesAddOnsListHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Return Service Add-ons
            type: ServiceAddOnsList
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: query
                  description: search Add-on name
                  type: string
                  paramType: query
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
        :swagger

        swaggerModels:
            ServiceAddOnsList:
                id: ServiceAddOnsList
                properties:
                    data:
                        type: array
                        items:
                            type: ServiceAddOn
        :swaggerModels
        """
        self.business_with_staffer(business_id, __check_region=False, __only='id')
        request_serializer = GetRequestSerializer(data=self._prepare_get_arguments())
        validated_data = self.validate_serializer(request_serializer)

        addons, count = self.get_addons_from_es(
            business_id=business_id,
            validated_data=validated_data,
        )
        self.finish_with_json(
            HTTP_200_OK,
            {'count': count, 'per_page': validated_data['per_page'], 'data': list(addons)},
        )

    @staticmethod
    def get_addons_from_es(business_id, validated_data):
        query = validated_data.get('query', '') or ''
        res = (
            ServiceAddOnSearchable(ESDocType.ADDON, serializer=ServiceAddOnDocumentHitSerializer)
            .params(
                from_=validated_data['per_page'] * (validated_data['page'] - 1),
                size=validated_data['per_page'],
            )
            .search(
                {
                    'business_id': business_id,
                    'query': query,
                }
            )
            .sort('order', 'created')
            .execute()
        )

        return res, res.hits.total['value']

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Create Service Add-on
            type: ServiceAddOnResponse
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: body
                  type: ServiceAddOn
                  paramType: body
        :swagger

        swaggerModels:
            ServiceAddOnResponse:
                id: ServiceAddOnResponse
                properties:
                    addon:
                        type: ServiceAddOn
        :swaggerModels
        """
        business = self.business_with_manager(business_id, __check_region=False)

        self.data['business_id'] = business_id
        serializer = ServiceAddOnSerializer(
            data=self.data,
            context={
                'business': business,
                'pos': business.pos,
            },
        )
        self.validate_serializer(serializer)
        instance = serializer.save()

        # manually add to ES index for instant update
        instance.reindex()
        instance.business.reindex_or_bump_river()

        self.finish_with_json(HTTP_201_CREATED, {'addon': serializer.data})

    @session(login_required=True)
    @json_request
    def put(self, business_id):
        """
        swagger:
            summary: Change orders of Service Add-on
            parameters:
              - name: business_id
                description: Business id
                type: integer
                paramType: path
              - name: body
                description:
                paramType: body
                type: Ordering
            type: Ordering
        """

        business = self.business_with_manager(business_id, __check_region=False)

        serializer = OrderingSerializer(data=self.data)
        validated_data = self.validate_serializer(serializer)
        dict_elements = {i['id']: i['order'] for i in validated_data['ordering']}

        addons = ServiceAddOn.objects.filter(business=business, id__in=dict_elements.keys())
        for addon in addons:
            addon.order = dict_elements[addon.id]
        ServiceAddOn.objects.bulk_update(addons, ['order'])
        ServiceAddOnDocument.reindex(addons.values_list('id', flat=True))

        self.finish_with_json(HTTP_200_OK, validated_data)


class ServicesAddOnsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)

    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id, addon_id):
        """
        swagger:
            summary: Return Service Add-on
            type: ServiceAddOnResponse
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: addon_id
                  description: ServiceAddOn id
                  type: integer
                  paramType: path
        :swagger
        """
        self.business_with_manager(business_id, __check_region=False, __only='id')
        addon = self.get_object_or_404(ServiceAddOn, pk=addon_id, business_id=business_id)

        serializer = ServiceAddOnRepresentationSerializer(instance=addon)

        self.finish_with_json(HTTP_200_OK, {'addon': serializer.data})

    @session(login_required=True)
    @json_request
    def put(self, business_id, addon_id):
        """
        swagger:
            summary: Update Service Add-on
            type: ServiceAddOnResponse
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: addon_id
                  description: ServiceAddOn id
                  type: integer
                  paramType: path
                - name: body
                  type: ServiceAddOn
                  paramType: body
        :swagger
        """
        business = self.business_with_manager(business_id, __check_region=False)
        addon = self.get_object_or_404(ServiceAddOn, pk=addon_id, business_id=business_id)

        serializer = ServiceAddOnRepresentationSerializer(
            instance=addon,
            data=self.data,
            context={
                'business': business,
                'pos': business.pos,
            },
        )
        self.validate_serializer(serializer)

        instance = serializer.save()
        instance.reindex()
        instance.business.reindex_or_bump_river()

        self.finish_with_json(HTTP_200_OK, {'addon': serializer.data})

    @session(login_required=True)
    def delete(self, business_id, addon_id):
        """
        swagger:
            summary: Delete Addon
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: addon_id
                  type: integer
                  paramType: path
                  required: true
        """
        self.business_with_manager(business_id, __only='id')
        addon = self.get_object_or_404(
            ServiceAddOn,
            id=addon_id,
            business_id=business_id,
        )
        addon.soft_delete()
        addon.reindex()
        addon.business.reindex_or_bump_river()
        ServiceAddOnDocument.reindex([addon.id])
        self.finish_with_json(HTTP_200_OK, {})


class ServiceAddOnsListHandler(RequestHandler):
    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id, service_id):
        """
        swagger:
            summary: Returns Add-ons of particular Service
            type: ServiceAddOnResponse
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_id
                  description: Service id
                  type: integer
                  paramType: path
        :swagger
        """
        self.business_with_staffer(business_id, __check_region=False, __only='id')
        request_serializer = GetRequestSerializer(data=self._prepare_get_arguments())
        validated_data = self.validate_serializer(request_serializer)

        service = self.get_object_or_404(
            Service,
            id=service_id,
            active=True,
            __only=['id', 'combo_type'],
        )

        service_ids = [service.id]
        if service.is_combo:
            service_ids.extend(
                ComboMembership.objects.filter(
                    combo__service_id=service_id,
                ).values_list('child__service_id', flat=True)
            )

        addons = (
            get_addons_list(business_id=business_id)
            .filter(
                services__in=service_ids,
            )
            .prefetch_related(*ServiceAddOnRepresentationSerializer.get_prefetches())
            .distinct()
        )

        paginator_page = PaginatorPage(addons, validated_data['per_page'], validated_data['page'])
        serializer = PaginatorPageSerializer(
            item_serializer_cls=ServiceAddOnRepresentationSerializer,
            instance=paginator_page,
        )
        self.finish_with_json(HTTP_200_OK, serializer.data)


class ServiceAddOnPhotoHandler(BasePhotoUploadHandler):
    @session(login_required=True)
    def post(self, business_id, addon_id):
        """
        swagger:
            summary: Upload Photo to AddOn
            consumes: multipart/form-data
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: addon_id
                  type: integer
                  paramType: path
                  required: true
                - name: photo
                  type: File
                  paramType: form
                  required: true
        :swagger
        """
        self.business_with_manager(business_id, __only='id')
        addon = self.get_object_or_404(ServiceAddOn, pk=addon_id, business_id=business_id)
        context = {
            'owner_model': addon,
            'image_type': ImageTypeEnum.ADDON_PHOTOS,
            'business_id': business_id,
        }
        serializer = self.save_photo(context=context)
        ServiceAddOnDocument.reindex([addon.id])

        self.finish_with_json(HTTP_201_CREATED, serializer.data)

    @session(login_required=True)
    def delete(self, business_id, addon_id):
        """
        swagger:
            summary: Delete Photo of AddOn
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: addon_id
                  type: integer
                  paramType: path
                  required: true
        """
        self.business_with_manager(business_id, __only='id')
        addon = self.get_object_or_404(ServiceAddOn, pk=addon_id, business_id=business_id)
        if addon.photo is not None:
            addon.photo = None
            addon.save()

        self.finish_with_json(HTTP_200_OK, {})


################
# CUSTOMER API #
################


class CustomerServiceAddOnsListHandler(RequestHandler):
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id, service_id):
        """
        swagger:
            summary: Returns Add-Ons of particular Service
            type: ServiceAddOnResponse
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_id
                  description: Service id
                  type: integer
                  paramType: path
                - name: combos_with_addons
                  description: whether addons should suggest use for combo services
                  type: integer
                  required: False
                  paramType: query
                  enum:
                    - 0
                    - 1
        :swagger
        """
        request_serializer = GetRequestSerializer(data=self._prepare_get_arguments())
        validated_data = self.validate_serializer(request_serializer)

        service = self.get_object_or_404(
            Service,
            id=service_id,
            active=True,
            __only=['id', 'combo_type'],
        )

        service_ids = [service.id]
        if service.is_combo:
            service_ids.extend(
                ComboMembership.objects.filter(
                    combo__service_id=service_id,
                ).values_list('child__service_id', flat=True)
            )

        if not validated_data['combos_with_addons'] and service.is_combo:
            addons = ServiceAddOn.objects.none()
        else:
            addons = (
                get_addons_list(business_id=business_id)
                .filter(
                    services__in=service_ids,
                    is_available_for_customer_booking=True,
                )
                .prefetch_related(*ServiceAddOnRepresentationSerializer.get_prefetches())
                .distinct()
            )

        paginator_page = PaginatorPage(addons, validated_data['per_page'], validated_data['page'])
        serializer = PaginatorPageSerializer(
            item_serializer_cls=ServiceAddOnRepresentationSerializer,
            instance=paginator_page,
        )
        self.finish_with_json(HTTP_200_OK, serializer.data)
