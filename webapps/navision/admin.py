from datetime import timed<PERSON><PERSON>

import simplejson as json
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import (
    Case,
    Count,
    DecimalField,
    F,
    Q,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Coalesce, Greatest
from django.urls import reverse, NoReverseMatch
from django.utils.html import format_html, format_html_join

from country_config import Country, BASE_INFO
from lib.admin_helpers import (
    admin_link,
    ReadOnlyTabular,
    NoAddDelMixin,
    BaseModelAdmin,
    ReadOnlyFieldsMixin,
    NoDelMixin,
    NoChangeMixin,
)
from lib.cache import lru_booksy_cache
from lib.tools import tznow
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.business.history_retriever import HistoryRetriever
from webapps.business.models import Business
from webapps.navision.enums import TargetAPI, InvoicePaymentSource, InvoicingErrorStatus
from webapps.navision.models import (
    Invoice,
    InvoiceErrorResponse,
    InvoicingSummary,
    InvoicingError,
    InvoiceItem,
    Merchant,
    MerchantErrorResponse,
    TaxRate,
    TaxGroup,
    NavisionSettings,
    BusinessInvoiceSummary,
)
from webapps.navision.ports.tax_rates import TaxMatrix
from webapps.navision.tasks.api import (
    create_or_update_merchant_in_navision,
    send_not_synced_invoices_to_navision,
)
from webapps.navision.tasks.booksy_billing import create_new_billing_saas_invoices_task
from webapps.navision.tasks.boost_online import (
    create_boost_online_invoices_task,
)
from webapps.navision.tasks.errors import process_error_task
from webapps.navision.tasks.invoicing_summaries import (
    approve_invoices_in_selected_invoice_summary_task,
    remove_unapproved_invoices_from_summaries_task,
    remove_test_invoices_from_invoicing_summary_task,
)
from webapps.navision.utils import (
    check_navision_admin_permission,
    check_navision_operator_permission,
)
from webapps.purchase.models import Subscription
from webapps.structure.enums import RegionType
from webapps.structure.models import Region
from webapps.user.groups import GroupNameV2
from webapps.user.tools import get_user_from_django_request


class ErrorResponseAdminInline(ReadOnlyFieldsMixin, ReadOnlyTabular, admin.StackedInline):
    fields = (
        'id',
        'response',
        'created',
    )
    classes = ['collapse']


class MerchantErrorResponseAdminInline(ErrorResponseAdminInline):
    model = MerchantErrorResponse
    fields = (
        'merchant',
        'target_api',
        'url',
        'payload',
        'response',
    )
    readonly_fields = (
        'target_api',
        'url',
        'payload',
        'response',
    )

    @staticmethod
    def target_api(obj: MerchantErrorResponse):
        metadata = obj.metadata or {}
        if metadata != {}:
            metadata = json.loads(metadata)
        if metadata.get('target_api') == TargetAPI.PRODUCTION.value:
            return TargetAPI.PRODUCTION.name
        if metadata.get('target_api') == TargetAPI.SANDBOX.value:
            return TargetAPI.SANDBOX.name
        return 'Unknown'

    @staticmethod
    def url(obj: MerchantErrorResponse):
        metadata = obj.metadata or {}
        if metadata != {}:
            metadata = json.loads(metadata)
        return metadata.get('url')

    @staticmethod
    def payload(obj: MerchantErrorResponse):
        metadata = obj.metadata or {}
        if metadata != {}:
            metadata = json.loads(metadata)
        payload = metadata.get('payload')
        if payload is not None:
            return json.dumps(payload, indent=2)
        return 'Unknown'


class InvoiceAdminInline(ReadOnlyTabular, admin.StackedInline):
    model = Invoice
    extra = 0

    show_change_link = True

    fields = (
        'id',
        'invoice_number',
        'invoice_date',
    )


@admin.action(
    description='Sync selected merchants',
    permissions=['navision_admin'],
)
def sync_selected_merchants(_model_admin, request, queryset):
    for merchant in queryset.iterator():
        create_or_update_merchant_in_navision.run(merchant.id)
        merchant.updated = tznow()
    Merchant.objects.bulk_update(queryset, ["updated"])

    messages.success(request, 'Merchants have been scheduled for sync, check them in a while.')


class MerchantAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.INVOICING_AND_ERP_ADVANCED,)
    read_only_access_groups = (GroupNameV2.INVOICING_AND_ERP_BASIC,)
    list_display = (
        'id',
        'merchant_id',
        'entity_name',
        'buyers',
        'tax_id',
        'synced_at',
        'errors_today',
    )
    search_fields = (
        '=id',
        '=tax_id',
    )
    readonly_fields = (
        'merchant_id',
        'businesses',
        'buyers',
        'tax_group',
        'tax_rates_business',
        'tax_rates',
        'sent_to_production',
    )
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'merchant_id',
                    'entity_name',
                    'tax_id',
                    'businesses',
                    'buyers',
                    'invoice_emails',
                    'payment_due_days',
                )
            },
        ),
        (
            'Taxes',
            {
                'fields': (
                    'accounting_group',
                    'price_with_vat',
                    'tax_group',
                    'tax_rates_business',
                    'tax_rates',
                )
            },
        ),
        (
            'Address',
            {
                'fields': (
                    'address_details1',
                    'zip_code',
                    'state',
                    'city',
                    'country_code',
                )
            },
        ),
        (
            'Admin data',
            {
                'fields': (
                    'created',
                    'updated',
                    'deleted',
                    'synced_at',
                    'sent_to_production',
                )
            },
        ),
    )
    change_list_template = 'admin/change_lists/change_list__merchant.html'
    inlines = [
        InvoiceAdminInline,
        MerchantErrorResponseAdminInline,
    ]
    actions = [
        sync_selected_merchants,
    ]

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}

        navision_settings = NavisionSettings.get_current_settings()
        if settings.NAVISION_INTEGRATION_ENABLED and navision_settings.navision_integration_enabled:
            extra_context['integration_enabled'] = True
        else:
            extra_context['integration_enabled'] = False

        return super().changelist_view(request, extra_context=extra_context)

    def get_search_results(self, request, queryset, search_term):
        old_queryset, use_distinct = super().get_search_results(request, queryset, search_term)

        merchant_true_id = Merchant.reverse_merchant_id(search_term)

        if merchant_true_id:
            return old_queryset | queryset.filter(id=merchant_true_id), use_distinct

        return old_queryset, use_distinct

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .prefetch_related('subscriptionbuyer_set')
            .annotate(errors_today=Count('errors', filter=Q(errors__created__date=tznow().date())))
        )

    @staticmethod
    def businesses(merchant: Merchant):
        return format_html_join(
            ',',
            '<a href="{}" class="viewsitelink">{}</a>',
            (
                (
                    reverse('admin:business_business_change', args=(business.id,)),
                    business.id,
                )
                for buyer in merchant.subscriptionbuyer_set.all()
                for business in buyer.businesses.all()
            ),
        )

    @staticmethod
    def buyers(merchant: Merchant):
        return format_html_join(
            ',',
            '<a href="{}" class="viewsitelink">{}</a>',
            (
                (
                    reverse('admin:purchase_subscriptionbuyer_change', args=(buyer.id,)),
                    buyer.id,
                )
                for buyer in merchant.subscriptionbuyer_set.all()
            ),
        )

    @staticmethod
    def tax_group(merchant: Merchant):
        if buyer := merchant.subscriptionbuyer_set.first():
            return format_html(
                '<a href="{hyperlink}" class="viewsitelink">{taxgroup_name}</a>',
                hyperlink=reverse('admin:navision_taxgroup_change', args=(buyer.tax_group_id,)),
                taxgroup_name=buyer.tax_group.name,
            )

    @staticmethod
    def tax_rates_business(merchant: Merchant):
        if merchant.owned_businesses_ids:
            business_id = merchant.owned_businesses_ids[0]
            return format_html(
                '<a href="{hyperlink}" class="viewsitelink">{tax_rates_business_id}</a>',
                hyperlink=reverse('admin:business_business_change', args=(business_id,)),
                tax_rates_business_id=business_id,
            )

    @staticmethod
    def tax_rates(merchant: Merchant):
        if merchant.owned_businesses_ids:
            tax_rates = TaxMatrix.for_business_id(merchant.owned_businesses_ids[0]).tax_rates
            return format_html_join(
                '\n',
                '<p><b>{}</b> : {}</p>',
                ((service, f'{tax_rate.tax_rate:.2%}') for service, tax_rate in tax_rates.items()),
            )

    def has_navision_admin_permission(self, request, _obj=None):
        return check_navision_admin_permission(request)

    def errors_today(self, merchant: Merchant):
        if merchant.errors_today == 0:
            return format_html('<span>{}</span>', merchant.errors_today)

        return format_html('<span style="color: #ff0000;">{}</span>', merchant.errors_today)

    errors_today.admin_order_field = 'errors_today'


admin.site.register(Merchant, MerchantAdmin)


class InvoiceItemInlineForm(forms.ModelForm):
    class Meta:
        model = InvoiceItem
        fields = '__all__'
        labels = {
            'base_gross_value': 'Gross amount',
            'discount_gross_value': 'Gross discount (SaaS offline only)',
            'discount_tax_value': 'Tax amount for discount (SaaS offline only)',
        }


class InvoiceItemAdminInline(ReadOnlyFieldsMixin, ReadOnlyTabular, admin.StackedInline):
    model = InvoiceItem
    form = InvoiceItemInlineForm
    readonly_fields = ('invoiced_object',)
    raw_id_fields = ('tax_rate',)
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'id',
                    'service',
                    'product',
                    'base_gross_value',
                    'transaction_id',
                    'charging_dt',
                    'billing_cycle_start',
                    'billing_cycle_end',
                    'discount_gross_value',
                )
            },
        ),
        (
            'Internal data',
            {
                'fields': (
                    'created',
                    'invoiced_object',
                    'charge_completed',
                    'update_status',
                    'base_tax_value',
                    'tax_rate',
                    'tax_additional_data',
                    'discount_tax_value',
                )
            },
        ),
        (
            'Navision data',
            {
                'fields': (
                    'document_id',
                    'final_net_value',
                    'final_tax_value',
                    'final_gross_value',
                )
            },
        ),
    )

    @staticmethod
    def invoiced_object(invoice_item: InvoiceItem):
        if invoice_item.invoiced_object is None:
            # It may be possible for manually crafted invoices
            return

        content_type = invoice_item.content_type

        url = f'admin:{content_type.app_label}_{content_type.model}_change'
        object_name = f'{content_type.model}: {invoice_item.object_id}'

        try:
            return format_html(
                '<a href="{hyperlink}" class="viewsitelink">{object_name}</a>',
                hyperlink=reverse(url, args=(invoice_item.object_id,)),
                object_name=object_name,
            )
        except NoReverseMatch:
            return object_name


class InvoiceErrorResponseInline(ErrorResponseAdminInline):
    model = InvoiceErrorResponse


@admin.action(
    description='Remove selected invoices',
    permissions=['navision_admin'],
)
def remove_selected_invoices(_model_admin, request, queryset):
    message = ''

    invoices_to_remove = queryset.filter(
        status=Invoice.Status.INIT,
    )
    if valid_ids := invoices_to_remove.values_list('id', flat=True):
        message += (
            f'These invoices have been successfully removed: {", ".join(map(str, valid_ids))} \n'
        )

    invalid_ids = queryset.exclude(id__in=invoices_to_remove).values_list('id', flat=True)
    if invalid_ids:
        message += (
            f'These invoices cannot be removed: {", ".join(map(str, invalid_ids))} '
            f'- make sure that selected invoices are in INIT status'
        )

    invoices_to_remove.delete()

    if message:
        messages.info(request, message)


@admin.action(
    description='Remove test invoices',
    permissions=['navision_admin'],
)
def remove_test_invoices(_model_admin, request, queryset):
    message = ''

    invoices_to_remove = queryset.filter(is_production=False)

    if valid_ids := invoices_to_remove.values_list('id', flat=True):
        message += (
            f'These invoices have been successfully removed: {", ".join(map(str, valid_ids))} \n'
        )

    invalid_ids = queryset.exclude(id__in=invoices_to_remove).values_list('id', flat=True)
    if invalid_ids:
        message += (
            f'These invoices cannot be removed: {", ".join(map(str, invalid_ids))} '
            f'- make sure the selected invoices are test invoices'
        )

    invoices_to_remove.delete()

    if message:
        messages.info(request, message)


@admin.action(
    description='Sync selected invoices',
    permissions=['navision_admin'],
)
def sync_selected_invoices(_model_admin, request, queryset):
    message = ''

    invoices_to_sync_qs = queryset.filter(
        Q(queued_at__isnull=True) | Q(queued_at__lte=tznow() - timedelta(hours=1))
    ).exclude(
        status=Invoice.Status.SENT,
    )

    invoices_to_sync_qs.filter(Q(approved=False) | Q(approved__isnull=True)).update(approved=True)

    merchants_to_sync_qs = (
        Merchant.objects.filter(
            invoices__id__in=invoices_to_sync_qs.values_list('id', flat=True),
        )
        .exclude(
            synced_at__isnull=False,
        )
        .distinct()
    )

    if merchants_to_sync_qs.exists():
        merchants_ids = set(merchants_to_sync_qs.values_list('id', flat=True))
        for merchant in merchants_to_sync_qs.iterator():
            create_or_update_merchant_in_navision.run(merchant.id)
            merchant.updated = tznow()
        Merchant.objects.bulk_update(merchants_to_sync_qs, ["updated"])

        not_synced_merchants_ids = set(merchants_to_sync_qs.values_list('id', flat=True))

        if not_synced_merchants_ids:
            message += (
                f'Merchants not synced properly: '
                f'{", ".join(map(str, sorted(not_synced_merchants_ids)))}. '
                f'You should sync them again later.'
            )

        if synced_merchants_ids := merchants_ids - not_synced_merchants_ids:
            message += (
                f'These merchants have been successfully synced: '
                f'{", ".join(map(str, sorted(synced_merchants_ids)))} \n'
            )

    invoices_ids_to_sync = list(
        invoices_to_sync_qs.exclude(
            merchant__synced_at__isnull=True,
        ).values_list('id', flat=True)
    )
    if invoices_ids_to_sync:
        send_not_synced_invoices_to_navision.delay(invoices_ids_to_sync)
        message += (
            f'These invoices have been scheduled for sync, check them in a while: '
            f'{", ".join(map(str, sorted(invoices_ids_to_sync)))} \n'
        )

    invalid_invoices_ids = list(
        queryset.exclude(id__in=invoices_ids_to_sync).values_list('id', flat=True)
    )
    if invalid_invoices_ids:
        message += (
            f'These invoices cannot be synced: {", ".join(map(str, sorted(invalid_invoices_ids)))} '
            f'- make sure that selected invoices are not synced already or try again later'
        )

    if message:
        messages.info(request, message)


@admin.action(
    description='Approve selected invoices',
    permissions=['navision_operator'],
)
def approve_selected_invoices(_model_admin, request, queryset):
    queryset.filter(
        Q(approved=False) | Q(approved__isnull=True),
    ).update(approved=True)
    messages.info(request, 'Invoices approved')


class InvoiceAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.INVOICING_AND_ERP_ADVANCED,)
    read_only_access_groups = (GroupNameV2.INVOICING_AND_ERP_BASIC,)
    list_display = (
        'id',
        'cycle_start',
        'cycle_end',
        'total_amount',
        'number_of_items',
        'booksy_id',
        'merchant_booksy_id',
        'business_ID',
        'business_name',
        'invoice_number',
        'service',
        'source',
        'invoice_date',
        'approved',
        'is_production',
        'status',
        'created',
        'synced_at',
        'summary_service',
    )

    fieldsets = (
        (
            None,
            {
                'fields': (
                    'id',
                    'booksy_id',
                    'merchant_booksy_id',
                    'invoice_number',
                    'invoice_file',
                    'business_ID',
                    'business_name',
                    'invoice_emails',
                    'invoice_date',
                )
            },
        ),
        (
            'Offline only',
            {
                'fields': (
                    'affected_subscriptions',
                    'sales_date',
                    'payment_due_date',
                )
            },
        ),
        (
            'Technical',
            {
                'fields': (
                    'created',
                    'updated',
                    'deleted',
                    'aggregation_uuid',
                    'navision_id',
                )
            },
        ),
        (
            'Summary',
            {
                'fields': (
                    'service',
                    'source',
                    'currency',
                )
            },
        ),
        (
            'Sync',
            {
                'fields': (
                    'status',
                    'queued_at',
                    'synced_at',
                    'is_production',
                    'approved',
                )
            },
        ),
    )
    readonly_fields = (
        'booksy_id',
        'merchant_booksy_id',
        'business_ID',
        'affected_subscriptions',
    )
    search_fields = (
        '=id',
        '=business_id',
        '=merchant__tax_id',
        '=invoice_number',
        # The next two lookups must stay with double underscores at the end
        # until a more thorough admin refactor,
        # otherwise the keyword search will break.
        'business_summary__report__id',
        'subscription_summary__subscription__id',
    )
    list_filter = (
        'status',
        'source',
        'service',
        'invoice_date',
    )
    inlines = [
        InvoiceItemAdminInline,
        InvoiceErrorResponseInline,
    ]
    actions = [
        remove_test_invoices,
        remove_selected_invoices,
        sync_selected_invoices,
        approve_selected_invoices,
    ]
    list_select_related = True
    date_hierarchy = 'invoice_date'

    @staticmethod
    def summary_service(invoice):
        if summary := invoice.business_summary.first():
            return summary.report.service
        return None

    def get_search_results(self, request, queryset, search_term):
        old_queryset, use_distinct = super().get_search_results(request, queryset, search_term)

        merchant_true_id = Merchant.reverse_merchant_id(search_term)

        if merchant_true_id:
            return old_queryset | queryset.filter(merchant__id=merchant_true_id), use_distinct

        return old_queryset, use_distinct

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .prefetch_related('subscription_summary', 'business_summary')
        )

    @staticmethod
    def booksy_id(invoice: Invoice):
        return invoice.booksy_id

    @staticmethod
    # pylint: disable=invalid-name
    def business_ID(invoice: Invoice):  # has to be named in this way because of name conflicts
        if invoice.business_id is not None:
            return format_html(
                '<a href="{hyperlink}" class="viewsitelink">{business_id}</a>',
                hyperlink=reverse('admin:business_business_change', args=(invoice.business_id,)),
                business_id=invoice.business_id,
            )
        return None

    @staticmethod
    def merchant_booksy_id(invoice: Invoice):
        return format_html(
            '<a href="{hyperlink}" class="viewsitelink">{merchant_booksy_id}</a>',
            hyperlink=reverse('admin:navision_merchant_change', args=(invoice.merchant_id,)),
            merchant_booksy_id=invoice.merchant.merchant_id,
        )

    @staticmethod
    def affected_subscriptions(invoice: Invoice):
        return format_html_join(
            ',',
            '<a href="{}" class="viewsitelink">{}</a>',
            (
                (
                    reverse('admin:purchase_subscription_change', args=(summary.subscription_id,)),
                    summary.subscription_id,
                )
                for summary in invoice.subscription_summary.all()
            ),
        )

    def number_of_items(self, invoice):
        return Invoice.number_of_items(invoice.id)

    def total_amount(self, invoice):
        return Invoice.total_amount(invoice.id)

    def cycle_start(self, invoice):
        return Invoice.cycle_start(invoice.id)

    def cycle_end(self, invoice):
        return Invoice.cycle_end(invoice.id)

    def has_navision_admin_permission(self, request, _obj=None):
        return check_navision_admin_permission(request)

    def has_navision_operator_permission(self, request, _obj=None):
        return check_navision_operator_permission(request)


admin.site.register(Invoice, InvoiceAdmin)


class TaxRateForm(forms.ModelForm):
    class Meta:
        model = TaxRate
        fields = (
            'tax_group',
            'tax_rate',
            'service',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if 'tax_group' in self.fields:
            if settings.NAVISION_USE_TAX_GROUPS:
                self.fields['tax_group'].required = True
            else:
                self.fields['tax_group'].disabled = True
                self.fields['tax_group'].required = False

    def clean_tax_rate(self):
        tax_rate = self.cleaned_data.get('tax_rate')
        if (tax_rate is None) or (tax_rate < 0) or (tax_rate > 1):
            self.add_error(
                'tax_rate', 'Tax rate must be higher or equal to 0 and lesser or equal to 1.0'
            )

        return tax_rate

    def clean_region(self):
        region = self.cleaned_data.get('region')
        if region is not None and region.type != settings.NAVISION_TAX_AREA:
            self.add_error(
                'region',
                f'TaxRate region in `{settings.API_COUNTRY}` must have type '
                f'of `{settings.NAVISION_TAX_AREA}`',
            )

        return region

    def clean(self):
        region = self.cleaned_data.get('region')

        if (
            settings.NAVISION_TAX_AREA == Region.Type.COUNTRY
            and not settings.NAVISION_USE_TAX_GROUPS
        ):
            country_info = BASE_INFO.get(settings.API_COUNTRY)

            current_country = Region.objects.filter(
                name=country_info.get('name'),
                type=Region.Type.COUNTRY,
            ).first()
            self.instance.region = current_country
            region = current_country

        tax_rate = self.cleaned_data.get('tax_rate')
        tax_group = self.cleaned_data.get('tax_group')
        valid_from = self.cleaned_data.get('valid_from')
        service = self.cleaned_data.get('service')

        if all([valid_from, tax_rate, service]) and (region or tax_group):
            old_tax_rate: TaxRate = TaxRate.find_by_region(
                region,
                service,
            )

            if old_tax_rate is not None:
                if old_tax_rate.valid_from > valid_from:
                    self.add_error(
                        '',
                        f'Previous TaxRate (ID: {old_tax_rate.id} have '
                        f'Valid from ({old_tax_rate.valid_from}) '
                        f'set after {valid_from}',
                    )

                if old_tax_rate.tax_rate == tax_rate:
                    self.add_error(
                        '',
                        f'Current TaxRate (ID: {old_tax_rate.id}, '
                        f'tax rate: {old_tax_rate.tax_rate}) '
                        f'have same tax_rate: {tax_rate}',
                    )

        return super().clean()

    def save(self, commit=True):
        with transaction.atomic():
            if settings.NAVISION_USE_TAX_GROUPS:
                old_tax_rate: TaxRate = TaxRate.find_by_group(
                    self.instance.tax_group,
                    self.instance.service,
                )
            else:
                old_tax_rate: TaxRate = TaxRate.find_by_region(
                    self.instance.region,
                    self.instance.service,
                )

            if old_tax_rate is not None:
                old_tax_rate.valid_to = self.instance.valid_from
                old_tax_rate.save(update_fields=['valid_to'])

            return super().save(commit=commit)


class OutdatedFilter(admin.SimpleListFilter):
    title = "State"
    parameter_name = "state"

    def lookups(self, request, model_admin):
        return (
            ("outdated", "Outdated"),
            ("active", "Active"),
        )

    def queryset(self, request, queryset):
        value = self.value()

        if value == "outdated":
            return queryset.filter(valid_to__isnull=False)

        if value == "active":
            return queryset.filter(valid_to__isnull=True)

        return queryset


class TaxRateAdmin(NoDelMixin, NoChangeMixin, GroupPermissionMixin, admin.ModelAdmin):
    full_access_groups = (GroupNameV2.INVOICING_AND_ERP_ADVANCED,)
    read_only_access_groups = (GroupNameV2.INVOICING_AND_ERP_BASIC,)
    form = TaxRateForm
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'tax_rate',
                    'service',
                    'region',
                    'tax_group',
                    'valid_from',
                    'valid_to',
                    'created',
                    'deleted',
                    'other_tax_rates',
                )
            },
        ),
    )
    list_display = (
        'region_name',
        'tax_rate',
        'service',
        'valid_from',
        'valid_to',
        'up_to_date',
        'created',
        'deleted',
    )
    list_filter = (OutdatedFilter, 'region__type')
    raw_id_fields = ('region',)
    search_fields = (
        'region__name',
        'region__type',
    )

    def get_readonly_fields(self, request, obj=None):
        return (
            'valid_from',
            'valid_to',
            'created',
            'deleted',
            'other_tax_rates',
        )

    def get_list_display(self, request):
        if settings.NAVISION_USE_TAX_GROUPS:
            return (
                'tax_group',
                'service',
                'valid_from',
                'valid_to',
                'up_to_date',
                'created',
                'deleted',
            )

        if settings.NAVISION_TAX_AREA == RegionType.ZIP:
            return (
                'zipcode',
                'tax_rate',
                'service',
                'valid_from',
                'valid_to',
                'up_to_date',
                'created',
                'deleted',
            )

        if settings.NAVISION_TAX_AREA == RegionType.COUNTRY:
            return (
                'country',
                'tax_rate',
                'service',
                'valid_from',
                'valid_to',
                'up_to_date',
                'created',
                'deleted',
            )

        return (
            'region_name',
            'tax_rate',
            'service',
            'valid_from',
            'valid_to',
            'up_to_date',
            'created',
            'deleted',
        )

    def get_fieldsets(self, request, obj=None):
        if settings.NAVISION_USE_TAX_GROUPS:
            fields = (
                'tax_rate',
                'tax_group',
                'service',
                'valid_from',
                'valid_to',
                'other_tax_rates',
            )
        elif settings.NAVISION_TAX_AREA == RegionType.COUNTRY:
            fields = (
                'tax_rate',
                'service',
                'valid_from',
                'valid_to',
                'created',
                'deleted',
                'other_tax_rates',
            )
        else:
            fields = (
                'tax_rate',
                'service',
                'region',
                'valid_from',
                'valid_to',
                'created',
                'deleted',
                'other_tax_rates',
            )

        return (
            (
                None,
                {
                    'fields': fields,
                },
            ),
        )

    def has_add_permission(self, request):
        # Tax rates in **us** can be added only during sync with navision
        return settings.API_COUNTRY != Country.US and super().has_add_permission(request)

    def get_queryset(self, request):
        return TaxRate.objects.get_queryset()

    @staticmethod
    def region_name(obj):
        return obj.region.name

    region_name.short_description = "Region"

    @staticmethod
    def up_to_date(obj):
        return obj.is_up_to_date

    up_to_date.short_description = "Active"

    @staticmethod
    def country(obj):
        return obj.region.name

    country.short_description = "Country"

    @staticmethod
    def zipcode(obj):
        return obj.region.name

    zipcode.short_description = "Zipcode"

    @staticmethod
    def other_tax_rates(obj):
        other_rates = (
            TaxRate.objects.filter(region=obj.region, service=obj.service)
            .exclude(pk=obj.pk)
            .order_by('-valid_from')[:20]
        )
        if not other_rates.exists():
            return 'No other rates found for this region and service.'
        output = '<ul>'
        for rate in other_rates:
            rate_data_string = f'{rate.tax_rate} ({rate.valid_from} - {rate.valid_to})'
            url = reverse('admin:navision_taxrate_change', args=(rate.id,))
            output += f'<li><a href="{url}">{rate_data_string}</a></li>'
        output += '</ul>'

        return format_html(output)

    other_tax_rates.short_description = 'Other tax rates for the given region and service, max 20.'


admin.site.register(TaxRate, TaxRateAdmin)


class ErrorInline(ReadOnlyTabular, admin.TabularInline):
    model = InvoicingError
    extra = 0


@admin.action(
    description='Approve invoices',
    permissions=['navision_operator'],
)
def approve_invoices(_model_admin, request, queryset):
    for summary_id in queryset.values_list('id', flat=True):
        approve_invoices_in_selected_invoice_summary_task.delay(summary_id)
    messages.success(request, 'Invoices approved, check them in a while')


@admin.action(
    description='Remove unapproved invoices',
    permissions=['navision_operator'],
)
def remove_unapproved_invoices(_model_admin, request, queryset):
    for summary_id in queryset.values_list('id', flat=True):
        remove_unapproved_invoices_from_summaries_task.delay(summary_id)
    messages.success(
        request,
        'Unapproved invoices from selected summaries have been scheduled for deletion, '
        'check it in a while.',
    )


@admin.action(
    description='Remove test invoices in selected summaries',
    permissions=['navision_admin'],
)
def remove_test_invoices_in_selected_summaries(_model_admin, request, queryset):
    for summary_id in queryset.values_list('id', flat=True):
        remove_test_invoices_from_invoicing_summary_task.delay(summary_id)
    messages.success(
        request,
        'Test invoices have been scheduled for deletion, check it in a while.',
    )


class InvoicingSummaryAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.INVOICING_AND_ERP_ADVANCED,)
    read_only_access_groups = (GroupNameV2.INVOICING_AND_ERP_BASIC,)
    list_display = (
        'report_link',
        'invoices',
        'invoice_count',
        'approved',
        'invoices_synced_with_navision',
        'invoicing_errors',
        'invoicing_target',
        'service',
        'source',
    )
    fields = (
        'invoicing_date',
        'invoicing_target',
        'invoices',
        'invoices_per_payment_source',
        'items_per_service',
        'invoice_items_tax_total',
        'invoice_items_gross_total',
        'invoice_items_net_total',
        'invoices_per_cycle',
        'invoice_items_per_cycle',
        'businesses_per_cycle',
        'total_gross_amounts_per_cycle',
        'operator_full_name',
        'operator_email',
        'service',
        'source',
    )
    readonly_fields = fields
    list_filter = (
        'service',
        'source',
    )
    inlines = [ErrorInline]
    actions = [
        approve_invoices,
        remove_unapproved_invoices,
        remove_test_invoices_in_selected_summaries,
    ]

    @staticmethod
    def invoices_per_payment_source(summary: InvoicingSummary):
        count = (
            BusinessInvoiceSummary.objects.filter(
                pk__in=summary.business_invoices.distinct('invoice_id').values_list('pk', flat=True)
            )
            .select_related('invoice__items')
            .values('invoice__source')
            .annotate(cnt=Count('invoice__source'))
        )
        summary = []
        for source_count in count:
            source_label = InvoicePaymentSource.choices_map().get(source_count['invoice__source'])
            summary.append(f"{source_label}: {source_count['cnt']}")

        items = [f'<li>{entry}</li>' for entry in summary]
        output = f"<ul>{''.join(items)}</ul>"
        return format_html(output)

    invoices_per_payment_source.short_description = 'Invoice per source'

    @staticmethod
    def items_per_service(summary: InvoicingSummary):
        service_counts = (
            BusinessInvoiceSummary.objects.filter(
                pk__in=summary.business_invoices.distinct('invoice_id').values_list('pk', flat=True)
            )
            .select_related('invoice__items')
            .exclude(invoice__items__service=None)
            .values('invoice__items__service')
            .annotate(count=Count('invoice__items__service'))
            .order_by()
        )

        items = [
            f"<li>{count['invoice__items__service']}: {count['count']}</li>"
            for count in service_counts
        ]
        output = f"<ul>{''.join(items)}</ul>"

        return format_html(output)

    items_per_service.short_description = 'Invoice items per service'

    @staticmethod
    def invoices(summary: InvoicingSummary):
        return format_html(
            '<a href="{}?business_summary__report__id={}" class="viewsitelink">{}</a>',
            reverse('admin:navision_invoice_changelist'),
            summary.id,
            summary.invoicing_date,
        )

    @staticmethod
    def invoice_count(summary: InvoicingSummary):
        return summary.business_invoices.distinct('invoice_id').count()

    @staticmethod
    def approved(summary: InvoicingSummary):
        return summary.business_invoices.filter(invoice__approved=True).count()

    @staticmethod
    def invoicing_errors(summary: InvoicingSummary):
        return summary.invoicing_errors.count()

    @staticmethod
    def invoices_synced_with_navision(summary: InvoicingSummary):
        return summary.business_invoices.filter(
            invoice__status=Invoice.Status.SENT,
        ).count()

    @staticmethod
    def operator_email(summary: InvoicingSummary):
        return summary.operator.email

    @staticmethod
    def operator_full_name(summary: InvoicingSummary):
        return summary.operator.full_name

    @staticmethod
    @lru_booksy_cache(timeout=30 * 60, skip_in_pytest=True)
    def billing_cycle_calculations(summary: InvoicingSummary):
        invoices_qs = (
            BusinessInvoiceSummary.objects.filter(
                pk__in=summary.business_invoices.distinct('invoice_id').values_list('pk', flat=True)
            )
            .prefetch_related('invoice__items')
            .filter(invoice__items__billing_cycle_end__isnull=False)
            .distinct()
        )

        invoice_queries = invoices_qs.values(
            cycle_start=F('invoice__items__billing_cycle_start__date'),
            cycle_end=F('invoice__items__billing_cycle_end__date'),
        ).annotate(
            invoice_count=Count('invoice', distinct=True),
            items_count=Count(
                'invoice__items',
                filter=~Q(invoice__items__service=Invoice.Service.DISCOUNT),
            ),
            business_count=Count('invoice__business_id', distinct=True),
            cycle_sum=Sum(
                Case(
                    When(
                        invoice__items__service=Invoice.Service.DISCOUNT,
                        then=Coalesce(F('invoice__items__base_gross_value'), Value(0)) * Value(-1),
                    ),
                    default=Greatest(
                        Coalesce(F('invoice__items__base_gross_value'), Value(0))
                        - Coalesce(F('invoice__items__discount_gross_value'), Value(0)),
                        Value(0),
                        output_field=DecimalField(),
                    )
                    * F('invoice__items__quantity'),
                    output_field=DecimalField(),
                ),
            ),
        )

        invoice_count_items = [
            f"<li>{query['cycle_start']} - {query['cycle_end']}: {query['invoice_count']}</li>"
            for query in invoice_queries
        ]

        items_count_items = [
            f"<li>{query['cycle_start']} - {query['cycle_end']}: {query['items_count']}</li>"
            for query in invoice_queries
        ]

        business_count_items = [
            f"<li>{query['cycle_start']} - {query['cycle_end']}: {query['business_count']}</li>"
            for query in invoice_queries
        ]

        cycle_sum_items = [
            f"<li>{query['cycle_start']} - {query['cycle_end']}: {query['cycle_sum']:.2f}</li>"
            for query in invoice_queries
        ]

        return invoice_count_items, items_count_items, business_count_items, cycle_sum_items

    @staticmethod
    def invoices_per_cycle(summary: InvoicingSummary):
        items = InvoicingSummaryAdmin.billing_cycle_calculations(summary)[0]
        output = f"<ul>{''.join(items)}</ul>"

        return format_html(output)

    @staticmethod
    def invoice_items_per_cycle(summary: InvoicingSummary):
        items = InvoicingSummaryAdmin.billing_cycle_calculations(summary)[1]
        output = f"<ul>{''.join(items)}</ul>"

        return format_html(output)

    @staticmethod
    def businesses_per_cycle(summary: InvoicingSummary):
        items = InvoicingSummaryAdmin.billing_cycle_calculations(summary)[2]
        output = f"<ul>{''.join(items)}</ul>"

        return format_html(output)

    @staticmethod
    def total_gross_amounts_per_cycle(summary: InvoicingSummary):
        items = InvoicingSummaryAdmin.billing_cycle_calculations(summary)[3]
        output = f"<ul>{''.join(items)}</ul>"

        return format_html(output)

    @staticmethod
    def report_link(summary: InvoicingSummary):
        return format_html(
            '<a href="{hyperlink}" class="viewsitelink">{report}</a>',
            hyperlink=reverse('admin:navision_invoicingsummary_change', args=(summary.id,)),
            report=summary,
        )

    def has_navision_operator_permission(self, request, _obj=None):
        return check_navision_operator_permission(request)

    def has_navision_admin_permission(self, request, _obj=None):
        return check_navision_admin_permission(request)


admin.site.register(InvoicingSummary, InvoicingSummaryAdmin)


class TaxGroupAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.INVOICING_AND_ERP_ADVANCED,)
    read_only_access_groups = (GroupNameV2.INVOICING_AND_ERP_BASIC,)
    list_display = (
        'name',
        'accounting_group',
    )
    fields = (
        'name',
        'accounting_group',
        'region',
    )
    raw_id_fields = ('region',)

    def has_view_permission(self, request, obj=None):
        return check_navision_operator_permission(request)

    @staticmethod
    def _check_us_permission(request):
        if settings.API_COUNTRY == Country.US:
            return False
        return check_navision_operator_permission(request)

    def has_add_permission(self, request):
        return self._check_us_permission(request)

    def has_change_permission(self, request, obj=None):
        return self._check_us_permission(request)

    def has_delete_permission(self, request, obj=None):
        return self._check_us_permission(request)


admin.site.register(TaxGroup, TaxGroupAdmin)


class NavisionSettingsForm(forms.ModelForm):
    run_online_saas_invoicing = forms.BooleanField(
        initial=False,
        required=False,
        help_text=(
            'WARNING! this is old invoicing process (based on charged transaction). '
            'If you want to invoice based on billing cycle start date you have to go to '
            'Home Page -> Invoice SaaS Online based on Billing Cycle',
        ),
    )
    run_online_boost_invoicing = forms.BooleanField(
        initial=False,
        required=False,
    )
    run_online_invoicing_from = forms.DateField(
        initial=lambda: tznow().date(),
    )
    run_online_invoicing_to = forms.DateField(
        initial=lambda: tznow().date(),
    )

    class Meta:
        model = NavisionSettings
        fields = (
            'region',
            'merchants_send_per_hour',
            'invoices_send_per_hour',
            'run_online_saas_invoicing',
            'run_online_boost_invoicing',
            'run_online_invoicing_from',
            'run_online_invoicing_to',
        )

    def clean(self):
        cleaned_data = super().clean()

        if (
            cleaned_data.get('run_online_invoicing_from')
            and cleaned_data.get('run_online_invoicing_to')
            and cleaned_data['run_online_invoicing_from'] > cleaned_data['run_online_invoicing_to']
        ):
            self.add_error(
                'run_online_invoicing_to',
                'End of date range cannot be set before start',
            )

        if (
            settings.LIVE_DEPLOYMENT
            and cleaned_data.get('run_online_invoicing_to')
            and cleaned_data.get('run_online_invoicing_to') > tznow().date()
        ):
            self.add_error(
                'run_online_invoicing_to',
                'You cannot set a future date on production',
            )

        return cleaned_data

    def schedule_online_invoicing(self, user_id=None):
        invoice_from = self.cleaned_data['run_online_invoicing_from']
        invoice_to = self.cleaned_data['run_online_invoicing_to']

        now = tznow().date()

        offset_start = (now - invoice_from).days
        offset_end = (now - invoice_to).days

        for i in range(offset_start, offset_end - 1, -1):
            if self.cleaned_data['run_online_saas_invoicing']:
                create_new_billing_saas_invoices_task.delay(
                    offset_days=i,
                    user_id=user_id,
                )
            if self.cleaned_data['run_online_boost_invoicing']:
                create_boost_online_invoices_task.delay(
                    offset_days=i,
                    user_id=user_id,
                )


class NavisionSettingsAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.INVOICING_AND_ERP_ADVANCED,)
    read_only_access_groups = (GroupNameV2.INVOICING_AND_ERP_BASIC,)
    form = NavisionSettingsForm
    list_display = (
        'id',
        'region',
    )
    fieldsets = (
        (None, {'fields': ('region',)}),
        (
            'Online invoice triggers',
            {
                'fields': (
                    'run_online_saas_invoicing',
                    'run_online_boost_invoicing',
                    'run_online_invoicing_from',
                    'run_online_invoicing_to',
                )
            },
        ),
        (
            'Invoice details settings',
            {'fields': ('enable_invoice_details_editing',)},
        ),
        (
            'Navision API integration',
            {
                'fields': (
                    'merchants_send_per_hour',
                    'invoices_send_per_hour',
                    'navision_integration_enabled',
                    'navision_invoicing_enabled',
                    'navision_sync_invoices_enabled',
                    'charge_to_invoice_delay',
                    'auto_run_saas_online_invoicing',
                    'auto_run_boost_online_invoicing',
                )
            },
        ),
    )
    readonly_fields = ('region',)
    raw_id_fields = ('region',)

    def has_change_permission(self, request, obj=None):
        return check_navision_admin_permission(request)

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)

        NavisionSettings.get_current_settings.cache_clear()

        user_id = request.user.id
        form.schedule_online_invoicing(user_id)


admin.site.register(NavisionSettings, NavisionSettingsAdmin)


@admin.action(
    description='Process eligible errors',
    permissions=['navision_operator'],
)
def process_eligible_errors(_model_admin, request, queryset):
    error_ids = queryset.filter(status=InvoicingErrorStatus.CREATED).values_list('id', flat=True)
    for error_id in error_ids.iterator():
        process_error_task.delay(error_id=error_id, operator_id=request.user.id)
    messages.success(
        request,
        'Attempted to process eligible errors, check them in a while.',
    )


@admin.action(
    description='Reject selected errors',
    permissions=['navision_operator'],
)
def reject_errors(_model_admin, request, queryset):
    queryset.filter(status=InvoicingErrorStatus.CREATED).update(
        status=InvoicingErrorStatus.REJECTED,
        _history={
            'operator': get_user_from_django_request(request),
            'metadata': {'action': 'reject_errors'},
            'fields': {'status'},
        },
    )
    messages.success(request, 'Selected errors marked as rejected')


class InvoicingErrorAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.INVOICING_AND_ERP_ADVANCED,)
    read_only_access_groups = (GroupNameV2.INVOICING_AND_ERP_BASIC,)
    COMMON_FIELDS = (
        'id',
        'report_link',
        'service',
        'source',
        'invoicing_target',
        'invoiced_object_link',
        'category',
        'status',
        'message',
    )

    list_display = COMMON_FIELDS + ('is_fixed',)
    readonly_fields = COMMON_FIELDS + ('buyer', 'error', 'object_history')
    fields = COMMON_FIELDS + ('buyer', 'error', 'is_fixed', 'object_history')

    list_filter = (
        'category',
        'status',
        'service',
        'source',
        'is_fixed',
    )

    search_fields = ("message", "report__operator__email")

    actions = [
        process_eligible_errors,
        reject_errors,
    ]

    query_fields_placeholders = {
        "report__operator__email": "operator email",
    }

    def changelist_view(self, request, extra_context=None):
        messages.info(
            request,
            'Only CREATED Invoicing Errors can be processed.'
            ' Errors with PROCESSED and REJECTED statuses will be omitted.',
        )
        messages.info(
            request,
            'Errors can be searched by Buyer ID (in the relevant searchfield '
            'and in the Keyword searchbar)'
            ' and by Business ID (in the Keyword searchbar).',
        )

        self.list_editable = ()
        if check_navision_operator_permission(request):
            self.list_editable = ('is_fixed',)

        return super().changelist_view(request, extra_context=extra_context)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(deleted__isnull=True)

    def get_search_results(self, request, queryset, search_term):
        if not search_term:
            return queryset, True

        old_queryset, use_distinct = super().get_search_results(request, queryset, search_term)

        try:
            search_term_int = int(search_term)
        except ValueError:
            return old_queryset, use_distinct

        business_content_type = ContentType.objects.get_for_model(Business)
        sub_content_type = ContentType.objects.get_for_model(Subscription)

        business_queryset = queryset.filter(
            content_type=business_content_type, object_id=search_term_int
        )

        related_sub_ids = Subscription.objects.filter(business__id=search_term_int).values_list(
            'id', flat=True
        )

        sub_queryset = queryset.filter(content_type=sub_content_type, object_id__in=related_sub_ids)

        buyer_queryset = queryset.filter(buyer__id=search_term_int)

        queryset = business_queryset | sub_queryset | buyer_queryset

        return old_queryset | queryset, use_distinct

    @staticmethod
    def report_link(error: InvoicingError):
        return format_html(
            '<a href="{hyperlink}" class="viewsitelink">{report}</a>',
            hyperlink=reverse('admin:navision_invoicingsummary_change', args=(error.report_id,)),
            report=error.report,
        )

    report_link.short_description = 'Error invoicing summary'

    @staticmethod
    def invoiced_object_link(error: InvoicingError):
        content_object = error.invoiced_object
        url = admin_link(content_object)
        return format_html('<a href="{}">{}</a>', url, content_object)

    invoiced_object_link.short_description = 'Invoiced object (business or sub)'

    def object_history(self, obj):
        return HistoryRetriever(self.model.history_model()).get_changes(obj)

    object_history.short_description = 'Object changes (history)'

    def has_navision_admin_permission(self, request, _obj=None):
        return check_navision_admin_permission(request)

    def has_navision_operator_permission(self, request, _obj=None):
        return check_navision_operator_permission(request)

    def has_change_permission(self, request, obj=None):
        return check_navision_operator_permission(request)

    def save_model(self, request, obj, form, change) -> None:
        obj.save(_history={'operator': request.user.id})


admin.site.register(InvoicingError, InvoicingErrorAdmin)
