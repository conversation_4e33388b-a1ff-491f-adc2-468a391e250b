"""Tests for the AddressValidationService domain service."""

from unittest.mock import Mock
import pytest

from webapps.google_places.domain.services.address_validation_service import (
    AddressValidationDomainService,
)
from webapps.google_places.domain.dtos.address_validation import GoogleAddressValidationResponse
from webapps.google_places.application.dtos.address_validation import PinLocation
from webapps.google_places.domain.dtos.shared import AddressComponents
from webapps.google_places.domain.enums import PinLocationStatus, PossibleNextAction, Country


class TestAddressValidationService:
    """Test the AddressValidationService domain service."""

    @pytest.mark.parametrize(
        "possible_next_action, pin_status, expected_result",
        [
            # Valid combinations
            (PossibleNextAction.ACCEPT, PinLocationStatus.OK, True),
            (PossibleNextAction.ACCEPT, PinLocationStatus.WARN, True),
            (PossibleNextAction.CONFIRM, PinLocationStatus.OK, True),
            (PossibleNextAction.CONFIRM, PinLocationStatus.WARN, True),
            (PossibleNextAction.CONFIRM_ADD_SUBPREMISES, PinLocationStatus.OK, True),
            (PossibleNextAction.CONFIRM_ADD_SUBPREMISES, PinLocationStatus.WARN, True),
            # Invalid combinations - invalid next action
            (PossibleNextAction.FIX, PinLocationStatus.OK, False),
            (PossibleNextAction.FIX, PinLocationStatus.WARN, False),
            # Invalid combinations - invalid pin status
            (PossibleNextAction.ACCEPT, PinLocationStatus.ERROR, False),
            (PossibleNextAction.CONFIRM, PinLocationStatus.ERROR, False),
            (PossibleNextAction.CONFIRM_ADD_SUBPREMISES, PinLocationStatus.ERROR, False),
        ],
    )
    def test_is_address_valid(self, possible_next_action, pin_status, expected_result):
        """Test that is_address_valid correctly validates addresses based on business rules."""
        mock_google_response = Mock(spec=GoogleAddressValidationResponse)
        mock_google_response.possible_next_action = possible_next_action

        mock_pin_location = Mock(spec=PinLocation)
        mock_pin_location.status = pin_status

        result = AddressValidationDomainService.is_address_valid(
            mock_google_response, mock_pin_location
        )

        assert result == expected_result

    @pytest.mark.parametrize(
        "address_lines, street_number, country, expected_lines",
        [
            (["Main St"], "123", Country.US, ["123 Main St"]),
            # Prepending countries (US, CA, GB, IE, AU, FR, ZA)
            (["Main St"], "123", Country.US, ["123 Main St"]),
            (["Downing Street"], "10", Country.GB, ["10 Downing Street"]),
            (["Champs-Élysées"], "101", Country.FR, ["101 Champs-Élysées"]),
            (["Collins Street"], "456", Country.AU, ["456 Collins Street"]),
            (["Cape Town Road"], "789", Country.ZA, ["789 Cape Town Road"]),
            # Appending countries (PL, ES, etc.)
            (["Aleje Jerozolimskie"], "98", Country.PL, ["Aleje Jerozolimskie 98"]),
            (["Calle de Alcalá"], "52", Country.ES, ["Calle de Alcalá 52"]),
            # Multiple lines
            (["Main St", "Apt 4B"], "123", Country.US, ["123 Main St", "Apt 4B"]),
            (
                ["Aleje Jerozolimskie", "Piętro 2"],
                "98",
                Country.PL,
                ["Aleje Jerozolimskie 98", "Piętro 2"],
            ),
            # Edge cases
            (["Main St"], "", Country.US, ["Main St"]),
            ([], "123", Country.US, []),
            (["123 Main St"], "123", Country.US, ["123 Main St"]),
        ],
    )
    def test_format_address_lines(self, address_lines, street_number, country, expected_lines):
        """
        Test that format_address_lines correctly formats address lines
        based on country conventions.
        """
        address_components = AddressComponents(
            street_number=street_number,
            street="",
            city="",
            country=country,
            zipcode="",
            subpremise="",
        )
        result = AddressValidationDomainService.format_address_lines(
            address_lines, address_components, country
        )

        assert result == expected_lines

    @pytest.mark.parametrize(
        "address_lines, street_number, country, subpremise, expected_lines",
        [
            # Subpremise with single line - should create second line
            (["Main St"], "123", Country.US, "Apt 4B", ["123 Main St", "Apt 4B"]),
            (
                ["Aleje Jerozolimskie"],
                "98",
                Country.PL,
                "Unit 5",
                ["Aleje Jerozolimskie 98", "Unit 5"],
            ),
            # Subpremise with existing second line - should append to second line
            (
                ["Main St", "Building A"],
                "123",
                Country.US,
                "Apt 4B",
                ["123 Main St", "Building A Apt 4B"],
            ),
            (
                ["Aleje Jerozolimskie", "Budynek B"],
                "98",
                Country.PL,
                "Unit 5",
                ["Aleje Jerozolimskie 98", "Budynek B Unit 5"],
            ),
            # Subpremise already present - should not duplicate
            (["Main St", "Apt 4B"], "123", Country.US, "Apt 4B", ["123 Main St", "Apt 4B"]),
            (
                ["Main St", "Building A Apt 4B"],
                "123",
                Country.US,
                "Apt 4B",
                ["123 Main St", "Building A Apt 4B"],
            ),
            # Empty subpremise - should not affect formatting
            (["Main St"], "123", Country.US, "", ["123 Main St"]),
            (["Main St", "Building A"], "123", Country.US, "", ["123 Main St", "Building A"]),
        ],
    )
    def test_format_address_lines_with_subpremise(
        self, address_lines, street_number, country, subpremise, expected_lines
    ):
        """
        Test that format_address_lines correctly handles subpremise formatting.
        """
        address_components = AddressComponents(
            street_number=street_number,
            street="",
            city="",
            country=country,
            zipcode="",
            subpremise=subpremise,
        )
        result = AddressValidationDomainService.format_address_lines(
            address_lines, address_components, country
        )

        assert result == expected_lines

    @pytest.mark.parametrize(
        "street_number, street, subpremise, country, expected_lines",
        [
            # Street only
            ("123", "Main Street", None, Country.US, ["123 Main Street"]),
            # Street and subpremise
            ("456", "Collins Street", "Apt 4B", Country.AU, ["456 Collins Street", "Apt 4B"]),
            # Subpremise only (no street)
            ("", "", "5", Country.PL, ["5"]),
            # Street only (no street_number)
            ("", "Broadway", None, Country.US, ["Broadway"]),
            # Appending country format (PL)
            (
                "98",
                "Aleje Jerozolimskie",
                "Piętro 2",
                Country.PL,
                ["Aleje Jerozolimskie 98", "Piętro 2"],
            ),
            # Prepending country format (FR)
            (
                "101",
                "Champs-Élysées",
                "Appartement 3",
                Country.FR,
                ["101 Champs-Élysées", "Appartement 3"],
            ),
        ],
    )
    def test_format_address_lines_empty_input(
        self, street_number, street, subpremise, country, expected_lines
    ):
        address_components = AddressComponents(
            street_number=street_number,
            street=street,
            city="Test City",
            country=country.value,
            zipcode="12345",
            subpremise=subpremise,
        )
        result = AddressValidationDomainService.format_address_lines(
            address_lines=[], address_components=address_components, country=country
        )
        assert result == expected_lines

    @pytest.mark.parametrize(
        "address_lines, street_number, expected_skip",
        [
            # Street number present in both - don't skip
            (["123 Main St"], "123", False),
            (["456 Collins Street"], "456", False),
            # Street number missing from address_lines but present in components - skip
            (["Main St"], "123", True),
            (["Collins Street"], "456", True),
            (["Broadway"], "789", True),
            # Street number not in components - don't skip
            (["Main St"], "", False),
            (["Collins Street"], None, False),
            # Empty address_lines - don't skip (handled by format_address_lines)
            ([], "123", False),
            # Street number partially matches - don't skip
            (["1234 Main St"], "123", False),
            (["Main St 123"], "123", False),
        ],
    )
    def test_should_skip_pin_location_validation(self, address_lines, street_number, expected_skip):
        address_components = AddressComponents(
            street_number=street_number,
            street="Test Street",
            city="Test City",
            country="Test Country",
            zipcode="12345",
            subpremise=None,
        )

        result = AddressValidationDomainService.should_skip_pin_location_validation(
            address_lines=address_lines, address_components=address_components
        )

        assert result == expected_skip
