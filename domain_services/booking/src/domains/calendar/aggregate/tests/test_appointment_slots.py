import pytest

from ...dto import ComboBooking, PossibleSlotsMap, SlotDetails
from ..time_slots import TimeSlotsCalculator
from .commons import (
    APPLIANCE_ID,
    BIN_LENGTH,
    DAILY_RESOURCES_BOOKED_OCCUPANCY_MAP,
    DAY_OPERATING,
    PRICE_LIST,
    SERVICE__WITH_APPLIANCE__ID,
    SERVICE_ID,
    STAFFER_1_ID,
    STAFFER_2_ID,
)

TIME_SLOTS_CALCULATOR = TimeSlotsCalculator(bin_length=BIN_LENGTH)


@pytest.mark.parametrize(
    ["bookings", "find_only_first", "expected"],
    [
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE_ID,
                    staffer_id=None,
                ),
            ],
            False,
            {
                DAY_OPERATING: {
                    12
                    * BIN_LENGTH: SlotDetails(
                        resources={STAFFER_1_ID},
                        discount=None,
                    ),
                    16
                    * BIN_LENGTH: SlotDetails(
                        resources={STAFFER_1_ID, STAFFER_2_ID},
                        discount=None,
                    ),
                },
            },
            id="slots for single item appointment",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                    staffer_id=None,
                ),
            ],
            False,
            {
                DAY_OPERATING: {
                    12
                    * BIN_LENGTH: SlotDetails(
                        resources={STAFFER_1_ID, APPLIANCE_ID},
                        discount=None,
                    ),
                },
            },
            id="slots for single item appointment with applianace",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE_ID,
                    staffer_id=None,
                ),
            ],
            True,
            {
                DAY_OPERATING: {
                    12
                    * BIN_LENGTH: SlotDetails(
                        resources={STAFFER_1_ID},
                        discount=None,
                    ),
                },
            },
            id="first slot for single item appointment",
        ),
        pytest.param(
            [
                ComboBooking(
                    service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                    staffer_id=STAFFER_2_ID,
                ),
            ],
            False,
            {},
            id="first slot for single item appointment with applianace and staffer selected",
        ),
    ],
)
def test__find_appointment_possible_slots(
    bookings: list[ComboBooking],
    find_only_first: bool,
    expected: PossibleSlotsMap,
) -> None:
    actual = TIME_SLOTS_CALCULATOR.find_time_slots(
        bookings=bookings,
        price_list=PRICE_LIST,
        daily_resources_booked_occupancy_map=DAILY_RESOURCES_BOOKED_OCCUPANCY_MAP,
        find_only_first=find_only_first,
    )

    assert actual == expected
