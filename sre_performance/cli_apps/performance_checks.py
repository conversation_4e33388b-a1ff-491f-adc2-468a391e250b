#!/usr/bin/env python
import os
from json import JSONDecodeError, load

import django

django.setup()

# pylint: disable=wrong-import-position, broad-exception-raised
from django.conf import settings
from elasticsearch.exceptions import ConnectionError as ESConnectionError
from lib.elasticsearch.default_connection import connection
from sre_performance.const import PerfEnvironment
from webapps.business.elasticsearch import BusinessIndex
from webapps.structure.elasticsearch import RegionIndex
from webapps.business.models import Business


ENV_REQUIREMENTS = {
    PerfEnvironment.PERF_DEV5: {
        'min_businesses_number': 1000,
        'min_index_size': {  # in bytes
            BusinessIndex: 10 * 1024 * 1024,
            RegionIndex: 100 * 1024 * 1024,
        },
    },
    PerfEnvironment.PERFORMANCETESTING1: {
        'min_businesses_number': 100,
        'min_index_size': {  # in bytes
            BusinessIndex: 1 * 1024 * 1024,
            RegionIndex: 100 * 1024 * 1024,
        },
    },
    PerfEnvironment.PERFORMANCETESTING2: {
        'min_businesses_number': 100,
        'min_index_size': {  # in bytes
            BusinessIndex: 1 * 1024 * 1024,
            RegionIndex: 100 * 1024 * 1024,
        },
    },
    PerfEnvironment.OBSERVABILITY: {
        'min_businesses_number': 100,
        'min_index_size': {  # in bytes
            BusinessIndex: 1 * 1024 * 1024,
            RegionIndex: 100 * 1024 * 1024,
        },
    },
}


def branch_info_check():
    try:
        with open(
            os.path.join(settings.PROJECT_PATH, 'version_file.json'), 'r', encoding='utf-8'
        ) as file:
            content = load(file)
    except FileNotFoundError:
        raise RuntimeError('No version_file.json') from None
    except JSONDecodeError:
        raise RuntimeError('version_file.json file doesn\'t contain json') from None
    if not content:
        raise RuntimeError('Empty dict in version_file.json')


def business_check(requirements):
    business_count = Business.objects.indexed_in_elasticsearch().count()
    if business_count < requirements['min_businesses_number']:
        raise RuntimeError('There are less than 100 active (indexable) businesses in database')


def elastic_search(requirements):
    try:
        health = connection.cluster.health()
    except ESConnectionError:
        raise RuntimeError('ElasticSearch connection error') from None
    if health['status'] != 'green':
        raise RuntimeError(f'ElasticSearch status: {health["status"]}')

    req_violated = []
    for index, req in requirements['min_index_size'].items():
        size = connection.indices.stats(index=index.name)['indices'][index.name]['total']['store'][
            'size_in_bytes'
        ]
        if size < req:
            req_violated.append(f'{index.name}: size = {size} bytes (requirement = {req})')

    if req_violated:
        sep = '\n'
        raise RuntimeError(
            f'Following indices do not meet size requirements:{sep}{sep.join(req_violated)}'
        )


if __name__ == '__main__':
    if settings.PERFORMANCE_TESTS:
        env_requirements = ENV_REQUIREMENTS[settings.LEGACY_DEPLOYMENT_LEVEL]

        branch_info_check()
        business_check(env_requirements)
        elastic_search(env_requirements)
        print('Performance checks done.')
