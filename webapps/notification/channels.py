import logging

from django.conf import settings
from django.utils.functional import cached_property
from django.utils.text import format_lazy
from django.utils.translation import get_language
from retrying import retry

from lib.booksy_sms import send_sms
from lib.email import send_email


from lib.jinja_renderer import NotificationJinjaRenderer
from webapps.notification.base import (
    Channel,
    PopupTemplate,
    Recipient,
)
from webapps.notification.notification_flow import create_notification_document
from webapps.notification.tasks.push import send_push_notification
from webapps.notification.utils import (
    ELASTIC_TRANSPORT_ERRORS,
    is_booksy_domain,
    is_elastic_transport_error,
)


log = logging.getLogger('booksy.notifications')


class EmailChannel(Channel):
    type = Channel.Type.EMAIL

    def render_template(self, template, context, recipient):
        return NotificationJinjaRenderer(language=get_language()).render(
            template_name=template,
            context=context,
        )

    def is_valid_recipient(self, recipient: Recipient):
        business = getattr(self.notification, 'business', None)
        if business and business.owner.is_test_user():
            return bool(recipient.email) and is_booksy_domain(recipient.email)
        return bool(recipient.email)

    def send(self, recipients):
        sender = self.notification.sender(self.notification).get_sender()
        history_data = self.notification.get_history_data()
        for recipients_group in self.recipients_by_language(recipients):
            for recipient in recipients_group:
                history_data_group = history_data.copy()
                if not history_data_group.get('customer_card_id'):
                    history_data_group['customer_card_id'] = recipient.customer_id
                if not history_data_group.get('customer_id'):
                    history_data_group['customer_id'] = recipient.user_id

                with self.notification.set_recipient(recipient):
                    content = self.get_content(recipient)
                    try:
                        send_email(
                            recipient.email,
                            body=content,
                            history_data=history_data_group,
                            from_data=(sender.name, sender.email),
                            to_name=recipient.name or recipient.email,
                            reply_to_email=sender.from_email,
                            check_if_unsubscribed=False,
                            omit_celery=True,
                            attachments=self.notification.attachment_list,
                            bcc=self.notification.email_bcc,
                            easy_unsubscribe_url=getattr(
                                self.notification, 'email_easy_unsubscribe_url', None
                            ),
                        )
                    except ELASTIC_TRANSPORT_ERRORS:
                        log.exception(
                            'Elastic error occurs during sending email id=%s',
                            self.notification.identity,
                        )
                        continue


class PushChannel(Channel):
    type = Channel.Type.PUSH

    def render_template(self, template, context, recipient):
        return template.format(**context)

    def is_valid_recipient(self, recipient: Recipient):
        return bool(recipient.push_receivers)

    def send(self, recipients):
        target = self.notification.get_target()

        push_data = {
            'task_id': self.notification.identity,
            'task_type': self.notification.category,
        }

        if target is not NotImplemented:
            push_data['type'] = target.type
            push_data['args'] = [target.id]
            push_data['title'] = getattr(target, 'title', None)

        if PopupChannel in self.notification.channels:
            push_data['notification_id'] = self.notification.identity

        history_data = self.notification.get_history_data()
        for recipients_group in self.recipients_by_language(recipients):
            content = self.get_content()
            meta_customers = []
            receivers = []

            for recipient in recipients_group:
                meta_customers.append([recipient.customer_id, recipient.user_id])
                for receiver in recipient.push_receivers:
                    receivers.append(receiver)

            history_data_group = history_data.copy()
            history_data_group['meta_customers'] = meta_customers

            send_push_notification(
                receivers,
                content,
                history_data=history_data_group,
                push_data=push_data,
            )


class SMSChannel(Channel):
    type = Channel.Type.SMS

    def render_template(self, template, context, recipient):
        return template.format(**context)

    def is_valid_recipient(self, recipient: Recipient) -> bool:
        return bool(recipient.phone)

    def send(self, recipients):
        # pylint: disable=cyclic-import
        from webapps.message_blast.notifications import MessageBlastNotification

        history_data = self.notification.get_history_data()
        business = getattr(self.notification, 'business', None)
        is_test_business = business and business.owner.is_test_user()
        sms_sent_from_test_business = 0
        test_business_sms_limit = 5

        for recipients_group in self.recipients_by_language(recipients):
            for recipient in recipients_group:
                history_data_group = history_data.copy()
                if not history_data_group.get('customer_card_id'):
                    history_data_group['customer_card_id'] = recipient.customer_id
                if not history_data_group.get('customer_id'):
                    history_data_group['customer_id'] = recipient.user_id

                with self.notification.set_recipient(recipient):
                    send_sms(
                        to=recipient.phone,
                        message=self.get_content(recipient),
                        history_data=history_data_group,
                        blast_id=(
                            self.notification.message_blast.id
                            if isinstance(self.notification, MessageBlastNotification)
                            else None
                        ),
                    )
                    if is_test_business:
                        sms_sent_from_test_business += 1
                    if sms_sent_from_test_business >= test_business_sms_limit:
                        return


class SMSChannelMarketing(SMSChannel):

    @cached_property
    def allow_no_sms_marketing_consent(self):
        return settings.API_COUNTRY not in settings.DOUBLE_OPT_IN_COUNTRIES

    def is_valid_recipient(self, recipient: Recipient) -> bool:
        is_valid = super().is_valid_recipient(recipient)
        if recipient.has_blocked_phone_number:
            return False
        if self.allow_no_sms_marketing_consent:
            return is_valid
        return is_valid and recipient.sms_marketing_consent


def retry_if_elastic_timeout(exception):
    log.exception('Failed to save notification document. Retrying')
    return is_elastic_transport_error(exception)


class PopupChannel(Channel):
    type = Channel.Type.POPUP

    def render_template(self, template: PopupTemplate, context, recipient):
        return create_notification_document(
            template.resolve(), context, recipient, self.notification
        )

    def is_valid_recipient(self, recipient: Recipient) -> bool:
        return recipient.user_id is not None

    @retry(
        retry_on_exception=retry_if_elastic_timeout,
        stop_max_attempt_number=5,
        wait_fixed=2 * 1000,
    )
    def _save_notification_document(self, recipient):
        document = self.get_content(recipient)
        document.save()
        return document

    def send(self, recipients) -> list:
        result = []
        for recipients_group in self.recipients_by_language(recipients):
            for recipient in recipients_group:
                with self.notification.set_recipient(recipient):
                    try:
                        document = self._save_notification_document(recipient)
                    except ELASTIC_TRANSPORT_ERRORS:
                        log.exception(
                            'Failed to save notification document id=%s. Giving up',
                            self.notification.identity,
                        )
                        continue
                    result.append(document)
        return result


def push_from_popup(popup_template):
    """Join messages by space as lazy string"""
    messages = popup_template.messages
    template = ' '.join(['{}'] * len(messages))
    return format_lazy(template, *messages)
