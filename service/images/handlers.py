import json
import logging
import sys

from bo_obs.datadog.enums import BooksyTeams
from django.utils.translation import gettext as _

import lib.tools
from lib.elasticsearch.consts import ESDocType
from service.images.helpers import get_image_map
from service.images.serializers import (
    ImageSearchSerializer,
    PublishToPortfolioSerializer,
)
from service.mixins.paginator import PaginatorMixin
from service.search.mixins import BusinessESMixin
from service.tools import (
    RequestHand<PERSON>,
    json_request,
    session,
)
from webapps.business.models import Business
from webapps.images.elasticsearch import ImageSearchHitSerializer
from webapps.images.enums import ImageTypeEnum
from webapps.images.events import (
    portfolio_photo_comment_added_event,
)
from webapps.images.models import (
    Image,
    ImageComment,
    ImageLike,
)
from webapps.images.searchables import ImageSearchable
from webapps.images.tasks import (
    publish_photo_to_portfolio_task,
)

log = logging.getLogger('booksy.images')
PY3 = int(sys.version[0]) >= 3


# pylint: disable=too-many-ancestors
class ImageSearchHandler(<PERSON><PERSON><PERSON><PERSON><PERSON>, PaginatorMixin):
    """
    swagger:
        summary: Search for images
        parameters:
            - name: business_id
              description: Business id
              type: integer
              paramType: path
            - name: page
              description: Results page
              paramType: query
              required: False
              type: integer
              defaultValue: 1
            - name: images_per_page
              description: How many results per page to return
              paramType: query
              required: False
              type: integer
              default_from_const: settings.IMAGES_PER_PAGE
        type: ImagesListing
    :swagger
    swaggerModels:
        ImagesListing:
            id: ImagesListing
            properties:
                images:
                    type: array
                    items:
                        type: BusinessImage
                images_count:
                    type: integer
                images_per_page:
                    type: integer
    :swaggerModels

    """

    page_name = 'page'
    per_page_name = 'images_per_page'
    default_per_page_setting = 'IMAGES_PER_PAGE'

    def format(self, doc):
        has_meta = hasattr(doc, '_meta')
        ret = {
            'image_id': doc['_id'],
            'business_id': doc._meta.parent if has_meta else {},
            'category': doc['category'],
            'is_cover_photo': doc['is_cover_photo'],
            'inspiration_categories': doc['inspiration_categories'],
            'description': doc['description'],
            'active': doc['active'],
            'visible': doc['visible'],
            'image': doc['image'],
            'width': doc['width'],
            'height': doc['height'],
            'order': doc['order'],
            'staffers': doc['staffers'],
            'tags': doc['tags'],
            'location': doc['location'],
            'thumbnails': doc.get('thumbnails', []),
            'comments': [],
            'liked': None,
            'created': doc['created'],
            'review_id': doc.get('review_id'),
        }

        if not has_meta:
            return ret

        comments_data = doc._meta.inner_hits[ESDocType.IMAGE_COMMENT]['hits']
        ret['comments_count'] = comments_data.get('total', {}).get('value') or 0
        for comment in comments_data['hits']:
            ret['comments'].append(
                {
                    'comment_id': int(comment['_id']),
                    'image_id': comment['_source']['_parent'],
                    'user_id': comment['_source']['user_id'],
                    'user_name': comment['_source']['user_name'],
                    'user_type': comment['_source']['user_type'],
                    'user_avatar': comment['_source'].get('user_avatar'),
                    'content': comment['_source']['content'],
                    'created': comment['_source']['created'],
                    'updated': comment['_source']['updated'],
                }
            )
        hits = doc._meta.inner_hits['business']['hits']['hits']
        if hits:
            business = hits[0]['_source']
            logo = (business.get('images', {}).get(ImageTypeEnum.LOGO) or [])[:1]
            ret['business'] = {
                'id': int(hits[0]['_id']),
                'name': business.get('name'),
                'thumbnail_photo': BusinessESMixin.format_media_url(
                    business.get('thumbnail_photo')
                ),
                'photo': BusinessESMixin.format_media_url(business.get('cover_photo')),
                'images': {ImageTypeEnum.LOGO: logo},
            }
        else:
            log.exception(
                'image %s - no business assigned!', doc._id  # pylint: disable=protected-access
            )
            ret['business'] = {}
        if self.user:
            ret['liked'] = False
            if 'user_like' in doc._meta.inner_hits:
                user_like = doc._meta.inner_hits['user_like']['hits']
                if user_like['total']['value'] > 0:
                    ret['liked'] = True
        ret['likes_count'] = doc._meta.inner_hits[ESDocType.IMAGE_LIKE]['hits']['total']['value']

        return ret

    @session(optional_login=True)
    def get(self, business_id=None):
        """Search for images.
        swagger:
            summary: Partially DEPRECATED
            notes: >
                Partially deprecated. Biz apps should use:
                "GET /business_api/me/businesses/(id)/images/v2/"
                in order to list busiess images.
            parameters:
                - name: image_id
                  description: ids of images separated with comma
                  type: string
                  paramType: query
                - name: category
                  description: Image category
                  type: string
                  paramType: query
                - name: tag
                  type: string
                  paramType: query
                - name: staffer
                  type: string
                  paramType: query
                - name: location_geo
                  description: Latitude and longitude separated with comma
                  type: string
                  paramType: query
                - name: distance_unit
                  description: Currently only meter ('m')
                  type: string
                  paramType: query
                - name: distance_radius
                  description: Between 1 and 2000000 distance units
                  type: integer
                  paramType: query
                - name: comments_size
                  description: Comments pagination limit,
                               max working value is 100
                  type: integer
                  paramType: query
                - name: comments_start_offset
                  description: Comments pagintation offset
                  type: int
                  paramType: query
                - name: business_category_id
                  type: integer
                  paramType: query
                - name: review_id
                  type: integer
                  paramType: query
                - name: no_thumbs
                  type: boolean
                  paramType: query
                - name: _include_cover_in_biz_photos
                  type: boolean
                  paramType: query
                  description: >
                    New frontends should pass this flag if they want cover
                    photos to be listed with venue photos. Cover photo is in
                    fact a venue photo (in new approach), but we can't include
                    it due to backward capability with old frontends which don't
                    expect cover photo within venue photos. When old frontends
                    are gone the flag can be removed. Optional. Default: False.
        """
        page, per_page = self.parse_page_values_from_get()
        data = self._prepare_get_arguments()

        serializer = ImageSearchSerializer(
            data=data,
            context={
                'user-agent': self.request.headers.get('User-Agent'),
            },
        )
        data = self.validate_serializer(serializer)

        biz_id = data.get('business_id') or business_id
        business = Business.objects.filter(id=biz_id).first()
        onboarding_hack = (
            data.get('category') == ImageTypeEnum.COVER_DEPRECATED or data.get('is_cover_photo')
        ) and business.status == Business.Status.SETUP
        data = self._prepare_data_for_es(data, biz_id, onboarding_hack)

        start = (page - 1) * per_page
        no_thumbs = data.pop('no_thumbs', False)
        images = (
            ImageSearchable(
                ESDocType.IMAGE,
                serializer=ImageSearchHitSerializer,
            )
            .params(
                from_=start,
                size=per_page,
                no_thumbs=no_thumbs,
            )
            .execute(data)
            .hits
        )

        if onboarding_hack and images and not images[0].business:
            # business is not indexed yet, but we need only a couple of fields
            images[0].business = {
                'id': business.id,
                'name': business.name,
            }

        if data.get('review_id') and business:
            image_map = get_image_map()
            images_count = 0
            other_images_count = 0
            categories_with_images = list(image_map.keys())

            for category in business.categories.all():
                category_name = category.internal_name.lower().replace(' ', '_').replace('&', '__')
                if category_name in categories_with_images:
                    images_count += len(image_map[category_name])
                else:
                    other_images_count = 1

            expected_rf_cunt = images_count + other_images_count

            if images.total.value < expected_rf_cunt:
                response = {
                    'images': [],
                    'images_count': 0,
                    'images_per_page': 0,
                }
                return self.finish_with_json(200, response)

        image_id = data.get('image_id')
        # get base images for marketing
        base_images = []
        if biz_id and data.get('category') == ImageTypeEnum.MARKETING and page == 1:
            categories = []
            if business is not None:
                categories.extend(list(business.categories.all()))
                categories.append(business.primary_category)

            base_images_q = (
                Image.objects.filter(
                    business_id__isnull=True,
                    category=ImageTypeEnum.MARKETING,
                    inspiration_categories__in=categories,
                )
                .distinct()
                .order_by('id')
            )

            if image_id:
                base_images_q = base_images_q.filter(id__in=image_id)

            for img in base_images_q.all():
                base_images.append(self.format(img.to_es(no_thumbs=no_thumbs)))

        # return nothing on digital flyers request
        # left for ancient apps
        if data.get('category') in (
            ImageTypeEnum.MARKETING_DIGITAL_FLYER,
            ImageTypeEnum.MARKETING_DIGITAL_FLYER__FACEBOOK,
        ):
            response = {'images': [], 'images_count': 0, 'images_per_page': 0}
        else:
            response = {
                'images': base_images + list(images),
                'images_count': images.total.value + len(base_images),
                'images_per_page': per_page + len(base_images),
            }
        self.finish_with_json(200, response)

    def _prepare_data_for_es(self, data, biz_id, onboarding_hack):
        if data.get('location_geo'):
            data['latitude'] = data['location_geo']['latitude']
            data['longitude'] = data['location_geo']['longitude']
        if self.user:
            data['user_id'] = self.user.id

        data['business_id'] = biz_id
        data['has_active_business'] = not onboarding_hack
        return data


# pylint: disable=too-many-ancestors
class ImageCommentHandler(PaginatorMixin, RequestHandler):
    @session(login_required=True)
    @json_request
    def post(self, image_id):
        """Add comment for an image.
        swagger:
            summary: Partially DEPRECATED
            notes: >
                Partially deprecated. Biz apps should use:
                "POST /business_api/me/businesses/(id)/images/v2/comments"
                in order to add new comments for images.
            consumes: application/json
            parameters:
                - name: image_id
                  description: id of image
                  type: integer
                  paramType: path
                - name: content
                  description: Comment string
                  type: string
                  paramType: body
        """
        lib.tools.sasrt(
            image_id,
            400,
            [
                {
                    'code': 'required',
                    'type': 'validation',
                    'description': _('Missing required image_id field.'),
                    'field': 'image_id',
                }
            ],
        )

        try:
            image = Image.objects.get(pk=image_id)
        except Image.DoesNotExist:
            image_id = None
            lib.tools.sasrt(
                image_id,
                400,
                [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'description': _('Invalid image_id.'),
                        'field': 'image_id',
                    }
                ],
            )
            return

        content = None
        if self.data.get('content'):
            content = self.data['content'].strip()

        lib.tools.sasrt(
            content,
            400,
            [
                {
                    'code': 'invalid',
                    'type': 'validation',
                    'description': _('Missing comment content'),
                    'field': 'content',
                }
            ],
        )

        lib.tools.sasrt(
            _get_unicode_len(content) <= 300,
            400,
            [
                {
                    'code': 'invalid',
                    'type': 'validation',
                    'description': _('Max. comment length is 300 chars.'),
                    'field': 'content',
                }
            ],
        )

        comment = ImageComment()
        comment.user = self.user
        comment.profile_type = self.user.profile.profile_type
        comment.image = image
        comment.content = content
        comment.save()
        comment.reindex()
        doc = comment.get_document(refresh=True)
        portfolio_photo_comment_added_event.send(comment)

        self.finish_with_json(201, doc)
        return

    @session(login_required=True)
    @json_request
    def put(self, image_id):
        """Add comment for an image.
        swagger:
            summary: Update comment for an image
            notes: Ask Piotr Z. for documentation
            consumes: application/json
            parameters:
                - name: image_id
                  description: id of image
                  type: integer
                  paramType: path
                - name: content
                  description: Comment string
                  type: string
                  paramType: body
                - name: comment_id
                  description: Comment id
                  type: integer
                  paramType: body
        """
        content = self.data.get('content') or None
        comment_id = self.data.get('comment_id') or None
        lib.tools.sasrt(
            image_id,
            400,
            [
                {
                    'code': 'required',
                    'type': 'validation',
                    'description': _('Missing required image_id field.'),
                    'field': 'image_id',
                }
            ],
        )

        lib.tools.sasrt(
            comment_id,
            400,
            [
                {
                    'code': 'required',
                    'type': 'validation',
                    'description': _('Missing required comment_id field.'),
                    'field': 'comment_id',
                }
            ],
        )
        try:
            Image.objects.get(pk=image_id)
        except Image.DoesNotExist:
            image_id = None
            lib.tools.sasrt(
                image_id,
                400,
                [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'description': _('Invalid image_id.'),
                        'field': 'image_id',
                    }
                ],
            )
            return

        lib.tools.sasrt(
            content,
            400,
            [
                {
                    'code': 'invalid',
                    'type': 'validation',
                    'description': _('Missing comment content'),
                    'field': 'content',
                }
            ],
        )

        lib.tools.sasrt(
            _get_unicode_len(content) <= 300,
            400,
            [
                {
                    'code': 'invalid',
                    'type': 'validation',
                    'description': _('Max. comment length is 300 chars.'),
                    'field': 'content',
                }
            ],
        )

        try:
            comment = ImageComment.objects.get(
                pk=comment_id,
                image_id=image_id,
                user=self.user,
            )
        except ImageComment.DoesNotExist:
            comment = None
            lib.tools.sasrt(
                comment,
                400,
                [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'description': _('Comment does not exist.'),
                        'field': 'comment_id',
                    }
                ],
            )
            return

        comment.content = content
        comment.save()
        comment.reindex()
        doc = comment.get_document(refresh=True)

        return self.finish_with_json(201, doc)

    @session(login_required=True)
    @json_request
    def delete(self, image_id):
        """Delete comment for an image.
        swagger:
            summary: Delete comment for an image
            notes: Ask Piotr Z. for documentation
            consumes: application/json
            parameters:
                - name: image_id
                  description: id of image
                  type: integer
                  paramType: path
                - name: comment_id
                  description: id of image
                  type: integer
                  paramType: body
        """
        comment_id = self.data.get('comment_id') or None
        lib.tools.sasrt(
            image_id,
            400,
            [
                {
                    'code': 'required',
                    'type': 'validation',
                    'description': _('Missing required image_id field.'),
                    'field': 'image_id',
                }
            ],
        )

        lib.tools.sasrt(
            comment_id,
            400,
            [
                {
                    'code': 'required',
                    'type': 'validation',
                    'description': _('Missing required comment_id field.'),
                    'field': 'comment_id',
                }
            ],
        )
        try:
            Image.objects.get(pk=image_id)
        except Image.DoesNotExist:
            image_id = None
            lib.tools.sasrt(
                image_id,
                400,
                [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'description': _('Invalid image_id.'),
                        'field': 'image_id',
                    }
                ],
            )
            return

        try:
            comment = ImageComment.objects.get(
                pk=comment_id,
                image_id=image_id,
                user=self.user,
            )
            comment.soft_delete()
            ret = {}
            self.set_status(201)
            self.finish(json.dumps(ret))
            return
        except ImageComment.DoesNotExist:
            comment = None
            lib.tools.sasrt(
                comment,
                400,
                [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'description': _('Invalid comment_id.'),
                        'field': 'comment_id',
                    }
                ],
            )
            return


# pylint: disable=too-many-ancestors
class ImageLikeHandler(PaginatorMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @session(login_required=True)
    def post(self, image_id):
        """
        swagger:
            summary: Ask Piotr Z. for documentation
        """
        # TODO: should check if image exist
        created = False
        like = ImageLike.objects.filter(
            user_id=self.user.id,
            image_id=image_id,
        ).first()

        if not like:
            like = ImageLike()
            like.user_id = self.user.id
            like.image_id = image_id
            like.save()
            created = True

        ret = {'like_created': created}
        self.set_status(201)
        self.finish(json.dumps(ret))

    @session(login_required=True)
    def delete(self, image_id):
        """
        swagger:
            summary: Ask Piotr Z. for documentation
        """
        likes = ImageLike.objects.filter(
            user_id=self.user.id,
            image_id=image_id,
        )
        for like in likes:
            like.soft_delete()

        self.finish_with_json(200, {})


def _get_unicode_len(txt):
    if not txt:
        return 0
    if PY3:
        return len(txt)
    # TODO delete after py3 migration
    try:
        return len(txt.decode('utf-8'))
    except UnicodeEncodeError:
        return len(txt)


class PublishPhotoToPortfolioHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Publish given photo to business portfolio.
            parameters:
                - name: business_id
                  description: Business Id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description: request body
                  paramType: body
                  type: PublishPhotoRequest
            type: PublishPhotoResponse
        """
        business = self.business_with_advanced_staffer(business_id)

        serializer = PublishToPortfolioSerializer(
            data=self.data,
            context={'business': business},
        )
        validated_data = self.validate_serializer(serializer)

        publish_photo_to_portfolio_task.delay(
            **validated_data,
            business_id=business_id,
            booking_source_id=self.booking_source.id,
        )
        self.finish_with_json(201, {})
