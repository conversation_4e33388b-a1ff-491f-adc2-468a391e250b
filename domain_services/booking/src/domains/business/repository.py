from typing import Protocol

from .dto import Business, PriceList, BusinessCustomerInfo


class BusinessRepository(Protocol):
    def get_business(self, business_id: int) -> Business | None: ...

    def get_price_list(self, business_id: int) -> PriceList: ...

    def get_business_customer_info_for_user(
        self,
        business_id: int,
        user_id: int | None,
    ) -> BusinessCustomerInfo | None: ...
