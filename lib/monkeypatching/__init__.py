# Various monkey-patches
import datetime
import functools
import os

from ddtrace.constants import MANUAL_KEEP_KEY
from django.utils.encoding import force_str

from lib.utils import str_to_bool


def monkeypatch_py3_10_collections():
    """Some ancient libs like tornado==4.5.3 are not ready for Python 3.10"""
    import collections
    from collections.abc import MutableMapping

    collections.MutableMapping = MutableMapping


def monkeypatch_py3_10_asyncio_lock_loop_deprecation():
    """Disable deprecation error on using loop argument in asyncio Lock.

    TODO: remove when aioapns iss compatible with Python 3.10
    """
    try:
        from asyncio.mixins import _LoopBoundMixin
    except ImportError:
        pass
    else:
        _LoopBoundMixin.__init__ = lambda *args, **kwargs: None


def psycopg2_time24_hour_extension():
    import psycopg2
    from lib.time_24_hour import cast_time24hour

    # postgres OID of time
    # TODO: get this dynamically, not write here as int, since it could change!
    time24oid = 1083
    time24type = psycopg2.extensions.new_type((time24oid,), force_str('time'), cast_time24hour)
    psycopg2.extensions.register_type(time24type)


def dateutil_tz_cache():
    # Monkey-patch dateutil.tz.gettz, because it's slow
    from dateutil import tz

    if hasattr(tz, '_oldtz'):
        return
    tz._oldtz = tz.gettz  # pylint: disable=protected-access

    gettz_cache = {}

    def gettz_cached(timezone):
        try:
            return gettz_cache[timezone]
        except KeyError:
            gettz_cache[timezone] = tz._oldtz(timezone)  # pylint: disable=protected-access
            return gettz_cache[timezone]

    tz.gettz = gettz_cached


def structured_values_in_queryset():
    from django.db.models import QuerySet
    from lib.queryset import structured_values

    QuerySet.structured_values = structured_values


def defaultfilters_date_am_pm_monkeypatch():
    from django.template import defaultfilters

    if hasattr(defaultfilters, '_old_date'):
        return
    defaultfilters._old_date = defaultfilters.date  # pylint: disable=protected-access

    @functools.wraps(defaultfilters._old_date)  # pylint: disable=protected-access
    def date(*args, **kwargs):
        result = defaultfilters._old_date(*args, **kwargs)  # pylint: disable=protected-access
        return result.replace('a.m.', 'AM').replace('p.m.', 'PM')

    defaultfilters.date = date


def requests_user_agent_monkeypatch(app_name: str):
    from requests import utils

    if hasattr(utils, '_old_default_user_agent'):
        return

    func = utils.default_user_agent
    utils._old_default_user_agent = func  # pylint: disable=protected-access

    @functools.wraps(func)
    def default_user_agent(name="python-requests"):
        return f'{app_name} {func(name=name)}'

    utils.default_user_agent = default_user_agent


def requests_timeout_monkeypatch(default_timeout: float):
    from requests.adapters import HTTPAdapter

    if hasattr(HTTPAdapter, '_old_send'):
        return

    func = HTTPAdapter.send
    HTTPAdapter._old_send = func  # pylint: disable=protected-access

    @functools.wraps(func)
    def send(*args, **kwargs):
        kwargs.setdefault('timeout', default_timeout)
        return func(*args, **kwargs)

    HTTPAdapter.send = send


_original_get_attribute = None


def rest_framework_get_attribute_monkeypatch():
    """Restore behaviour from djangorestframework<3.7 of get_attribute.

    It returns None if you encounter None on the nested lookup.
    Newer versions raise Attribute error which breaks whole Booksy.

    Look at lib.monkeypatching._original_get_attribute for original
    implementation.

    See:
    https://github.com/encode/django-rest-framework/commit/07258ca032e062334310c112469d6432f6eeb818#diff-af402f515db75c7c6754453cf15455d9

    """
    global _original_get_attribute  # pylint: disable=global-statement
    if _original_get_attribute is not None:
        # already patched
        return
    from rest_framework import fields

    _original_get_attribute = fields.get_attribute

    @functools.wraps(_original_get_attribute)
    def get_attribute(instance, attrs):
        for attr in attrs:
            if instance is None:
                # Break out early if we get `None`
                # at any point in a nested lookup.
                return None
            instance = _original_get_attribute(instance, [attr])
        return instance

    fields.get_attribute = get_attribute


def django_m2m_intermediary_model_monkeypatch():
    """We monkey patch function responsible for creation of intermediary m2m
    models in order to inject there a "created" timestamp.
    """
    from django.db.models.fields import related
    from lib.monkeypatching.custom_m2m_intermediary_model import (
        create_many_to_many_intermediary_model,
    )

    original_function = related.create_many_to_many_intermediary_model
    setattr(create_many_to_many_intermediary_model, '_original_django_function', original_function)

    # Patch django function with our version of this function
    setattr(
        related, 'create_many_to_many_intermediary_model', create_many_to_many_intermediary_model
    )


def appconfigstub_monkeypatch():
    """Extend AppConfigStub to support using fixtures in legacy migrations."""
    from django.db.migrations.state import AppConfigStub

    def _path(self):
        """WARNING: this works only for webapps.

        Fixtures were not used in other app migrations and are forbidden in future,
        so we only need to support webapps.
        """
        try:
            app_module = __import__(f'webapps.{self.name}')
            return self._path_from_module(app_module)  # pylint: disable=protected-access
        except ModuleNotFoundError:
            return self.name

    AppConfigStub.path = property(_path)
    AppConfigStub.models_module = None


def patch_celery_worker():
    from lib.celery_utils.worker_profiling import (
        patch_worker_up_execution_time,
        patch_subworker_profile_on_startup,
    )

    patch_subworker_profile_on_startup()
    patch_worker_up_execution_time()


def config_freezegun_ignore_list():
    import freezegun

    # Metrics generated in this module should not have pinned time.
    freezegun.configure(extend_ignore_list=['lib.celery_utils'])


def patch_get_redis_connection():
    from ddtrace.trace import Pin
    import redis

    from lib.datadog.consts import REDIS_NAMES_MAPPING_PRD, REDIS_NAMES_SUBSTRING

    old_init = redis.StrictRedis.__init__

    def init_patched(self, *args, **kwargs):
        from django.conf import settings

        old_init(self, *args, **kwargs)
        connection_kwargs = self.connection_pool.connection_kwargs
        host = connection_kwargs.get('host')
        port = connection_kwargs.get('port')
        db = connection_kwargs.get('db')
        if settings.LIVE_DEPLOYMENT:
            try:
                host = REDIS_NAMES_MAPPING_PRD[host]
            except KeyError:
                for substring in REDIS_NAMES_SUBSTRING:
                    if substring in host:
                        host = substring
                        break

        if str_to_bool(os.getenv("USE_UNIFIED_ENV_PROD", "False")):
            redis_service = f'{os.getenv("DD_SERVICE_BASE")}{host}:{port}/{db}-{os.getenv("BOOKSY_COUNTRY_CODE")}'  # pylint: disable=line-too-long
        else:
            redis_service = f'{os.getenv("DD_SERVICE")}-{host}:{port}/{db}'

        Pin.override(redis.StrictRedis, service=redis_service)

    redis.StrictRedis.__init__ = init_patched


ES_CLUSTER_NAME = None


def patch_elasticsearch_connection():
    from ddtrace.trace import Pin
    from elasticsearch_dsl.connections import Connections

    old_create_connection = Connections.create_connection

    def create_connection_patched(self, alias="default", **kwargs):
        global ES_CLUSTER_NAME  # pylint: disable=global-statement
        conn = old_create_connection(self, alias, **kwargs)
        if not ES_CLUSTER_NAME:
            ES_CLUSTER_NAME = conn.info().get('cluster_name')  # pylint: disable=invalid-name

        if str_to_bool(os.getenv("USE_UNIFIED_ENV_PROD", "False")):
            es_service = f'core-elasticsearch-{ES_CLUSTER_NAME}-{os.getenv("BOOKSY_COUNTRY_CODE")}'
        else:
            es_service = f'core-elasticsearch-{ES_CLUSTER_NAME}'

        Pin.override(conn.transport, service=es_service)
        return conn

    Connections.create_connection = create_connection_patched


def patch_softtimelimit_sighandler():
    import logging
    import traceback

    import billiard.pool

    logger = logging.getLogger('celery_metric.limit')

    def soft_timeout_sighandler(signum, frame):  # pylint: disable=unused-argument
        if False:  # pylint: disable=using-constant-test
            logger.warning(
                'soft_timeout_sighandler traceback %s',
                traceback.format_stack(),
            )
        raise billiard.pool.SoftTimeLimitExceeded()

    billiard.pool.soft_timeout_sighandler = soft_timeout_sighandler


def patch_redis_connectionpool_lock():
    from hashlib import sha256
    from inspect import getsource
    import threading

    from redis.connection import ConnectionPool

    OLD_METHOD_SHA = (  # pylint: disable=invalid-name
        b"\xa5rH\xd3\xab\xe7.'f\xfeMO{\x9d\xbf\xfb\xa6D\xc0\xe2\xdd\xe3Y\xd0c\xf2\xecuN\xa9\xae\x81"
    )

    def reset(self):
        self._lock = threading.RLock()  # pylint: disable=protected-access
        self._created_connections = 0  # pylint: disable=protected-access
        self._available_connections = []  # pylint: disable=protected-access
        self._in_use_connections = set()  # pylint: disable=protected-access

        # this must be the last operation in this method. while reset() is
        # called when holding _fork_lock, other threads in this process
        # can call _checkpid() which compares self.pid and os.getpid() without
        # holding any lock (for performance reasons). keeping this assignment
        # as the last operation ensures that those other threads will also
        # notice a pid difference and block waiting for the first thread to
        # release _fork_lock. when each of these threads eventually acquire
        # _fork_lock, they will notice that another thread already called
        # reset() and they will immediately release _fork_lock and continue on.
        self.pid = os.getpid()

    assert OLD_METHOD_SHA == sha256(getsource(ConnectionPool.reset).encode()).digest()
    ConnectionPool.reset = reset


def patch_celery_eta_implementation():  # pylint: disable=protected-access
    from hashlib import sha256
    from inspect import getsource

    from kombu.transport.redis import Channel, dumps

    from lib.tasks.consts import (
        ETA_CELERY_ID_BY_TIME_KEY,
        ETA_CELERY_QUEUE_BY_ID,
        ETA_CELERY_BODY_BY_ID,
    )

    OLD_METHOD_SHA = (  # pylint: disable=invalid-name
        b"7\xbbd\xc9\x90\xc1=\x9a\xc4\xd9\x8b{\x15\xc2l\xb6\xc6\xa8\xe6\x01'\xb8\xb6M5"
        b"\xe8\xe4@\xcc\xca\x80d"
    )

    def _put(self, queue, message, **kwargs):
        """Deliver message."""

        pri = self._get_message_priority(message, reverse=False)  # pylint: disable=protected-access

        with self.conn_or_acquire() as client:
            if 'eta' in message['headers'] and (
                eta := (message['headers']['eta'] or message['headers'].get('old_eta', None))
            ):
                message['headers']['eta'] = None
                message['headers']['old_eta'] = eta
                timestamp = datetime.datetime.fromisoformat(eta).timestamp()
                pipeline = client.pipeline(transaction=True)
                pipeline.hset(
                    ETA_CELERY_BODY_BY_ID,
                    key=message['headers']['id'],
                    value=dumps(message),
                )
                pipeline.hset(
                    ETA_CELERY_QUEUE_BY_ID,
                    key=message['headers']['id'],
                    value=self._q_for_pri(queue, pri),  # pylint: disable=protected-access
                )
                pipeline.zadd(
                    ETA_CELERY_ID_BY_TIME_KEY,
                    mapping={message['headers']['id']: timestamp},
                )
                pipeline.execute()
            else:
                client.lpush(
                    self._q_for_pri(queue, pri),  # pylint: disable=protected-access
                    dumps(message),
                )

    assert (
        OLD_METHOD_SHA
        == sha256(getsource(Channel._put).encode()).digest()  # pylint: disable=protected-access
    )
    Channel._put = _put  # pylint: disable=protected-access


def patch_trace_django_signals():
    import logging

    from ddtrace import tracer
    from lib.datadog.enums import DatadogCustomServices
    from bo_obs.datadog.enums import DatadogOperationNames

    logger = logging.getLogger("django.dispatch")

    # A marker for caching
    NO_RECEIVERS = object()  # pylint: disable=invalid-name

    if str_to_bool(os.getenv("USE_UNIFIED_ENV_PROD", "False")):
        signals_service = f'{os.getenv("DD_SERVICE_BASE")}-{DatadogCustomServices.DJANGO_SIGNALS}'
    else:
        signals_service = f'{os.getenv("DD_SERVICE")}-{DatadogCustomServices.DJANGO_SIGNALS}'

    def traced_send(self, sender, **named):
        if not self.receivers or self.sender_receivers_cache.get(sender) is NO_RECEIVERS:
            return []
        responses = []
        for receiver in self._live_receivers(sender):  # pylint: disable=protected-access
            name = receiver.__name__ if hasattr(receiver, '__name__') else str(receiver)
            with tracer.trace(
                DatadogOperationNames.SIGNALS_SEND,
                service=signals_service,
                resource=name,
            ):
                result = receiver(signal=self, sender=sender, **named)
                responses.append((receiver, result))
        return responses

    def traced_send_robust(self, sender, **named):
        if not self.receivers or self.sender_receivers_cache.get(sender) is NO_RECEIVERS:
            return []
        responses = []
        for receiver in self._live_receivers(sender):  # pylint: disable=protected-access
            name = receiver.__name__ if hasattr(receiver, '__name__') else str(receiver)
            with tracer.trace(
                DatadogOperationNames.SIGNALS_SEND_ROBUST,
                service=signals_service,
                resource=name,
            ):
                try:
                    response = receiver(signal=self, sender=sender, **named)
                except Exception as err:  # pylint: disable=broad-exception-caught
                    logger.error(
                        "Error calling %s in Signal.send_robust() (%s)",
                        receiver.__qualname__,
                        err,
                        exc_info=err,
                    )
                    responses.append((receiver, err))
                else:
                    responses.append((receiver, response))
        return responses

    from django.dispatch.dispatcher import Signal

    Signal.send = traced_send
    Signal.send_robust = traced_send_robust


def patch_trace_django_commands():
    """Instrument Django management commands with Datadog tracing by creating a span around command
    execution, so we can observe what commands and when were run.
    """
    from ddtrace import tracer
    from django.core.management.base import BaseCommand
    from lib.datadog.enums import DatadogCustomServices

    if hasattr(BaseCommand, "_old_run_from_argv"):
        return

    BaseCommand._old_run_from_argv = BaseCommand.run_from_argv  # pylint: disable=protected-access

    def run_from_argv_traced(self, argv):
        resource = self.__module__ or argv[1]
        try:
            with tracer.trace(
                name="django.management.command",
                service=DatadogCustomServices.DJANGO_COMMANDS,
                resource=resource,
            ) as span:
                span.set_tag(MANUAL_KEEP_KEY)
                return BaseCommand._old_run_from_argv(  # pylint: disable=protected-access
                    self, argv
                )
        finally:
            tracer.flush()

    BaseCommand.run_from_argv = run_from_argv_traced
