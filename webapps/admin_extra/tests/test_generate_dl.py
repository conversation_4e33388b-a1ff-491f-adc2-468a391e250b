import re

from django.urls.base import reverse
from django.test.utils import override_settings

from model_bakery import baker

from webapps.admin_extra.tests import DjangoTestCase
from webapps.business.models import Business
from webapps.user.models import User

from country_config.enums import Country


class TestWhatsappGenerateDLButton(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.login_admin()
        user = baker.make(User)
        self.business = baker.make(Business, owner=user)

    @override_settings(API_COUNTRY=Country.ES)
    def test_btn_exist_on_business_page(self):
        url = reverse('admin:business_business_change', args=(self.business.id,))
        resp = self.client.get(url).content.decode('utf-8')

        btn_name = 'Generate marketplace deeplink'
        btn_url = reverse('admin:generate_mp_invite_deeplink', args=(self.business.id,))
        self.assertIn(btn_name, resp)
        self.assertIn(btn_url, resp)

    @override_settings(API_COUNTRY=Country.ES)
    def test_get_generate_dl(self):
        url = reverse('admin:generate_mp_invite_deeplink', args=(self.business.id,))
        resp = self.client.get(url).content.decode('utf-8')
        self.assertIn('Channel', resp)
        self.assertIn('Campaign', resp)
        self.assertIn('Feature', resp)

    @override_settings(API_COUNTRY=Country.ES)
    def test_post_generate_dl(self):
        url = reverse('admin:generate_mp_invite_deeplink', args=(self.business.id,))
        resp = self.client.post(
            url, {'feature': 'test_feature', 'channel': 'whatsapp', 'campaign': 'test'}
        )

        regex = re.compile(r"Deeplink generated for (?P<name>[\w\s]+): (?P<dl>https?://\S+)")
        match = regex.search(resp.content.decode('UTF-8'))
        self.assertIsNotNone(match)
        name = match.group('name')
        self.assertEqual(name, self.business.name)
