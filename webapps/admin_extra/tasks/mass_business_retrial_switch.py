import contextlib
import io
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
from django.conf import settings
from django.db import transaction

from lib.celery_tools import (
    celery_task,
)
from lib.email import (
    send_email,
)
from lib.tools import tznow
from webapps.admin_extra.tasks.utils import BaseCSVLoader
from webapps.admin_extra.tasks.switch_merchants_to_billing import SwitchBusinessResult
from webapps.business.models import Business, ReTrialAttempt
from webapps.business.models.business_change import BusinessChange
from webapps.segment.tasks import analytics_business_re_trial_eligible_task
from webapps.user.models import User


@dataclass
class ParseDurationResult:
    """Result of parsing duration value."""

    duration: int | None = None
    error: str | None = None

    @property
    def success(self) -> bool:
        """True if parsing was successful (no error)."""
        return self.error is None


_logger = logging.getLogger('booksy.admin_extra')

CSV_READ_FIELD_NAMES = ('business_id',)  # Keep backward compatibility

CSV_WRITE_FIELDS = ['business_id', 'duration', 'result', 'message']

IMPORT_NAME = f'Business ReTrial switch [{settings.API_COUNTRY}]'


class CSVLoader(BaseCSVLoader):
    dtype_mapping = {
        'business_id': int,
        # duration is optional - handle in processing logic
    }
    max_records_in_batch = 500


def _parse_date_string(duration_str: str, loading_date: datetime) -> tuple[datetime, str]:
    """Helper function to parse date string in multiple formats."""
    date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y']

    for date_format in date_formats:
        try:
            end_date = datetime.strptime(duration_str, date_format)
        except ValueError:
            continue

        # Set the time and timezone to match loading_date to ensure accurate day calculation
        end_date = end_date.replace(
            tzinfo=loading_date.tzinfo,
            hour=loading_date.hour,
            minute=loading_date.minute,
            second=loading_date.second,
            microsecond=loading_date.microsecond,
        )
        return end_date, None

    return None, (
        f'Invalid date format: {duration_str}. ' 'Use YYYY-MM-DD, DD/MM/YYYY, or number of days'
    )


def _validate_duration_range(days: int) -> str:
    """Helper function to validate duration is within allowed range."""
    if days < 1 or days > settings.MAX_TRIAL_EXTENSION_DURATION:
        return f'Duration must be between 1 and {settings.MAX_TRIAL_EXTENSION_DURATION} days'
    return None


def parse_duration(  # pylint: disable=too-many-return-statements
    duration_value: str, loading_date: datetime
) -> ParseDurationResult:
    """
    Parse duration value which can be either:
    - Number of days (e.g., '14')
    - End date (e.g., '2024-01-15' or '15/01/2024')

    Returns: ParseDurationResult with duration and error fields
    """
    if not duration_value or pd.isna(duration_value) or str(duration_value).strip() == '':
        return ParseDurationResult(duration=14)

    duration_str = str(duration_value).strip()

    # Try to parse as integer (number of days)
    with contextlib.suppress(ValueError):
        days = int(float(duration_str))
        error = _validate_duration_range(days)
        return (
            ParseDurationResult(duration=days)
            if error is None
            else ParseDurationResult(error=error)
        )

    # Try to parse as date
    try:
        end_date, error = _parse_date_string(duration_str, loading_date)
        if error:
            return ParseDurationResult(error=error)

        # Calculate days from loading date to end date
        days_diff = (end_date - loading_date).days

        if days_diff < 1:
            return ParseDurationResult(
                error=f'End date must be after loading date ({loading_date.strftime("%Y-%m-%d")})'
            )

        error = _validate_duration_range(days_diff)
        return (
            ParseDurationResult(duration=days_diff)
            if error is None
            else ParseDurationResult(error=error)
        )

    except (ValueError, TypeError) as e:
        return ParseDurationResult(error=f'Error parsing duration: {str(e)}')


def business_validate(  # pylint: disable=too-many-return-statements
    business: Business,
    active_retrial_attempt: bool,
    duration: int = None,
) -> SwitchBusinessResult:
    """Validate business for retrial eligibility."""
    if business is None:
        return SwitchBusinessResult(success=False, message='Business does not exist')

    if business.status != Business.Status.TRIAL_BLOCKED:
        return SwitchBusinessResult(
            success=False,
            message=(
                f'Business status different from: TRIAL_BLOCKED '
                f'(current status: {business.get_status_display()})'
            ),
        )

    if active_retrial_attempt:
        return SwitchBusinessResult(
            success=False, message='Business already has an active ReTrial record'
        )

    if duration is not None:
        error = _validate_duration_range(duration)
        if error:
            return SwitchBusinessResult(success=False, message=error)

    return SwitchBusinessResult(success=True, message=None)


def _load_and_validate_csv_data(file_path: str, email: str, loading_dt: str, dry_run_info: str):
    """Load and validate CSV data, return None if failed."""
    try:
        data_frames = CSVLoader.load_from_file(file_path)
        return CSVLoader.clean_data_frames(data_frames, business_index=True)
    except Exception as err:  # pylint: disable=broad-except
        send_email(
            to_addr=email,
            body=f'{IMPORT_NAME}{dry_run_info} error: {err}',
            subject=f'{IMPORT_NAME}{dry_run_info} - error loading file {loading_dt}',
        )
        return None


def _prepare_business_data(business_ids):
    """Prepare business and retrial attempt data."""
    businesses = {
        business.id: business for business in Business.objects.filter(pk__in=business_ids)
    }
    active_retrial_attempts = ReTrialAttempt.objects.filter(
        business_id__in=business_ids,
        used__isnull=True,
    ).values_list('business_id', flat=True)
    return businesses, active_retrial_attempts


def _validate_and_parse_records(input_records, businesses, active_retrial_attempts, loading_date):
    """Validate and parse all input records."""
    to_process = []
    for business_id, row in input_records.items():
        duration_value = row.get('duration', '')
        parse_result = parse_duration(duration_value, loading_date)

        if not parse_result.success:
            row.update({'result': 'ERROR', 'message': parse_result.error})
            continue

        result = business_validate(
            business=businesses.get(business_id),
            active_retrial_attempt=business_id in active_retrial_attempts,
            duration=parse_result.duration,
        )

        if not result.success:
            row.update({'result': 'ERROR', 'message': result.message})
            continue

        row['duration'] = parse_result.duration
        to_process.append((business_id, parse_result.duration, row))

    return to_process


def _apply_single_retrial(  # pylint: disable=too-many-arguments,too-many-positional-arguments
    business, duration, operator, business_id, row, loading_date
):
    """Apply retrial to a single business."""
    with transaction.atomic():
        ReTrialAttempt.objects.create(
            business=business,
            duration=duration,
            operator=operator,
            switched_from_status=business.status,
            used=loading_date,
        )

        old_business_vars = BusinessChange.extract_vars(business)
        business.status = Business.Status.TRIAL
        business.active = True
        business.trial_till = loading_date + timedelta(days=duration)
        business.save(update_fields=['status', 'active', 'trial_till'])

        BusinessChange.add(
            business,
            business,
            old_business_vars,
            operator=operator,
            metadata={'endpoint': 'mass_business_retrial_switch', 'duration': duration},
        )

        analytics_business_re_trial_eligible_task.delay(context={'business_id': business_id})

        trial_end_date = business.trial_till.strftime("%Y-%m-%d")
        row.update({'result': 'SUCCESS', 'message': f'Trial applied until {trial_end_date}'})


def _apply_retrials(to_process, businesses, operator, loading_date):
    """Apply retrials to all valid businesses."""
    for business_id, duration, row in to_process:
        try:
            business = businesses[business_id]
            _apply_single_retrial(business, duration, operator, business_id, row, loading_date)
        except (ValueError, TypeError, AttributeError) as e:
            _logger.error('Error processing business %s: %s', business_id, str(e))
            row.update({'result': 'ERROR', 'message': f'Processing error: {str(e)}'})


def _handle_dry_run(to_process, loading_date):
    """Handle dry run scenario."""
    for _, duration, row in to_process:
        trial_till = loading_date + timedelta(days=duration)
        row.update(
            {
                'result': 'DRY_RUN_SUCCESS',
                'message': f'Would apply trial until {trial_till.strftime("%Y-%m-%d")}',
            }
        )


def _send_completion_email(input_records, email, loading_dt, dry_run_info):
    """Send completion email with results."""
    df_output = pd.DataFrame.from_dict(input_records, orient='index', columns=CSV_WRITE_FIELDS)
    df_output = df_output.replace(np.nan, 'NULL')

    writer = io.BytesIO()
    writer.write(df_output.to_csv(index=False).encode())
    writer.seek(0)

    send_email(
        to_addr=email,
        body=f'{IMPORT_NAME}{dry_run_info} report in attachment',
        subject=f'{IMPORT_NAME}{dry_run_info} - report {loading_dt}',
        attachments=[
            (
                f'business_retrial_switch_{settings.API_COUNTRY}_{loading_dt}.csv',
                writer.getvalue(),
                'text/csv',
            )
        ],
    )


@celery_task
def mass_business_retrial_switch_task(
    file_path: str,
    email: str,
    operator_id: int,
    dry_run: bool = False,
) -> None:
    # Single source of truth for loading timestamp - ensures consistency across all operations
    loading_date = tznow()
    loading_dt = loading_date.strftime('%d_%m_%Y_%H_%M_%S')
    dry_run_info = '[Dry Run!]' if dry_run else ''

    input_records = _load_and_validate_csv_data(file_path, email, loading_dt, dry_run_info)
    if input_records is None:
        return

    operator = User.objects.get(id=operator_id)
    businesses, active_retrial_attempts = _prepare_business_data(input_records.keys())
    to_process = _validate_and_parse_records(
        input_records, businesses, active_retrial_attempts, loading_date
    )

    if to_process and not dry_run:
        _apply_retrials(to_process, businesses, operator, loading_date)
    elif dry_run:
        _handle_dry_run(to_process, loading_date)

    _send_completion_email(input_records, email, loading_dt, dry_run_info)
