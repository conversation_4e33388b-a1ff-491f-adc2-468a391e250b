from unittest.mock import Mock

import pytest

from webapps.google_places.application.dtos.address_validation import (
    AddressValidationRequestData,
    AddressComponents,
)
from webapps.google_places.application.services.address_validation import AddressValidationService
from webapps.google_places.domain.dtos.address_validation import (
    GoogleAddressValidationResponse,
    AddressComponents as DomainAddressComponents,
)
from webapps.google_places.domain.dtos.shared import Location
from webapps.google_places.domain.enums import Country


@pytest.fixture
def mock_google_gateway():
    """Fixture for the GoogleAddressValidationGateway mock."""
    gateway = Mock()
    mock_response = GoogleAddressValidationResponse(
        possible_next_action=Mock(),
        formatted_address="mock address",
        address_complete=True,
        has_replaced_components=False,
        missing_components=[],
        unconfirmed_components=[],
        replaced_components=[],
        address_components=DomainAddressComponents(
            street_number="123",
            street="Main St",
            city="Anytown",
            country="USA",
            zipcode="12345",
            subpremise=None,
        ),
        address_lines=["123 Main St"],
        location=Location(latitude=0, longitude=0),
        response_id="mock-response-id",
    )
    gateway.validate_address.return_value = mock_response
    return gateway


@pytest.fixture
def mock_settings_provider():
    """Fixture for the SettingsProvider mock."""
    provider = Mock()
    provider.get_settings.return_value.api_country = Country.US
    return provider


@pytest.fixture
def mock_pin_location_service():
    """Fixture for the PinLocationValidationService mock."""
    return Mock()


@pytest.fixture
def mock_domain_address_validation_service():
    """Fixture for the DomainAddressValidationService mock."""
    return Mock()


@pytest.fixture
def mock_translator():
    """Fixture for the Translator mock."""
    return Mock()


@pytest.fixture
def address_validation_service(  # pylint: disable=redefined-outer-name
    mock_google_gateway,
    mock_settings_provider,
    mock_pin_location_service,
    mock_domain_address_validation_service,
    mock_translator,
):
    """Fixture for the AddressValidationService instance with mocked dependencies."""
    return AddressValidationService(
        google_gateway=mock_google_gateway,
        settings_provider=mock_settings_provider,
        pin_location_validation_service=mock_pin_location_service,
        address_validation_service=mock_domain_address_validation_service,
        translator=mock_translator,
    )


# pylint: disable=redefined-outer-name
class TestAddressValidationServiceBehavior:
    @pytest.mark.parametrize(
        "address_lines, street_number, country, expected_lines",
        [
            # Prepending countries
            (["Main St"], "123", Country.US, ["123 Main St"]),
            (["Downing Street"], "10", Country.GB, ["10 Downing Street"]),
            (["Champs-Élysées"], "101", Country.FR, ["101 Champs-Élysées"]),
            # Appending countries
            (["Aleje Jerozolimskie"], "98", Country.PL, ["Aleje Jerozolimskie 98"]),
            (["Calle de Alcalá"], "52", Country.ES, ["Calle de Alcalá 52"]),
            # Multiple lines
            (["Main St", "Apt 4B"], "123", Country.US, ["123 Main St", "Apt 4B"]),
        ],
    )
    def test_validate_address_formats_address_lines_correctly(
        self,
        address_validation_service,
        mock_google_gateway,
        mock_settings_provider,
        mock_domain_address_validation_service,
        address_lines,
        street_number,
        country,
        expected_lines,
    ):
        """
        Tests that validate_address correctly formats address lines before
        passing them to the gateway, based on country conventions.
        """
        mock_domain_address_validation_service.format_address_lines.return_value = expected_lines

        mock_settings_provider.get_settings.return_value.api_country = country
        request_data = AddressValidationRequestData(
            address_lines=address_lines,
            administrative_area="Test",
            location=Location(latitude=0, longitude=0),
            address_components=AddressComponents(
                street_number=street_number,
                street="Test Street",
                city="Test City",
                country="Test Country",
                zipcode="12345",
                subpremise=None,
            ),
            previous_response_id=None,
        )
        address_validation_service.validate_address(request_data)

        mock_domain_address_validation_service.format_address_lines.assert_called_once_with(
            address_lines=address_lines,
            address_components=request_data.address_components,
            country=country,
        )

        mock_google_gateway.validate_address.assert_called_once()
        call_args = mock_google_gateway.validate_address.call_args
        called_params = call_args.kwargs['validate_address_params']
        assert called_params.address.address_lines == expected_lines

    @pytest.mark.parametrize(
        "address_lines, street_number, country",
        [
            # Street number already present
            (["123 Main St"], "123", Country.US),
            # Empty street number
            (["Main St"], "", Country.US),
            # Empty address lines
            ([], "123", Country.US),
        ],
    )
    def test_validate_address_does_not_change_address_lines_when_unnecessary(
        self,
        address_validation_service,
        mock_google_gateway,
        mock_settings_provider,
        mock_domain_address_validation_service,
        address_lines,
        street_number,
        country,
    ):
        """
        Tests that validate_address does not modify address lines when
        the street number is already present, missing, or the lines are empty.
        """
        mock_domain_address_validation_service.format_address_lines.return_value = address_lines

        mock_settings_provider.get_settings.return_value.api_country = country
        request_data = AddressValidationRequestData(
            address_lines=address_lines,
            administrative_area="Test",
            location=Location(latitude=0, longitude=0),
            address_components=AddressComponents(
                street_number=street_number,
                street="Test Street",
                city="Test City",
                country="Test Country",
                zipcode="12345",
                subpremise=None,
            ),
            previous_response_id=None,
        )
        address_validation_service.validate_address(request_data)

        mock_domain_address_validation_service.format_address_lines.assert_called_once_with(
            address_lines=address_lines,
            address_components=request_data.address_components,
            country=country,
        )

        mock_google_gateway.validate_address.assert_called_once()
        call_args = mock_google_gateway.validate_address.call_args
        called_params = call_args.kwargs['validate_address_params']
        assert called_params.address.address_lines == address_lines

    def test_validate_address_skips_pin_location_validation_when_street_number_missing(
        self,
        address_validation_service,
        mock_google_gateway,
        mock_settings_provider,
        mock_domain_address_validation_service,
        mock_pin_location_service,
    ):
        mock_domain_address_validation_service.format_address_lines.return_value = ["Main St"]
        mock_domain_address_validation_service.is_address_valid.return_value = True
        mock_domain_address_validation_service.should_skip_pin_location_validation.return_value = (
            True
        )

        request_data = AddressValidationRequestData(
            address_lines=["Main St"],
            administrative_area="Test",
            location=Location(latitude=0, longitude=0),
            address_components=AddressComponents(
                street_number="123",
                street="Main St",
                city="Test City",
                country="Test Country",
                zipcode="12345",
                subpremise=None,
            ),
            previous_response_id=None,
        )

        result = address_validation_service.validate_address(request_data)

        mock_domain_address_validation_service.should_skip_pin_location_validation.assert_called_once_with(  # pylint: disable=line-too-long
            address_lines=["Main St"], address_components=request_data.address_components
        )

        mock_pin_location_service.validate_pin_location.assert_not_called()

        assert result.pin_location.status.value == "OK"
