from webapps.business_customer_info.v2.application.future_domain_services.bci_duplicates import (
    BCIDuplicatesServiceImpl,
)
from webapps.business.elasticsearch.business_customer import BusinessCustomerDocument
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.marketing.models import Unsubscribed


class UnsubscribedService:
    """Service class with methods to operate Unsubscribed model class objects."""

    @classmethod
    def unsubscribe_bci_and_duplicates(cls, bci: BusinessCustomerInfo) -> set[int]:
        bci_ids = BCIDuplicatesServiceImpl.get_bci_with_duplicates(bci)
        bci_to_unsubscribe = BusinessCustomerInfo.objects.filter(id__in=bci_ids)
        already_unsubscribed = Unsubscribed.objects.filter(bci__in=bci_ids).values_list(
            'bci', flat=True
        )

        if already_unsubscribed:
            bci_to_unsubscribe = bci_to_unsubscribe.exclude(id__in=already_unsubscribed)

        Unsubscribed.objects.bulk_create(
            [Unsubscribed(bci=bci_instance) for bci_instance in bci_to_unsubscribe]
        )

        BusinessCustomerDocument.reindex(ids=bci_ids)
        return bci_ids
