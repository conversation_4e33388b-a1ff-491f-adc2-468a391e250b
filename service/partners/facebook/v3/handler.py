import json
import logging
import re
import urllib
from typing import Optional
from urllib.parse import quote

import requests
import tornado.web
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from rest_framework import status

import versions
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature.integrations import (
    FacebookFBEExtrasMessengerChatFlag,
    FacebookFBEExtrasStafferFlag,
)
from lib.tools import id_to_external_api
from service.partners.events import spawn_ig_integration_attempt_analytics_event
from service.partners.facebook.v3.enums import ClientType
from service.partners.facebook.v3.serializers import (
    <PERSON><PERSON><PERSON><PERSON>UpdateSerializer,
    <PERSON><PERSON><PERSON>WebhookSerializer,
    FacebookRerouterSerializer,
)
from service.partners.tools import PartnerRerouterMixin, reroute_api_request
from service.tools import RequestHandler, json_request, session
from webapps.business.enums import FacebookFBEConnect, StaffAccessLevels
from webapps.business.models import Business, Resource
from webapps.feeds.facebook import config
from webapps.feeds.facebook.api_client import F<PERSON><PERSON><PERSON>lient, FBEAPIError
from webapps.feeds.facebook.tasks import disconnect_business_from_fbe_task, update_fb_services_task
from webapps.feeds.facebook.tools import FacebookFBEAuth, verify_sha1

logger = logging.getLogger('booksy.facebook.fbe.handler')


def staffer_access_level(access_level: str):
    return access_level in [
        Resource.STAFF_ACCESS_LEVEL_STAFF,
        Resource.STAFF_ACCESS_LEVEL_ADVANCED,
        Resource.STAFF_ACCESS_LEVEL_RECEPTION,
    ]


def staffer_business_id(key: str):
    pattern = r'^([\w\d]+\-){1,3}\-?\d+\-\d+$'
    return bool(re.match(pattern, key))


class FacebookFBEExtrasBaseHandler(RequestHandler):
    # https://developers.facebook.com/docs/facebook-business-extension/fbe/get-started/appointments-onboarding
    def get_extras(self, business: Business, access_level: StaffAccessLevels) -> dict:
        external_business_id = id_to_external_api(business.id)
        business_url_for_cta = self.get_fb_external_link(business.id, True)
        business_url_for_fb_cta = self.get_fb_external_link(business.id, False)
        user_data = UserData(custom={CustomUserAttributes.BUSINESS_ID: business.id})

        if staffer_access_level(access_level):
            if not FacebookFBEExtrasStafferFlag(user_data):
                return {}
            external_business_id = f'{id_to_external_api(business.id)}-{self.user_staffer_id}'
            business_url_for_cta = self.get_fb_external_link(
                business.id, True, None, self.user_staffer_id
            )
            business_url_for_fb_cta = self.get_fb_external_link(
                business.id, False, None, self.user_staffer_id
            )

        extras = {
            "setup": {
                "external_business_id": external_business_id,
                "timezone": business.get_timezone()._long_name,  # pylint: disable=protected-access
                "currency": settings.CURRENCY_CODE,
                "business_vertical": "APPOINTMENTS",
            },
            "business_config": {
                "business": {
                    "name": business.name,
                },
                "ig_cta": {
                    "enabled": True,
                    "cta_button_text": "Book Now",
                    "cta_button_url": business_url_for_cta,
                },
                "page_cta": {
                    "enabled": True,
                    "cta_button_text": "Book Now",
                    "cta_button_url": business_url_for_fb_cta,
                    "below_button_text": "Powered by Booksy",
                },
                "page_card": {
                    "enabled": True,
                    "see_all_text": "See All",
                    "see_all_url": business_url_for_fb_cta,
                    "cta_button_text": "Book",
                },
                "messenger_menu": {
                    "enabled": True,
                    "cta_button_text": "Book Now",
                    "cta_button_url": business_url_for_fb_cta,
                },
                "thread_intent": {
                    "enabled": True,
                    "cta_button_url": business_url_for_fb_cta,
                },
            },
            "repeat": False,
        }

        if FacebookFBEExtrasMessengerChatFlag(user_data):
            extras["business_config"] |= {
                # Meta business extension feature, we can use it later
                "messenger_chat": {
                    "enabled": True,
                    "domains": ["https://booksy.com"],
                },
            }

        return extras

    def get_fb_external_link(
        self,
        business_id: int,
        ig: bool,  # pylint: disable=invalid-name
        service_variant_id: Optional[int] = None,
        staffer_id: Optional[int] = None,
    ) -> str:
        url = (
            f"{settings.MARKETPLACE_URL}/{settings.MARKETPLACE_LANG_COUNTRY}"
            f"/instant-experiences/widget/{business_id}?"
            "instant_experiences_enabled=true"
        )
        if ig:  # pylint: disable=invalid-name
            url = f"{url}&ig_ix=true"
            spawn_ig_integration_attempt_analytics_event(business_id, self.user.id)
        else:
            url = f"{url}&is_fb=1"
        if staffer_id:
            url = f"{url}&staffer_id={staffer_id}"
        if service_variant_id:
            url = f"{url}&variantId={service_variant_id}"
        return url

    def get_owned_business(self):
        return (
            self.user.businesses.filter(
                active=True,
                deleted__isnull=True,
            )
            .exclude(
                status__in=[
                    Business.Status.VENUE,
                    Business.Status.B_LISTING,
                ],
            )
            .first()
        )

    def get_staffer_business(self) -> Business:
        query = {
            'staff_user': self.user,
            'active': True,
            'deleted__isnull': True,
        }
        # inspired by `get_access_level` from `service/tools.py`:
        staffer = Resource.objects.filter(**query).order_by('-created').first()
        self.user_staffer_id = staffer.id
        return staffer.business


class FacebookFBEOauthUrlHandler(FacebookFBEExtrasBaseHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self):
        """
        swagger:
            summary: Facebook Dialog OAuth link
            notes: Returns redirect url to Facebook OAuth; extras dict is
                   returned as encoded path parameter
            type: FacebookFBEDialogURL
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: query
            parameters:
                - name: client_type
                  description: Client type
                  type: string
                  paramType: query
                  enum_from_const: service.partners.facebook.v3.enums.ClientType
        :swagger
        swaggerModels:
            FacebookFBEDialogURL:
                id: FacebookFBEDialogURL
                properties:
                    facebook_dialog_url:
                        type: string
                        description: Redirect url to Facebook OAuth
        :swaggerModels
        """

        data = self._prepare_get_arguments()
        business_id = data.get('business_id')
        client_type = data.get('client_type', ClientType.WEB)
        if not business_id:
            raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND)

        business = self.business_with_manager(business_id)
        # only owner data
        extras = self.get_extras(business, Resource.STAFF_ACCESS_LEVEL_OWNER)
        response = {
            'facebook_dialog_url': config.FB_DIALOG_URL.format(
                client_id=settings.FB_APP_ID,
                redirect_target=self._redirect_uri(client_type),
                extras_dict_as_str=quote(json.dumps(extras)),
            )
        }
        return self.finish_with_json(status.HTTP_200_OK, response)

    @staticmethod
    def _redirect_uri(client_type: ClientType) -> str:
        uri_templates = {
            ClientType.WEB: config.FBE_REDIRECT_URI_WEB,
            ClientType.ANDROID: config.FBE_REDIRECT_URI_MOBILE,
            ClientType.IOS: config.FBE_REDIRECT_URI_MOBILE,
            ClientType.FRONTDESK: config.FBE_REDIRECT_URI_FRONTDESK,
        }
        return quote(uri_templates.get(client_type, config.FBE_REDIRECT_URI_WEB))


class FacebookFBEExtrasHandler(FacebookFBEExtrasBaseHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self):
        """
        swagger:
            summary: Facebook extras dict
            type: FacebookFBEExtras
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: query
        :swagger
        swaggerModels:
            FacebookFBEExtras:
                id: FacebookFBEExtras
                properties:
                    setup:
                        type: FacebookFBESetup
                        description: Business basic data
                    business_config:
                        type: FacebookFBEBusinessConfig
                        description: Business' Facebook page config
                    repeat:
                        type: boolean
                        description: Constant value based on facebook docs
            FacebookFBESetup:
                id: FacebookFBESetup
                properties:
                    external_business_id:
                        type: string
                        description: Business' external ID
                    timezone:
                        type: string
                        description: Business' timezone (long name)
                    currency:
                        type: string
                        description: Business' currency code
                    business_vertical:
                        type: string
                        description: constant based on facebook docs
                        enum:
                            - SERVICES
            FacebookFBEBusinessConfig:
                id: FacebookFBEBusinessConfig
                properties:
                    business:
                        type: string
                        description: business object, structure irrelevant
        :swaggerModels
        """
        data = self._prepare_get_arguments()
        business_id = data.get('business_id')
        access_level = self.get_access_level(self.user)
        if business_id:
            # New approach
            business = self.business_with_staffer(business_id)
        else:
            # Old one - guesses the business (works only for owner)
            # TODO: change frontend clients to send business_id explicitly
            if staffer_access_level(access_level):
                business = self.get_staffer_business()
            else:
                business = self.get_owned_business()
            if not business:
                if access_level != Resource.STAFF_ACCESS_LEVEL_OWNER:
                    # it's not possible to have a resource w/o having business
                    raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND)
                # Possible during onboarding when business doesn't exist yet
                # (see self.get_access_level method) but FB/IG login is used (and extras fetched)
                return self.finish_with_json(status.HTTP_200_OK, {})

        extras = self.get_extras(business, access_level)
        return self.finish_with_json(status.HTTP_200_OK, extras)


class FacebookFBETokenHandler(RequestHandler):
    """
    swagger:
        summary: FB token endpoint
        type: string
        parameters:
            - name: business_id
              description: Business ID
              type: integer
              paramType: query
            - name: access_token
              description: FB Token
              type: string
              paramType: query
    :swagger
    """

    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def post(self):
        self._check_access_level()
        data = self._prepare_get_arguments()
        logger.debug('FB token POST request. Query params: %s', data)
        serializer = FacebookFBETokenUpdateSerializer(data=data)
        self.validate_serializer(serializer)
        data = serializer.validated_data
        business = self.business_with_manager(data['business_id'])
        access_token = data['access_token']
        success = self.handle_add_integration(business, access_token)
        if success:
            self.handle_fb_services_update(business.id)
        self.finish_with_json(status.HTTP_200_OK, {'message': 'Token created'})

    @session(login_required=True)
    def delete(self):
        self._check_access_level()
        data = self._prepare_get_arguments()
        logger.debug('FB token DELETE request. Query params: %s', data)
        serializer = FacebookFBETokenUpdateSerializer(data=data)
        self.validate_serializer(serializer)
        data = serializer.validated_data
        business = self.business_with_manager(data['business_id'])
        if not business.is_fbe_connected():
            raise tornado.web.HTTPError(
                status.HTTP_400_BAD_REQUEST,
                reason="Given business doesn't have FBE connection set",
            )
        self.handle_remove_integration(business)
        self.finish_with_json(status.HTTP_200_OK, {'message': 'Token deleted'})

    def _check_access_level(self):
        access_level = self.get_access_level(self.user)
        if access_level not in (
            Resource.STAFF_ACCESS_LEVEL_OWNER,
            Resource.STAFF_ACCESS_LEVEL_MANAGER,
        ):
            raise tornado.web.HTTPError(status.HTTP_401_UNAUTHORIZED)

    def handle_add_integration(self, business: Business, access_token: str) -> bool:
        user_data = self.fetch_user_data_from_fb(business.id, access_token)
        if not user_data:
            return False
        connect_data = FacebookFBEConnect(
            enabled=True,
            access_token=access_token,
            business_manager_id=user_data.get('business_manager_id'),
            catalog_id=user_data.get('catalog_id'),
            pixel_id=user_data.get('pixel_id'),
        )
        business.set_facebook_fbe_connection_data(connect_data)
        return bool(user_data.get('catalog_id'))

    @staticmethod
    def handle_remove_integration(business: Business) -> None:
        access_token = business.get_facebook_fbe_connection_data().access_token
        disconnect_business_from_fbe_task.delay(business.id, access_token)
        business.remove_facebook_fbe_connection()

    @staticmethod
    def fetch_user_data_from_fb(business_id: int, access_token: str) -> dict:
        params = {
            'fbe_external_business_id': id_to_external_api(business_id),
            'access_token': access_token,
        }
        client = FBEAPIClient()
        response = None
        try:
            logger.debug('FBE fetch business details. Query params: %s', params)
            response = client.get(
                config.FBE_DETAILS_ENDPOINT,
                params=params,
            )
            logger.debug('FBE fetch business details. Response: %s', response)
            return response['data'][0]
        except FBEAPIError as error:
            raise tornado.web.HTTPError(
                status.HTTP_400_BAD_REQUEST,
                reason="Error when fetching data from FB. Make sure provided "
                f"data is valid. Original error: {error}.",
            ) from error
        except KeyError as error:
            raise tornado.web.HTTPError(
                status.HTTP_400_BAD_REQUEST,
                reason="Couldn't fetch expected data from FB. Make sure"
                " provided data is valid. Response from FB: "
                f"{response}.",
            ) from error

    @staticmethod
    def handle_fb_services_update(business_id: int) -> None:
        update_fb_services_task.delay(business_id)


class FacebookFBEWebhookHandler(PartnerRerouterMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @property
    def rerouter_logger(self):
        return logging.getLogger('booksy.facebook.fbe.rerouter')

    def rerouter_data(self, *_args, **_kwargs):
        business_id = None
        if (entry := self.data.get('entry', [])) and (changes := entry[0].get('changes', [])):
            business_id = changes[0].get('value', {}).get('business_id')
        return {'business_id': business_id}

    def finish_with_error(self, _status, error):
        # We should not return other responses than 200 from webhook
        return self.finish_with_json(status.HTTP_200_OK, error)

    def get(self):
        """
        swagger:
            summary: FBE webhook verify endpoint
            description: External endpoint for Facebook
            type: string
            parameters:
                - name: hub.mode
                  description: FB webhook mode
                  type: string
                  paramType: query
                - name: hub.verify_token
                  description: Expected FB token
                  type: string
                  paramType: query
                - name: hub.challenge
                  description: FB webhook challenge
                  type: integer
                  paramType: query
        :swagger
        """
        logger.info('FB webhook verification GET request')
        data = self._prepare_get_arguments()
        logger.debug('Query params: %s', data)
        challenge = data.get('hub.challenge')
        token = data.get('hub.verify_token')
        mode = data.get('hub.mode')
        if not challenge or not token or mode != 'subscribe':
            raise tornado.web.HTTPError(
                status.HTTP_400_BAD_REQUEST,
                reason='Invalid request parameters',
            )
        try:
            challenge = int(challenge)
        except ValueError as error:
            raise tornado.web.HTTPError(
                status.HTTP_400_BAD_REQUEST,
                reason='Invalid challenge value type',
            ) from error
        if token != settings.FB_VERIFICATION_TOKEN:
            raise tornado.web.HTTPError(
                status.HTTP_403_FORBIDDEN,
                reason='Invalid token',
            )
        self.finish_with_json(status.HTTP_200_OK, challenge)

    @json_request
    @verify_sha1()
    @reroute_api_request(
        serializer=FacebookRerouterSerializer,
        error='Unable to redirect webhook request',
        endpoint_url='business_api/me/facebook/v3/webhook',
    )
    def post(self):
        """
        swagger:
            summary: FBE event webhook endpoint
            description: External endpoint for Facebook to send event
            parameters:
                - name: body
                  paramType: body
                  type: FacebookFBEEventPayload
                  description: Facebook event payload (may vary)
        :swagger
        swaggerModels:
            FacebookFBEEventPayload:
                id: FacebookFBEEventPayload
                properties:
                    entry:
                        type: array
                        description: Events
                    object:
                        type: string
                        description: Event type
        :swaggerModels
        """
        logger.debug('FB webhook event POST request. Payload: %s', self.data)
        serializer = FacebookFBEWebhookSerializer(data=self.data)

        if not serializer.is_valid():
            logger.debug('Invalid payload: %r', serializer.errors)
            return self.finish_with_json(status.HTTP_200_OK, serializer.errors)
        data = serializer.validated_data
        for values in data['fb_installs']:
            business_id = values.get('business_id')
            if business_id and staffer_business_id(business_id):
                # for now we don't need to proceed with staffer due to he can add
                # `instagram book now button` without facebook business page
                logger.info('business_id was considered as staffer, so skipped: %s', business_id)
                continue

            internal_business_id = values.get('internal_business_id')
            try:
                business = Business.objects.get(id=internal_business_id)
            except Business.DoesNotExist:
                logger.warning('Business not found, id=%s', internal_business_id)
                continue
            incoming_token = values.get('access_token')
            if not incoming_token:
                business.remove_facebook_fbe_connection()
            else:
                existing = business.get_facebook_fbe_connection_data()
                catalog_id = values.get('catalog_id') or existing.catalog_id
                pixel_id = values.get('pixel_id') or existing.pixel_id
                business_manager_id = (
                    values.get('business_manager_id') or existing.business_manager_id
                )

                if not catalog_id:
                    try:
                        client = FBEAPIClient()
                        fb_data = client.get(
                            config.FBE_DETAILS_ENDPOINT,
                            params={
                                'fbe_external_business_id': id_to_external_api(business.id),
                                'access_token': incoming_token,
                            },
                        )
                        fb_first = (fb_data or {}).get('data', [{}])[0]
                        catalog_id = fb_first.get('catalog_id') or catalog_id
                        pixel_id = fb_first.get('pixel_id') or pixel_id
                        business_manager_id = (
                            fb_first.get('business_manager_id') or business_manager_id
                        )
                    except (FBEAPIError, requests.RequestException, ValueError) as exc:
                        logger.info(
                            'Could not backfill catalog_id from FB for business %s: %s',
                            business.id,
                            exc,
                        )

                connect_data = FacebookFBEConnect(
                    enabled=True,
                    access_token=incoming_token,
                    business_manager_id=business_manager_id or '',
                    catalog_id=catalog_id or '',
                    pixel_id=pixel_id or '',
                )
                business.set_facebook_fbe_connection_data(connect_data)

                if connect_data.access_token and connect_data.catalog_id:
                    self.handle_fb_services_update(business.id)

        if data['redirect_payload']:
            self.handle_redirect_payload(
                data['redirect_payload'],
                data['redirect_country'],
            )

    @staticmethod
    def handle_fb_services_update(business_id: int) -> None:
        update_fb_services_task.delay(business_id)

    def handle_redirect_payload(self, payload: dict, country_code: str) -> None:
        target_url = urllib.parse.urljoin(
            settings.TEMPLATE_API_URL,
            'api/{country_code}/{version}/{endpoint_url}',
        ).format(
            country_code=country_code,
            version=str(versions.VERSION),
            endpoint_url='business_api/me/facebook/v3/webhook',
        )
        logger.warning(
            'Redirecting webhook payload: %r, headers: %r to url %s',
            payload,
            dict(self.request.headers),
            target_url,
        )
        requests.post(
            url=target_url,
            json=payload,
            timeout=30,
            auth=FacebookFBEAuth(),
        )
