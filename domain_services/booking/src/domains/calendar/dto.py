from dataclasses import field
from datetime import date
from typing import NamedTuple

from bitarray import bitarray
from pydantic.dataclasses import dataclass

from ...commons.types import (
    DayMinutes,
    Discount,
    ResourceId,
    ServiceAddonId,
    ServiceVariantId,
)


@dataclass(frozen=True)
class AddOn:
    id: ServiceAddonId
    quantity: int = 1


@dataclass(frozen=True)
class Booking:
    service_variant_id: ServiceVariantId
    staffer_id: int | None = None
    addons: list[AddOn] = field(default_factory=list)

    def __post_init__(self) -> None:
        # Workaroud for beloved serializers
        if self.staffer_id and self.staffer_id < 1:
            object.__setattr__(self, 'staffer_id', None)


@dataclass(frozen=True)
class ComboBooking(Booking):
    combo_children: list[Booking] = field(default_factory=list)


@dataclass(frozen=True)
class TimeSlotsQuery:
    business_id: int
    start_date: date
    end_date: date
    bookings: list[ComboBooking]


Occupancy = bitarray


class ResourceBookedOccupancy(NamedTuple):
    booking: Occupancy
    padding: Occupancy


ResourceBookedOccupancyMap = dict[ResourceId, ResourceBookedOccupancy | None]
DailyResourceBookedOccupancyMap = dict[date, ResourceBookedOccupancyMap | None]


@dataclass
class SlotDetails:
    resources: set[ResourceId]
    discount: Discount | None


# None means Px is not operating, empty dict means that there is not free slots
PossibleSlotsMap = dict[date, dict[DayMinutes, SlotDetails | None]]


@dataclass(frozen=True)
class TimeSlotStart:
    date: date
    day_minutes: DayMinutes


@dataclass(frozen=True)
class StaffersAvailability:
    staffers_slots: dict[ResourceId, TimeSlotStart | None]
    customer_selected_slot: TimeSlotStart
