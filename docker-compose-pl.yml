version: '3.7'

services:
  db:
    container_name: db
    build: .docker/postgres_with_pglogical
    volumes:
      - ./.docker/postgres/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
      - ./.docker/postgres/init_data:/init_data
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - default
      - envoymesh
    command:
      - "postgres"
      - "-c"
      - "max_locks_per_transaction=128"
      - "-c"
      - "wal_level=logical"
      - "-c"
      - "shared_preload_libraries=pglogical"
    environment:
      POSTGRES_PASSWORD: example
  db_reports_full:
    container_name: db_reports_full
    build: .docker/postgres_with_pglogical
    volumes:
      - pgdata_reports_full:/var/lib/postgresql/data
    ports:
      - "5437:5437"
    networks:
      - default
    command:
      - "postgres"
      - "-c"
      - "port=5437"
      - "-c"
      - "wal_level=logical"
      - "-c"
      - "shared_preload_libraries=pglogical"
    environment:
      - POSTGRES_DB=reports_full
      - POSTGRES_USER=replication
      - POSTGRES_PASSWORD=replication
      - POSTGRES_HOST_AUTH_METHOD=trust
  elastic:
    build:
      context: .
      dockerfile: .docker/elasticsearch/DockerfileElastic
    container_name: elastic
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - esdata:/usr/share/elasticsearch/data
  kibana:
    build:
      context: .
      dockerfile: .docker/kibana/DockerfileKibana
    container_name: kibana
    ports:
      - "5601:5601"
    volumes:
      - kibanadata:/usr/share/kibana/data
      - ./.docker/kibana/config:/usr/share/kibana/config
    depends_on:
      - elastic
  redis:
    build:
      context: .
      dockerfile: .docker/redis/DockerfileRedis
    container_name: redis
    ports:
      - "6379:6379"
  pubsub_emulator:
    container_name: pubsub_emulator
    build:
      context: .
      dockerfile: .docker/pubsub_emulator/Dockerfile
    environment:
      - PUBSUB_GC_PROJECT_ID=bks-test-project
      - EMULATOR_PORT=8085
  public_api:
    container_name: public_api
    build: .
    # prod mode
    # command: ["uwsgi", "-i", "uwsgi_public_api.ini"]
    command: python manage.py runserver 0.0.0.0:9090
    volumes:
      - .:/home/<USER>/code
      # emails
      - ./.docker_volume/smtp_sink_emails:/home/<USER>/smtp_sink_emails
      # log and media
      - booksy_code_logs:/home/<USER>/logs
      - booksy_code_media:/home/<USER>/media
      - booksy_code_business_files:/home/<USER>/business-files
    ports:
      - "9090:9090" # public_api
    depends_on:
      - db
      - redis
      - elastic
    stdin_open: true
    tty: true
    networks:
      - default
      - booksy_merger_network
      - booksy_subdomain_network
    environment:
      - BOOKSY_COUNTRY_CODE=pl
      - DB_NAME=booksy_pl
      - MODE_API=public_api
      - PUBSUB_GC_PROJECT_ID=bks-test-project
      - EMULATOR_PORT=8085
      - pubsub_emulator
  api_pl:
    container_name: api_pl
    build:
      context: .
      secrets:
        - gcp
    command: [
       "python", "manage.py", "runserver", "0.0.0.0:8888",
       # "uwsgi", "-i", "uwsgi.ini",  # to use it comment MODE_API env
    ]
    volumes:
      - .:/home/<USER>/code
      - ./.docker/api/temp:/tmp
      # emails
      - ./.docker_volume/smtp_sink_emails:/home/<USER>/smtp_sink_emails
      # log and media
      - booksy_code_logs:/home/<USER>/logs
      - booksy_code_media:/home/<USER>/media
      - booksy_code_business_files:/home/<USER>/business-files
    ports:
      - "8888:8888"
      - "8887:8887"
    depends_on:
      - db
      - redis
      - elastic
      - pubsub_emulator
    stdin_open: true
    tty: true
    networks:
      - default
      - booksy_auth_network
      - booksy_subdomain_network
      - envoymesh
      - booksy_performance_network
    environment:
      - BOOKSY_COUNTRY_CODE=pl
      - DB_NAME=booksy_pl
      - MODE_API=drf_api
      - BOOKSY_AUTH_HOST=auth_api_grpc:8010
      - BOOKSY_AUTH_API_KEY=GdnDkDmbC1RwpEZricNbVTqHe1GEeaoWeFAngyN2yiKNwGBWLJZ28dMTuPWE5E9V
      - CLIENT_PROXY=http://bridge-client-proxy:9911
      - AUTH_USERNAME=rwgserver
      - AUTH_PASSWORD=1booksyFTW!
      - ES_NUMBER_OF_SHARDS=1
      - ES_NUMBER_OF_REPLICAS=0
      - SUBDOMAIN_HOST=subdomain_api_grpc:9099
      - EDITOR=vim
      - PUBSUB_GC_PROJECT_ID=bks-test-project
      - EMULATOR_PORT=8085
    env_file:
      - ./secrets.env
      - .env
  celery-template: &celery-template
    build: .
    environment:
      - C_FORCE_ROOT=true
      - BOOKSY_COUNTRY_CODE=pl
      - CELERY_RDB_PORT=6900
      - CELERY_RDB_HOST=0.0.0.0
      - BOOKSY_AUTH_API_KEY=GdnDkDmbC1RwpEZricNbVTqHe1GEeaoWeFAngyN2yiKNwGBWLJZ28dMTuPWE5E9V
      - SUBDOMAIN_HOST=subdomain_api_grpc:9099
      - COLUMNS=80
      - EDITOR=vim
      - PUBSUB_GC_PROJECT_ID=bks-test-project
      - EMULATOR_PORT=8085
    env_file:
      - ./secrets.env
      - .env
    command: ['celery', '-A', 'celery_app.booksy_app', 'worker', '-l', 'info']

    volumes:
      - .:/home/<USER>/code
      - ./.docker_volume/smtp_sink_emails:/home/<USER>/smtp_sink_emails
      - booksy_code_logs:/home/<USER>/logs
      - booksy_code_media:/home/<USER>/media
      - booksy_code_business_files:/home/<USER>/business-files
    depends_on:
      - db
      - redis
      - elastic
      - pubsub_emulator
      # uncomment if you want sync users in celery
      # - booksy_auth
    stdin_open: true
    tty: true
    networks:
      - default
      - booksy_auth_network
      - booksy_subdomain_network
  celery:
    container_name: celery_pl
    <<: *celery-template
    command: [ 'celery', '-A', 'celery_app.all_queues_booksy_app', 'worker', '-l', 'info' ]
  celery_priority:
    container_name: celery_priority_pl
    <<: *celery-template
    command: ['celery', '-A', 'celery_app.priority_booksy_app', 'worker', '-l', 'info']
  celery_push:
    container_name: celery_push_pl
    <<: *celery-template
    command: ['celery', '-A', 'celery_app.push_booksy_app', 'worker', '-l', 'info']
  celery_regular:
    container_name: celery_regular_pl
    <<: *celery-template
    command: ['celery', '-A', 'celery_app.regular_booksy_app', 'worker', '-l', 'info']
  celery_segment:
    container_name: celery_segment_pl
    <<: *celery-template
    command: [ 'celery', '-A', 'celery_app.segment_booksy_app', 'worker', '-l', 'info' ]
  celery_index:
    container_name: celery_index_pl
    <<: *celery-template
    command: [ 'celery', '-A', 'celery_app.index_booksy_app', 'worker', '-l', 'info' ]
  celerybeat:
    container_name: celerybeat_pl
    build: .
    command: ['/bin/sh', '-c', 'rm -f celerybeat.pid && celery -A celery_app.celerybeat_booksy_app beat']
    volumes:
      - .:/home/<USER>/code
    depends_on:
      - db
      - redis
      - elastic
    environment:
      - BOOKSY_COUNTRY_CODE=pl
      - EDITOR=vim
    env_file:
      - ./secrets.env
      - .env
  admin_pl:
    container_name: admin_pl
    hostname: 'core-us-admin-some-random-hash'
    build: .
    command:  python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/home/<USER>/code
      - ./.docker_volume/smtp_sink_emails:/home/<USER>/smtp_sink_emails
      - booksy_code_logs:/home/<USER>/logs
      - booksy_code_media:/home/<USER>/media
      - booksy_code_business_files:/home/<USER>/business-files
    ports:
      - "8000:8000"
    depends_on:
      - db
      - elastic
      - redis
      - pubsub_emulator
    stdin_open: true
    tty: true
    networks:
      - default
      - booksy_subdomain_network
    environment:
      - BOOKSY_COUNTRY_CODE=pl
      - EDITOR=vim
      - SUBDOMAIN_HOST=subdomain_api_grpc:9099
      - MODE_API=django_admin
      - PUBSUB_GC_PROJECT_ID=bks-test-project
      - EMULATOR_PORT=8085
    env_file:
      - ./secrets.env
      - .env

networks:
  # in order to use this remember
  # to add docker network create booksy_auth_network
  # for local debugging without image build=>push=>pull
  booksy_auth_network:
    external: true
  envoymesh:
    name: mesh_bridge
  bridge:
    name: grpc_bridge
  booksy_merger_network:
    external: true
  booksy_subdomain_network:
    external: true
  booksy_performance_network:
    external: true

volumes:
  pgdata:
    driver: local
  pgdata_reports_full:
    driver: local
  esdata:
    driver: local
  booksy_auth_data:
    driver: local
  kibanadata:
    driver: local
  booksy_code_logs:
    driver: local
  booksy_code_media:
    driver: local
  booksy_code_business_files:
    driver: local

secrets:
  gcp:
    file: ~/.config/gcloud/application_default_credentials.json
