from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin
from webapps.billing.interfaces import boost
from webapps.billing.serializers.boost.next_payment import BoostNextPaymentSerializer
from webapps.business.models import Resource


class BoostNextPaymentView(
    BusinessViewValidatorMixin,
    BaseBooksySessionGenericAPIView,
):
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = BoostNextPaymentSerializer

    def get(self, *args, **kwargs):
        business = self.get_business(has_new_billing=True)

        next_payment_info = boost.business_next_payment_info(business_id=business.id)

        serializer = self.get_serializer(
            {
                'total_amount': next_payment_info.total_gross_amount,
                'appointment_count': next_payment_info.appointment_count,
                'currency': next_payment_info.currency,
            }
        )

        return Response(
            serializer.data,
            status=status.HTTP_200_OK,
        )
