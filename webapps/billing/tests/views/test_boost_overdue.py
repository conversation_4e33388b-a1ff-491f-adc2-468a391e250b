from decimal import Decimal
from unittest.mock import patch

from django.conf import settings
from django.shortcuts import reverse
from django.test import TestCase
from model_bakery import baker
from rest_framework import status

from drf_api.service.tests.base import AuthenticatedBusinessAPITestCase
from lib.tools import tznow
from webapps.billing.views.boost.utils import get_boost_overdue_info
from webapps.boost.apis.overdue_transactions import (
    BoostAppointmentDataClass,
    BoostOverdueInfo,
    BoostTransaction,
)
from webapps.business.models import Business, BusinessPromotion, Resource
from webapps.invoicing.baker_recipes import user_recipe
from webapps.session.utils import get_user


class TestBoostTotalOverdue(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.business.has_new_billing = True
        self.business.save()
        self.url = reverse('boost_total_overdue', args=(self.business.id,))

    @patch('webapps.billing.interfaces.boost.total_overdue_info')
    def test_success(self, mocked_total_overdue):
        mocked_total_overdue.return_value = BoostOverdueInfo(
            business_id=self.business.id,
            gross_amount=Decimal('30.12'),
        )
        baker.make(BusinessPromotion, business=self.business)
        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_amount'], '30.12')
        self.assertEqual(response.data['currency'], settings.CURRENCY_CODE)
        self.assertFalse(response.data['business_boost_info']['boost_blocked'])
        self.assertEqual(response.data['business_boost_info']['status'], 'disabled')
        self.assertTrue(response.data['business_boost_info']['promoted_before'])

    def test_promoted_before_is_false(self):
        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_amount'], '0.00')
        self.assertEqual(response.data['currency'], settings.CURRENCY_CODE)
        self.assertFalse(response.data['business_boost_info']['boost_blocked'])
        self.assertEqual(response.data['business_boost_info']['status'], 'disabled')
        self.assertFalse(response.data['business_boost_info']['promoted_before'])

    def test_has_new_billing_is_false(self):
        self.business.has_new_billing = False
        self.business.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_not_authenticated(self):
        self.session.delete()
        get_user.clear_from_cache(user_id=self.user.id)

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_not_authorized(self):
        self.business.owner = user_recipe.make()
        self.business.save()
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        self.resource.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class TestBoostOverdueDetails(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.business.has_new_billing = True
        self.business.save()
        self.url = reverse('boost_overdue_appointments', args=(self.business.id,))

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_success(self, mocked_interface):
        mocked_interface.return_value = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(100),
                appointments=[
                    BoostAppointmentDataClass(
                        public_id='456',
                        business_id=self.business.id,
                        gross_amount=Decimal('12.567'),
                        client='client 1!',
                        date=tznow(),
                    )
                ],
                description='description',
                created=tznow(),
            ),
            BoostTransaction(
                public_id='999',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(12),
                appointments=[
                    BoostAppointmentDataClass(
                        public_id='654',
                        business_id=self.business.id,
                        gross_amount=Decimal('0.1'),
                        client='client 2!',
                        date=tznow(),
                    ),
                    BoostAppointmentDataClass(
                        public_id='111',
                        business_id=self.business.id,
                        gross_amount=Decimal(56),
                        client='client 3!',
                        date=tznow(),
                    ),
                ],
                description='description',
                created=tznow(),
            ),
        ]

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(response.data['currency'], 'USD')
        self.assertEqual(response.data['total_amount'], '112.00')
        self.assertEqual(len(response.data['appointments']), 3)
        self.assertEqual(response.data['appointments'][0]['amount'], '12.57')
        self.assertEqual(response.data['appointments'][0]['client_name'], 'client 1!')

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_no_transactions(self, mocked_interface):
        mocked_interface.return_value = []

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(response.data['currency'], 'USD')
        self.assertEqual(response.data['total_amount'], '0.00')
        self.assertEqual(len(response.data['appointments']), 0)

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_no_appointments(self, mocked_interface):
        mocked_interface.return_value = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(100),
                appointments=[],
                description='description',
                created=tznow(),
            ),
            BoostTransaction(
                public_id='999',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(12),
                appointments=None,
                description='description',
                created=tznow(),
            ),
        ]

        response = self.client.get(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['currency'], 'USD')
        self.assertEqual(response.data['total_amount'], '112.00')
        self.assertEqual(len(response.data['appointments']), 0)

    def test_has_new_billing_is_false(self):
        self.business.has_new_billing = False
        self.business.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_not_authenticated(self):
        self.session.delete()
        get_user.clear_from_cache(user_id=self.user.id)

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_not_authorized(self):
        self.business.owner = user_recipe.make()
        self.business.save()
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        self.resource.save()

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


@patch('webapps.billing.views.boost.retry_charge.batch_boost_overdue_charge_task.delay')
class TestBoostOverdueRetryCharge(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.business.has_new_billing = True
        self.business.save()
        self.url = reverse('boost_overdue_retry_charge', args=(self.business.id,))

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_ok(self, mocked_interface, mocked_task):
        mocked_interface.return_value = [
            BoostTransaction(
                public_id='123',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
            BoostTransaction(
                public_id='999',
                charge_lock_key='123',
                business_id=self.business.id,
                gross_amount=Decimal(123),
                appointments=[],
                description='description',
                created=tznow(),
            ),
        ]
        response = self.client.post(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        self.assertTrue(response.data['task_id'])
        self.assertEqual(mocked_task.call_count, 1)

        mocked_task.assert_called_with(
            business_id=self.business.id,
            selected_boost_ids=None,
            operator_id=self.business.owner_id,
        )

    @patch('webapps.billing.interfaces.boost.overdue_transactions')
    def test_validation_error(self, mocked_interface, mocked_task):
        mocked_interface.return_value = []
        response = self.client.post(self.url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            'You currently have no overdue payments.',
            response.data['errors'][0]['description'],
        )
        self.assertEqual(mocked_task.call_count, 0)

    def test_has_new_billing_is_false(self, mocked_task):
        self.business.has_new_billing = False
        self.business.save()

        response = self.client.post(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(mocked_task.call_count, 0)

    def test_not_authenticated(self, mocked_task):
        self.session.delete()
        get_user.clear_from_cache(user_id=self.user.id)

        response = self.client.post(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(mocked_task.call_count, 0)

    def test_not_authorized(self, mocked_task):
        self.business.owner = user_recipe.make()
        self.business.save()
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        self.resource.save()

        response = self.client.post(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(mocked_task.call_count, 0)


class TestBoostUtils(TestCase):
    def setUp(self):
        super().setUp()
        self.business = baker.make(Business)

    @patch('webapps.billing.interfaces.boost.total_overdue_info')
    def test_get_boost_overdue_info__success(self, mocked_total_overdue):
        mocked_total_overdue.return_value = BoostOverdueInfo(
            business_id=self.business.id,
            gross_amount=Decimal('30.12'),
        )
        baker.make(BusinessPromotion, business=self.business)

        boost_status, boost_overdue = get_boost_overdue_info(business_id=self.business.id)

        self.assertEqual(boost_overdue.gross_amount, Decimal('30.12'))
        self.assertEqual(boost_overdue.currency, settings.CURRENCY_CODE)
        self.assertFalse(boost_status.boost_blocked)
        self.assertEqual(boost_status.status, 'disabled')
        self.assertTrue(boost_status.promoted_before)

    def test_get_boost_overdue_info__promoted_before_is_false(self):
        boost_status, boost_overdue = get_boost_overdue_info(business_id=self.business.id)

        self.assertEqual(boost_overdue.gross_amount, Decimal(0))
        self.assertEqual(boost_overdue.currency, settings.CURRENCY_CODE)
        self.assertFalse(boost_status.boost_blocked)
        self.assertEqual(boost_status.status, 'disabled')
        self.assertFalse(boost_status.promoted_before)
