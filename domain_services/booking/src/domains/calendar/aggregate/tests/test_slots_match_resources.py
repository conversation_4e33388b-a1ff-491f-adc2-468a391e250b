import pytest

from ...dto import <PERSON><PERSON>Booking
from ..time_slots import TimeSlotsC<PERSON>culator
from ..types import MatchingBinsMap
from .commons import (
    APPLIANCE_ID,
    BIN_LENGTH,
    PRICE_LIST,
    RESOURCES_BOOKED_OCCUPANCY_MAP,
    SERVICE__DECREASED_RESOLUTION__ID,
    SERVICE__WITH_APPLIANCE__ID,
    SERVICE_ID,
    STAFFER_1_ID,
    STAFFER_2_ID,
)


@pytest.mark.parametrize(
    [
        "appointment_item",
        "add_padding_before",
        "add_padding_after",
        "offset",
        "expected",
    ],
    [
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE_ID,
                staffer_id=None,
            ),
            False,
            False,
            0,
            {
                6: {STAFFER_1_ID, STAFFER_2_ID},
                11: {STAFFER_1_ID},
                12: {STAFFER_1_ID, STAFFER_2_ID},
                16: {STAFFER_1_ID, STAFFER_2_ID},
                17: {STAFFER_1_ID, STAFFER_2_ID},
            },
            id="happy path",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE_ID,
                staffer_id=None,
            ),
            False,
            False,
            13,
            {
                16: {STAFFER_1_ID, STAFFER_2_ID},
                17: {STAFFER_1_ID, STAFFER_2_ID},
            },
            id="offset set",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE_ID,
                staffer_id=STAFFER_2_ID,
            ),
            False,
            False,
            0,
            {
                6: {STAFFER_2_ID},
                12: {STAFFER_2_ID},
                16: {STAFFER_2_ID},
                17: {STAFFER_2_ID},
            },
            id="staffer selected",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE_ID,
                staffer_id=None,
            ),
            False,
            True,
            0,
            {
                11: {STAFFER_1_ID},
                12: {STAFFER_1_ID, STAFFER_2_ID},
                16: {STAFFER_1_ID, STAFFER_2_ID},
            },
            id="with padding after",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE_ID,
                staffer_id=STAFFER_2_ID,
            ),
            False,
            True,
            13,
            {
                16: {STAFFER_2_ID},
            },
            id="all options on",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE__DECREASED_RESOLUTION__ID,
                staffer_id=None,
            ),
            False,
            False,
            0,
            {
                6: {STAFFER_1_ID, STAFFER_2_ID},
                11: {STAFFER_1_ID},
                16: {STAFFER_1_ID, STAFFER_2_ID},
            },
            id="decreased resolution, all staffers",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE__DECREASED_RESOLUTION__ID,
                staffer_id=STAFFER_2_ID,
            ),
            False,
            False,
            0,
            {
                6: {STAFFER_2_ID},
                12: {STAFFER_2_ID},
                16: {STAFFER_2_ID},
            },
            id="decreased resolution, staffer selected",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                staffer_id=None,
            ),
            False,
            False,
            0,
            {
                6: {STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID},
                11: {STAFFER_1_ID, APPLIANCE_ID},
                12: {STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID},
                17: {STAFFER_1_ID, STAFFER_2_ID, APPLIANCE_ID},
            },
            id="servie with appliance",
        ),
        pytest.param(
            ComboBooking(
                service_variant_id=SERVICE__WITH_APPLIANCE__ID,
                staffer_id=None,
            ),
            True,
            True,
            0,
            {
                12: {STAFFER_1_ID, APPLIANCE_ID},
            },
            id="servie with appliance, padding before and after",
        ),
    ],
)
def test__match_resources_occupancy(
    appointment_item: ComboBooking,
    add_padding_before: bool,
    add_padding_after: bool,
    offset: int,
    expected: MatchingBinsMap,
) -> None:
    actual = TimeSlotsCalculator(  # pylint: disable=protected-access
        bin_length=BIN_LENGTH,
    )._match_resources_occupancy(
        appointment_item=appointment_item,
        price_list=PRICE_LIST,
        resources_booked_occupancy_map=RESOURCES_BOOKED_OCCUPANCY_MAP,
        add_padding_before=add_padding_before,
        add_padding_after=add_padding_after,
        offset=offset,
    )
    assert actual == expected
