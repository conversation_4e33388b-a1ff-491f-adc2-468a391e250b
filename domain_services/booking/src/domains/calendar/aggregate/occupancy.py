from dataclasses import dataclass
from datetime import date
from itertools import chain
from typing import ClassVar, Generator, Iterable

from ....commons.types import Minutes, ResourceId
from ..dto import (
    DailyResourceBookedOccupancyMap,
    Occupancy,
    ResourceBookedOccupancy,
    ResourceBookedOccupancyMap,
)
from .commons import BinMixin
from .types import (
    AppointmentType,
    AvailabilitySlotsMap,
    BinSlot,
    BookedSlotsMap,
    DailyAvailabilitySlotsMap,
    DailyBookedSlotsMap,
    DailyOccupancySlotsMap,
    OccupancySlotsMap,
    ResourcesOccupancyMap,
    TimeSlot,
)


@dataclass(frozen=True)
class OccupancyCalculator(BinMixin):
    _minutes_in_day: ClassVar[Minutes] = 24 * 60

    def _build_occupancy(self, slots: Iterable[BinSlot]) -> Occupancy:
        occupancy = Occupancy(self._num_of_bins)
        for beg, end in slots:
            occupancy[self._to_bin(beg) : self._to_bin(end)] = 1
        return occupancy

    @property
    def _num_of_bins(self) -> int:
        return self._minutes_in_day // self.bin_length  # default 288

    def build_daily_resources_booked_occupancy_map(  # pylint: disable=too-many-arguments
        self,
        resource_ids: list[ResourceId],
        dates: Iterable[date],
        resources_working_hours_map: DailyAvailabilitySlotsMap,
        resources_time_offs_map: DailyOccupancySlotsMap,
        daily_bookings_slots_map: DailyBookedSlotsMap,
    ) -> DailyResourceBookedOccupancyMap:

        return {
            date_: (
                self.build_resources_booked_occupancy_map(
                    resource_ids=resource_ids,
                    resources_working_hours=resources_working_hours,
                    resources_time_offs=resources_time_offs_map.get(date_),
                    booking_slots_map=daily_bookings_slots_map.get(date_),
                )
                if (resources_working_hours := resources_working_hours_map.get(date_))
                else None
            )
            for date_ in dates
        }

    def build_resources_booked_occupancy_map(
        self,
        resource_ids: list[ResourceId],
        resources_working_hours: AvailabilitySlotsMap,
        resources_time_offs: OccupancySlotsMap | None = None,
        booking_slots_map: BookedSlotsMap | None = None,
    ) -> ResourceBookedOccupancyMap | None:
        resources_occupancy_map = self.build_resources_occupancy_map(
            resource_ids=resource_ids,
            resources_working_hours=resources_working_hours,
            resources_time_offs=resources_time_offs,
        )
        if not resources_occupancy_map or not any(resources_occupancy_map.values()):
            return None
        return self.build_booked_occupancy_map(
            resource_ids=resource_ids,
            resources_occupancy_map=resources_occupancy_map,
            booked_slots_map=booking_slots_map or {},
        )

    def build_resources_occupancy_map(
        self,
        resource_ids: Iterable[ResourceId],
        resources_working_hours: AvailabilitySlotsMap,
        resources_time_offs: OccupancySlotsMap | None = None,
    ) -> ResourcesOccupancyMap | None:
        resources_occupancy: ResourcesOccupancyMap = {}
        for resource_id in resource_ids:
            working_hours_slots = resources_working_hours.get(resource_id)
            if not working_hours_slots:
                resources_occupancy[resource_id] = None
                continue

            time_offs_slots = []
            if resources_time_offs:
                time_offs_slots = resources_time_offs.get(resource_id) or []
                # Case when time off covers whole day
                if len(time_offs_slots) == 1 and time_offs_slots[0][1] == 0:
                    resources_occupancy[resource_id] = None
                    continue

            resources_occupancy[resource_id] = self._build_occupancy(
                slots=chain(
                    self._convert_to_occupancy_time_slot(working_hours_slots),
                    time_offs_slots,
                )
            )

        return resources_occupancy

    @classmethod
    def _convert_to_occupancy_time_slot(
        cls,
        availability_slots: Iterable[TimeSlot],
    ) -> Generator[TimeSlot, None, None]:
        beg = 0
        for slot_beg, slot_end in availability_slots:
            yield [beg, slot_beg]
            beg = slot_end
        yield [beg, cls._minutes_in_day]

    def build_booked_occupancy_map(
        self,
        resource_ids: Iterable[ResourceId],
        resources_occupancy_map: ResourcesOccupancyMap,
        booked_slots_map: BookedSlotsMap,
    ) -> ResourceBookedOccupancyMap:
        result_map = {}
        for resource_id in resource_ids:
            occupancy = resources_occupancy_map.get(resource_id)
            if not occupancy:
                result_map[resource_id] = None
                continue
            occupancy = occupancy.copy()
            resource_booked_slots_map = booked_slots_map.get(resource_id) or {}
            occupancy |= self._build_occupancy(
                chain(
                    resource_booked_slots_map.get(AppointmentType.RESERVATION) or [],
                    resource_booked_slots_map.get(AppointmentType.TIMEOFF__DEPRECATED) or [],
                ),
            )
            px_occupancy = occupancy.copy()
            occupancy |= self._build_occupancy(
                chain(
                    resource_booked_slots_map.get(AppointmentType.BUSINESS) or [],
                    resource_booked_slots_map.get(AppointmentType.CUSTOMER) or [],
                ),
            )
            result_map[resource_id] = ResourceBookedOccupancy(
                booking=occupancy, padding=occupancy ^ px_occupancy
            )
        return result_map
