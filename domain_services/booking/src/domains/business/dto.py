from dataclasses import dataclass
from decimal import Decimal
from enum import StrEnum

from dateutil.relativedelta import relativedelta
from pydantic.dataclasses import dataclass as p_dataclass

from ...commons.types import Minutes, ResourceId, ServiceAddonId, ServiceVariantId


class PriceType(StrEnum):
    FIXED = 'X'
    VARIES = 'V'
    DONT_SHOW = 'D'
    FREE = 'F'
    STARTS_AT = 'S'


class ComboType(StrEnum):
    PARALLEL = 'P'
    SEQUENCE = 'S'


class PaddingType(StrEnum):
    AFTER = "A"
    BEFORE = "B"
    BEFORE_AND_AFTER = "C"


@dataclass
class BookingSettings:
    '''WARNING: This DTO is not translatable to simple types.'''

    # TODO: consider replacing with in number of minutes
    min_lead_time: relativedelta
    # TODO: consider replacing with enum (so it may use weeks and months)
    max_lead_time: relativedelta


@dataclass
class Business:
    id: int
    name: str
    timezone: str
    booking_settings: BookingSettings
    primary_category: str
    city: str | None = None
    deeplink: str | None = None
    state: str | None = None
    zip_code: str | None = None


@p_dataclass(frozen=True)
class Service:  # pylint: disable=too-many-instance-attributes
    service_variant_id: ServiceVariantId
    service_id: int
    # time slot
    duration: Minutes | None = None
    padding_type: PaddingType | None = None
    padding_duration: Minutes | None = None
    gap_start_after: Minutes = 0
    gap_duration: Minutes = 0
    post_processing_duration: Minutes | None = None
    parallel_clients: int = 1
    time_slot_interval: Minutes = 15
    # price
    price: Decimal | None = None
    price_type: PriceType | None = None
    tax_rate: Decimal | None = None
    # combo
    combo_type: ComboType | None = None
    # related objects
    appliance_ids: set[ResourceId] | None = None
    staffer_ids: set[ResourceId] | None = None
    addon_ids: set[ServiceAddonId] | None = None

    @property
    def is_combo(self) -> bool:
        return self.combo_type is not None


@p_dataclass(frozen=True)
class ServiceAddon:
    addon_id: ServiceAddonId
    duration: Minutes
    max_allowed_quantity: int
    price: Decimal | None = None
    price_type: PriceType | None = None
    tax_rate: Decimal | None = None


@p_dataclass(frozen=True)
class ServiceComboChild:
    service_variant_id: ServiceVariantId
    post_processing_duration: Minutes
    price: Decimal | None = None
    price_type: PriceType | None = None


ServicesMap = dict[ServiceVariantId, Service]
ServiceAddonsMap = dict[ServiceAddonId, ServiceAddon]
ServiceComboChildrenMap = dict[ServiceVariantId, list[ServiceComboChild]]


@p_dataclass(frozen=True)
class PriceList:
    services: ServicesMap
    addons: ServiceAddonsMap
    combo_children: ServiceComboChildrenMap


@dataclass(frozen=True)
class BusinessCustomerInfo:
    invited: bool
