from abc import ABC
from dataclasses import dataclass
from datetime import date
from decimal import Decimal
from ...commons.types import DayMinutes


PointInTime = tuple[date, DayMinutes]


@dataclass
class Discount:
    discount_amount: Decimal
    discounted_price: Decimal
    variant_price: Decimal


TimeSlotDiscount = dict[PointInTime, Discount]


@dataclass
class DiscountDefinition(ABC):
    business_id: int
    value: Decimal


@dataclass
class FlashSaleDiscountDefinition(DiscountDefinition):
    booking_start = PointInTime
    booking_end = PointInTime
    promotion_start: PointInTime
    promotion_end: PointInTime | None


@dataclass
class LastMinuteDiscountDefinition(DiscountDefinition):
    minutes_before: int


@dataclass
class HappyHoursDiscountDefinition(DiscountDefinition):
    happy_hours_week_days: list[int]
    booking_start = PointInTime
    booking_end = PointInTime
