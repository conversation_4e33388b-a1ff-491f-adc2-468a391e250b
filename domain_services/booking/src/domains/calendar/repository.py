from datetime import datetime
from typing import Protocol

from ...commons.types import ResourceId
from ..business.dto import PriceList
from .dto import DailyResourceBookedOccupancyMap


class BusinessRepository(Protocol):
    def get_price_list(self, business_id: int) -> PriceList: ...


class CalendarRepository(Protocol):
    def get_daily_resources_booked_occupancy_map(
        self,
        business_id: int,
        resource_ids: list[ResourceId],
        start_date: datetime,
        end_date: datetime,
    ) -> DailyResourceBookedOccupancyMap: ...
