import pytest
from model_bakery import baker
from parameterized import parameterized

from service.message_blast.tests import conftest
from service.tests import BaseAsyncHTTPTest
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models.category import BusinessCategory
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.message_blast.enums import (
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
)
from webapps.message_blast.helpers import update_message_blast_images
from webapps.message_blast.tests.test_helpers import (
    create_templates,
    update_message_blast_groups,
)


@pytest.mark.django_db
class MessageBlastTemplateImageHandler(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()
        update_message_blast_images()

        self.url = f'/business_api/me/businesses/{self.business.id}/message_blast/template_images/'

    def _create_category(self, category_name):
        category = baker.make(
            BusinessCategory,
            internal_name=category_name,
        )
        self.business.primary_category = category
        self.business.save()

    def test_business_category_image_us(self):
        test_cases = [
            (BusinessCategoryEnum.BARBERS, conftest.BARBER_IMAGES_COUNT),
            (BusinessCategoryEnum.SKIN_CARE, 19),
            (BusinessCategoryEnum.BROWS_AND_LASHES, 18),
            (BusinessCategoryEnum.DAY_SPA, 27),
            (BusinessCategoryEnum.AESTHETIC_MEDICINE, 29),
            (BusinessCategoryEnum.HAIR_REMOVAL, 20),
            (BusinessCategoryEnum.HAIR_SALONS, conftest.HAIR_SALON_IMAGES_COUNT),
            (BusinessCategoryEnum.MAKE_UP, 25),
            (BusinessCategoryEnum.MASSAGE, 23),
            (BusinessCategoryEnum.NAIL_SALONS, conftest.NAIL_SALON_IMAGES_COUNT),
            (BusinessCategoryEnum.KOMPUTRONIK, 5),
        ]

        for category, image_number in test_cases:
            self._create_category(category)

            url = (
                self.url + f'?template_internal_name={MessageBlastInternalNames.WELCOME_NEW_CLIENT}'
            )

            response = self.fetch(url, method='GET')
            assert response.json['images_count'] == image_number

    @parameterized.expand(
        [
            (True, conftest.BARBER_IMAGES_COUNT),
            (False, 0),
        ]
    )
    def test_message_blast_images_us(self, business_category, category_images):
        self._create_category(BusinessCategoryEnum.BARBERS)

        test_cases = [
            (MessageBlastInternalNames.WELCOME_NEW_CLIENT, 0),
            (MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES, 0),
            (MessageBlastInternalNames.PROMOTE_GIFT_CARDS, 7),
            (MessageBlastInternalNames.INVITE_FOR_VISIT, 8),
            (MessageBlastInternalNames.REINVITE_FREE_SERVICE, 9),
            (MessageBlastInternalNames.REINVITE_DISCOUNT, 9),
            (MessageBlastInternalNames.HAPPY_BIRTHDAY, 6),
            (MessageBlastInternalNames.HOLIDAY_BOOKING, 17),
            (MessageBlastInternalNames.SPRING, 4),
            (MessageBlastInternalNames.FATHERS_DAY, 8),
            (MessageBlastInternalNames.MOTHERS_DAY, 6),
            (MessageBlastInternalNames.HALLOWEEN, 10),
            (MessageBlastInternalNames.NEW_YEAR, 7),
            (MessageBlastInternalNames.HOLDAY_GIFTCARD, 17),
            (MessageBlastInternalNames.VALENTINES_DAY, 13),
            (MessageBlastInternalNames.BACK_TO_SCHOOL, 9),
            (MessageBlastInternalNames.INDEPENDENCE_DAY, 6),
            (MessageBlastInternalNames.BR_INDEPENDENCE_DAY, 0),
            (MessageBlastInternalNames.MEMORIAL_DAY, 6),
            (MessageBlastInternalNames.MLK_DAY, 4),
            (MessageBlastInternalNames.VETERANS_DAY, 6),
            (MessageBlastInternalNames.THANKSGIVING_DAY, 6),
            (MessageBlastInternalNames.CANADA_DAY, 0),
            (MessageBlastInternalNames.REMEMBERANCE_DAY, 0),
            (MessageBlastInternalNames.EASTER, 11),
            (MessageBlastInternalNames.BLACK_FRIDAY, 5),
            (MessageBlastInternalNames.CARNAVAL, 0),
            (MessageBlastInternalNames.CHILD_DAY, 0),
            (MessageBlastInternalNames.TIRADENTES, 0),
            (MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING, 5),
            (MessageBlastInternalNames.INFORM_ABOUT_TIME_OFF, 12),
            (MessageBlastInternalNames.INFORM_ABOUT_NEW_SERVICES, 0),
            (MessageBlastInternalNames.INVITE_TO_EVENT, 0),
            (MessageBlastInternalNames.ASK_FOR_REFERRAL, 5),
            (MessageBlastInternalNames.INFORM_ABOUT_PRICE_CHANGES, 8),
            (MessageBlastInternalNames.GIFT_CARD_BALANCES, 7),
            (MessageBlastInternalNames.ASK_FOR_REVIEWS, 5),
            (MessageBlastInternalNames.APPRECIATE_CUSTOMER, 15),
            (MessageBlastInternalNames.UPDATED_BOOKS, 8),
            (MessageBlastInternalNames.WHERE_YOU_CAN_BOOK, 8),
        ]

        for internal_name, image_number in test_cases:
            url = (
                self.url
                + f'?template_internal_name={internal_name}&business_category={business_category}'
            )

            response = self.fetch(url, method='GET')
            assert response.json['images_count'] - category_images == image_number

    @parameterized.expand(
        [
            (True, conftest.BARBER_IMAGES_COUNT),
            (False, 0),
        ]
    )
    def test_message_blast_images_groups_us(self, business_category, category_images):
        self._create_category(BusinessCategoryEnum.BARBERS)

        test_cases = [
            (MessageBlastGroupEnum.FIRST_IMPRESSION, 7),
            (MessageBlastGroupEnum.REACTIVATE, 26),
            (MessageBlastGroupEnum.SPECIAL_OCCASIONS, 131),
            (MessageBlastGroupEnum.YOUR_OWN_MESSAGES, 73),
        ]

        for internal_name, image_number in test_cases:
            url = (
                self.url
                + f'?group_internal_name={internal_name}&business_category={business_category}'
            )

            response = self.fetch(url, method='GET')
            assert response.json['images_count'] - category_images == image_number


@pytest.mark.django_db
class MessageBlastAllTemplateImageHandler(BaseAsyncHTTPTest):
    template_images = 237

    def setUp(self):
        super().setUp()

        update_message_blast_groups()
        create_templates()
        update_message_blast_images()

        self.url = (
            f'/business_api/me/businesses/{self.business.id}'
            '/message_blast/template_images/all_images/'
        )

    def _create_category(self, category_name):
        category = baker.make(
            BusinessCategory,
            internal_name=category_name,
        )
        self.business.primary_category = category
        self.business.save()

    def test_business_all_template_images(self):
        test_cases = [
            (BusinessCategoryEnum.BARBERS, conftest.BARBER_IMAGES_COUNT),
            (BusinessCategoryEnum.SKIN_CARE, 19),
            (BusinessCategoryEnum.BROWS_AND_LASHES, 18),
            (BusinessCategoryEnum.DAY_SPA, 27),
            (BusinessCategoryEnum.AESTHETIC_MEDICINE, 29),
            (BusinessCategoryEnum.HAIR_REMOVAL, 20),
            (BusinessCategoryEnum.HAIR_SALONS, conftest.HAIR_SALON_IMAGES_COUNT),
            (BusinessCategoryEnum.MAKE_UP, 25),
            (BusinessCategoryEnum.MASSAGE, 23),
            (BusinessCategoryEnum.NAIL_SALONS, conftest.NAIL_SALON_IMAGES_COUNT),
            (BusinessCategoryEnum.KOMPUTRONIK, 5),
        ]

        for category, images_count in test_cases:
            self._create_category(category)

            response = self.fetch(self.url, method='GET')
            assert response.json['images_count'] - self.template_images == images_count

    def test_business_all_template_images_with_uploads(self):
        self._create_category(BusinessCategoryEnum.BARBERS)

        baker.make(
            Image,
            business=self.business,
            category=ImageTypeEnum.MESSAGE_BLAST_PHOTO,
            _quantity=2,
        )

        baker.make(
            Image,
            business=self.business,
            category=ImageTypeEnum.INSPIRATION,
            _quantity=3,
        )

        response = self.fetch(self.url, method='GET')
        assert (
            response.json['images_count'] - self.template_images == conftest.BARBER_IMAGES_COUNT + 5
        )


@pytest.mark.django_db
class MessageBlastImageCategoryTreeHandler(BaseAsyncHTTPTest):
    template_images = 237

    def setUp(self):
        super().setUp()

        update_message_blast_groups()
        create_templates()
        update_message_blast_images()

        self.url = (
            f'/business_api/me/businesses/{self.business.id}'
            '/message_blast/template_images/category_tree/'
        )

    def _create_category(self, category_name: BusinessCategoryEnum):
        category = baker.make(
            BusinessCategory,
            internal_name=category_name,
        )
        self.business.primary_category = category
        self.business.save()

    def test_business_category_images_tree(self):
        test_cases = [
            (BusinessCategoryEnum.BARBERS, conftest.BARBER_IMAGES_COUNT),
            (BusinessCategoryEnum.SKIN_CARE, 19),
            (BusinessCategoryEnum.BROWS_AND_LASHES, 18),
            (BusinessCategoryEnum.DAY_SPA, 27),
            (BusinessCategoryEnum.AESTHETIC_MEDICINE, 29),
            (BusinessCategoryEnum.HAIR_REMOVAL, 20),
            (BusinessCategoryEnum.HAIR_SALONS, conftest.HAIR_SALON_IMAGES_COUNT),
            (BusinessCategoryEnum.MAKE_UP, 25),
            (BusinessCategoryEnum.MASSAGE, 23),
            (BusinessCategoryEnum.NAIL_SALONS, conftest.NAIL_SALON_IMAGES_COUNT),
            (BusinessCategoryEnum.KOMPUTRONIK, 5),
        ]

        for category, images_count in test_cases:
            self._create_category(category)

            response = self.fetch(self.url, method='GET')
            assert response.json['images_count'] - self.template_images == images_count

    def test_business_category_images_tree_with_uploads(self):
        self._create_category(BusinessCategoryEnum.BARBERS)

        baker.make(
            Image,
            business=self.business,
            category=ImageTypeEnum.MESSAGE_BLAST_PHOTO,
            _quantity=2,
        )

        baker.make(
            Image,
            business=self.business,
            category=ImageTypeEnum.INSPIRATION,
            _quantity=3,
        )

        response = self.fetch(self.url, method='GET')
        assert (
            response.json['images_count'] - self.template_images == conftest.BARBER_IMAGES_COUNT + 5
        )
