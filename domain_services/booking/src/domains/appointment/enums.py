from enum import StrEnum


class AppointmentStatusV2(StrEnum):
    ACCEPTED = 'A'  # Accepted by business
    CANCELED = 'C'
    DECLINED = 'D'
    FINISHED = 'F'
    MODIFIED = 'M'  # Modified by customer
    NOSHOW = 'N'
    PROPOSED = 'P'  # Proposed by staff
    UNCONFIRMED = 'W'
    PENDING_PAYMENT = 'O'
    DRAFT = 'X'


class AppointmentFeatureId(StrEnum):
    MULTI_BOOKING = 'multi-booking'
    COMBO_BOOKING = 'combo-booking'
    MOBILE_BOOKING = 'mobile-booking'
    ONLINE_BOOKING = 'online-booking'
    FAMILY_AND_FRIENDS = 'family-and-friends'
    ADDONS = 'addons'


class BookingFeature(StrEnum):
    STREAMLINED_BOOK_AGAIN = 'streamlined-book-again'
