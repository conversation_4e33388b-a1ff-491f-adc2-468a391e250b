from decimal import Decimal
from unittest.mock import MagicMock, call, patch

from django.test import TestCase
from model_bakery import baker

from lib.locks import BillingBoostLock
from webapps.billing.models import BillingTransaction
from webapps.billing.services.boost import OverdueBoostFlowService
from webapps.billing.services.charge import ChargeResult, StripeChargeService
from webapps.billing.tasks import (
    batch_boost_overdue_charge_task,
    finish_batch_boost_overdue_charge_task,
)
from webapps.billing.tests.services.test_boost import BoostServiceMixin
from webapps.stripe_app.enums import PaymentIntentStatus
from webapps.stripe_app.models import PaymentIntentObject


class TestBatchBoostOverdueChargeTask(BoostServiceMixin, TestCase):
    def test_task_locked(self):
        lock_ = BillingBoostLock.lock(self.business.id)

        result = batch_boost_overdue_charge_task.run(business_id=self.business.id)
        self.assertDictEqual(
            result,
            {
                'message': 'Retry charge skipped - conflicting billing process in progress.',
                'error_code': 131,
            },
        )

        BillingBoostLock.unlock(lock_)

    @patch.object(OverdueBoostFlowService, 'charge_overdue')
    def test_service_called(self, patched_service):
        batch_boost_overdue_charge_task.run(
            business_id=self.business.id,
            selected_boost_ids=[1, 2, 3],
            operator_id=4,
        )

        self.assertEqual(
            patched_service.call_args,
            call(
                business_id=self.business.id,
                selected_boost_ids=[1, 2, 3],
                operator_id=4,
            ),
        )


class TestFinishBatchBoostOverdueChargeTask(BoostServiceMixin, TestCase):
    def test_task_locked(self):
        billing_transaction = baker.make(
            BillingTransaction,
            business_id=self.business.id,
            external_id='abc',
        )
        lock_ = BillingBoostLock.lock('abc')

        result = finish_batch_boost_overdue_charge_task.run(
            billing_transaction_id=billing_transaction.id
        )
        self.assertDictEqual(
            result,
            {
                'message': 'Retry charge skipped - conflicting billing process in progress.',
                'error_code': 131,
            },
        )

        BillingBoostLock.unlock(lock_)

    @patch.object(StripeChargeService, 'retrieve_charge')
    @patch.object(OverdueBoostFlowService, 'finish_interrupted_process')
    def test_service_called(self, patched_boost_service, patched_retrieve_charge):
        billing_transaction = baker.make(
            BillingTransaction,
            business_id=self.business.id,
            external_id='123',
        )

        patched_retrieve_charge.return_value = ChargeResult(
            is_success=True,
            transaction=PaymentIntentObject(
                id=1,
                stripe_id='123',
                decimal_amount=Decimal(14),
                amount=1400,
                currency='pln',
                customer=MagicMock(),
                status=PaymentIntentStatus.REQUIRES_CONFIRMATION,
                payment_method=MagicMock(stripe_id='toooken!'),
                last_payment_error=None,
                next_action=None,
                metadata={'boost_payment_request_id': str(444)},
            ),
        )

        finish_batch_boost_overdue_charge_task.run(billing_transaction_id=billing_transaction.id)

        self.assertEqual(
            patched_boost_service.call_args,
            call(
                payment_request=444,
                billing_transaction=billing_transaction,
                payment_response=patched_retrieve_charge.return_value,
            ),
        )
